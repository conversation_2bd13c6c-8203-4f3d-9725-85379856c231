```mermaid
graph TD
    subgraph "书籍解析模块"
        A[XML内容] --> B[XMLParser.parseXml]
        B --> |解析标签| C[处理各类标签方法]
        C --> D[SpannableStringBuilder]
        B --> |样式查询| E[CSSParser]
        E --> |元素定义| F[CssElement]
        F --> |数据解析| G[DataFormatParser]
    end
    
    subgraph "排版引擎模块"
        D --> H[TextDrawViewHelper.measureAll]
        H --> I[TextPageList.addSpanned]
        I --> |创建行| J[TextLineList]
        J --> |创建单词| K[Word对象]
        I --> |辅助计算| L[TextHelper]
        L --> |宽度计算| M[getWordWidth]
        L --> |换行判断| N[isSymbolCannotLineEnd/Begin]
    end
    
    subgraph "实时渲染模块"
        K --> O[FormatContentView.dispatchDraw]
        O --> |绘制背景| P[drawBackground]
        O --> |绘制高亮| Q[drawBookmarkBg]
        O --> |绘制选择| R[drawSelection]
        J --> S[TextLineView.onDraw]
        S --> T[Word.draw]
        O --> |视图管理| U[BookTextViewHolder]
        U --> |页面管理| V[BookPageAdapter]
    end
    
    subgraph "样式处理"
        D -.-> |包含| W[各种Span]
        W --> X[AlignSpan/BlockSpan/FootnoteSpan等]
    end
    
    style A fill:#f9f,stroke:#333,stroke-width:2px
    style D fill:#bbf,stroke:#333,stroke-width:2px
    style K fill:#bfb,stroke:#333,stroke-width:2px
    style O fill:#fbf,stroke:#333,stroke-width:2px
```

# 电子书阅读应用技术文档

## 目录
1. [系统架构概述](#系统架构概述)
2. [书籍解析模块](#书籍解析模块)
3. [排版引擎模块](#排版引擎模块)
4. [实时渲染模块](#实时渲染模块)
5. [样式处理模块](#样式处理模块)
6. [核心流程详解](#核心流程详解)

---

## 系统架构概述

本电子书阅读应用采用模块化架构设计，主要分为三个核心处理流程：

1. **XML解析流程**：将XML格式的电子书内容解析为带样式的Spanned数据
2. **排版计算流程**：将Spanned数据按照页面尺寸进行分页、分行处理，生成Word二维矩阵
3. **渲染展示流程**：将Word矩阵数据实时渲染到屏幕上，并处理用户交互

---

## 书籍解析模块

### 1. XMLParser类 - XML内容解析器

**文件路径**：`com/wedevote/wdbook/ui/read/lib/xml/XMLParser.java`

**类的主要作用**：
- 解析XML格式的电子书内容
- 将XML标签转换为Android的SpannableStringBuilder对象
- 处理各种HTML标签（如p、br、a、img、table等）
- 管理脚注、锚点等特殊内容

**核心方法详解**：

#### parseXml() - 主解析方法
```java
public SpannableStringBuilder parseXml(String xmlContent, boolean paragraphIntend, boolean showPageInfo)
```
- **功能**：解析XML内容的主入口
- **参数说明**：
  - `xmlContent`：原始XML字符串
  - `paragraphIntend`：是否启用段落首行缩进
  - `showPageInfo`：是否显示页码信息
- **返回值**：SpannableStringBuilder对象，包含格式化的文本和样式
- **处理流程**：
  1. 预处理XML内容（替换&符号）
  2. 创建XmlPullParser解析器
  3. 遍历XML节点，根据标签类型调用相应处理方法
  4. 将解析结果追加到SpannableStringBuilder中

#### 标签处理方法族

- **checkLinkTag()**：处理超链接`<a>`标签
  - 支持普通链接和脚注链接
  - 创建WDLinkSpan或FootnoteSpan
  
- **checkImageTag()**：处理图片`<img>`标签
  - 支持Base64编码图片和文件路径图片
  - 创建WDImageSpan
  
- **checkBlockTag()**：处理注释块`<aside>`标签
  - 收集脚注内容
  
- **checkFontTag()**：处理字体`<font>`标签
  - 应用颜色样式

**关键成员变量**：
- `stack: Stack<FormatTextRange>`：管理嵌套标签的堆栈
- `anchors: ArrayList<FormatTextRange>`：存储锚点位置信息
- `footNoteList: ArrayList<FormatTextRange>`：存储脚注列表
- `cssParser: CSSParser`：CSS样式解析器实例
- `textHelper: TextHelper`：文本辅助工具类实例

### 2. CSSParser类 - CSS样式解析器

**文件路径**：`com/wedevote/wdbook/ui/read/lib/css/CSSParser.java`

**类的主要作用**：
- 解析CSS样式文本
- 管理样式规则映射
- 为HTML标签应用相应的样式

**核心方法详解**：

#### getInstance() - 获取单例实例
```java
public static synchronized CSSParser getInstance()
```
- 采用单例模式，确保全局只有一个CSS解析器实例
- 初始化时会根据主题加载不同的CSS文件（style.css或style_night.css）

#### loadCSSText() - 加载CSS文本
```java
public void loadCSSText(String cssText)
```
- **功能**：解析CSS文本并构建样式映射表
- **处理流程**：
  1. 移除CSS注释
  2. 解析CSS规则（选择器和属性）
  3. 创建CssElement对象存储到cssMap中

#### setTextSpans() - 应用文本样式
```java
public void setTextSpans(SpannableStringBuilder ssb, String tag, String cls, int start, int end, int flags)
```
- **功能**：为指定文本范围应用CSS样式
- **参数说明**：
  - `tag`：HTML标签名
  - `cls`：CSS类名
  - `start/end`：文本范围
- **逻辑**：查找对应的CssElement，获取样式Span并应用

**关键成员变量**：
- `cssMap: HashMap<String, CssElement>`：CSS规则映射表，key为选择器，value为样式元素

### 3. CssElement类 - CSS元素定义

**文件路径**：`com/wedevote/wdbook/ui/read/lib/css/CssElement.java`

**类的主要作用**：
- 定义各种CSS属性的内部类
- 实现CSS属性到Android Span的转换

**重要内部类**：

- **CSSColor**：颜色属性 → ForegroundColorSpan
- **CSSFontSize**：字体大小 → RelativeSizeSpan
- **CSSDecoration**：文本装饰 → UnderlineSpan/StrikethroughSpan
- **CSSTextAlign**：文本对齐 → AlignSpan
- **CSSPadding**：内边距属性 → 用于BlockSpan
- **CSSBorder**：边框属性 → 用于BlockSpan
- **CSSListStyle**：列表样式 → 决定列表标记类型

**核心方法**：
- `getTextSpans()`：获取所有文本样式Span的列表
- `merge(CssElement e)`：合并两个CSS元素的属性

### 4. DataFormatParser类 - 数据格式解析器

**文件路径**：`com/wedevote/wdbook/ui/read/lib/css/DataFormatParser.java`

**类的主要作用**：
- 解析CSS中的数值单位
- 解析CSS颜色值

**核心方法**：
- `parseFloat(String paramString)`：解析带单位的数值（em、px）
- `parseColor(String paramString)`：解析颜色值，支持简写格式（如#FFF → #FFFFFF）

---

## 排版引擎模块

### 1. TextDrawViewHelper类 - 文本绘制辅助类

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/TextDrawViewHelper.java`

**类的主要作用**：
- 将SpannableStringBuilder分割成可渲染的单词单元
- 作为排版流程的入口

**核心方法详解**：

#### measureAll() - 测量所有文本
```java
public TextPageList measureAll(float parentWidth, float parentHeight)
```
- **功能**：将文本内容进行分页处理
- **参数**：父容器的宽度和高度
- **返回值**：TextPageList对象（分页后的文本数据）

#### measureUnknownText() - 测量未知文本
```java
private TextPageList measureUnknownText(TextPageList textPageList)
```
- **功能**：将文本按字符或单词分割
- **逻辑**：
  1. 判断字符是否为连续符号（英文字母、数字等）
  2. 连续符号作为一个整体（单词）
  3. 其他字符单独处理
  4. 调用TextPageList.addSpanned()添加到页面

### 2. TextPageList类 - 文本页面列表

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/TextPageList.java`

**类的主要作用**：
- 管理一页内容的所有文本行
- 处理自动换行逻辑
- 处理特殊元素（图片、表格等）

**核心方法详解**：

#### addSpanned() - 添加文本片段
```java
public void addSpanned(Spanned spanned, int location)
```
- **功能**：将一个Spanned对象添加到页面中
- **参数说明**：
  - `spanned`：带样式的文本片段（通常是单个字符或单词）
  - `location`：在原文中的位置索引
- **处理流程**：
  1. 初始化当前文本的Span（样式信息）
  2. 计算文本宽度
  3. 判断是否需要换行
  4. 处理特殊情况（表格、长单词等）
  5. 添加到当前行或创建新行

#### addNewTextLine() - 创建新文本行
```java
private void addNewTextLine()
```
- **功能**：创建新的文本行并添加到页面
- **处理内容**：
  - 创建TextLineList对象
  - 设置行的样式（图片、块样式、表格等）
  - 计算并应用左边距

#### handleExceedLineMaxWidth() - 处理超出行宽
```java
private void handleExceedLineMaxWidth()
```
- **功能**：当文本超出行最大宽度时的处理
- **逻辑**：
  1. 检查是否有不能作为行尾的字符
  2. 将这些字符移到下一行
  3. 调整当前行的字间距

**关键成员变量**：
- `currentLine: TextLineList`：当前正在处理的行
- `textHelper: TextHelper`：文本辅助工具
- `contentWidth: float`：内容区域宽度
- `pageHeight: float`：页面高度
- `blockSpan: BlockSpan`：当前块级样式
- `tableWordLists: LinkedList<Spanned>[]`：表格单元格文本列表

### 3. TextLineList类 - 文本行列表

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/TextLineList.java`

**类的主要作用**：
- 管理一行内的所有Word对象
- 处理文本对齐和字间距调整
- 支持图片、表格、块级元素的特殊渲染
- 处理希伯来语等RTL（从右到左）语言的排版

**核心方法详解**：

#### addSpanned() - 添加文本到行
```java
public void addSpanned(Spanned content, int location, float wordWidth)
```
- **功能**：将带样式的文本片段添加到当前行
- **参数说明**：
  - `content`：带样式的文本内容
  - `location`：在原文中的位置
  - `wordWidth`：文本宽度
- **处理逻辑**：
  1. 判断字符类型（符号、空格）
  2. 创建Word对象
  3. 更新行宽度和非空白字符计数

#### adjustWordPosition() - 调整单词位置
```java
public void adjustWordPosition(float maxLineWidth)
```
- **功能**：根据对齐方式调整行内单词位置
- **处理流程**：
  1. 处理希伯来语文本（反转排列顺序）
  2. 根据对齐方式处理：
     - 左对齐：不做处理
     - 居中对齐：调用moveToCenter()
     - 两端对齐：计算并分配额外空间
  3. 处理行首行尾的空格

#### getLineHeight() - 计算行高
```java
public float getLineHeight(float originalHeight)
```
- **功能**：根据内容类型计算实际行高
- **逻辑**：
  1. 图片行：使用图片高度（如果大于原始行高）
  2. 块级元素：加上边框和内边距
  3. 相对字体大小：按比例调整
  4. 空行：高度减半

#### drawBackground() - 绘制背景
```java
public void drawBackground(Canvas canvas, int width, float offsetX, float offsetY)
```
- **功能**：绘制行的背景元素
- **支持类型**：
  - 图片：居中绘制
  - 块级元素：绘制边框和背景色
  - 表格：绘制单元格边框

**关键成员变量**：
- `lineWidth: float`：当前行的总宽度
- `lineHeight: float`：行高
- `y: float`：行的Y坐标
- `nonBlankOrSymbolWordCount: int`：非空白和符号的单词数
- `alignType: TextAlignType`：文本对齐方式
- `imageSpan: WDImageSpan`：图片样式（如果是图片行）
- `blockSpan: BlockSpan`：块级样式
- `tableDataSpan: TableDataSpan`：表格样式

### 4. TextHelper类 - 文本处理辅助类

**文件路径**：`com/wedevote/wdbook/ui/read/lib/TextHelper.java`

**类的主要作用**：
- 计算文本宽度
- 判断字符类型和换行规则
- 提供文本绘制功能

**核心方法详解**：

#### getWordWidth() - 计算文本宽度
```java
public float getWordWidth(CharSequence word)
```
- **功能**：计算单词或字符的显示宽度
- **优化策略**：
  1. 对ASCII字符使用缓存（charWidthCache数组）
  2. 中文字符使用预计算的固定宽度
  3. 特殊字符单独处理

#### 字符判断方法族
- `isSymbolCannotLineEnd(char c)`：判断字符是否不能作为行尾
- `isSymbolCannotLineBegin(char c)`：判断字符是否不能作为行首
- `isChineseSymbol(char c)`：判断是否为中文标点
- `isEnglishSymbol(char c)`：判断是否为英文标点

**关键成员变量**：
- `charWidthCache: float[]`：字符宽度缓存（128个ASCII字符）
- `CHINESE_WORD_WIDTH`：中文字符标准宽度
- `CHINESE_SYMBOL_WIDTH`：中文标点标准宽度

### 5. Word类 - 单词/字符对象

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/Word.java`

**类的主要作用**：
- 表示文本中的最小渲染单元
- 存储位置、样式和绘制信息

**核心属性**：
- `x, y`：屏幕坐标位置
- `location`：在原文本中的绝对位置
- `word: Spanned`：包含样式信息的文本内容
- `wordWidth`：显示宽度
- `linkSpan: WDLinkSpan`：链接信息
- `specialElementSpan: SpecialElementSpan`：特殊元素（如脚注图标）

**核心方法**：

#### draw() - 绘制单词
```java
public void draw(Canvas canvas, float topMargin, int height)
```
- **功能**：将单词绘制到画布上
- **处理逻辑**：
  1. 判断是否为特殊元素
  2. 特殊元素调用自定义绘制方法
  3. 普通文本调用TextHelper.drawWord()

### 6. TextPageManager类 - 文本页面管理器

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/TextPageManager.kt`

**类的主要作用**：
- 管理页面视图的创建
- 处理单词位置计算
- 记录首尾单词信息

**核心方法详解**：

#### initPageView() - 初始化页面视图
```kotlin
fun initPageView(viewGroup: ViewGroup, pageHeight: Float)
```
- **功能**：根据TextPageList创建视图组件
- **处理流程**：
  1. 遍历所有文本行
  2. 为每行创建TextLineView
  3. 记录第一个和最后一个单词
  4. 处理页面高度不足的情况

#### getWordPosition() - 获取单词位置
```kotlin
fun getWordPosition(x: Float, linePosition: Int): Int
```
- **功能**：根据触摸坐标获取单词在行中的位置
- **用途**：文本选择功能

---

## 实时渲染模块

### 1. FormatContentView类 - 格式化内容视图

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/FormatContentView.kt`

**类的主要作用**：
- 自定义视图，负责文本内容的绘制
- 处理用户交互（文本选择、笔记操作等）
- 管理高亮和笔记的显示

**核心方法详解**：

#### dispatchDraw() - 主绘制方法
```kotlin
override fun dispatchDraw(canvas: Canvas)
```
- **功能**：绘制所有内容
- **绘制顺序**：
  1. 绘制背景（drawBackground）
  2. 绘制笔记高亮（drawBookmarkBg）
  3. 绘制选择状态（drawSelection）
  4. 调用父类方法绘制子视图

#### drawBookmarkBg() - 绘制笔记背景
```kotlin
fun drawBookmarkBg(canvas: Canvas, noteEntity: NoteEntity, previousNoteY: Float): Float
```
- **功能**：绘制笔记的高亮背景
- **参数说明**：
  - `noteEntity`：笔记实体对象
  - `previousNoteY`：上一个笔记标记的Y坐标
- **处理逻辑**：
  1. 计算笔记在当前页的起始和结束位置
  2. 绘制高亮背景或下划线
  3. 绘制笔记标记图标（如果有笔记内容）

#### 交互处理方法
- `getTouchNoteData()`：根据触摸坐标获取笔记数据
- `getSelectedWordsBoundary()`：获取选中文本的边界
- `setSelectionPosition()`：设置选择起始/结束位置

**关键成员变量**：
- `textPageList: TextPageList`：页面文本数据
- `noteList: List<NoteEntity>`：笔记列表
- `wordSelectionData: WordSelectionData`：文本选择状态数据
- `highlightColor`：高亮颜色

### 2. TextLineView类 - 文本行视图

**文件路径**：`com/wedevote/wdbook/ui/read/lib/view/TextLineView.java`

**类的主要作用**：
- 渲染单行文本内容
- 作为FormatContentView的子视图

**核心方法**：

#### onDraw() - 绘制方法
```java
protected void onDraw(Canvas canvas)
```
- **功能**：绘制行内所有单词
- **逻辑**：
  1. 检查是否需要绘制文本
  2. 遍历行内所有Word对象
  3. 调用每个Word的draw方法

### 3. BookTextViewHolder类 - 书籍文本视图持有者

**文件路径**：`com/wedevote/wdbook/ui/read/BookTextViewHolder.kt`

**类的主要作用**：
- ViewHolder模式的实现
- 管理整个页面的UI组件
- 处理用户交互逻辑

**核心方法详解**：

#### reloadNoteListData() - 重新加载笔记数据
```kotlin
fun reloadNoteListData()
```
- **功能**：加载并显示当前页的笔记
- **处理流程**：
  1. 获取当前页范围内的笔记
  2. 处理搜索高亮
  3. 判断是否需要折叠笔记
  4. 更新FormatContentView

#### processOnTouch() - 处理触摸事件
```kotlin
fun processOnTouch(event: MotionEvent): Boolean
```
- **功能**：处理文本选择的拖拽操作
- **逻辑**：
  1. ACTION_DOWN：判断拖拽起始点
  2. ACTION_MOVE：更新选择范围
  3. ACTION_UP：显示操作菜单

#### 交互监听器设置
- 长按监听：开始文本选择
- 点击监听：处理链接、图片、笔记点击
- 触摸监听：处理拖拽和翻页

**关键成员变量**：
- `textPageManager: TextPageManager`：页面管理器
- `formatContentView: FormatContentView`：内容视图
- `textSelectOperateLayout: TextSelectOperateLayout`：文本选择操作栏

### 4. BookPageAdapter类 - 页面适配器

**文件路径**：`com/wedevote/wdbook/ui/read/BookPageAdapter.kt`

**类的主要作用**：
- ViewPager的适配器
- 管理多个页面的创建和销毁
- 处理页面间的切换逻辑

**核心方法**：

#### instantiateItem() - 创建页面项
```kotlin
override fun instantiateItem(container: ViewGroup, position: Int): Any
```
- **功能**：创建指定位置的页面视图
- **逻辑**：
  1. 根据position计算章节和页面索引
  2. 创建BookTextViewHolder
  3. 初始化页面数据

#### checkAndLoadText() - 检查并加载文本
```kotlin
fun checkAndLoadText(position: Int)
```
- **功能**：预加载相邻页面的文本内容
- **优化**：保持当前页、上一页、下一页三页数据在内存中

---

## 样式处理模块

### Span类族

这些类实现了各种文本样式效果：

1. **AlignSpan**：文本对齐样式
2. **BlockSpan**：块级元素样式（边框、背景、内边距）
3. **FootnoteSpan**：脚注图标样式，继承自WDLinkSpan
4. **PageInfoSpan**：页面信息显示样式
5. **WDImageSpan**：图片样式，支持缩放
6. **WDLeadingMarginSpan**：段落缩进样式

每个Span类都实现了特定的绘制逻辑，在渲染时被应用到相应的文本范围。

---

## 核心流程详解

### 1. XML到Spanned的转换流程

```
XML文本 → XMLParser.parseXml() → 解析标签和文本 → 查询CSS样式 → 创建Span对象 → SpannableStringBuilder
```

**详细步骤**：
1. XMLParser接收XML格式的电子书内容
2. 使用XmlPullParser逐个解析XML节点
3. 根据标签类型（p、a、img等）调用相应的处理方法
4. 通过CSSParser查询标签的样式信息
5. 创建对应的Span对象（如ForegroundColorSpan、UnderlineSpan等）
6. 将文本和Span添加到SpannableStringBuilder中

### 2. Spanned到TextPage的排版流程

```
SpannableStringBuilder → TextDrawViewHelper.measureAll() → TextPageList.addSpanned() → 分行分页 → Word二维矩阵
```

**详细步骤**：
1. TextDrawViewHelper将文本按字符或单词分割
2. 每个片段调用TextPageList.addSpanned()方法
3. TextPageList计算文本宽度，判断是否需要换行
4. 创建TextLineList管理每一行
5. 创建Word对象存储每个字符/单词的位置和样式信息
6. 形成页面（TextPageList） → 行（TextLineList） → 单词（Word）的层次结构

### 3. TextPage到屏幕的渲染流程

```
Word矩阵 → FormatContentView.dispatchDraw() → 绘制背景 → 绘制高亮 → TextLineView.onDraw() → Word.draw()
```

**详细步骤**：
1. FormatContentView.dispatchDraw()被系统调用
2. 先调用drawBackground()绘制页面背景
3. 调用drawBookmarkBg()绘制笔记高亮
4. 每个TextLineView绘制一行文本
5. TextLineView遍历行内的Word对象
6. 每个Word调用draw()方法绘制自身内容
7. 特殊元素（如脚注图标）使用自定义绘制逻辑

---

## 关键参数说明

### 位置相关参数
- **location**：字符在原始文本中的绝对位置索引
- **x, y**：字符/单词在屏幕上的坐标位置
- **wordStartOffset/wordEndOffset**：笔记或高亮的起始和结束位置

### 样式相关参数
- **flags**：Span应用标志，通常为SPAN_EXCLUSIVE_EXCLUSIVE
- **relativeSizeSpan**：相对字体大小
- **styleSpan**：字体样式（粗体、斜体等）

### 页面相关参数
- **contentWidth**：内容区域宽度（不包括边距）
- **pageHeight**：页面高度
- **lineHeight**：行高，用于计算行间距
- **pathIndex**：章节在EPub文件中的索引
- **indexInChapter**：页面在章节中的索引

### 性能优化相关
- **charWidthCache**：字符宽度缓存，避免重复计算
- **单例模式**：CSSParser使用单例减少内存占用
- **分页渲染**：只渲染当前可见页面，提高性能
- **三页缓存**：保持当前页、上一页、下一页在内存中

---

## 模块间的协作关系

1. **XMLParser** 依赖 **CSSParser** 获取样式信息
2. **TextPageList** 依赖 **TextHelper** 计算文本宽度和换行规则
3. **FormatContentView** 持有 **TextPageList** 引用进行渲染
4. **BookTextViewHolder** 协调各个组件，处理用户交互
5. **BookPageAdapter** 管理多个 **BookTextViewHolder** 实例

这种模块化设计使得各部分职责明确，便于维护和扩展。

---

## 类与类之间的调用关系

### 1. 书籍解析模块内部调用关系

```
XMLParser
├── 持有 CSSParser（单例）
├── 持有 TextHelper
├── 创建 FormatTextRange（用于记录标签范围）
├── 创建各种Span对象
└── 使用 DataFormatParser（静态方法）

CSSParser
├── 持有 HashMap<String, CssElement>
└── 创建 CssElement

CssElement
├── 包含多个 CSSAttribute 子类
└── 使用 DataFormatParser 解析数值
```

### 2. 排版引擎模块内部调用关系

```
TextDrawViewHelper
└── 创建并返回 TextPageList

TextPageList（继承LinkedList<TextLineList>）
├── 持有 TextHelper
├── 创建 TextLineList
├── 使用 Word 位置信息
└── 管理 FormatTextRange（锚点等）

TextLineList（继承LinkedList<Word>）
├── 持有 TextPaint
├── 持有 TextHelper
├── 创建 Word对象
└── 持有各种Span引用（WDImageSpan、BlockSpan等）

Word
├── 持有 Spanned（文本内容）
├── 引用 WDLinkSpan（如果是链接）
└── 引用 SpecialElementSpan（如果是特殊元素）

TextPageManager
├── 持有 TextPageList
└── 创建 TextLineView
```

### 3. 实时渲染模块内部调用关系

```
BookPageAdapter（PagerAdapter）
├── 创建多个 BookTextControllerHelper
├── 创建多个 BookTextViewHolder
├── 持有 PageInfoCalculateHelper
└── 管理 ReadProgressEntity

BookTextViewHolder（BaseViewHolder）
├── 持有 BookTextControllerHelper
├── 持有 TextPageManager
├── 包含 FormatContentView
├── 包含 TextSelectOperateLayout
└── 处理各种监听器

FormatContentView（LinearLayout）
├── 持有 TextPageList
├── 持有 List<NoteEntity>
├── 使用 WordSelectionData
└── 包含多个 TextLineView

TextLineView（View）
└── 持有 TextLineList
```

### 4. 跨模块调用关系

```
BookPageAdapter.buildText()
└── BookTextControllerHelper.parseText()
    └── XMLParser.parseXml()
        └── SpannableStringBuilder

BookTextControllerHelper
└── TextDrawViewHelper.measureAll()
    └── TextPageList（分页数据）

BookTextViewHolder
├── 使用 BookTextControllerHelper 的数据
└── 将 TextPageList 传递给 FormatContentView

FormatContentView
└── 遍历 TextPageList → TextLineList → Word 进行绘制
```

---

## 方法与方法之间的调用关系

### 1. XML解析流程的方法调用链

```
XMLParser.parseXml()
├── XmlPullParser.next() [循环]
├── 根据标签类型调用：
│   ├── checkLinkTag()
│   │   └── stack.push(FormatTextRange)
│   ├── checkImageTag()
│   │   └── ssb.setSpan(WDImageSpan)
│   ├── checkFontTag()
│   │   └── DataFormatParser.parseColor()
│   ├── checkBlockTag()
│   │   └── 收集脚注内容
│   └── checkListTag()
│       └── cssParser.getListStyleString()
└── cssParser.setTextSpans()
    ├── cssMap.get()
    └── element.getTextSpans()
```

### 2. 排版计算流程的方法调用链

```
TextDrawViewHelper.measureAll()
└── measureUnknownText()
    └── TextPageList.addSpanned() [循环]
        ├── initSpans() - 初始化当前文本的样式
        ├── addNewTextLine() - 需要换行时
        │   ├── new TextLineList()
        │   └── currentLine.setMargin()
        ├── TextHelper.getWordWidth() - 计算宽度
        ├── 判断是否超出行宽
        │   └── handleExceedLineMaxWidth()
        │       ├── currentLine.adjustWordPosition()
        │       └── addNewTextLine()
        └── currentLine.addSpanned()
            └── TextLineList.addWord()
                └── new Word()

TextLineList.adjustWordPosition()
├── 处理希伯来语（reverse()）
├── 根据对齐方式：
│   ├── moveToCenter() - 居中对齐
│   └── 计算间距 - 两端对齐
└── Word.addRightPosition()
```

### 3. 渲染绘制流程的方法调用链

```
FormatContentView.dispatchDraw()
├── drawBackground()
│   └── TextLineList.drawBackground()
│       ├── 图片：canvas.drawBitmap()
│       ├── 块级：blockSpan.drawBlock()
│       └── 表格：drawTableBack()
├── drawBookmarkBg()
│   ├── 计算笔记位置
│   ├── drawHighlightBlock()
│   └── drawNoteFlag()
├── drawSelection()
│   └── drawSelectionLine()
└── super.dispatchDraw()
    └── TextLineView.onDraw()
        └── Word.draw()
            ├── specialElementSpan.drawIt()
            └── TextHelper.drawWord()
```

### 4. 用户交互处理的方法调用链

```
FormatContentView.onTouchListener
└── BookTextViewHolder.processOnTouch()
    ├── ACTION_DOWN
    │   └── formatContentView.getDragPosition()
    ├── ACTION_MOVE
    │   ├── formatContentView.getLinePosition()
    │   ├── textPageManager.getWordPosition()
    │   └── formatContentView.setSelectionPosition()
    └── ACTION_UP
        ├── textSelectOperateLayout.setSelectedContainerLayoutPosition()
        └── onTextOperateListener.onTextClick()

长按事件处理：
FormatContentView.onLongClickListener
├── formatContentView.getLinePosition()
├── textPageManager.getWordPosition()
└── showTextSelectLayout()
    ├── formatContentView.setSelectionPosition()
    └── textSelectOperateLayout.setTextOperateViewShowState()
```

### 5. 页面切换的方法调用链

```
ViewPager.OnPageChangeListener.onPageSelected()
└── BookPageAdapter.checkAndLoadText()
    ├── 判断翻页方向
    ├── 更新 currentBookTextHelper
    ├── 预加载相邻章节
    │   └── buildText()
    │       ├── EPubBook.getText()
    │       ├── new BookTextControllerHelper()
    │       └── controllerHelper.parseText()
    ├── 更新工具栏
    │   └── toolLayout.setJumpTitle()
    └── 保存阅读进度
        └── savePosition()
```

### 6. 文本选择操作的方法调用链

```
用户选择文本后的操作：
TextSelectOperateLayout.onBookNoteActionListener
├── onHighlightAction() - 高亮
│   ├── initNoteEntity()
│   ├── checkAndMergeNote()
│   └── formatContentView.cancelSelection()
├── onNoteAdd() - 添加笔记
│   ├── initNoteEntity()
│   └── NoteEditActivity.gotoNoteEdit()
└── onCopy() - 复制
    └── iGetSelectTextListener.getSelectText()
        └── formatContentView.getSelectedText()
```

---

## 核心数据流向

1. **解析阶段**：XML文本 → SpannableStringBuilder（带样式）
2. **排版阶段**：SpannableStringBuilder → TextPageList → TextLineList → Word
3. **渲染阶段**：Word矩阵 → Canvas绘制
4. **交互阶段**：触摸坐标 → 行索引/单词索引 → 文本位置 → 操作响应

这种设计实现了数据与视图的分离，每个阶段都可以独立优化和测试。
