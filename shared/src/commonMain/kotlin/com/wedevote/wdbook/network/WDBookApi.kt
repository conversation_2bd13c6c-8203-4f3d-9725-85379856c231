package com.wedevote.wdbook.network

import com.wedevote.wdbook.SDKConfig
import com.wedevote.wdbook.constants.APPDistributionChannel
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.constants.UserDataType
import com.wedevote.wdbook.entity.APKDownloadUrlEntity
import com.wedevote.wdbook.entity.DeviceCountEntity
import com.wedevote.wdbook.entity.EphemeraKeyEntity
import com.wedevote.wdbook.entity.SyncDataEntity
import com.wedevote.wdbook.entity.SystemParamEntity
import com.wedevote.wdbook.entity.UpgradeEntity
import com.wedevote.wdbook.entity.UserInfoEntity
import com.wedevote.wdbook.entity.coupon.CouponCountEntity
import com.wedevote.wdbook.entity.coupon.CouponEntity
import com.wedevote.wdbook.entity.feedback.FeedbackDetailEntity
import com.wedevote.wdbook.entity.feedback.FeedbackSaveEntity
import com.wedevote.wdbook.entity.feedback.FeedbackStatusEntity
import com.wedevote.wdbook.entity.feedback.FeedbackTagEntity
import com.wedevote.wdbook.entity.feedback.HelpArticleEntity
import com.wedevote.wdbook.entity.home.NewWidgetDetailEntity
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity
import com.wedevote.wdbook.entity.home.WidgetVersionEntity
import com.wedevote.wdbook.entity.notification.NotificationDetailEntity
import com.wedevote.wdbook.entity.notification.NotificationEntity
import com.wedevote.wdbook.entity.notification.NotificationStatusEntity
import com.wedevote.wdbook.entity.shelf.FileEncryptionKeyEntity
import com.wedevote.wdbook.entity.shelf.ReportProgressResponseEntity
import com.wedevote.wdbook.entity.shelf.ShelfSyncResponse
import com.wedevote.wdbook.entity.shelf.SyncProgressEntity
import com.wedevote.wdbook.entity.store.AlipayResultInfo
import com.wedevote.wdbook.entity.store.BookFileDownloadEntity
import com.wedevote.wdbook.entity.store.CartPublisherEntity
import com.wedevote.wdbook.entity.store.EditorDetailsEntity
import com.wedevote.wdbook.entity.store.FavoriteBookEntity
import com.wedevote.wdbook.entity.store.FavoriteCountEntity
import com.wedevote.wdbook.entity.store.FileEntity
import com.wedevote.wdbook.entity.store.InAppPurchaseProductEntity
import com.wedevote.wdbook.entity.store.OrderPaymentStatusEntity
import com.wedevote.wdbook.entity.store.PaymentAmountEntity
import com.wedevote.wdbook.entity.store.ProductActivityEntity
import com.wedevote.wdbook.entity.store.ProductCountEntity
import com.wedevote.wdbook.entity.store.ProductDetailEntity
import com.wedevote.wdbook.entity.store.ProductPageListEntity
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import com.wedevote.wdbook.entity.store.SuggestProductList
import com.wedevote.wdbook.entity.user.InAppPurchaseOrderEntity
import com.wedevote.wdbook.entity.user.InAppPurchaseOrderUpdateResult
import com.wedevote.wdbook.entity.user.OrderEntity
import com.wedevote.wdbook.entity.user.UserDeviceEntity
import com.wedevote.wdbook.entity.user.UserInAppPurchaseOrder
import com.wedevote.wdbook.entity.user.WalletBalanceEntity
import com.wedevote.wdbook.entity.user.WalletTransactionEntity
import com.wedevote.wdbook.entity.search.SentenceSearchResultEntity
import io.github.aakira.napier.Napier
import io.ktor.client.HttpClient
import io.ktor.client.request.forms.FormDataContent
import io.ktor.client.request.get
import io.ktor.client.request.header
import io.ktor.client.request.headers
import io.ktor.client.request.parameter
import io.ktor.client.request.post
import io.ktor.client.request.setBody
import io.ktor.client.statement.HttpResponse
import io.ktor.http.ContentType
import io.ktor.http.Parameters
import io.ktor.http.contentType
import io.ktor.http.takeFrom

/***
 * Created by Stephen on 2021/4/21 10:44
 * @date 创建时间 2021/4/27 17:46
 * <AUTHOR> W.YuLong
 * @description
 */
internal class WDBookApi(private val sdkConfig: SDKConfig, private val httpClient: HttpClient) {
    @Throws(Throwable::class)
    private suspend inline fun <reified T> handleResponse(block: (() -> HttpResponse)): T? {
        return sdkConfig.handleResponse(block)
    }

    suspend fun getNotificationStatus(): List<NotificationStatusEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.NOTIFICATION_STATUS}")
                }
            }
        }
    }

    suspend fun uploadExceptionLogger(batchDataJson: String): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdFeedbackExceptionEndpoint()}v1/log/report")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.BATCH_DATA, batchDataJson)
                        },
                    ),
                )
            }
        }
    }

    /*验证接口返回是否正常*/
    suspend fun checkAPIHostEnable(serverUrl: String?): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("$serverUrl/testapi")
                }
            }
        }
    }

    /*获取APIServer的接口*/
    suspend fun getHostAPIServer(apiUrl: String): String? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom(apiUrl)
                }
                headers {
                    header(ApiHeader.SIGN, sdkConfig.platform.getBootstrapSign())
                }
            }
        }
    }

    suspend fun makeNotificationRead(): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.MAKE_NOTIFICATION_READ}")
                }
            }
        }
    }

    suspend fun getActivityDetailData(activityId: Long): ProductActivityEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.getActivityDetailAPI(activityId)}")
                }
            }
        }
    }

    /*获取优惠券列表*/
    suspend fun getCouponList(status: Int, minId: Long): List<CouponEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.getCouponStatusList(status)}")
                    parameter(ApiParameter.ID, minId)
                }
            }
        }
    }

    suspend fun getCouponCount(status: Int): CouponCountEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.COUPONS_COUNT}")
                    parameter(ApiParameter.STATUS, status)
                }
            }
        }
    }

    /*获取购物车列表数据*/
    suspend fun getShoppingCartList(lastUpdateTime: Long, language: String): List<CartPublisherEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SHOPPING_CART_LIST}")
                    parameter(ApiParameter.LAST_UPDATE_TIME, lastUpdateTime)
                    parameter(ApiParameter.LANGUAGE, language)
                }
            }
        }
    }

    suspend fun postShoppingCartAdd(productId: Long, language: String): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SHOPPING_CART_ADD}")
                    contentType(ContentType.Application.Json)
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append(ApiParameter.PRODUCT_ID, productId.toString())
                            },
                        ),
                    )
                }
            }
        }
    }

    suspend fun postShoppingCartRemover(productId: Long, language: String): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SHOPPING_CART_REMOVE}")
                    contentType(ContentType.Application.Json)
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append(ApiParameter.PRODUCT_ID, productId.toString())
                            },
                        ),
                    )
                }
            }
        }
    }

    /*获取消息的详情*/
    suspend fun getNotificationDetail(id: Long, type: Int): NotificationDetailEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.getNotificationDetail(id)}")
                    parameter(ApiParameter.TYPE, type)
                }
            }
        }
    }

    /*增量获取获取消息列表, type = 0是同步所有 */
    suspend fun getNotificationList(lastPublishId: Long, typeList: List<Int>? = null): List<NotificationEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.getNotificationList(lastPublishId)}")
                    val sb = StringBuilder()
                    if (!typeList.isNullOrEmpty()) {
                        for (i in typeList.indices) {
                            sb.append(typeList[i])
                            if (i < typeList.size - 1) {
                                sb.append(",")
                            }
                        }
                    } else {
                        // type = 0是同步所有
                        sb.append("0")
                    }
                    parameter(ApiParameter.TYPE, sb.toString())
                }
            }
        }
    }

    /*获取系统的参数*/
    suspend fun getSystemParam(paramsName: String): List<SystemParamEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.APP_SYSTEM_PARAM}")
                    parameter(ApiParameter.LANGUAGE, sdkConfig.market.value)
                    parameter(ApiParameter.NAME, paramsName)
                }
            }
        }
    }

    /*获取反馈类型列表*/
    suspend fun getFeedbackTagList(entrance: Int): List<FeedbackTagEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.FEEDBACK_TAGS}")
                    parameter(ApiParameter.ENTRANCE, entrance)
                    parameter(ApiParameter.LANGUAGE, sdkConfig.market.value)
                }
            }
        }
    }

    suspend fun saveFeedback(entity: FeedbackSaveEntity): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.FEEDBACK_SAVE}")
                    contentType(ContentType.Application.Json)
                    setBody(entity)
                }
            }
        }
    }

    /*获取反馈状态，是否有新反馈消息*/
    suspend fun getFeedbackStatus(): FeedbackStatusEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.FEEDBACK_STATUS}")
                }
            }
        }
    }

    /*获取反馈消息对话的列表数据*/
    suspend fun getFeedbackDetailList(feedbackId: Long): List<FeedbackDetailEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.getFeedbackDetail(feedbackId)}")
                }
            }
        }
    }

    /*获取帮助中心的列表数据*/
    suspend fun getHelpArticle(articleId: Long): HelpArticleEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.APP_HELP_ARTICLE}")
                    parameter(ApiParameter.ID, articleId)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getWidgetContainerList_V2(lastSyncKey: Long): List<WidgetContainerCombineEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WIDGET_CONTAINERS_V2}")
                    parameter(ApiParameter.LANGUAGE, sdkConfig.market.value)
                    parameter(ApiParameter.SYNC_KEY, lastSyncKey)
                }
            }
        }
    }

    /*注销账户*/
    @Throws(Throwable::class)
    suspend fun deleteUserAccount(): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.ACCOUNT_DELETE}")
                }
            }
        }
    }

    /*获取账户注销状态，0:正常 1:注销*/
    @Throws(Throwable::class)
    suspend fun getUserAccountStatus(): Int? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.ACCOUNT_STATUS}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getTokenNonce(): String? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.V2_TOKEN_NONCE}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getWidgetDetailList_V2(maxSyncKey: Long): List<NewWidgetDetailEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WIDGET_DETAILS_V2}")
                    parameter(ApiParameter.LANGUAGE, sdkConfig.market.value)
                    parameter(ApiParameter.SYNC_KEY, maxSyncKey)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getDeviceManagerCount(): DeviceCountEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.DEVICE_APP_CONSUMER}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getWidgetVersionList(maxSyncKey: Long): List<WidgetVersionEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WIDGET_VERSION_V2}")
                    parameter(ApiParameter.SYNC_KEY, maxSyncKey)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getProductWebUrl(productId: Long): String? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.PRODUCT_WEB_URL}")
                    parameter(ApiParameter.PRODUCT_ID, productId)
                }
            }
        }
    }

//    @Throws(Throwable::class)
//    suspend fun getWidgetContainerList(lastPublishId: Long): List<WidgetContainerEntity>? {
//        return handleResponse {
//            httpClient.get<ApiResponse<List<WidgetContainerEntity>>> {
//                url {
//                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WIDGET_CONTAINERS}")
//                    parameter(ApiParameter.MARKET, sdkConfig.market.value)
//                    parameter(ApiParameter.LAST_PUBLISH_ID, lastPublishId)
//                }
//            }
//        }
//    }
//
//    @Throws(Throwable::class)
//    suspend fun getWidgetDetailList(lastPublishId: Long): List<WidgetDetailEntity>? {
//        return handleResponse {
//            httpClient.get<ApiResponse<List<WidgetDetailEntity>>> {
//                url {
//                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WIDGET_DETAILS}")
//                    parameter(ApiParameter.MARKET, sdkConfig.market.value)
//                    parameter(ApiParameter.LAST_PUBLISH_ID, lastPublishId)
//                }
//            }
//        }
//    }

    @Throws(Throwable::class)
    suspend fun getSuggestKeywords(): MutableList<String>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SUGGEST_KEYWORDS}")
                }
            }
        }
    }

    /*检查错误码的空接口*/
    @Throws(Throwable::class)
    suspend fun checkErrorCodeWithEmptyData(): String? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.EMPTY_CHECK_ERROR_CODE}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun checkAppVersion(channel: APPDistributionChannel = APPDistributionChannel.ANDROID): UpgradeEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.CHECK_APP_VERSION}")
                    parameter(ApiParameter.APP_DISTRIBUTION_CHANNEL, channel.value)
                }
                headers {
                    header(ApiHeader.DEVICE_INFORMATION, sdkConfig.deviceInformation)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getAppDownloadUrl(
        fileId: String,
        channel: APPDistributionChannel,
    ): APKDownloadUrlEntity? {
        val apiPath = ApiPath.appDownloadUrl(fileId)
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}/$apiPath")
                    parameter(ApiParameter.APP_DISTRIBUTION_CHANNEL, channel.value)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getUserInfoEntity(): UserInfoEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.GET_USER_INFO}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getPurchasedList(lastSyncKey: Long): List<SyncPurchasedResponse>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_PURCHASED_LIST}")
                    parameter(ApiParameter.LAST_SYNC_KEY, lastSyncKey)
                }
            }
        }
    }

    /*更新书籍文件夹的名称*/
    @Throws(Throwable::class)
    suspend fun updateArchive(batchDataJson: String): EmptyResponse? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.UPDATE_ARCHIVE_DATA}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.BATCH_DATA, batchDataJson)
                        },
                    ),
                )
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun updateShelfBookResource(dataJson: String): List<SyncResourceDetailResponse>? {
        Napier.d(dataJson, tag = "WDBokApi")
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.UPDATE_RESOURCE}")
                    contentType(ContentType.Application.Json)
                    setBody(
                        FormDataContent(
                            Parameters.build {
                                append(ApiParameter.BATCH_DATA, dataJson)
                            },
                        ),
                    )
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getUpdatedResourceList(lastSyncKey: Long): List<SyncResourceDetailResponse>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_UPDATED_RESOURCE_LIST}")
                    parameter(ApiParameter.LAST_SYNC_KEY, lastSyncKey)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun syncShelfList(bookLastSyncKey: Long, archiveLastSyncKey: Long): ShelfSyncResponse? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SYNC_SHELF_LIST}")
                    parameter(ApiParameter.ITEM_SYNC_KEY, bookLastSyncKey)
                    parameter(ApiParameter.ARCHIVE_SYNC_KEY, archiveLastSyncKey)
                }
            }
        }
    }

    /*同步书籍的阅读历史*/
    @Throws(Throwable::class)
    suspend fun syncReadProgressData(lastSyncKey: Long): ArrayList<SyncProgressEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SYNC_GET_PROGRESS}")
                    parameter(ApiParameter.LAST_SYNC_KEY, lastSyncKey)
                }
            }
        }
    }

    /*同步所有的书籍数据*/
    @Throws(Throwable::class)
    suspend fun syncBookDataWithType(
        type: UserDataType,
        syncKey: Long,
        limit: Int = 50,
    ): ArrayList<SyncDataEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SYNC_DOWNLOAD}")
                    parameter(ApiParameter.LAST_SYNC_KEY, syncKey)
                    parameter(ApiParameter.DATA_TYPE, type.value)
                    parameter(ApiParameter.LIMIT, limit)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun uploadData(batchData: String): ArrayList<SyncDataEntity>? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SYNC_UPLOAD}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.BATCH_DATA, batchData)
                        },
                    ),
                )
            }
        }
    }

    suspend fun getUserCoupon(couponId: Long): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_COUPON}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.COUPON_ID, couponId.toString())
                        },
                    ),
                )
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun uploadProgress(batchData: String): ArrayList<ReportProgressResponseEntity>? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SYNC_REPORT_PROGRESS}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.BATCH_DATA, batchData)
                        },
                    ),
                )
            }
        }
    }

    /**********StoreBL模块***********************************/

    @Throws(Throwable::class)
    suspend fun getResourceFileEntityList(resourceId: String?): List<FileEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.resourceFileList(resourceId ?: "")}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getBookDetailInfo(productId: Long): ProductDetailEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.productDetailInfo(productId)}")
                }
            }
        }
    }

    suspend fun getCategoryBookList(categoryId: Long, page: Int): ProductPageListEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.productList(categoryId)}")
                    parameter(ApiParameter.PAGE, page.toString())
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getSearchBookList(page: Int, pageSize: Int, text: String): ProductPageListEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_ES_SEARCH}")
                    parameter(ApiParameter.PAGE, page.toString())
                    parameter(ApiParameter.PAGE_SIZE, pageSize.toString())
                    parameter(ApiParameter.TEXT, text)
                }
            }
        }
    }

    /*获取书籍分类的数量*/
    @Throws(Throwable::class)
    suspend fun getBookCategoryProductCount(categoryIds: String): ArrayList<ProductCountEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.STORE_CATEGORY_PRODUCT_COUNT}")
                    parameter(ApiParameter.CATEGORY_IDS, categoryIds)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getRecommendProductListByAPI(
        api: String,
        paramsMap: HashMap<String, String?>? = null,
    ): SuggestProductList? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}$api")

                    if (!paramsMap.isNullOrEmpty()) {
                        for (entry in paramsMap.entries) {
                            parameter(entry.key, entry.value)
                        }
                    }
//                    parameter(ApiParameter.LANGUAGE, sdkConfig.market.value)
//                    parameter(ApiParameter.LAST_PUBLISH_ID, lastPublishId.toString())
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getSuggestProductList(typeKey: String, lastPublishId: Long): SuggestProductList? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.suggestProductList(typeKey)}")
                    parameter(ApiParameter.MARKET, sdkConfig.market.value)
                    parameter(ApiParameter.LAST_PUBLISH_ID, lastPublishId.toString())
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getBookEncryptionKey(fileId: String): FileEncryptionKeyEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.encryptionKey(fileId)}")
                }
            }
        }
    }

    /**********************************************************************************************************************************/

    @Throws(Throwable::class)
    suspend fun getPaymentStatus(orderId: String): OrderPaymentStatusEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_ORDER_PAYMENT_STATUS}")
                    parameter(ApiParameter.ORDER_ID, orderId)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun createPaymentOrder(productIdList: List<Long>, entity: PaymentAmountEntity): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.CREATE_ORDER}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    CreateOrderRequest(
                        productIdList,
                        entity.actualAmount,
                        entity.couponAmount,
                        entity.activityAmount,
                        entity.getCouponIdList(),
                        entity.getActivityIdList(),
                        entity.trialCalType,
                    ),
                )
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun postYuansferPrepay(orderId: String, payMethod: String): AlipayResultInfo? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.YUANSFER_PREPAY}")
                }
                contentType(ContentType.Application.Json)
                setBody(CreatePrepayOrder(orderId, payMethod))
            }
        }
    }

    /*通过订单的ID获取支付信息*/
    @Throws(Throwable::class)
    suspend fun getPayAmountEntityWithOrderId(orderId: Long): PaymentAmountEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.getUnpaidInfo(orderId)}")
                }
            }
        }
    }

    // 试算方式：0:默认最优试算 1:指定优惠券试算
    // ignoreRepeatPurchased: true 忽略check在购买中的订单。false check在购买中的订单
    @Throws(Throwable::class)
    suspend fun getPayAmountEntity(
        productIdList: List<Long>,
        couponIds: List<Long>? = null,
        activityIds: List<Long>? = null,
        trialCalType: Int = 0,
        ignoreRepeatPurchased:Boolean = false
    ): PaymentAmountEntity? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.USER_PAYMENT_AMOUNT}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    PaymentAmountRequest(
                        productIdList,
                        couponIds,
                        activityIds,
                        trialCalType,
                        ignoreRepeatPurchased
                    ),
                )
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun createPaymentIntent(orderId: String, amount: Float): String? {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.CREATE_PAYMENT_INTENT}")
                }
                contentType(ContentType.Application.Json)
                setBody(PaymentIntentRequest(orderId, amount))
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun cancelOrder(orderId: String): Int {
        return handleResponse {
            httpClient.post() {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.USER_ORDER_CANCEL}")
                    parameter(ApiParameter.ORDER_ID, orderId)
                }
            }
        } ?: 0
    }

    @Throws(Throwable::class)
    suspend fun getEphemeralKeyEntity(): EphemeraKeyEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_STRIPE_EPHEMERAL_KEYS}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getOrderEntityList(lastUpdateTime: Long): ArrayList<OrderEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_ORDER_LIST}")
                    parameter(ApiParameter.LAST_UPDATE_TIME, lastUpdateTime.toString())
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getUserDeviceList(): List<UserDeviceEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.USER_DEVICE_LIST}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun deleteUserDevice(deviceId: String): Int {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.USER_DEVICE_DEL}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.DEVICE_ID, deviceId)
                        },
                    ),
                )
            }
        } ?: 0
    }

    @Throws(Throwable::class)
    suspend fun checkAndAddDevice(deviceId: String): Int {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.CHECK_ADD_DEVICE}")
                }
                contentType(ContentType.Application.Json)
                setBody(
                    FormDataContent(
                        Parameters.build {
                            append(ApiParameter.DEVICE_ID, deviceId)
                        },
                    ),
                )
            }
        } ?: 0
    }

    @Throws(Throwable::class)
    suspend fun initPayPalToken(): String? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_BRAINTREE_TOKEN}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun checkoutBraintree(orderId: String, nonce: String): Int {
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.CHECKOUT_BRAINTREE}")
                }
                contentType(ContentType.Application.Json)
                setBody(BraintreePaymentRequest(orderId, nonce))
            }
        } ?: 0
    }

    @Throws(Throwable::class)
    suspend fun getFileDownloadUrl(fileId: String): BookFileDownloadEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.downloadUrl(fileId)}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getWalletBalance(): WalletBalanceEntity? {
        sdkConfig.requireLogin()

        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WALLET_BALANCE}")
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getWalletTransactions(lastUpdateTime: Long): List<WalletTransactionEntity>? {
        sdkConfig.requireLogin()

        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_WALLET_TRANSACTIONS}")
                    parameter(ApiParameter.LAST_UPDATE_TIME, lastUpdateTime)
                }
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun getInAppPurchaseProductList(lastPublishId: Long): List<InAppPurchaseProductEntity>? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.GET_IN_APP_PURCHASE_PRODUCT_LIST}")
                    parameter(ApiParameter.LAST_PUBLISH_ID, lastPublishId)
                }
            }
        }
    }

    private fun getRequestParametersSign(params: Parameters): String {
        val sortedNames = params.names().sorted()
        val parts = mutableListOf<String>()
        for (paramName in sortedNames) {
            parts.add("$paramName=${params.get(paramName) ?: ""}")
        }
        val signString = "${ApiConstants.REQUEST_PARAMETERS_SIGN_PREFIX}${parts.joinToString("&")}"
        Napier.d("The sign string is $signString", tag = "WDBokApi")
        return sdkConfig.platform.getMd5Hash(signString)
    }

    @Throws(Throwable::class)
    suspend fun createInAppPurchaseOrder(
        inAppTransactionId: String,
        inAppProductId: String,
        productPrice: String,
    ): InAppPurchaseOrderEntity? {
        sdkConfig.requireLogin()

        val params = Parameters.build {
            append(ApiParameter.IN_APP_TRANSACTION_ID, inAppTransactionId)
            append(ApiParameter.IN_APP_PRODUCT_ID, inAppProductId)
            append(ApiParameter.PRODUCT_PRICE, productPrice)
        }

        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.CREATE_IN_APP_PURCHASE_ORDER}")
                    header(ApiHeader.SIGN, getRequestParametersSign(params))
                }
                contentType(ContentType.Application.Json)
                setBody(FormDataContent(params))
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun updateInAppPurchaseOrder(order: UserInAppPurchaseOrder): InAppPurchaseOrderUpdateResult? {
        sdkConfig.requireLogin()

        val params = Parameters.build {
            append(ApiParameter.RECEIPT, order.receipt)
            append(ApiParameter.IN_APP_TRANSACTION_ID, order.inAppTransactionId)
            append(ApiParameter.IOS_TRANSACTION_ID, order.appleTransactionId)
            append(ApiParameter.IN_APP_PRODUCT_ID, order.inAppProductId)
            append(ApiParameter.AMOUNT, order.productPrice)
            append(ApiParameter.STATUS, order.status.value)
        }

        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.UPDATE_IN_APP_PURCHASE_ORDER}")
                    header(ApiHeader.SIGN, getRequestParametersSign(params))
                }
                contentType(ContentType.Application.Json)
                setBody(FormDataContent(params))
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun updateInAppPurchaseReceipt(receipt: String, transactionId: String): String? {
        sdkConfig.requireLogin()

        val params = Parameters.build {
            append(ApiParameter.RECEIPT, receipt)
            append(ApiParameter.TRANSACTION_ID, transactionId)
        }
        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.UPDATE_IN_APP_PURCHASE_RECEIPT}")
                }
                setBody(FormDataContent(params))
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun buyFromWallet(
        productIds: List<Long>,
        couponIds: List<Long>?,
        activityIds: List<Long>?,
        amount: Float,
        couponAmount: Float,
        activityAmount: Float,
        trialCalType: Int,
    ): Long? {
        sdkConfig.requireLogin()

        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.BUY_FROM_WALLET}")
                }
                contentType(ContentType.Application.Json)
                setBody(BuyFromWalletRequest(productIds, couponIds, activityIds, amount, couponAmount, activityAmount, trialCalType))
            }
        }
    }

    @Throws(Throwable::class)
    suspend fun postDeviceToken(
        deviceToken: String,
        granted: Int,
    ): String? {
        val params = Parameters.build {
            append("deviceToken", deviceToken)
            append("granted", granted.toString())
        }

        return handleResponse {
            httpClient.post {
                url {
                    takeFrom("${sdkConfig.wdBibleEndpoint()}${ApiPath.POST_DEVICE_TOKEN}")
                    contentType(ContentType.Application.Json)
                    setBody(FormDataContent(params))
                }
            }
        }
    }

    /*获取书籍内搜索结果列表*/
    suspend fun getSentenceSearchResultList(resourceId: String, text: String, page: Int, pageSize: Int): SentenceSearchResultEntity? {
        return handleResponse {
            httpClient.get {
                url {
                    takeFrom("${sdkConfig.wdBookEndpoint()}${ApiPath.SENTENCE_SEARCH}")
                    parameter(ApiParameter.RESOURCE_ID, resourceId)
                    parameter(ApiParameter.TEXT, text)
                    parameter(ApiParameter.PAGE, page)
                    parameter(ApiParameter.PAGE_SIZE, pageSize)
                }
            }
        }
    }
}
