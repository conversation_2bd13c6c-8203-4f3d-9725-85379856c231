package com.wedevote.wdbook.network

/***
 * Created by <PERSON> on 2021/4/27 10:33
 *
 * @description
 */
internal object ApiPath {


    /*获取购物车的列表*/
    var SHOPPING_CART_LIST = "/v1/user/shoppingCart/list"

    /*移除购物车*/
    var SHOPPING_CART_REMOVE = "/v1/user/shoppingCart/remove"

    /*添加购物车*/
    var SHOPPING_CART_ADD = "/v1/user/shoppingCart/add"


    /*优惠券模块*/
    /*根据状态获取优惠券列表，
    * status  int  // 0:全部 1:未使用 2:已使用 3:已过期*/
    var getCouponStatusList = { status: Int -> "/v1/user/coupons/$status" }

    var COUPONS_COUNT = "/v1/user/coupons/count"

    /*领取优惠券*/
    var GET_COUPON = "/v1/user/coupon/get"

    /*获取在支付过程中的订单信息*/
    fun getUnpaidInfo(orderId: Long) = "/v1/user/order/unpaid/$orderId"

    var getActivityDetailAPI = { activityId: Long -> "/v1/store/activity/$activityId" }


    /*消息通知模块*/
    val getNotificationList =
        { minPublishedId: Long -> "/v1/app/notification/increment/$minPublishedId" }
    val getNotificationDetail = { id: Long -> "/v1/app/notification/detail/$id" }
    val MAKE_NOTIFICATION_READ = "/v1/app/notification/mark-read"
    val NOTIFICATION_STATUS = "/v1/app/notification/status"

    val getFeedbackDetail = { feedbackId: Long -> "/v1/user/feedback/$feedbackId" }

    /*用户反馈时选择反馈类型*/
    const val FEEDBACK_TAGS = "/v1/user/feedback/tags"
    const val FEEDBACK_SAVE = "/v1/user/feedback/save"
    const val FEEDBACK_LIST = "/v1/user/feedback/list"
    const val FEEDBACK_STATUS = "/v1/user/feedback/status"

    const val APP_HELP_FAQS = "/v1/app/help/faqs"
    const val APP_HELP_ARTICLE = "/v1/app/help/article"

    // Search
    const val SEARCH_PRODUCTS = "/v1/store/product/search"
    const val SUGGEST_KEYWORDS = "/v1/store/suggest/keywords"

    const val STORE_PRODUCT_FILTER = "/v1/store/product/filter"

    const val FAVORITE_ADD = "/v1/user/favorite/add"
    const val FAVORITE_REMOVE = "/v1/user/favorite/remove"
    const val FAVORITE_LIST = "/v1/user/favorite/list"
    const val FAVORITE_COUNT = "/v1/user/favorite/count"

    const val PRODUCT_WEB_URL = "/v1/store/product/web"

    /*系统参数的*/
    const val APP_SYSTEM_PARAM = "/v1/app/system/param"

    const val V2_TOKEN_NONCE = "/v2/tokenNonce"

    // 获取app配置
    const val DEVICE_APP_CONSUMER = "/v2/app/consumer"

    // SSO
    const val SSO_LOGIN = "/book/login"


    // Token
    const val V2_REFRESH_TOKEN = "/v2/refreshtoken"

    // Login Register
    const val V2_LOGIN = "/v2/login"
    const val V2_GET_CODE_TOKEN = "/v2/user/codetoken" //获取验证码token
    const val V2_CODE_WITH_SIGN = "/v2/user/code" //发送验证码
    const val V3_CHECK_CODE_WITH_SIGN = "/v3/user/checkcodeWithSign" //校验验证码
    const val V3_RESET_PASSWORD = "/v3/user/resetpassword"
    const val V3_REGISTRATION = "/v3/user/registration" //注册

    // Account
    const val GET_USER_INFO = "/v2/user/info"
    const val UPDATE_USER_INFO = "/v2/user/update"
    const val CHECK_PASSWORD = "/v2/user/checkpassword"
    const val AVATAR_UPLOAD = "/v2/user/avatar"
    const val ACCOUNT_DELETE = "/v1/user/account/delete"
    const val ACCOUNT_STATUS = "/v1/user/account/status"

    // APP
    const val GET_WIDGET_CONTAINERS_V2 = "/v2/app/widget/containers"
    const val GET_WIDGET_DETAILS_V2 = "/v2/app/widget/details"
    const val GET_WIDGET_VERSION_V2 = "/v2/app/widget/versions"

    const val GET_WIDGET_CONTAINERS = "/v1/app/widget/containers"
    const val GET_WIDGET_DETAILS = "/v1/app/widget/details"
    const val CHECK_APP_VERSION = "/v1/app/checkversion"
    val appDownloadUrl = { fileId: String -> "/v1/app/appdownloadurl/$fileId" }

    // Store
    const val STORE_CATEGORY_PRODUCT_COUNT = "/v1/store/category/productCount"
    const val GET_IN_APP_PURCHASE_PRODUCT_LIST = "/v1/store/inAppPurchase/list"
    const val GET_ES_SEARCH = "/v1/store/product/es_search"
    val encryptionKey = { fileId: String -> "/v1/store/encryptionKey/$fileId" }
    val resourceFileList = { resourceId: String -> "/v1/store/resource/files/$resourceId" }
    val downloadUrl = { fileId: String -> "/v1/store/downloadUrl/$fileId" }
    val suggestProductList = { typeKey: String -> "/v1/store/suggestProducts/$typeKey" }
    val productDetailInfo = { productId: Long -> "/v1/store/product/detail/$productId" }
    val productList = { categoryId: Long -> "/v1/store/product/list/$categoryId" }
    val editorDetails = "/v1/editor/detail"
    val publisherDetails = "/v1/publisher/detail"

    // User
    const val GET_ORDER_LIST = "/v1/user/order/list"
    const val GET_PURCHASED_LIST = "/v1/user/purchasedList"
    const val GET_UPDATED_RESOURCE_LIST = "/v1/user/updatedResourceList"
    const val GET_ORDER_PAYMENT_STATUS = "/v1/user/order/paymentStatus"
    const val USER_PAYMENT_AMOUNT = "/v1/user/payment/amount"
    const val USER_ORDER_CANCEL = "/v1/user/order/cancel"
    const val USER_ORDER_PAY_IOS = "/v1/user/order/payment" //苹果待支付支付接口
    const val USER_ORDER_RECEIPT = "/v1/user/order/receipt" //获取收据

    /*创建商品订单，返回订单号及支付金额*/
    const val CREATE_ORDER = "/v1/user/order/create"
    const val GET_WALLET_BALANCE = "/v1/user/wallet/balance"
    const val GET_WALLET_TRANSACTIONS = "/v1/user/wallet/transactions"
    const val CREATE_IN_APP_PURCHASE_ORDER = "/v1/user/inAppPurchase/create"
    const val UPDATE_IN_APP_PURCHASE_ORDER = "/v1/user/inAppPurchase/update"
    const val UPDATE_IN_APP_PURCHASE_RECEIPT = "/v1/user/inAppPurchase/receipt"
    const val BUY_FROM_WALLET = "/v1/user/order/buyFromWallet"
    const val USER_DEVICE_LIST = "/v2/user/device/list"
    const val USER_DEVICE_DEL = "/v2/user/device/del"
    const val CHECK_ADD_DEVICE = "/v2/user/device/checkAndAdd"

    //iOS 推送
    const val POST_DEVICE_TOKEN = "/v2/user/uploaddevicetoken"

    // Payment
    const val GET_STRIPE_EPHEMERAL_KEYS = "/v1/payment/stripe/ephemeral_keys"
    const val CREATE_PAYMENT_INTENT = "/v1/payment/stripe/customer/create_payment_intent"

    // Sync
    const val SYNC_REPORT_PROGRESS = "/v2/sync/progress/report"
    const val SYNC_GET_PROGRESS = "/v2/sync/progress/list"
    const val SYNC_OPEN_RESOURCE = "/v1/sync/openResource"
    const val SYNC_UPLOAD = "/v1/sync/upload"
    const val SYNC_DOWNLOAD = "/v1/sync/download"

    const val SYNC_SHELF_LIST = "/v1/sync/shelf/list"
    const val SYNC_STORE_CATEGORY_LIST = "/v1/store/category/list"

    val UPDATE_ARCHIVE_DATA = "/v1/sync/shelf/updateArchive"

    /*移动资源到分组, 从分组移出资源*/
    val UPDATE_RESOURCE = "/v1/sync/shelf/resource/update"

    const val GET_BRAINTREE_TOKEN = "/v1/payment/braintree/client_token"
    const val CHECKOUT_BRAINTREE = "/v1/payment/braintree/checkout"

    /*圆支付的预付款接口*/
    const val YUANSFER_PREPAY = "/v1/payment/yuansfer/prepay"

    /*书籍内搜索 API*/
    const val SENTENCE_SEARCH = "/v1/reader/sentence/search"

    /*空数据接口，用于检查错误码*/
    const val EMPTY_CHECK_ERROR_CODE = "/empty"
}

object ApiHeader {
    const val LOGIN_TYPE = "X-WS-LoginType"
    const val SIGN = "X-WS-Sign"
    const val ACCESS_TOKEN = "X-WS-Token"
    const val REFRESH_TOKEN = "X-WS-RefreshToken"
    const val DEVICE_INFORMATION = "X-WS-DeviceInformation"
    const val LOCALE = "X-WS-Locale"
    const val TRACE_ID = "X-WS-TraceId"
    const val REFERER = "referer"
    const val SIGN_INFO = "X-WS-SignInfo"
    const val SIGN_CODE_TOKEN = "X-WS-CodeToken"
    const val CFT_CONNECTED = "X-WS-Flag"
}

object ApiParameter {
    const val ID = "id"
    const val ENTRANCE = "entrance"
    const val DEVICE_ID = "deviceId"
    const val COUPON_ID = "couponId"
    const val APP_KEY = "appkey"
    const val MARKET = "market"
    const val LANGUAGE = "language"
    const val LAST_PUBLISH_ID = "lastPublishId"
    const val LAST_SYNC_KEY = "lastSyncKey"
    const val SYNC_KEY = "syncKey"
    const val ITEM_SYNC_KEY = "itemSyncKey"
    const val ARCHIVE_SYNC_KEY = "archiveSyncKey"
    const val DATA_TYPE = "dataType"
    const val LIMIT = "limit"
    const val BATCH_DATA = "batchData"
    const val CATEGORY_IDS = "categoryIds"
    const val TYPE_KEY = "typeKey"
    const val PAGE = "page"
    const val PAGE_SIZE = "pageSize"
    const val ORDER_ID = "orderId"
    const val EDITOR_ID = "editorId"
    const val PUBLISHER_ID = "publisherId"
    const val AMOUNT = "amount"
    const val COUPON_AMOUNT = "couponAmount"
    const val ACTIVITY_AMOUNT = "activityAmount"
    const val PRODUCT_IDS = "productIds"
    const val COUPON_IDS = "couponIds"
    const val ACTIVITY_IDS = "activityIds"
    const val TRIAL_CAL_TYPE = "trialCalType"
    const val IGNORE_REPEAT_PURCHASED = "ignoreRepeatPurchased"

    const val LAST_UPDATE_TIME = "lastUpdateTime"
    const val NONCE = "nonce"
    const val IN_APP_TRANSACTION_ID = "inAppTransactionId"
    const val IN_APP_PRODUCT_ID = "inAppProductId"
    const val PRODUCT_PRICE = "productPrice"
    const val IOS_TRANSACTION_ID = "iosTransactionId"
    const val STATUS = "status"
    const val RECEIPT = "receipt"
    const val TRANSACTION_ID = "transactionId"
    const val APP_DISTRIBUTION_CHANNEL = "distributionChannel"
    const val CLIENT_ARCHIVE_ID = "clientArchiveId"
    const val ARCHIVE_NAME = "archiveName"
    const val ARCHIVE_ID = "archiveId"
    const val RESOURCE_IDS = "resourceIds"
    const val PAY_METHOD = "payMethod"
    const val TEXT = "text"
    const val PRODUCT_ID = "productId"
    const val NAME = "name"
    const val TYPE = "type"
    const val SOURCE_TYPE = "sourceType"
    const val PLATFORM_ID = "platformId"
    const val WX_APPLET_USER_DATA = "wxAppletUserData"

    const val EMAIL = "email"
    const val MOBILE = "mobile"
    const val VERIFICATION_CODE = "verificationCode"
    const val VERIFICATION_CODE_TOKEN = "verificationCodeToken"
    const val CODE_TYPE = "codeType"
    const val TOKEN_TYPE = "tokenType"
    const val TOKEN = "token"
    const val UPDATE_TYPE = "updateType" //email,mobile,ageRange,convertTime,gender,password,nickname
    const val NICKNAME = "nickname"
    const val PASSWORD = "password"
    const val FILE = "file"
    const val RESOURCE_ID = "resourceId"
}

object ApiErrorCode {
    const val EmptyToken = 400
    const val InvalidToken = 401
    const val DeviceExceedLimit_1312 = 1312
}

internal object ApiConstants {
    const val REQUEST_PARAMETERS_SIGN_PREFIX = "wd_ios%"
}

internal enum class LoginType(val value: String) {
    Password("password")
}

/*系统参数的定义类*/
object SystemParamDefine {
    val OPERATION_AVATAR = "operationAvatar"
    val WEB_SERVER_URL = "web_server_url"
    val PAYMETHOD_CARD_DISABLE = "paymethod_card_disable"
    val PAYMETHOD_ALIPAY_DISABLE = "paymethod_alipay_disable"
    val PAYMETHOD_PAYPAL_DISABLE = "paymethod_paypal_disable"
}