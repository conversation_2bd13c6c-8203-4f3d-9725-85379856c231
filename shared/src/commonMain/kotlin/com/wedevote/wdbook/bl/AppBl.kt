package com.wedevote.wdbook.bl

import com.wedevote.wdbook.SDKConfig
import com.wedevote.wdbook.constants.APPDistributionChannel
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.constants.HighlightColorType
import com.wedevote.wdbook.constants.HomeType
import com.wedevote.wdbook.constants.ThemeStyle
import com.wedevote.wdbook.db.DbCache
import com.wedevote.wdbook.entity.APKDownloadUrlEntity
import com.wedevote.wdbook.entity.DeviceCountEntity
import com.wedevote.wdbook.entity.SystemParamEntity
import com.wedevote.wdbook.entity.UpgradeEntity
import com.wedevote.wdbook.entity.home.NewWidgetDetailEntity
import com.wedevote.wdbook.entity.home.WidgetContainerCombineEntity
import com.wedevote.wdbook.entity.store.InAppPurchaseProductEntity
import com.wedevote.wdbook.entity.store.SearchItemEntity
import com.wedevote.wdbook.entity.store.StoreCategoryEntity
import com.wedevote.wdbook.network.WDBookApi
import io.github.aakira.napier.Napier
import kotlinx.coroutines.CoroutineExceptionHandler
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

/***
 * Created by Stephen on 2021/4/21 10:36
 *
 * @description
 */
class AppBl internal constructor(
    private val sdkConfig: SDKConfig,
    dbCache: DbCache,
    private val wdBookApi: WDBookApi,
) {
    private val appDbHelper = dbCache.appDbHelper

    val imageServerEndpoint: String = sdkConfig.imageServerEndpoint()
    val wdBookSchema = sdkConfig.wdBookSchema
    val wdBookSchemaHost = sdkConfig.wdBookSchemaHost
    val androidWDBiblePackage = sdkConfig.androidWDBiblePackage
    val androidWDBibleHDPackage = sdkConfig.androidWDBibleHDPackage
    val wdBookPackage = sdkConfig.wdBookPackage

    val deviceId = sdkConfig.prefs.deviceId

    suspend fun getSuggestSearch(): MutableList<String>? {
        return wdBookApi.getSuggestKeywords()
    }

    fun getWidgetDetailDataEntity(detailId: Long): NewWidgetDetailEntity? {
        return appDbHelper.getWidgetDetailEntity(detailId)
    }

    fun getNewWidgetCombineDataList(
        homeType: HomeType,
        versionCode: Int = sdkConfig.widgetVersionCode
    ): List<WidgetContainerCombineEntity> {
        val list = appDbHelper.getNewWidgetCombineDataList(homeType.value, versionCode)
        if (list.isNotEmpty()) {
            for (item in list) {
                item.detailEntityList =
                    appDbHelper.getWidgetDetailDataList(item.containerId, item.versionId)
                        .onEach {
                            it.imgPath = sdkConfig.getFullImageUrl(it.imgPath)
                        }
            }
        }
        return list
    }

    fun getWidgetCombineEntity(containerId: Long): WidgetContainerCombineEntity? {
        try {
            val result = appDbHelper.getWidgetCombineEntity(containerId)
            if (result != null) {
                result.detailEntityList =
                    appDbHelper.getWidgetDetailDataList(result.containerId, result.versionId)
                        .onEach {
                            it.imgPath = sdkConfig.getFullImageUrl(it.imgPath)
                        }
            }
            return result
        } catch (e: Exception) {
            e.printStackTrace()
            return null
        }
    }

    fun saveSearchData(entity: SearchItemEntity) {
        if (entity.searchId.isEmpty()) {
            entity.searchId = sdkConfig.platform.createUUID()
        }
        appDbHelper.saveSearchData(entity)
    }

    fun getCategoryById(categoryId: Long): StoreCategoryEntity? {
        return appDbHelper.getCategoryById(categoryId)
    }

    fun getSearchDataList(limit: Long = 20): MutableList<SearchItemEntity> {
        return appDbHelper.getSearchHistoryList(limit)
    }

    fun clearSearchHistory() {
        appDbHelper.clearSearchHistory()
    }

    suspend fun getDeviceCount(): DeviceCountEntity? {
        return wdBookApi.getDeviceManagerCount()
    }

    suspend fun checkAppVersion(channel: APPDistributionChannel = APPDistributionChannel.ANDROID): UpgradeEntity? {
        return wdBookApi.checkAppVersion(channel)
    }

    suspend fun checkErrorCodeWithEmptyData(): String? {
        return wdBookApi.checkErrorCodeWithEmptyData()
    }

    suspend fun getAppDownloadUrl(
        fileId: String,
        channel: APPDistributionChannel
    ): APKDownloadUrlEntity? {
        return wdBookApi.getAppDownloadUrl(fileId, channel)
    }

    fun getStripeKey(): String {
        return when (sdkConfig.env) {
            AppEnvironment.TEST -> Constants.STRIPE_KEY_DEBUG
            AppEnvironment.PROD, AppEnvironment.BETA -> Constants.STRIPE_KEY_RELEASE
            AppEnvironment.INNER -> Constants.STRIPE_KEY_RELEASE
        }
    }


    fun getSystemParam(paramsName: String, callBack: (result: SystemParamEntity?) -> Unit) {
        MainScope().launch(
            CoroutineExceptionHandler { _, exception ->
                Napier.e("getSystemParam $paramsName , exception : $exception", tag = "AppBl")
            },
        ) {
            val resultList = wdBookApi.getSystemParam(paramsName)
            if (!resultList.isNullOrEmpty()) {
                for (item in resultList) {
                    appDbHelper.saveSystemParamsData(item)
                }
            }
            callBack(appDbHelper.getSystemParamsEntity(paramsName, ""))
        }
    }

    //            callBack("https://wdbook.com/privacy")
    /*隐私政策链接*/
    suspend fun getBookPrivacyURL(): String {
        val params = wdBookApi.getSystemParam("web_server_url")
        return if (!params.isNullOrEmpty()) {
            params[0].value + "/privacy"
        } else {
            "https://wdbook.com/privacy"
        }
    }

    /*用户协议链接*/
    suspend fun getBookAgreementURL(): String {
        val params = wdBookApi.getSystemParam("web_server_url")
        return if (!params.isNullOrEmpty()) {
            params[0].value + "/agreement"
        } else {
            "https://wdbook.com/agreement"
        }
    }

    suspend fun getTokenNonce(): String? {
        return wdBookApi.getTokenNonce()
    }

    @Throws(Throwable::class)
    fun getInAppPurchaseProductList(): List<InAppPurchaseProductEntity> {
        return appDbHelper.getInAppPurchaseProductList()
    }

    var currentTheme: ThemeStyle = sdkConfig.prefs.themeStyle

    fun isCurrentThemeLight(): Boolean {
        return currentTheme == ThemeStyle.LIGHT || (currentTheme == ThemeStyle.FIT_SYSTEM && sdkConfig.platform.checkSystemThemeLight())
    }

    fun toggleCurrentTheme() {
        currentTheme = if (isCurrentThemeLight()) {
            ThemeStyle.DARK
        } else {
            ThemeStyle.LIGHT
        }
        sdkConfig.prefs.themeStyle = currentTheme
    }

    fun setCurrentThemeStyle(theme: ThemeStyle) {
        currentTheme = theme
        sdkConfig.prefs.themeStyle = currentTheme
    }

    fun getHighlightStokeColor(): String {
        return if (isCurrentThemeLight()) "F5F5F5" else "CF9A57"
    }

    fun getHighlightColor(
        type: HighlightColorType,
        isLightMode: Boolean = isCurrentThemeLight()
    ): String {
        val rgb =
            if (isLightMode) {
                when (type) {
                    HighlightColorType.COLOR_RED -> "FFBFCE"
                    HighlightColorType.COLOR_BLUE -> "B2CDFA"
                    HighlightColorType.COLOR_YELLOW -> "FFF7AF"
                    HighlightColorType.COLOR_ORANGE -> "FED7AE"
                    HighlightColorType.LINE_ORANGE -> "E9973E"
                }
            } else {
                when (type) {
                    HighlightColorType.COLOR_RED -> "572C32"
                    HighlightColorType.COLOR_BLUE -> "1F3565"
                    HighlightColorType.COLOR_YELLOW -> "5C5519"
                    HighlightColorType.COLOR_ORANGE -> "65451F"
                    HighlightColorType.LINE_ORANGE -> "966124"
                }
            }
        return rgb
//        return if (sdkConfig.isIOSPlatform) "$rgb" else "$rgb"
    }

    fun getFullImageUrl(url: String): String {
        return sdkConfig.getFullImageUrl(url)
    }

}
