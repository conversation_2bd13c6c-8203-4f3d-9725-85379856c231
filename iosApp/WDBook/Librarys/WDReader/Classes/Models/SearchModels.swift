//
//  SearchModels.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/25.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

// Add cached regex at file level
private let highlightPattern = "\\{\\{\\{(.+?)\\}\\}\\}"
private let highlightRegex: NSRegularExpression? = {
    do {
        return try NSRegularExpression(pattern: highlightPattern)
    } catch {
        logRegexError(error)
        return nil
    }
}()

private func logRegexError(_ error: Error) {
    print("Failed to compile highlight regex: \(error)")
}

/// Error type for highlight text processing operations
enum HighlightError: LocalizedError {
    case regexCompilationFailed(underlying: Error)
    case invalidContent

    var errorDescription: String? {
        switch self {
        case let .regexCompilationFailed(error):
            return "Failed to compile highlight regex: \(error.localizedDescription)"
        case .invalidContent:
            return "Invalid content format"
        }
    }
}

/// Utility type for handling text highlighting operations
enum HighlightProcessor {
    private static let regex = highlightRegex

    /// Processes text content containing highlight markers (e.g., {{{highlighted text}}}) and returns
    /// the processed content with markers removed and information about the highlighted portions.
    ///
    /// - Parameters:
    ///   - content: The original text containing highlight markers in the format {{{text}}}
    ///
    /// - Returns: A tuple containing:
    ///   - processedContent: The string with highlight markers removed
    ///   - ranges: An array of NSRange values indicating the positions of highlighted text in the processed content
    ///   - highlightTexts: An array of text segments that were originally highlighted (without {{{ and }}})
    ///   These return values are typically combined into HighlightItem instances for use in the UI.
    ///
    /// - Throws: `HighlightError` if regex compilation failed or content is invalid
    static func process(_ content: String) throws -> (String, [NSRange], [String]) {
        guard let regex = regex else {
            throw HighlightError.regexCompilationFailed(underlying: NSError(domain: "HighlightProcessor", code: -1))
        }

        // Validate input
        guard !content.isEmpty else { return ("", [], []) }

        let content = content.attributeStringFromHTML().string
        let range = NSRange(content.startIndex..., in: content)
        let matches = regex.matches(in: content, range: range)

        // Early return if no highlights found
        guard !matches.isEmpty else { return (content, [], []) }

        return processMatches(matches, in: content)
    }

    /// Processes matches to extract highlighted portions and update content
    private static func processMatches(_ matches: [NSTextCheckingResult], in content: String) -> (String, [NSRange], [String]) {
        var ranges: [NSRange] = []
        var highlightTexts: [String] = []
        let processedContent = NSMutableString(string: content)
        var currentAdjustment = 0
        let nsString = content as NSString

        for match in matches {
            if let (range, text) = processMatch(match, in: nsString, currentAdjustment: currentAdjustment, contentLength: processedContent.length) {
                // Check for RTL text and validate the range
                if RTLTextProcessor.isRTLText(text) {
                    // For RTL text, find the actual occurrence in the content
                    if let correctedRange = RTLTextProcessor.findCorrectRTLRange(text: text, in: processedContent as String, currentRange: range) {
                        // Use the corrected range instead
                        ranges.append(correctedRange)
                    } else {
                        ranges.append(range)
                    }
                } else {
                    ranges.append(range)
                }

                highlightTexts.append(text)

                // Get the full original match text to find any bidirectional control characters
                let originalMatchRange = NSRange(location: match.range.location - currentAdjustment, length: match.range.length)
                let originalMatchText = processedContent.substring(with: originalMatchRange)

                // Replace the original match range "{{{text}}}" directly with the cleaned inner text "text".
                processedContent.replaceCharacters(in: originalMatchRange, with: text)
                // Adjust the current offset based on the difference between the original match length and the replacement text length.
                currentAdjustment += match.range.length - text.count
            }
        }

        return (processedContent as String, ranges, highlightTexts)
    }

    /// Processes a single match and returns the range and text if valid
    private static func processMatch(_ match: NSTextCheckingResult, in nsString: NSString, currentAdjustment: Int, contentLength: Int) -> (NSRange, String)? {
        guard match.range(at: 1).location != NSNotFound,
              match.range(at: 1).length > 0 else { return nil }

        let innerContent = nsString.substring(with: match.range(at: 1))
        let adjustedLocation = match.range.location - currentAdjustment

        guard adjustedLocation >= 0,
              adjustedLocation + innerContent.count <= contentLength else { return nil }

        // Remove bidirectional control characters from the highlighted text for internal storage
        let cleanedInnerContent = innerContent
            .replacingOccurrences(of: "\u{202A}", with: "")
            .replacingOccurrences(of: "\u{202B}", with: "")
            .replacingOccurrences(of: "\u{202C}", with: "")

        return (NSRange(location: adjustedLocation, length: cleanedInnerContent.count), cleanedInnerContent)
    }
}

/// Represents a single highlight item within search results
///
/// - Properties:
///   - range: The NSRange indicating the position of the highlighted text within the processed content
///   - text: The actual text segment that is highlighted
///
/// This struct is used to maintain both the position and content of highlighted
/// portions for accurate display and navigation within search results.
struct HighlightItem {
    let range: NSRange
    let text: String
}

/// Represents a single search result with highlighted text portions
///
/// Example:
/// ```
/// let result = SearchResult(
///     content: "The {{{quick}}} brown fox",
///     chapterIndex: 1,
///     fileId: 123,
///     fileUuid: "abc-123"
/// )
/// ```
struct SearchResult: Identifiable {
    let id = UUID()
    let content: String
    let highlights: [HighlightItem]
    let chapterIndex: Int
    let fileId: Int64
    let fileUuid: String
    let startOffset: Int
    let endOffset: Int
    var adjustedStartOffset: Int = -1

    init(content: String, chapterIndex: Int, fileId: Int64, fileUuid: String, startOffset: Int, endOffset: Int) {
        do {
            let (processedContent, ranges, highlightTexts) = try HighlightProcessor.process(content)
            self.content = processedContent
            highlights = zip(ranges, highlightTexts).map { HighlightItem(range: $0, text: $1) }
        } catch {
            self.content = content
            highlights = []
        }
        self.chapterIndex = chapterIndex
        self.fileId = fileId
        self.fileUuid = fileUuid
        self.startOffset = startOffset
        self.endOffset = endOffset
        adjustedStartOffset = -1 // Default value indicating not calculated yet
    }

    /// Returns the chapter path in the format "EPUB/{fileId}.xhtml"
    var href: String {
        return "EPUB/\(fileId).xhtml"
    }

    /// Calculates the final offset for navigation, including adjustment and first highlight location.
    var navigationOffset: Int {
        // Start with adjusted offset, fallback to original, then 0 if invalid
        let baseOffset = adjustedStartOffset >= 0 ? adjustedStartOffset : startOffset

        var finalOffset: Int
        if !highlights.isEmpty, baseOffset >= 0 {
            // Add the location of the first highlight relative to the start of the sentence
            finalOffset = Int(baseOffset) + highlights[0].range.location // Ensure types match if needed
        } else {
            finalOffset = baseOffset >= 0 ? Int(baseOffset) : 0 // Use base or 0 if invalid/no highlights
        }

        // Ensure finalOffset is not negative
        return max(0, finalOffset)
    }
}

/// Represents a grouped section of search results
///
/// Used to organize search results into logical sections, such as by book or chapter
struct SearchSection: Identifiable {
    let id = UUID()
    let title: String
    var results: [SearchResult]
}

/// Result type for search operations
enum SearchPerformResult {
    case success
    case failure(Error?)
    case cancelled
}
