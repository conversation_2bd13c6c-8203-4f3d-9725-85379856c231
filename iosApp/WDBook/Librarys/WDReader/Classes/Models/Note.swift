//
//  Note.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/7/9.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import CoreGraphics
import Foundation
import shared
import Swift

class Note: ObservableObject {
    var style: HighlightStyle
    var chapterPath: String
    var filePosition: Int
    var range: NSRange
    var summary: String
    var tocTitle: String
    @Published var noteText: String
    @Published var createTime: Date

    var noteEntity: NoteEntity?
    var entryRect: CGRect? // 快捷入口显示区域
    var entryInteractRect: CGRect? // 快捷入口交互区域（大于显示区域）
    var mergeNotes: [Note]?

    init() {
        style = HighlightRangeManager.defaultStyle
        chapterPath = ""
        filePosition = 0
        range = NSRange()
        summary = ""
        noteText = ""
        tocTitle = ""
        createTime = Date()
    }

    init(chapterPath: String, range: NSRange, summary: String, noteText: String = "", style: HighlightStyle = HighlightRangeManager.defaultStyle) {
        self.style = style
        self.chapterPath = chapterPath
        filePosition = WDReaderCenter.shared.getFileChapterIndex(href: chapterPath)
        self.range = range
        self.summary = summary
        self.noteText = noteText
        tocTitle = Paginator.current.getSubLevelChapterName(indexPath: Paginator.current.currentIndexPath) ?? ""
        createTime = Date()
    }

    init(noteEntity entity: NoteEntity) {
        chapterPath = entity.pagePath
        filePosition = Int(entity.filePosition)
        summary = entity.summary
        noteText = entity.noteText
        tocTitle = entity.tocTitle
        createTime = Date(timeIntervalSince1970: Double(entity.createTime) / 1000)
        range = NSRange(location: Int(entity.wordStartOffset), length: Int(entity.wordEndOffset - entity.wordStartOffset))
        style = HighlightStyle.generate(type: entity.highlightColorType, isUnderLine: entity.markStyle == 1 ? true : false)
        noteEntity = entity
    }

    func changeNoteEntity(entity: NoteEntity) {
        chapterPath = entity.pagePath
        filePosition = Int(entity.filePosition)
        summary = entity.summary
        noteText = entity.noteText
        tocTitle = entity.tocTitle
        createTime = Date(timeIntervalSince1970: Double(entity.createTime) / 1000)
        range = NSRange(location: Int(entity.wordStartOffset), length: Int(entity.wordEndOffset - entity.wordStartOffset))
        style = HighlightStyle.generate(type: entity.highlightColorType, isUnderLine: entity.markStyle == 1 ? true : false)
        noteEntity = entity
    }

    var isConflict: Bool {
        if let conflictRemoteId = noteEntity?.conflictRemoteId,!conflictRemoteId.isEmpty {
            return true
        } else {
            return false
        }
    }
}

extension Note: Identifiable {
    public var id: String {
        return noteEntity?.dataId ?? ""
    }
}

extension Note: Equatable {
    static func == (lhs: Note, rhs: Note) -> Bool {
        lhs.id == rhs.id
    }
}

extension NoteEntity: Identifiable {
    public var id: String {
        return dataId ?? ""
    }

    var isConflict: Bool {
        !conflictRemoteId.isEmpty
    }
}

extension Note {
    var showRange: NSRange {
        if let content = Paginator.current.getChapter(href: chapterPath)?.displayedAttributedString.resetImageToBlank().string, !content.isEmpty {
            return noteEntity?.showRange(chapterContent: content) ?? range
        } else {
            return range
        }
    }
}

extension NoteEntity {
    func showRange(chapterContent: String) -> NSRange {
        let entity = WDBookUserSDK.shared.matchNoteInContent(noteEntity: self, content: chapterContent)
        return NSRange(location: Int(entity.wordStartOffset), length: Int(entity.wordEndOffset - entity.wordStartOffset))
    }
}
