//
//  HighlightStyle.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/7/9.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared
import SwiftyUserDefaults
import UIKit

struct HighlightStyle {
    static let Styles = [HighlightStyle(id: 0, type: .colorRed, showColor: dynamicHighlightPinkColor),
                         HighlightStyle(id: 1, type: .colorBlue, showColor: dynamicHighlightBlueColor),
                         HighlightStyle(id: 2, type: .colorYellow, showColor: dynamicHighlightYellowColor),
                         HighlightStyle(id: 3, type: .colorOrange, showColor: dynamicHighlightOrangeColor),
                         HighlightStyle(id: 4, type: .lineOrange, showColor: HighlightUnderlineColor, fillColor: dynamicHighlightUnderlineFillColor, isUnderline: true)]
    static var defaultStyle: HighlightStyle {
        get {
            return HighlightStyle.Styles[Defaults[key: DefaultsKeys.HIGH_LIGHT_STYLE]]
        }
        set {
            Defaults[key: DefaultsKeys.HIGH_LIGHT_STYLE] = newValue.id
        }
    }

    var id: Int
    var type: HighlightColorType
    var showColor: UIColor // 显示色
    var fillColor: UIColor? // 下划线情况下的填充色。
    var isUnderline: Bool = false

    static func generate(type: HighlightColorType, isUnderLine _: Bool) -> HighlightStyle {
        return Styles.first(where: { $0.type == type }) ?? defaultStyle
    }
}

extension HighlightStyle: Equatable {
    static func == (lhs: Self, rhs: Self) -> Bool {
        lhs.type == rhs.type
    }
}

extension DefaultsKeys {
    static let HIGH_LIGHT_STYLE_KEY = "HIGH_LIGHT_STYLE_KEY"
    static let HIGH_LIGHT_STYLE = DefaultsKey<Int>(HIGH_LIGHT_STYLE_KEY, defaultValue: 2)
}
