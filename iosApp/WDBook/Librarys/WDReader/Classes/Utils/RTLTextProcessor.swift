//
//  RTLTextProcessor.swift
//  WDBook
//
//  Created by <PERSON> on 2025/3/19.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

/// Utility for handling right-to-left text detection and processing
enum RTLTextProcessor {
    /// Detects the writing direction of text using the Unicode Bidirectional Algorithm
    /// This handles all RTL languages (Hebrew, Arabic, Persian, etc.)
    static func detectTextDirection(_ text: String) -> NSWritingDirection {
        // Use Unicode Bidirectional Algorithm to determine overall text direction
        // This will handle all RTL languages, not just Hebrew
        let detector = NSLinguisticTagger(tagSchemes: [.language], options: 0)
        detector.string = text
        if let language = detector.dominantLanguage,
           ["he", "ar", "fa", "ur"].contains(language)
        {
            return .rightToLeft
        }
        return .leftToRight
    }

    /// Checks if text is in a right-to-left language
    static func isRTLText(_ text: String) -> <PERSON>ol {
        // Quick check for common RTL scripts
        let rtlRanges = [
            // Hebrew
            CharacterSet(charactersIn: "\u{0590}" ... "\u{05FF}"),
            // Arabic
            CharacterSet(charactersIn: "\u{0600}" ... "\u{06FF}"),
            // Arabic Supplement and Extended
            CharacterSet(charactersIn: "\u{0750}" ... "\u{077F}"),
            CharacterSet(charactersIn: "\u{08A0}" ... "\u{08FF}"),
            // Hebrew and Arabic presentation forms
            CharacterSet(charactersIn: "\u{FB1D}" ... "\u{FB4F}"),
            CharacterSet(charactersIn: "\u{FB50}" ... "\u{FDFF}"),
            CharacterSet(charactersIn: "\u{FE70}" ... "\u{FEFF}"),
        ]

        // First check for any RTL characters as a quick filter
        for range in rtlRanges {
            if text.rangeOfCharacter(from: range) != nil {
                return true
            }
        }

        // If no obvious RTL characters, use linguistic tagger as fallback
        return detectTextDirection(text) == .rightToLeft
    }

    /// Find the actual ranges of RTL text in content
    /// Returns an array of tuples with NSRange and the corresponding text
    static func findActualRangesForRTLHighlights(in content: String, highlights: [HighlightItem]) -> [(NSRange, String)] {
        var result: [(NSRange, String)] = []
        let nsString = content as NSString

        for highlight in highlights where isRTLText(highlight.text) {
            // Try to find the actual occurrence of the RTL text in the content
            let rtlText = highlight.text

            // RTL text might be surrounded by RTL markers, so search for it directly
            var range = nsString.range(of: rtlText)

            // If direct search failed, try with different character combinations
            if range.location == NSNotFound {
                // Try to find text with potential RTL markers
                let rtlMarkedText = "\u{202B}" + rtlText + "\u{202C}"
                range = nsString.range(of: rtlMarkedText)

                if range.location == NSNotFound {
                    // Try with LTR markers
                    let ltrText = "\u{202A}" + rtlText + "\u{202C}"
                    range = nsString.range(of: ltrText)

                    if range.location == NSNotFound {
                        // Try just the embedded text between any RTL markers
                        for i in 0 ..< nsString.length - 2 {
                            let threeChars = nsString.substring(with: NSRange(location: i, length: min(3, nsString.length - i)))
                            if threeChars.contains(rtlText) {
                                // Found a potential match - expand to check surrounding characters
                                let start = max(0, i - 2)
                                let length = min(nsString.length - start, rtlText.count + 4) // Allow for RTL markers
                                let potentialRange = NSRange(location: start, length: length)
                                let potentialText = nsString.substring(with: potentialRange)

                                if potentialText.contains(rtlText) {
                                    // Further refine the range to just the RTL text itself
                                    if let actualRange = potentialText.range(of: rtlText) {
                                        let startOffset = potentialText.distance(from: potentialText.startIndex, to: actualRange.lowerBound)
                                        let refinedRange = NSRange(location: start + startOffset, length: rtlText.count)
                                        range = refinedRange
                                        break
                                    }
                                }
                            }
                        }
                    }
                }
            }

            if range.location != NSNotFound {
                result.append((range, rtlText))
            } else {
                // Fallback to the original range if we couldn't find a better one
                result.append((highlight.range, rtlText))
            }
        }

        return result
    }

    /// Performs a character-by-character sliding window search for RTL text
    /// This is more intensive but can handle complex bidirectional text cases
    static func slidingWindowSearchForRTL(text: String, in content: String) -> NSRange {
        // Use the faster Boyer-Moore algorithm for better performance
        return boyerMooreSearchForRTL(text: text, in: content)
    }

    /// Implementation of Boyer-Moore string search algorithm optimized for RTL text
    /// This has better average-case performance than the simple sliding window search (closer to O(n/m) in the best case)
    static func boyerMooreSearchForRTL(text: String, in content: String) -> NSRange {
        let textChars = Array(text)
        let contentChars = Array(content)
        let textLen = textChars.count
        let contentLen = contentChars.count

        guard textLen > 0, contentLen >= textLen else { return NSRange(location: NSNotFound, length: 0) }

        // Build the bad character table
        var badCharTable = [Character: Int]()
        for i in 0 ..< textLen {
            badCharTable[textChars[i]] = textLen - 1 - i
        }

        // Build the good suffix table
        var goodSuffixSkip = Array(repeating: 0, count: textLen)
        computeGoodSuffixShift(pattern: textChars, goodSuffixSkip: &goodSuffixSkip)

        // Build border position table (used by good suffix rule)
        var borderPos = Array(repeating: 0, count: textLen + 1)
        computeBorderPositions(pattern: textChars, borderPos: &borderPos)

        // Main search loop
        var i = textLen - 1
        while i < contentLen {
            var j = textLen - 1
            var k = i

            // Check current window
            while j >= 0 && contentChars[k] == textChars[j] {
                j -= 1
                k -= 1
            }

            // If we matched the entire pattern
            if j < 0 {
                return NSRange(location: k + 1, length: textLen)
            }

            // Calculate how many positions to skip using both rules
            let badCharSkip = badCharTable[contentChars[i]] ?? textLen
            let goodSuffixSkip = j < textLen - 1 ? goodSuffixSkip[j + 1] : 0

            // Take the maximum of the two skip values
            let skip = max(badCharSkip, goodSuffixSkip)

            i += skip
        }

        return NSRange(location: NSNotFound, length: 0)
    }

    /// Compute the good suffix shift table for Boyer-Moore algorithm
    private static func computeGoodSuffixShift(pattern: [Character], goodSuffixSkip: inout [Int]) {
        let m = pattern.count
        var suffixLength = Array(repeating: 0, count: m)

        // Find the length of the longest suffix at each position
        suffixLength[m - 1] = m
        var g = m - 1
        var f = 0

        for i in stride(from: m - 2, through: 0, by: -1) {
            if i > g && suffixLength[i + m - 1 - f] < i - g {
                suffixLength[i] = suffixLength[i + m - 1 - f]
            } else {
                if i < g {
                    g = i
                }
                f = i

                while g >= 0 && pattern[g] == pattern[g + m - 1 - f] {
                    g -= 1
                }

                suffixLength[i] = f - g
            }
        }

        // Calculate the shift values using the suffix lengths
        for i in 0 ..< m {
            goodSuffixSkip[i] = m
        }

        var j = 0
        for i in stride(from: m - 1, through: 0, by: -1) {
            if suffixLength[i] == i + 1 {
                while j < m - 1 - i {
                    if goodSuffixSkip[j] == m {
                        goodSuffixSkip[j] = m - 1 - i
                    }
                    j += 1
                }
            }
        }

        // Set the shift values for matching suffixes
        for i in 0 ..< m - 1 {
            goodSuffixSkip[m - 1 - suffixLength[i]] = m - 1 - i
        }
    }

    /// Compute border positions for handling bidirectional text more effectively
    private static func computeBorderPositions(pattern: [Character], borderPos: inout [Int]) {
        let m = pattern.count
        var i = 0
        var j = -1

        borderPos[0] = -1

        while i < m {
            while j >= 0 && pattern[i] != pattern[j] {
                j = borderPos[j]
            }

            i += 1
            j += 1
            borderPos[i] = j
        }
    }

    /// Preserves bidirectional text control characters while replacing highlight markers
    static func preserveBidirectionalMarkers(originalText: String, highlightedText: String) -> String {
        // Analyze text directionality and markers
        let hasLTRMark = originalText.contains("\u{202A}")
        var hasRTLMark = originalText.contains("\u{202B}")
        let hasEndMark = originalText.contains("\u{202C}")
        let containsRTL = isRTLText(highlightedText)

        var result = highlightedText

        // For RTL text, ensure proper directionality markers
        if containsRTL {
            // If no explicit direction markers exist but we have RTL text,
            // add RTL markers to ensure proper display
            if !hasLTRMark && !hasRTLMark {
                result = "\u{202B}" + result
                hasRTLMark = true
            }

            // Ensure end marker is present for RTL text with RTL marker
            if hasRTLMark && !hasEndMark {
                result = result + "\u{202C}"
            }
        }
        // Re-apply existing bidirectional markers if they existed in the original text
        else {
            if hasLTRMark {
                result = "\u{202A}" + result
            } else if hasRTLMark {
                result = "\u{202B}" + result
            }

            if hasEndMark {
                result = result + "\u{202C}"
            }
        }

        return result
    }

    /// Find the correct range for RTL text, considering bidirectional control characters
    static func findCorrectRTLRange(text: String, in content: String, currentRange _: NSRange) -> NSRange? {
        let nsString = content as NSString

        // First try direct search
        var range = nsString.range(of: text)

        if range.location == NSNotFound {
            // Try with RTL markers
            let rtlText = "\u{202B}" + text + "\u{202C}"
            range = nsString.range(of: rtlText)

            if range.location == NSNotFound {
                // Try with LTR markers
                let ltrText = "\u{202A}" + text + "\u{202C}"
                range = nsString.range(of: ltrText)

                if range.location == NSNotFound {
                    // Try sliding window search for complex cases
                    range = slidingWindowSearchForRTL(text: text, in: content)

                    if range.location == NSNotFound {
                        // If still not found, return nil to use original range
                        return nil
                    }
                }
            }
        }

        // Return the range corresponding to just the text (without markers)
        if range.location != NSNotFound {
            // If the range we found is for text with markers, adjust it to point to just the text
            if range.length > text.count {
                let adjustedRange = NSRange(location: range.location + 1, length: text.count)
                return adjustedRange
            }
            return range
        }

        return nil
    }
}

extension String {
    // 用于调试：打印字符串中的控制字符
    func debugBiDiControlCharacters() {
        print("Debug BiDi control characters in string (length: \(count)):")
        var index = 0
        for char in self {
            if char == "\u{202A}" || char == "\u{202B}" || char == "\u{202C}" ||
                char == "\u{202D}" || char == "\u{202E}" || char == "\u{200E}" || char == "\u{200F}"
            {
                let unicodeScalar = char.unicodeScalars.first!
                let hex = String(format: "0x%04X", unicodeScalar.value)
                print("Position \(index): Control character \(hex) (\(describeBiDiChar(hex: hex)))")
            }
            index += 1
        }
    }

    private func describeBiDiChar(hex: String) -> String {
        switch hex {
        case "0x202A": return "LRE - Left-to-Right Embedding"
        case "0x202B": return "RLE - Right-to-Left Embedding"
        case "0x202C": return "PDF - Pop Directional Formatting"
        case "0x202D": return "LRO - Left-to-Right Override"
        case "0x202E": return "RLO - Right-to-Left Override"
        case "0x200E": return "LRM - Left-to-Right Mark"
        case "0x200F": return "RLM - Right-to-Left Mark"
        default: return "Unknown control character"
        }
    }
}
