//
//  BibleContent.swift
//  WDReader
//
//  Created by <PERSON> on 2020/9/11.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import GRDB

struct Verse {
    var id:Int64 = 0
    var book:String = ""
    var chapterNum:Int?
    var verseNum:Int?
    
    var booklocalized:String{
        return Verse.scripts[book.uppercased()] ?? ""
    }
    var chapterNumS:Int{
        return chapterNum ?? 1
    }
    var verseNumS:Int{
        return verseNum ?? 1
    }
    
    static let scripts:[String:String] = ["GEN":"创世记","EXO":"出埃及记","LEV":"利未记","NUM":"民数记","DEU":"申命记","JOS":"约书亚记","JDG":"士师记","RUT":"路得记","1SA":"撒母耳记上","2SA":"撒母耳记下","1KI":"列王纪上","2KI":"列王纪下","1CH":"历代志上","2CH":"历代志下","EZR":"以斯拉记","NEH":"尼希米记","EST":"以斯帖记","JOB":"约伯记","PSA":"诗篇","PRO":"箴言","ECC":"传道书","SNG":"雅歌","ISA":"以赛亚书","JER":"耶利米书","LAM":"耶利米哀歌","EZK":"以西结书","DAN":"但以理书","HOS":"何西阿书","JOL":"约珥书","AMO":"阿摩司书","OBA":"俄巴底亚书","JON":"约拿书","MIC":"弥迦书","NAM":"那鸿书","HAB":"哈巴谷书","ZEP":"西番雅书","HAG":"哈该书","ZEC":"撒迦利亚书","MAL":"玛拉基书","MAT":"马太福音","MRK":"马可福音","LUK":"路加福音","JHN":"约翰福音","ACT":"使徒行传","ROM":"罗马书","1CO":"哥林多前书","2CO":"哥林多后书","GAL":"加拉太书","EPH":"以弗所书","PHP":"腓立比书","COL":"歌罗西书","1TH":"帖撒罗尼迦前书","2TH":"帖撒罗尼迦后书","1TI":"提摩太前书","2TI":"提摩太后书","TIT":"提多书","PHM":"腓利门书","HEB":"希伯来书","JAS":"雅各书","1PE":"彼得前书","2PE":"彼得后书","1JN":"约翰一书","2JN":"约翰二书","3JN":"约翰三书","JUD":"犹大书","REV":"启示录"]
}

let DBNAME_WDREADER = "wdreader"

class BibleContent : DBModel {

    var USFM : String = ""
    var verse : Int = 0
    var content : String = ""

    
    /// 通过书卷名获取所有content
    /// - Parameter bookName: <#bookName description#>
    /// - Returns: <#description#>
    static func findBibleContents(bookName:String) -> [BibleContent]{
        do{
            return try getDBQueue().read { (db) in
                try filter(Columns.USFM.like("%\(bookName.uppercased())%")).order(Column("id").asc).fetchAll(db)
            }
        }catch{
            return []
        }
    }
    
    static func findTitleContent(herf:String) -> (String,String){
        let versesContent = findVerseContents(herf: herf)
        
        if versesContent.0.count == 1 {
            let verse = versesContent.0.first!
            var title = verse.booklocalized
            if verse.chapterNum != nil{
                title += " \(verse.chapterNum!)"
                if verse.verseNum != nil{
                    title += ":\(verse.verseNum!)"
                }
            }
            return (title,versesContent.1)
            
        }else if versesContent.0.count == 2{
            let start = versesContent.0.first!
            let end = versesContent.0.last!
            if start.book == end.book {
                if start.chapterNum == end.chapterNum {
                    let title = "\(start.booklocalized) \(start.chapterNumS):\(start.verseNumS)-\(end.verseNumS)"
                    return (title,versesContent.1)
                    
                }else{
                    var startInfo = "\(start.chapterNumS)"
                    if let startVerse = start.verseNum {
                        startInfo = "\(startInfo):\(startVerse)"
                    }
                    var endInfo = "\(end.chapterNumS)"
                    if let endVerse = end.verseNum {
                        endInfo = "\(endInfo):\(endVerse)"
                    }
                    let title = "\(start.booklocalized) \(startInfo)-\(endInfo)"
                    return (title,versesContent.1)
                }
            }else{
                let title = "\(start.booklocalized) \(start.chapterNumS):\(start.verseNumS)-\(end.booklocalized) \(end.chapterNumS):\(end.verseNumS)"
                return (title,versesContent.1)
            }
            
        }else{
            return ("","")
        }
    }
    
    static func findVerseContents(herf:String) -> ([Verse],String){
        do{
            return try getDBQueue().read { (db) in
                try findVerses(herf: herf, db: db)
            }
        }catch{
            return ([],"")
        }
    }
    
    static func findVerses(herf:String, db: Database) throws -> ([Verse],String){
        let verseStrs = herf.split(separator: "-")
        
        var verseModels = [Verse]()
        
        for verse in verseStrs{
            var verseModel = Verse()
            let verseComponents = verse.split(separator: ".")
            for i in 0 ..< verseComponents.count{
                if i == 0 {
                    verseModel.book = String(verseComponents[i])
                }else if i == 1{
                    verseModel.chapterNum = Int(String(verseComponents[i]))
                }else if i == 2{
                    verseModel.verseNum = Int(String(verseComponents[i]))
                }
            }
            verseModels.append(verseModel)
        }
        
        
        if verseModels.count == 1{
            if verseModels.first?.verseNum == nil {
                //查询全章
                let vs = try filter(Columns.USFM == "\(verseModels.first!.book.uppercased()).\(verseModels.first!.chapterNumS)").order(Columns.verse.asc).fetchAll(db)
                let contentsStr = getAllVersesContent(verses: vs, isAcrossChapter: false)
                return (verseModels,contentsStr)
            }else{
                //查询一节
                let m = verseModels.first!
                var verseNum = m.verseNum ?? 1
                var v:BibleContent?
                while v == nil && verseNum > 0 {
                    v = try filter(Columns.USFM == "\(m.book.uppercased()).\(m.chapterNumS)" && Columns.verse == String(verseNum)).fetchOne(db)
                    verseNum -= 1
                }
                return (verseModels,v?.content ?? "")
            }
            
            
            //查询多节
        }else if verseModels.count == 2{
            let start = verseModels.first!
            let startV = try filter(Columns.USFM == "\(start.book.uppercased()).\(start.chapterNumS)" && Columns.verse == start.verseNumS).fetchOne(db)
            
            let end = verseModels.last!
            var endV: BibleContent?
            if let endVerse = end.verseNum {
                endV = try filter(Columns.USFM == "\(end.book.uppercased()).\(end.chapterNumS)" && Columns.verse == endVerse).fetchOne(db)
            } else {
                endV = try filter(Columns.USFM == "\(end.book.uppercased()).\(end.chapterNumS)").order(Columns.verse.desc).fetchOne(db)
            }
//            let endV = try filter(Columns.USFM == "\(end.book.uppercased()).\(end.chapterNumS)" && Columns.verse == end.verseNumS).fetchOne(db)
            
            if let s = startV, let e = endV{
                //同一章（只显示节号）
                if s.USFM == e.USFM{
                    do{
                        let contents = try filter((s.id...e.id).contains(DBModel.Columns.id)).fetchAll(db)
                        let contentsStr = getAllVersesContent(verses: contents, isAcrossChapter: false)
                        return (verseModels,contentsStr)
                    }catch let err{
                        WDDBLog.e(err)
                        return (verseModels,"")
                    }
                    
                //不同章
                }else{
                    do{
                        let contents = try filter((s.id...e.id).contains(DBModel.Columns.id)).fetchAll(db)
                        let contentStr = getAllVersesContent(verses:contents,isAcrossChapter: true)
                        return (verseModels,contentStr)
                    }catch let err{
                        WDDBLog.e(err)
                        return (verseModels,"")
                    }
                    
                }
                
            }
            
            return (verseModels,"")
        }else{
            return (verseModels,"")
        }
       
    }
    
    private static func getAllVersesContent(verses:[BibleContent],isAcrossChapter:Bool)->String{
        //        verses.map{$0.USFM.split(separator: ".").last! + ":" + "\($0.verse) \($0.content)"}.joined(separator: "\n")
//        verses.map{$0.USFM.split(separator: ".").last! + ":" + "\($0.verse) \($0.content)"}.joined(separator: "\n")
        var content = ""
        for i in 0 ..< verses.count{
            let v = verses[i]
            
            if isAcrossChapter {
                let chapterNum = v.USFM.split(separator: ".").last! + ":"
                content.append(contentsOf: chapterNum)
            }
            
            var verseContent = "\(v.verse) \(v.content)\n"
            if i + 1 < verses.count && verses[i + 1].verse > v.verse && verses[i + 1].verse != v.verse + 1 {
                verseContent = "\(v.verse)-\(verses[i + 1].verse - 1) \(v.content)\n"
            }
            content.append(verseContent)
        }
        return content
    }
    
    required init() {
        super.init()
    }
    
    //MARK: database -- config
    override public class var dbName:String?{
        return DBNAME_WDREADER
    }
    
    override class var nameOfTable: String{
        return "bible_content"
    }
    
    //MARK: database -- map
    public enum Columns: String, ColumnExpression,CaseIterable {
        case USFM
        case verse
        case content
    }
    
    required init(row: Row) throws{
        USFM = row[Columns.USFM]
        verse = row[Columns.verse]
        content = row[Columns.content]
        try super.init(row: row)
    }
    
    required init(from decoder: Decoder) throws {
        fatalError("init(from:) has not been implemented")
    }
    
    public override func encode(to container: inout PersistenceContainer) {
        container[Columns.USFM] = USFM
        container[Columns.verse] = verse
        container[Columns.content] = content
        super.encode(to: &container)
    }
}
