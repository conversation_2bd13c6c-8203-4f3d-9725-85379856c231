//
//  CTRunDelegate+Extension.swift
//  WDReader
//
//  Created by <PERSON> on 2020/12/7.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import CoreText

struct RunStruct {
  let ascent: CGFloat
  let descent: CGFloat
  let width: CGFloat
}

extension CTRunDelegate{
    
    static func create(width:CGFloat,height:CGFloat) -> CTRunDelegate?{
        let extentBuffer = UnsafeMutablePointer<RunStruct>.allocate(capacity: 1)
        extentBuffer.initialize(to: RunStruct(ascent: height, descent: 0, width: width))

        var callbacks = CTRunDelegateCallbacks(version: kCTRunDelegateVersion1, dealloc: { (pointer) in
        }, getAscent: { (pointer) -> CGFloat in
          let d = pointer.assumingMemoryBound(to: RunStruct.self)
          return d.pointee.ascent
        }, getDescent: { (pointer) -> CGFloat in
          let d = pointer.assumingMemoryBound(to: RunStruct.self)
          return d.pointee.descent
        }, getWidth: { (pointer) -> CGFloat in
          let d = pointer.assumingMemoryBound(to: RunStruct.self)
          return d.pointee.width
        })

        let delegate = CTRunDelegateCreate(&callbacks, extentBuffer)

//        let attrDictionaryDelegate = [(kCTRunDelegateAttributeName as NSAttributedString.Key): (delegate as Any)]
//        return attrDictionaryDelegate
        return delegate
    }
}

