//
//  NSAttributedString.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/22.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import DTCoreText
import Foundation
import SwiftyUserDefaults
import UIKit

extension NSAttributedString {
//    func adjustingWritingDirectionByWord() -> NSAttributedString {
//        guard let output = self.mutableCopy() as? NSMutableAttributedString else {
//            return self
//        }
//
//        // 定义希伯来文字符范围
//        let hebrewRange = UnicodeScalar("א").value...UnicodeScalar("ת").value
//
//        output.beginEditing()
//        output.enumerateAttribute(.paragraphStyle, in: NSRange(location: 0, length: output.length), options: .byWords) { value, range, _ in
//
//            let mutableParagraphStyle: NSMutableParagraphStyle
//            if let style = value as? NSParagraphStyle {
//                mutableParagraphStyle = style.mutableCopy() as! NSMutableParagraphStyle
//            } else {
//                mutableParagraphStyle = NSMutableParagraphStyle()
//            }
//
//            // 检查当前范围内是否有希伯来文字符
//            let substring = (self.string as NSString).substring(with: range)
//            if substring.unicodeScalars.contains(where: { hebrewRange.contains($0.value) }) {
//                mutableParagraphStyle.baseWritingDirection = .rightToLeft
//            } else {
//                mutableParagraphStyle.baseWritingDirection = .natural
//            }
//
//            output.addAttribute(.paragraphStyle, value: mutableParagraphStyle, range: range)
//        }
//        output.endEditing()
//
//        return output
//    }

    func addFirstLetter() -> NSAttributedString {
//        let invisibleChineseCharacter = "\u{200B}" // 零宽空格
        let att = NSMutableAttributedString(string: "你", attributes: attributes(at: 0, effectiveRange: nil))
//        let att = NSMutableAttributedString(string: "\u{200E}",attributes: self.attributes(at: 0, effectiveRange: nil))
        att.append(self)

        return att
    }

    func adjustingWritingDirectionByWord() -> NSAttributedString {
        let mutableAttributedString = NSMutableAttributedString(attributedString: self)

        mutableAttributedString.beginEditing()

        // 使用 String 的范围进行遍历
        mutableAttributedString.string.enumerateSubstrings(in: string.startIndex ..< string.endIndex, options: .byWords) { substring, substringRange, _, _ in

            // 将 String.Index 转换为 NSRange
            let nsRange = NSRange(substringRange, in: self.string)

            let mutableParagraphStyle: NSMutableParagraphStyle
            if let paragraphStyle = mutableAttributedString.attribute(.paragraphStyle, at: nsRange.location, effectiveRange: nil) as? NSParagraphStyle {
                mutableParagraphStyle = paragraphStyle.mutableCopy() as! NSMutableParagraphStyle
            } else {
                mutableParagraphStyle = NSMutableParagraphStyle()
            }

            // 检查 substring 是否包含希伯来字符
            if let substr = substring, substr.isHebrewText() {
                debugPrint("希伯来文测试：\(substr)")
                mutableParagraphStyle.baseWritingDirection = .rightToLeft
                mutableParagraphStyle.alignment = .natural
                // 应用修改后的段落样式
                mutableAttributedString.addAttribute(.paragraphStyle, value: mutableParagraphStyle, range: nsRange)
            } else {
//                mutableParagraphStyle.baseWritingDirection = .natural
            }
        }

        mutableAttributedString.endEditing()

        return mutableAttributedString
    }

    func addBiDiMarkersToHebrew() -> NSAttributedString {
        let attributedString = self
        // 如果原始文本为空，直接返回
        if attributedString.length == 0 {
            return attributedString
        }

        let hebrewPattern = "([\\u0590-\\u05FF]+\\s*)+"
        let mutableAttributedString = NSMutableAttributedString(attributedString: attributedString)

        // 创建正则表达式来匹配希伯来文
        let regex = try! NSRegularExpression(pattern: hebrewPattern, options: [])
        let range = NSRange(location: 0, length: mutableAttributedString.length)

        // 查找所有的希伯来文匹配项并添加BiDi控制字符
        let matches = regex.matches(in: attributedString.string, options: [], range: range)

        // 没有找到匹配项，直接返回原始文本
        if matches.isEmpty {
            return attributedString
        }

        // 跟踪已处理的范围，避免重复处理
        var processedRanges = [NSRange]()

        // 由于添加字符会改变长度，从后往前替换可以避免影响之前的range
        for match in matches.reversed() {
            // 确保范围有效
            guard match.range.location != NSNotFound &&
                match.range.location < attributedString.length &&
                match.range.location + match.range.length <= attributedString.length
            else {
                continue
            }

            // 检查是否已经处理过这个范围或重叠的范围
            let isOverlapping = processedRanges.contains { NSIntersectionRange(match.range, $0).length > 0 }
            if isOverlapping {
                continue
            }

            let contentRange = match.range
            let hebrewString = attributedString.safeAttributedSubstring(from: contentRange).string

            // 跳过空字符串
            if hebrewString.isEmpty {
                continue
            }

            // 检查文本是否已经有BiDi标记
            let hasBiDiMarkers = hebrewString.contains("\u{202B}") && hebrewString.contains("\u{202C}")
            if hasBiDiMarkers {
                print("Skip adding BiDi markers to text that already has them: \(hebrewString)")
                continue
            }

            // Use RLM (Right-to-Left Mark) for Hebrew text instead of LRM with embedding
            let wrappedHebrew = "\u{202B}" + hebrewString + "\u{202C}"

            // Preserve attributes while replacing text
            let attrs = attributedString.attributes(at: contentRange.location, effectiveRange: nil)
            let replacement = NSAttributedString(string: wrappedHebrew, attributes: attrs)

            mutableAttributedString.replaceCharacters(in: contentRange, with: replacement)
            processedRanges.append(contentRange)
        }

        return mutableAttributedString
    }

    func wrapHebrewInBiDiMarkers() -> NSAttributedString {
        let attributedString = self
        let mutableAttributedString = NSMutableAttributedString(attributedString: attributedString)

        // 匹配格式 "(希伯来文，#数字)" 的正则表达式
        let pattern = "\\(([\\u0590-\\u05FF]+)\\s*，#(\\d+)\\)"
        let regex = try! NSRegularExpression(pattern: pattern, options: [])
        let range = NSRange(location: 0, length: mutableAttributedString.length)

        let matches = regex.matches(in: attributedString.string, options: [], range: range)

        for match in matches.reversed() {
            guard match.numberOfRanges == 3 else { continue }
            let hebrewRange = match.range(at: 1)
            let numberRange = match.range(at: 2)

            guard let hebrewRangeInString = Range(hebrewRange, in: attributedString.string),
                  let numberRangeInString = Range(numberRange, in: attributedString.string) else { continue }

            let hebrewString = String(attributedString.string[hebrewRangeInString])
            let numberString = String(attributedString.string[numberRangeInString])

            let wrappedHebrew = "(\u{202A}\(hebrewString)\u{202C}，#\(numberString))"
            mutableAttributedString.replaceCharacters(in: match.range, with: wrappedHebrew)
        }

        return mutableAttributedString
    }

    func addingInvisibleChineseCharacterAtParagraphs() -> NSAttributedString {
        let mutableAttributedString = NSMutableAttributedString(attributedString: self)
//            let invisibleChineseCharacter = "\u{200B}" // 零宽空格
        let invisibleChineseCharacter = "你" // 零宽空格
        let paragraphSeparator = "\n"

        // 定义希伯来字符的范围
        let hebrewRange = UnicodeScalar("א").value ... UnicodeScalar("ת").value

        mutableAttributedString.beginEditing()

        // 找到所有段落的范围
        let paragraphRanges = mutableAttributedString.string.components(separatedBy: paragraphSeparator)

        // 从后往前插入字符，这样我们不会干扰后续的范围
        var locationOffset = 0
        for (index, paragraph) in paragraphRanges.enumerated() {
            let paragraphLength = paragraph.count
            if index != 0 { // 不是第一个段落，第一个段落前不需要插入
                mutableAttributedString.insert(NSAttributedString(string: invisibleChineseCharacter), at: locationOffset)
                locationOffset += 1 // 增加因插入字符而偏移的位置
            }
            locationOffset += paragraphLength + (index < paragraphRanges.count - 1 ? 1 : 0) // 加上换行符的长度，除了最后一个段落

            // 为希伯来文设置书写方向
            let paragraphRange = NSRange(location: locationOffset - paragraphLength - 1, length: paragraphLength)
            mutableAttributedString.enumerateAttribute(.paragraphStyle, in: paragraphRange, options: []) { value, range, _ in
                let mutableParagraphStyle: NSMutableParagraphStyle
                if let paragraphStyle = value as? NSParagraphStyle {
                    mutableParagraphStyle = paragraphStyle.mutableCopy() as! NSMutableParagraphStyle
                } else {
                    mutableParagraphStyle = NSMutableParagraphStyle()
                }

                // 检查段落是否包含希伯来字符
                let substring = (mutableAttributedString.string as NSString).substring(with: range)
                if substring.unicodeScalars.contains(where: { hebrewRange.contains($0.value) }) {
                    mutableParagraphStyle.baseWritingDirection = .rightToLeft
                } else {
                    mutableParagraphStyle.baseWritingDirection = .natural
                }

                // 应用修改后的段落样式
                mutableAttributedString.addAttributes([.paragraphStyle: mutableParagraphStyle], range: range)
            }
        }

        mutableAttributedString.endEditing()

        return mutableAttributedString
    }

    // 这个函数需要您实现，以检测字符串是否包含希伯来文
//    func isHebrewText(_ text: String) -> Bool {
//        // 检测字符串是否包含希伯来文字符的逻辑
//        // 例如，可以检测Unicode范围是否在希伯来字符集内
//        return text.rangeOfCharacter(from: CharacterSet(charactersIn: "\u{0590}"..."\u{05FF}")) != nil
//    }

    /// 返回锚点的内容和位置
    /// - Parameter id: 锚点的url
    /// - Returns:
    func getEpubFootNote(id: String) -> FootNote? {
        var targetUrlStr = id
        targetUrlStr.removeFirst() // 删除#符号

        var content: String?
        var r: NSRange?
        var footNoteNum = 0
        var i = 0
        enumerateAttribute(NSAttributedString.Key(rawValue: EPUBAsideLinkIdAttribute),
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            if let footNoteUrlStr = value as? String {
                if footNoteUrlStr == targetUrlStr {
                    let text = self.attributedSubstring(from: range).string
                    content = content ?? "" + text
                    if r == nil {
                        r = range
                        footNoteNum = i + 1
                    }
                }

                i += 1
            }
        }

        if content != nil {
            return FootNote(type: .epub, title: "脚注 \(footNoteNum)", content: content!, location: r!.location, numInChapter: footNoteNum)
        } else {
            return nil
        }
    }

    func resetImageToBlank(shouldManageEditing _: Bool = true) -> NSAttributedString {
        guard let output = mutableCopy() as? NSMutableAttributedString else {
            return self
        }
        output.enumerateAttribute(NSAttributedString.Key.attachment,
                                  in: NSRange(location: 0, length: length),
                                  options: []) { value, range, _ in
            guard value != nil else {
                return
            }
            let attributeStringInRange = output.attributedSubstring(from: range)
            // Check if the range is valid and not empty before accessing attributes
            if attributeStringInRange.length > 0 {
                let dict = attributeStringInRange.attributes(at: 0, effectiveRange: nil)
                output.replaceCharacters(in: range, with: NSAttributedString(string: " ", attributes: dict)) // 必须替代才能把原文字去掉。使用横线不是空格为了占位，防止文字边缘忽略空格。
            }
        }
        return output
    }

    func isEmpty() -> Bool {
        isEmptyText() && isEmptyImage()
    }

    func isEmptyText() -> Bool {
        string.trimmingCharacters(in: .whitespacesAndNewlines).count == 0
    }

    func isEmptyImage() -> Bool {
        var isEmpty = true
        enumerateAttribute(NSAttributedString.Key.paragraphStyle,
                           in: NSRange(location: 0, length: length),
                           options: []) { _, range, _ in

            if let _ = attributedSubstring(from: range).attribute(NSAttributedString.Key.attachment, at: 0, effectiveRange: nil) as? DTImageTextAttachment {
                isEmpty = false
                return
            }
        }
        return isEmpty
    }

    var isEffectivelyEmpty: Bool {
        var charactersToIgnore = CharacterSet.whitespacesAndNewlines
        charactersToIgnore.insert(charactersIn: "\u{00AD}\u{200B}\u{FEFF}\u{200D}\u{200C}\u{FFFC}\t")

        for character in string {
            if !charactersToIgnore.contains(character.unicodeScalars.first!) {
                return false
            }
        }
        return true
    }

    func toHtmlString() -> String? {
        let documentAttributes: [NSAttributedString.DocumentAttributeKey: Any] = [
            .documentType: NSAttributedString.DocumentType.html,
            .characterEncoding: String.Encoding.utf8.rawValue,
        ]

        guard let htmlData = try? data(from: NSRange(location: 0, length: length), documentAttributes: documentAttributes) else {
            return nil
        }

        return String(data: htmlData, encoding: .utf8)
    }

    // Prepare text for selection by ensuring RTL texts have proper markers
    func prepareForRTLTextSelection() -> NSAttributedString {
        // Quick check if there's any Hebrew text
        if !string.isHebrewText() {
            return self
        }

        // First add BiDi markers to Hebrew text
        let withBiDiMarkers = addBiDiMarkersToHebrew()

        // For simple implementation, just return the text with BiDi markers
        // This avoids the complex paragraph style manipulation that was causing crashes
        return withBiDiMarkers
    }

    /// Safely get a substring of an attributed string, preventing out of bounds errors
    func safeAttributedSubstring(from range: NSRange) -> NSAttributedString {
        // Make sure we don't try to access out of bounds
        let safeRange = range.constrainedTo(length: length)

        // If the safe range has zero length or is invalid, return an empty attributed string
        if safeRange.length == 0 || safeRange.location == NSNotFound {
            return NSAttributedString()
        }

        // Use the safe range to get the substring
        return attributedSubstring(from: safeRange)
    }
}

extension NSMutableAttributedString {
    private static let semaphore = DispatchSemaphore(value: 1)

    func resetFormat() -> Self {
        NSMutableAttributedString.semaphore.wait()
        defer { NSMutableAttributedString.semaphore.signal() }

//        self.beginEditing()
//        addFirstLetter() //希伯来文问题，首字母添加字符
        resetFormatNormal()
//            .resetParagraphStyle(alignmentJustified: true)
            .resetHeaderAlignmentCenter()
            .resetAnnotationToImage()
            .resetRemoveAnnotationInMainBody()
            .resetScriptStyle()

        if Defaults[key: DefaultsKeys.SHOW_PAPER_BOOK_PAGE_NUM] {
            resetPageNumToImage()
        } else {
            removePageNum()
        }
        // 希伯来文问题，设置baseWritingDirection
        setParagraphStyleLeftToRight()
//            .wrapHebrewInBiDiMarkers()
//            .adjustingWritingDirectionByWord()
//        self.endEditing()
        return self
    }

//    func resetFormatForFirstChapter() -> NSAttributedString{
//        resetFormatNormal()
    ////            .resetParagraphStyle(alignmentJustified: false) //不做段落任何修改
//            .resetParagraphStyleForFirstChapter()
//            .resetHeaderAlignmentCenter()
//        .resetAnnotationToImage()
//        .resetRemoveAnnotationInMainBody()
//    }

    func resetFootSize(shouldManageEditing: Bool = true) {
        if shouldManageEditing { beginEditing() }

        // 2、修改字体大小（解析时候填入默认字体，此处再plus）
        enumerateAttribute(NSAttributedString.Key.font,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let oldFont = value as? UIFont else {
                return
            }
//            let newFont = oldFont.withSize(oldFont.pointSize + sizePlus)
            let newFont = oldFont.withSize(CGFloat(WDReaderConfig.currentFontSize))
            addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }

        if shouldManageEditing { endEditing() }
    }

    func resetFormatNormal(shouldManageEditing: Bool = true) -> Self {
        // 1、去掉之后，正常显示linkcolor
        if shouldManageEditing { beginEditing() }
        removeAttribute(kCTForegroundColorFromContextAttributeName as NSAttributedString.Key, range: NSMakeRange(0, length))
        if shouldManageEditing { endEditing() }

        // 2、修改字体大小（解析时候填入默认字体，此处再plus）
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.font,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let oldFont = value as? UIFont else {
                return
            }
//            let newFont = oldFont.withSize(oldFont.pointSize + sizePlus)
            let newFont = oldFont.withSize(CGFloat(WDReaderConfig.currentFontSize))
            addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }
        if shouldManageEditing { endEditing() }

        // 3、修改默认颜色
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.foregroundColor,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let color = value as? UIColor else {
                return
            }
            addAttribute(NSAttributedString.Key.foregroundColor, value: dynamicTextColor3, range: range)
        }
        if shouldManageEditing { endEditing() }

        // 3、修改高亮颜色
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.link,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let link = value as? URL else {
                return
            }
            addAttribute(NSAttributedString.Key.foregroundColor, value: dynamicLinkColor, range: range)
        }

        if shouldManageEditing { endEditing() }
        return self
    }

    func resetScriptStyle(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key(rawValue: String(kCTSuperscriptAttributeName)),
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let scriptStyle = value as? Int else {
                return
            }

            if scriptStyle == 1 {
                if let newFont = attributedSubstring(from: range).attribute(NSAttributedString.Key.font, at: 0, effectiveRange: nil) as? UIFont {
                    let newFontSize = newFont.ascender - newFont.xHeight // newFont.pointSize/3
                    addAttributes([NSAttributedString.Key.font: newFont.withSize(newFontSize),
                                   NSAttributedString.Key.baselineOffset: newFontSize], range: range)
                }
            }
        }
        if shouldManageEditing { endEditing() }
        return self
    }

    func resetParagraphStyle(alignmentJustified: Bool, shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.paragraphStyle,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let style = value as? NSParagraphStyle else {
                return
            }

            guard style.firstLineHeadIndent > 0 else { // 默认左缩进 影响的标签。再次重设缩进
                return
            }

            let newStyle = style.mutableCopy() as! NSMutableParagraphStyle
            if let _ = attributedSubstring(from: range).attribute(NSAttributedString.Key.attachment, at: 0, effectiveRange: nil) as? DTImageTextAttachment {
                // 有图片的设置缩进和对齐方式
                newStyle.firstLineHeadIndent = 0 // 缩进
                newStyle.alignment = .left // 图片左对齐
            } else if let isLi = attributedSubstring(from: range).attribute(NSAttributedString.Key(rawValue: TagLi), at: 0, effectiveRange: nil) as? Bool, isLi {
                newStyle.alignment = .left
            } else {
                // 所有章设置缩进和段间距。
                newStyle.firstLineHeadIndent = CGFloat(WDReaderConfig.currentFontSize) * 2.0 // 缩进
                newStyle.paragraphSpacing = newStyle.paragraphSpacing * 2.0 / 3.0 // 段间距
                // 格式内部设置了左对齐（诗歌体），就不再设置左右对齐了。此处逻辑放在了DTCoreText的设置代码中。
                if !(newStyle.alignment == .left) {
                    if alignmentJustified {
//                        newStyle.alignment = .justified //左右对齐
                    }
                }
            }
            addAttribute(NSAttributedString.Key.paragraphStyle, value: newStyle, range: range)
        }

        if shouldManageEditing { endEditing() }
        return self
    }

    func resetParagraphStyleForFirstChapter(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.paragraphStyle,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let style = value as? NSParagraphStyle else {
                return
            }
            guard style.firstLineHeadIndent > 0 else { // 默认左缩进 影响的标签。再次重设缩进
                return
            }

            let newStyle = style.mutableCopy() as! NSMutableParagraphStyle
            newStyle.firstLineHeadIndent = 0 // 缩进
//            newStyle.paragraphSpacing = newStyle.paragraphSpacing * 2.0/3.0 //段间距
            newStyle.alignment = .left // 左右对齐

            addAttribute(NSAttributedString.Key.paragraphStyle, value: newStyle, range: range)
        }
        if shouldManageEditing { endEditing() }
        return self
    }

    func resetHeaderAlignmentCenter(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        // <h> 等居中
        enumerateAttribute(NSAttributedString.Key(rawValue: DTHeaderLevelAttribute),
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let headerLevel = value as? Int else {
                return
            }
            let attributeStringInRange = attributedSubstring(from: range)
            let dict = attributeStringInRange.attributes(at: 0, effectiveRange: nil)
            let style = dict[NSAttributedString.Key.paragraphStyle] as! NSParagraphStyle

            let newStyle = style.mutableCopy() as! NSMutableParagraphStyle
            newStyle.alignment = .center
            addAttribute(NSAttributedString.Key.paragraphStyle, value: newStyle, range: range)
        }

        if shouldManageEditing { endEditing() }
        return self
    }

    // 注脚文字改成图片
    func resetAnnotationToImage(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.link,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let link = value as? URL else {
                return
            }

            // 4,修改注脚字号; TODO: 查找属性失败EPUBTypeAttribute!!
            guard link.absoluteString.hasPrefix("#")
//                  ,let epubType = output.attribute(NSAttributedString.Key(rawValue: EPUBTypeAttribute), at: 0, longestEffectiveRange: nil, in: range) as? String, epubType == "noteref"
            else {
                return
            }

            let attributeStringInRange = attributedSubstring(from: range)
            var dict = attributeStringInRange.attributes(at: 0, effectiveRange: nil)
            dict[NSAttributedString.Key.underlineStyle] = nil

            // 4,注释文字改成图片；
            let attachment = DTImageTextAttachment()
            let fontId = WDReaderConfig.getCurrentFontId()
            if fontId == 0 {
                attachment.image = UIImage(named: "icon_footnote")
            } else {
                attachment.image = UIImage(named: "icon_footnote_2")
            }
            attachment.image.isFontNote = true
//            let ctRunDelegate = createEmbeddedObjectRunDelegate(attachment)!
//            let ctRunDelegate = CTRunDelegate.create(width: attachment.image.size.width, height: attachment.image.size.height)!
            let fontNoteSize = CGFloat(WDReaderConfig.currentFootNoteSize)
            let ctRunDelegate = CTRunDelegate.create(width: fontNoteSize, height: fontNoteSize)!
            dict[NSAttributedString.Key.attachment] = attachment
            dict[kCTRunDelegateAttributeName as NSAttributedString.Key] = (ctRunDelegate as Any)
            dict[NSAttributedString.Key.foregroundColor] = dynamicBackgroundColor1 // 阅读界面背景色

//            output.insert(NSAttributedString(string: "xxxxxxxxxxxx", attributes: attDic), at: range.location)
//            output.addAttributes(attDic, range: range)
            replaceCharacters(in: range, with: NSAttributedString(string: "-", attributes: dict)) // 必须替代才能把原文字去掉。使用横线不是空格为了占位，防止文字边缘忽略空格。

            // 修改注脚字号
//            guard let oldFont = output.attribute(NSAttributedString.Key.font, at: 0, longestEffectiveRange: nil, in: range) as? UIFont else {
//                return
//            }
            ////            let newFont = oldFont.withSize(oldFont.pointSize + sizePlus - 4)
//            let newFont = oldFont.withSize(CGFloat(WDReaderConfig.currentFootNoteSize))
//            output.removeAttribute(NSAttributedString.Key.font, range: range)
//            output.addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }

        /*
         //打印图形的属性。
         output.enumerateAttribute(NSAttributedString.Key.attachment,
                                   in: NSRange(location: 0, length: self.length),
                                   options: []) { (value, range, stop) -> Void in
             guard let attachment = value as? DTImageTextAttachment else {
                 return
             }

             let attributeStringInRange = output.attributedSubstring(from: range)
             let dict = attributeStringInRange.attributes(at: 0, effectiveRange: nil)
             let ctrun = dict[NSAttributedString.Key(kCTRunDelegateAttributeName as String)] as! Swift.Unmanaged<CTRunDelegate>

             let attachmentParagraphSpacingAttribute = dict[NSAttributedString.Key(rawValue: DTAttachmentParagraphSpacingAttribute)] as? NSNumber

             Log.d("")
         }
          */

        /*
          {
              CTRunDelegate = "<CTRunDelegate 0x6000029ea820 [0x7fff8002e7f0]>";
              DTAttachmentParagraphSpacing = 0;
              NSAttachment = "<DTImageTextAttachment: 0x600003facf30>";
              NSColor = "<UIDynamicProviderColor: 0x600000d940e0; provider = <__NSMallocBlock__: 0x6000003d98f0>>";
              NSFont = "<UICTFont: 0x7fdceb541510> font-family: \"Times New Roman\"; font-weight: normal; font-style: normal; font-size: 22.00pt";
              NSParagraphStyle = "Alignment 4, LineSpacing 0, ParagraphSpacing 0, ParagraphSpacingBefore 0, HeadIndent 0, TailIndent 0, FirstLineHeadIndent 0, LineHeight 0/0, LineHeightMultiple 0, LineBreakMode 0, Tabs (\n    28L,\n    56L,\n    84L,\n    112L,\n    140L,\n    168L,\n    196L,\n    224L,\n    252L,\n    280L,\n    308L,\n    336L\n), DefaultTabInterval 36, Blocks (\n), Lists (\n), BaseWritingDirection -1, HyphenationFactor 0, TighteningForTruncation NO, HeaderLevel 0 LineBreakStrategy 0";
              TagIDAttribute = "partial_52497";
          }
         */

        if shouldManageEditing { endEditing() }
        return self
    }

    // 删除正文中注脚
    func resetRemoveAnnotationInMainBody(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key(rawValue: EPUBAsideLinkIdAttribute),
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard value != nil else {
                return
            }
            deleteCharacters(in: NSRange(location: range.location, length: range.length + 1))
        }

        /*        //删除正文中注脚
                 output.enumerateAttribute(NSAttributedString.Key(rawValue: EPUBTypeAttribute),
                                           in: NSRange(location: 0, length: self.length),
                                           options: []) { (value, range, stop) -> Void in
                     guard let epubType = value as? String else {
                         return
                     }
                     guard epubType == "footnote"
         //                  ,let epubType = output.attribute(NSAttributedString.Key(rawValue: EPUBTypeAttribute), at: 0, longestEffectiveRange: nil, in: range) as? String, epubType == "noteref"
                         else{
                         return
                     }
                     output.deleteCharacters(in: NSRange(location: range.location, length: range.length + 1))
         //            guard let oldFont = output.attribute(NSAttributedString.Key.font, at: 0, longestEffectiveRange: nil, in: range) as? UIFont else {
         //                return
         //            }
         //            let newFont = oldFont.withSize(CGFloat(WDReaderConfig.currentFootNoteSize))
         //            output.removeAttribute(NSAttributedString.Key.font, range: range)
         //            output.addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
                 }
         */
        if shouldManageEditing { endEditing() }
        return self
    }

    func changeResetColor(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }

        // 去掉之后，正常显示linkcolor
        removeAttribute(kCTForegroundColorFromContextAttributeName as NSAttributedString.Key, range: NSMakeRange(0, length))

        /*
         //        output.removeAttribute(kCTForegroundColorAttributeName as NSAttributedString.Key ,range:NSMakeRange(0, output.length))
         //        output.removeAttribute(NSAttributedString.Key.foregroundColor, range: NSMakeRange(0, output.length))
                 output.enumerateAttribute(NSAttributedString.Key.foregroundColor,
                                           in: NSRange(location: 0, length: self.length),
                                           options: []) { (value, range, stop) -> Void in
                     guard let oldColor = value as? UIColor else {
                         return
                     }
         //            let oldColor = value as? UIColor
         //            var r:CGFloat = 0
         //            var g:CGFloat = 0
         //            var b:CGFloat = 0
         //            var a:CGFloat = 0
         //            oldColor.getRed(&r, green: &g, blue: &b, alpha: &a)
         //                                    let sp = CGColorSpace(name:CGColorSpace.sRGB)!
         //                                    let comps : [CGFloat] = [r, g, b, a]
         //                                    let c = CGColor(colorSpace: sp, components: comps)!
         //            let newColor = UIColor(cgColor: c)
         //            output.removeAttribute(kCTForegroundColorAttributeName as NSAttributedString.Key ,range:range)
         //            output.removeAttribute(NSAttributedString.Key.foregroundColor, range: range)
         //                                    output.addAttribute(NSAttributedString.Key.foregroundColor, value: newColor, range: range)
         //                                    output.addAttribute(NSAttributedString.Key.strokeColor,value: newColor ,range: range)
         //                                    output.addAttribute(NSAttributedString.Key.strokeWidth, value: 0, range: range)
         //                                    output.addAttribute(NSAttributedString.Key.kern, value: 0, range: range)
                 }
          */
        if shouldManageEditing { endEditing() }
        return self
    }

    func changeFontSize(factor: CGFloat, shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.font,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let oldFont = value as? UIFont else {
                return
            }
            let newFont = oldFont.withSize(oldFont.pointSize * factor)
            addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }
        if shouldManageEditing { endEditing() }
        return self
    }

    func changeFontSize(sizePlus: CGFloat, shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.font,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let oldFont = value as? UIFont else {
                return
            }
            let newFont = oldFont.withSize(oldFont.pointSize + sizePlus)
            addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
        }
        if shouldManageEditing { endEditing() }
        return self
    }

    func setParagraphStyleLeftToRight(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key.paragraphStyle,
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let style = value as? NSParagraphStyle else {
                return
            }

            let newStyle = style.mutableCopy() as! NSMutableParagraphStyle
            newStyle.baseWritingDirection = .leftToRight
            addAttribute(NSAttributedString.Key.paragraphStyle, value: newStyle, range: range)
        }

        if shouldManageEditing { endEditing() }
        return self
    }

    func resizeToFit(originalSize: CGSize, maxSize: CGSize) -> CGSize {
        let widthRatio = maxSize.width / originalSize.width
        let heightRatio = maxSize.height / originalSize.height
        let ratio = min(widthRatio, heightRatio)
        if ratio >= 1 {
            return originalSize
        }
        let newSize = CGSize(width: originalSize.width * ratio, height: originalSize.height * ratio)
        return newSize
    }

    func log(_ logId: Int, _ content: String) {
        if logId == 0 {
            return
        }
        Log.d(">>>>>\(logId) \(content)")
    }

    func resizedImages(maxSize: CGSize, shouldManageEditing: Bool = true, logId: Int = 0) -> Self {
        if shouldManageEditing { beginEditing() }
        log(logId, "attr.enumerateAttribute(Key.attachment, in: NSRange(location: 0, length: \(length)), options: []), maxSize is (\(maxSize.width), \(maxSize.height))")
        enumerateAttribute(NSAttributedString.Key.attachment, in: NSRange(location: 0, length: length), options: []) { value, _, _ in
            if let attachment = value as? DTImageTextAttachment {
                log(logId, "enumerateAttribute find DTImageTextAttachment attach.imagePath:\(attachment.imagePath ?? "nil") attach.image:\(attachment.image == nil ? "nil" : "UIImage") attach.displaySize:(\(attachment.displaySize.width), \(attachment.displaySize.height)")

                if attachment.displaySize.width > maxSize.width * 0.25 || attachment.displaySize.height > maxSize.height * 0.25 {
                    log(logId, "if (attach.displaySize.width > maxSize.width * 0.25 || attach.displaySize.height > maxSize.height * 0.25) is true")
                    let newSize = resizeToFit(originalSize: attachment.displaySize, maxSize: maxSize)
                    log(logId, "newSize = resizeToFit(originalSize: displaySize, maxSize: maxSize) is (\(newSize.width), \(newSize.height))")
                    if attachment.displaySize != newSize {
                        log(logId, "attach.displaySize != newSize, will set attach.displaySize = newSize")
                        attachment.displaySize = newSize
                    } else {
                        log(logId, "attach.displaySize == newSize, will continue enumerate attach")
                    }
                } else {
                    log(logId, "if (attach.displaySize.width > maxSize.width * 0.25 || attach.displaySize.height > maxSize.height * 0.25) is false, will continue enumerate attach")
                }
                log(logId, "continue enumerate attach")
            }
        }
        log(logId, "end attr.enumerateAttribute(Key.attachment, range) will return self from attr.resizedImages(...)")
        if shouldManageEditing { endEditing() }
        return self
    }

    // MARK: 页码相关

    // 页码文字改成图片
    private func resetPageNumToImage(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key(rawValue: TagSpanClassPage),
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let isTagSpanClassPage = value as? Bool else {
                return
            }

            let attributeStringInRange = attributedSubstring(from: range)
            var sourceAttributes = attributeStringInRange.attributes(at: 0, effectiveRange: nil)
            var imageAttributes = sourceAttributes
            imageAttributes[NSAttributedString.Key.attachment] = nil
            imageAttributes[kCTRunDelegateAttributeName as NSAttributedString.Key] = nil

            if let oldStyle = sourceAttributes[NSAttributedString.Key.paragraphStyle] as? NSParagraphStyle,
               let newStyle = oldStyle.mutableCopy() as? NSMutableParagraphStyle
            {
                newStyle.firstLineHeadIndent = 0
                newStyle.headIndent = 0
                newStyle.defaultTabInterval = 5
                let tab1 = NSTextTab(textAlignment: .left, location: 5, options: [:])
                newStyle.tabStops = [tab1]
                newStyle.alignment = .center
//                newStyle.lineSpacing = 0
//                newStyle.paragraphSpacingBefore = 0
//                newStyle.paragraphSpacing = 0
//                newStyle.lineHeightMultiple = 1
                imageAttributes[NSAttributedString.Key.paragraphStyle] = newStyle
            }

            sourceAttributes[NSAttributedString.Key.foregroundColor] = UIColor.clear // "-"占位符透明色 //dynamicBackgroundColor1
            imageAttributes[NSAttributedString.Key.foregroundColor] = UIColor(hex: 0xE9973E)

            sourceAttributes[NSAttributedString.Key.underlineStyle] = nil
            imageAttributes[NSAttributedString.Key.underlineStyle] = nil

            imageAttributes[NSAttributedString.Key(rawValue: DTLinkHighlightColorAttribute)] = nil
            imageAttributes[NSAttributedString.Key.link] = nil

            var trimString: String = attributeStringInRange.string.trimmingCharacters(in: .whitespaces).lowercased()
            if trimString.hasPrefix("p") && trimString.count > 1 {
                trimString.insert(contentsOf: "\t", at: trimString.index(trimString.startIndex, offsetBy: 1))
            }

            let font = imageAttributes[NSAttributedString.Key.font] as! UIFont
            let attachment = DTImageTextAttachment()
            attachment.image = ImageUtils.generatePageNumImage(str: trimString, attrs: imageAttributes)
            attachment.setDisplay(CGSize(width: attachment.image.size.width, height: attachment.image.size.height), withMaxDisplay: CGSize(width: attachment.image.size.width, height: attachment.image.size.height))
            sourceAttributes[NSAttributedString.Key.attachment] = attachment
            let ctRunDelegate = CTRunDelegate.create(width: attachment.image.size.width, height: font.ascender)!
            sourceAttributes[kCTRunDelegateAttributeName as NSAttributedString.Key] = (ctRunDelegate as Any)

            replaceCharacters(in: range, with: NSAttributedString(string: "-", attributes: sourceAttributes)) // 必须替代才能把原文字去掉。使用横线不是空格为了占位，防止文字边缘忽略空格。
        }

        if shouldManageEditing { endEditing() }
        return self
    }

    private func removePageNum(shouldManageEditing: Bool = true) -> Self {
        if shouldManageEditing { beginEditing() }
        enumerateAttribute(NSAttributedString.Key(rawValue: TagSpanClassPage),
                           in: NSRange(location: 0, length: length),
                           options: []) { value, range, _ in
            guard let isTagSpanClassPage = value as? Bool else {
                return
            }

//            let attributeStringInRange = output.attributedSubstring(from: range)
//            var sourceAttributes = attributeStringInRange.attributes(at: 0, effectiveRange: nil)
//            var imageAttributes = sourceAttributes
//            imageAttributes[NSAttributedString.Key.attachment] = nil
//            imageAttributes[kCTRunDelegateAttributeName as NSAttributedString.Key] = nil
//
//            if let oldStyle = sourceAttributes[NSAttributedString.Key.paragraphStyle] as? NSParagraphStyle,
//               let newStyle = oldStyle.mutableCopy() as? NSMutableParagraphStyle{
//                newStyle.firstLineHeadIndent = 0
//                newStyle.headIndent = 0
//                newStyle.defaultTabInterval = 5
//                let tab1 = NSTextTab(textAlignment: .left, location: 5, options: [:])
//                newStyle.tabStops = [tab1]
//                newStyle.alignment = .center
            ////                newStyle.lineSpacing = 0
            ////                newStyle.paragraphSpacingBefore = 0
            ////                newStyle.paragraphSpacing = 0
            ////                newStyle.lineHeightMultiple = 1
//                imageAttributes[NSAttributedString.Key.paragraphStyle] = newStyle
//            }
//
//            sourceAttributes[NSAttributedString.Key.foregroundColor] = dynamicBackgroundColor1
//            imageAttributes[NSAttributedString.Key.foregroundColor] = UIColor(hex: 0xE9973E)
//
//            sourceAttributes[NSAttributedString.Key.underlineStyle] = nil
//            imageAttributes[NSAttributedString.Key.underlineStyle] = nil
//
//            imageAttributes[NSAttributedString.Key.init(rawValue: DTLinkHighlightColorAttribute)] = nil
//            imageAttributes[NSAttributedString.Key.link] = nil

//            output.replaceCharacters(in: range, with: NSAttributedString(string: "-", attributes: sourceAttributes))
            deleteCharacters(in: range)
        }

        if shouldManageEditing { endEditing() }
        return self
    }
}

extension NSMutableAttributedString {
    func with(font: UIFont) -> NSMutableAttributedString {
        enumerateAttribute(NSAttributedString.Key.font, in: NSMakeRange(0, length), options: .longestEffectiveRangeNotRequired, using: { value, range, _ in
            if let originalFont = value as? UIFont, let newFont = applyTraitsFromFont(originalFont, to: font) {
                addAttribute(NSAttributedString.Key.font, value: newFont, range: range)
            }
        })

        return self
    }

    func applyTraitsFromFont(_ originalFont: UIFont, to newFont: UIFont) -> UIFont? {
        let originalTrait = originalFont.fontDescriptor.symbolicTraits

        if originalTrait.contains(.traitBold) {
            var traits = newFont.fontDescriptor.symbolicTraits
            traits.insert(.traitBold)

            if let fontDescriptor = newFont.fontDescriptor.withSymbolicTraits(traits) {
                return UIFont(descriptor: fontDescriptor, size: 0)
            }
        }

        return newFont
    }
}

enum ImageUtils {
    /// 文子转图片工具方法
    static func generatePageNumImage(str: String, attrs: [NSAttributedString.Key: Any]) -> UIImage {
        let font = attrs[NSAttributedString.Key.font] as! UIFont
        let textSize = (str as NSString).size(withAttributes: attrs)
        // font.lineHeight 等于 font.ascender - font.descender
        // 文字绘制高度textSize比实际字体高度font.ascender - font.descender高。根据底部对齐，上部y取负数绘制。
        let rect = CGRect(x: 0, y: 0, width: textSize.width + 6, height: font.lineHeight)
        let textColor = attrs[NSAttributedString.Key.foregroundColor] as? UIColor ?? UIColor(hex: 0xE9973E)

        UIGraphicsBeginImageContextWithOptions(rect.size, false, 0.0)
        let context: CGContext = UIGraphicsGetCurrentContext()!
        context.setAllowsAntialiasing(true) // 抗锯齿设置

        let roundingCornerPath = UIBezierPath(roundedRect: rect, byRoundingCorners: [.topLeft], cornerRadii: CGSize(width: 3, height: 3))
        textColor.setFill()
        roundingCornerPath.fill()

        let innerRect = CGRect(x: 1, y: 1, width: rect.width, height: rect.height)
        let innerPath = UIBezierPath(roundedRect: innerRect, byRoundingCorners: [.topLeft], cornerRadii: CGSize(width: 3, height: 3))
        context.setBlendMode(.clear)
        innerPath.fill()

        context.setBlendMode(.normal)
        // 文字绘制高度textSize比实际字体高度font.ascender - font.descender高。根据底部对齐，上部y取负数绘制。
        (str as NSString).draw(in: CGRect(x: (rect.size.width - textSize.width) / 2, y: rect.height - textSize.height, width: textSize.width, height: textSize.height), withAttributes: attrs)

        let image = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        image!.disableTap = true
        return image!
    }
}

private var UIIMAGE_DISABLE_TAP: UInt8 = 0
extension UIImage {
    var disableTap: Bool? {
        set {
            objc_setAssociatedObject(self, &UIIMAGE_DISABLE_TAP, newValue, .OBJC_ASSOCIATION_ASSIGN)
        }
        get {
            return objc_getAssociatedObject(self, &UIIMAGE_DISABLE_TAP) as? Bool
        }
    }
}

private var UIIMAGE_IS_FONTNOTE: UInt8 = 0
extension UIImage {
    var isFontNote: Bool? {
        set {
            objc_setAssociatedObject(self, &UIIMAGE_IS_FONTNOTE, newValue, .OBJC_ASSOCIATION_ASSIGN)
        }
        get {
            return objc_getAssociatedObject(self, &UIIMAGE_IS_FONTNOTE) as? Bool
        }
    }
}

extension String {
    // 实现希伯来文检测
    func isHebrewText() -> Bool {
        // 定义希伯来文字符的Unicode标量范围
        let hebrewRange = UnicodeScalar("א").value ... UnicodeScalar("ת").value
        // 检查字符串中的每一个Unicode标量是否在希伯来文范围内
//        return self.unicodeScalars.contains { hebrewRange.contains($0.value) }

        return rangeOfCharacter(from: CharacterSet(charactersIn: "\u{0590}" ... "\u{05FF}")) != nil
    }

    // Add a method to properly format RTL text for selection
    func ensureProperRTLDirection() -> String {
        // Check if this is RTL text
        if isHebrewText() {
            // Wrap in RTL marks if not already present
            if !contains("\u{202B}") {
                return "\u{202B}" + self + "\u{202C}"
            }
        }
        return self
    }
}
