//
//  NSRange+Extension.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/7/9.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation

extension NSRange {
    func contains(_ range: NSRange) -> Bool {
        return location <= range.location && location + length >= range.location + range.length
    }

    /// Creates a safe NSRange that ensures it stays within the bounds of a given length
    static func safeRange(location: Int, length: Int, withinLength totalLength: Int) -> NSRange {
        // Ensure location is within bounds
        let safeLocation = max(0, min(location, totalLength))

        // Ensure length doesn't exceed bounds
        let safeLength = max(0, min(length, totalLength - safeLocation))

        return NSRange(location: safeLocation, length: safeLength)
    }

    /// Safely returns a range that is guaranteed to be within the bounds of a given length
    func constrainedTo(length: Int) -> NSRange {
        let safeLocation = max(0, min(location, length))
        let safeLength = max(0, min(self.length, length - safeLocation))
        return NSRange(location: safeLocation, length: safeLength)
    }

    /// Creates a safe intersection that never returns nil, but might return an empty range
    func safeIntersection(with otherRange: NSRange) -> NSRange {
        guard let intersection = intersection(otherRange) else {
            // No intersection, return an empty range at the start of self
            return NSRange(location: location, length: 0)
        }
        return intersection
    }

    /// Safe union that handles edge cases better
    func union(_ range: NSRange) -> NSRange {
        // If either range is empty, return the other
        if length == 0 { return range }
        if range.length == 0 { return self }

        let newLocation = min(location, range.location)
        let maxExtent1 = location + length
        let maxExtent2 = range.location + range.length
        let newLength = max(maxExtent1, maxExtent2) - newLocation

        return NSRange(location: newLocation, length: newLength)
    }
}
