//
//  DarkTheme.swift
//  WDReader
//
//  Created by <PERSON> on 2020/8/18.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import shared
import SwiftyUserDefaults
import UIKit

let dynamicLinkColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x6F8BA8)
    } else {
        return UIColor(hex: 0x3982C8)
    }
}

let dynamicBorderColor1 = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return darkBGColor
    } else {
        return UIColor(hex: 0xD8D8D8)
    }
}

let dynamicNavBarBottomLineColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x444444)
    } else {
        return UIColor(hex: 0xD8D8D8)
    }
}

let dynmicBGColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x444444)
    } else {
        return UIColor(hex: 0x1F1D1D)
    }
}

let dynmicLabelColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor.white
    } else {
        return UIColor(hex: 0x333333)
    }
}

let dynmicLabelColor2 = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor.white
    } else {
        return UIColor(hex: 0xE9973E)
    }
}

let dynmicBgColor2 = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x4C4C4C)
    } else {
        return UIColor(hex: 0xF4F4F4)
    }
}

let dynmicBgColor3 = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x4C4C4C)
    } else {
        return UIColor.white
    }
}

// 高亮相关
////粉色
// let HighlightPinkColor = UIColor(hex: 0xFDA4A5)
// let dynmicHighlightPinkColor = UIColor { (trainCollection) -> UIColor in
//    if systemUserInterfaceStyle == .dark {
//        return HighlightPinkColor.alpha(0.5)
//    } else {
//        return HighlightPinkColor
//    }
// }
//
////浅蓝
// let HighlightBlueColor = UIColor(hex: 0xA5C5FE)
// let dynmicHighlightBlueColor = UIColor { (trainCollection) -> UIColor in
//    if systemUserInterfaceStyle == .dark {
//        return HighlightBlueColor.alpha(0.5)
//    } else {
//        return HighlightBlueColor
//    }
// }
//
////黄色
// let HighlightYellowColor = UIColor(hex: 0xF2E165)
// let dynmicHighlightYellowColor = UIColor { (trainCollection) -> UIColor in
//    if systemUserInterfaceStyle == .dark {
//        return HighlightYellowColor.alpha(0.5)
//    } else {
//        return HighlightYellowColor
//    }
// }
//
////橘色
// let HighlightOrangeColor = UIColor(hex: 0xFDB75D)
// let dynmicHighlightOrangeColor = UIColor { (trainCollection) -> UIColor in
//    if systemUserInterfaceStyle == .dark {
//        return HighlightOrangeColor.alpha(0.5)
//    } else {
//        return HighlightOrangeColor
//    }
// }
//
////下划线
// let HighlightUnderlineColor = UIColor(hex: 0xE9973E)
// let dynmicHighlightUnderlineColor = UIColor { (trainCollection) -> UIColor in
//    if systemUserInterfaceStyle == .dark {
//        return HighlightUnderlineColor.alpha(0.5)
//    } else {
//        return HighlightUnderlineColor
//    }
// }

// KMM颜色
// 粉色
let dynamicHighlightPinkColor = UIColor { trainCollection -> UIColor in
    UIColor(hex: WDBookAppSDK.shared.getHighlightColor(type: .colorRed, isLightMode: trainCollection.isLight))!
}

// 浅蓝
let dynamicHighlightBlueColor = UIColor { trainCollection -> UIColor in
    UIColor(hex: WDBookAppSDK.shared.getHighlightColor(type: .colorBlue, isLightMode: trainCollection.isLight))!
}

// 黄色
let dynamicHighlightYellowColor = UIColor { trainCollection -> UIColor in
    UIColor(hex: WDBookAppSDK.shared.getHighlightColor(type: .colorYellow, isLightMode: trainCollection.isLight))!
}

// 橘色
let dynamicHighlightOrangeColor = UIColor { trainCollection -> UIColor in
    UIColor(hex: WDBookAppSDK.shared.getHighlightColor(type: .colorOrange, isLightMode: trainCollection.isLight))!
}

let dynamicHighlightSearchResultColor = UIColor { _ -> UIColor in
    UIColor(hex: "FF8A00")!.withAlphaComponent(0.4)
}

// 下划线
let HighlightUnderlineColor = UIColor(hex: 0xE9973E)
let dynamicHighlightUnderlineColor = UIColor { trainCollection -> UIColor in
    UIColor(hex: WDBookAppSDK.shared.getHighlightColor(type: .lineOrange, isLightMode: trainCollection.isLight))!
}

let HighlightUnderlineFillColor = UIColor(hex: 0xF5F5F5)
let dynamicHighlightUnderlineFillColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return HighlightUnderlineFillColor.alpha(0.5)
    } else {
        return HighlightUnderlineFillColor
    }
}

// 书签
let dynamicBookMarkSelectedColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0xA5343C)
    } else {
        return UIColor(hex: 0xD43A42)
    }
}

let dynamicQuickEntryColor = UIColor { trainCollection -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0xB67836)
    } else {
        return UIColor(hex: 0xE9973E)
    }
}

// 黑夜模式切换UI的设置颜色
let dayTextColor = UIColor(hex: 0x4C4C4C)
let dayBGColor = UIColor.white

let darkTextColor = UIColor(hex: 0x98999A)
let darkBGColor = UIColor(hex: 0x010203)

let dynamicDarkModeTextColor = UIColor { _ -> UIColor in
    if systemUserInterfaceStyle == .dark {
        return darkTextColor
    } else {
        return dayTextColor
    }
}

let dynamicDarkModeBGColor = UIColor { _ -> UIColor in
    if systemUserInterfaceStyle == .dark {
        return darkBGColor
    } else {
        return dayBGColor
    }
}

// MARK：------------控制方法--------------
public enum DarkMode: Int {
    case follow = 0
    case light
    case dark
}

let NOTI_SYSTEM_DARKMODE_CHANGED = NSNotification.Name(rawValue: "NOTI_SYSTEM_DARK_MODE_CHANGED")
let NOTI_SYSTEM_DARKMODE_DID_CHANGED = NSNotification.Name(rawValue: "NOTI_SYSTEM_DARKMODE_DID_CHANGED")
public var systemUserInterfaceStyle: UIUserInterfaceStyle = UITraitCollection.current.userInterfaceStyle {
    didSet {
        NotificationCenter.default.post(name: NOTI_SYSTEM_DARKMODE_CHANGED, object: nil)
    }
}

func initDarkMode() {
    if let savedValue = UserDefaults.standard.object(forKey: DefaultsKeys.DARK_MODE_KEY) as? Int,
       let savedMode = DarkMode(rawValue: savedValue)
    {
        setDarkMode(mode: savedMode)
    } else {
        setDarkMode(mode: .follow)
    }
}

func setDarkMode(mode: DarkMode) {
    Defaults[key: DefaultsKeys.DARK_MODE] = mode.rawValue

    if mode == .follow {
        Defaults[key: DefaultsKeys.IS_DARK_MODE] = systemUserInterfaceStyle == .dark
        UIApplication.shared.windows[0].overrideUserInterfaceStyle = systemUserInterfaceStyle
    } else if mode == .light {
        Defaults[key: DefaultsKeys.IS_DARK_MODE] = false
        UIApplication.shared.windows[0].overrideUserInterfaceStyle = .light
    } else {
        Defaults[key: DefaultsKeys.IS_DARK_MODE] = true
        UIApplication.shared.windows[0].overrideUserInterfaceStyle = .dark
    }

    // CGColor在相应UI中来修改。
//    //解决切换黑夜模式，文中高亮的cgColor没有改变的问题。手动修改。
//    systemUserInterfaceStyle = UIApplication.shared.windows[0].overrideUserInterfaceStyle

    NotificationCenter.default.post(name: NOTI_SYSTEM_DARKMODE_DID_CHANGED, object: nil)
}

public func checkDarkMode() {
    DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
        systemUserInterfaceStyle = UITraitCollection.current.userInterfaceStyle
        initDarkMode()
        if UserDefaults.standard.isFollowSystemDarkMode {
            if UserDefaults.standard.isDarkMode != (systemUserInterfaceStyle == .dark) {
                setDarkMode(mode: .follow)
            }
        }
    }
}

extension DefaultsKeys {
    static let DARK_MODE_KEY = "WD_DARK_MODE"
    static let IS_DARK_MODE_KEY = "WD_IS_DARK_MODE"
    static let DARK_MODE = DefaultsKey<Int>(DARK_MODE_KEY, defaultValue: DarkMode.follow.rawValue) // 0,跟随系统，1浅色，2深色
    static let IS_DARK_MODE = DefaultsKey<Bool>(IS_DARK_MODE_KEY, defaultValue: false)
}

public extension UserDefaults {
    var isFollowSystemDarkMode: Bool {
        return Defaults[key: DefaultsKeys.DARK_MODE] == DarkMode.follow.rawValue
    }

    var isDarkMode: Bool {
        return Defaults[key: DefaultsKeys.IS_DARK_MODE]
    }
}

// 在书卷和章目录有效；但进入经文节目录后，无效。
private var DYNAMIC_BORDER_COLOR: UInt8 = 0
extension UIView {
    var layerBorderColorDynimic: UIColor? {
        set {
            objc_setAssociatedObject(self, &DYNAMIC_BORDER_COLOR, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
            layer.borderColor = newValue?.cgColor

            swizzle(cls: type(of: self), #selector(traitCollectionDidChange(_:)), with: #selector(swizzled_traitCollectionDidChange(_:)))
        }
        get {
            return objc_getAssociatedObject(self, &DYNAMIC_BORDER_COLOR) as? UIColor
        }
    }

    @objc func swizzled_traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        swizzled_traitCollectionDidChange(previousTraitCollection)
        layer.borderColor = layerBorderColorDynimic?.cgColor
    }

    public func swizzle(cls: AnyClass, _ originalSelector: Selector, with swizzledSelector: Selector) {
        guard let originalMethod = class_getInstanceMethod(cls, originalSelector), let swizzledMethod = class_getInstanceMethod(cls, swizzledSelector) else {
            return
        }

        let didAddMethod = class_addMethod(cls, originalSelector, method_getImplementation(swizzledMethod), method_getTypeEncoding(swizzledMethod))

        if didAddMethod {
            class_replaceMethod(cls, swizzledSelector, method_getImplementation(originalMethod), method_getTypeEncoding(originalMethod))
        } else {
            method_exchangeImplementations(originalMethod, swizzledMethod)
        }
    }
}

// public protocol UITraitEnvironment : NSObjectProtocol {
//    // 当前模式
//    @available(iOS 8.0, *)
//    var traitCollection: UITraitCollection { get }
//
//    // 重写该方法监听模式的改变
//    @available(iOS 8.0, *)
//    func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?)
// }
//
//
//// 使用方法
// override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
//    super.traitCollectionDidChange(previousTraitCollection)
//
//    // 每次模式改变的时候, 这里都会执行
//    print("模式改变了")
// }
