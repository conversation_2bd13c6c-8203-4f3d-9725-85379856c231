//
//  Paginator.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/18.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import CoreText
import DeviceKit
import DTCoreText
import Foundation
import UIKit
import ZIPFoundation

extension Paginator {
    static let isAllLoadChangedNotification = Notification.Name("Paginator_isAllLoadChangedNotification")
}

class Paginator {
    // MARK: 常规页面信息配置。

    @Atomic var loadFileTime: Double = 0.0
    @Atomic var modifyHTMLTime: Double = 0.0
    @Atomic var attributTime: Double = 0.0
    @Atomic var attributTimeMaxOne: Double = 0.0
    @Atomic var resetFormatTime: Double = 0.0
    @Atomic var splitPageTime: Double = 0.0

    static let PAGE_INSETS: UIEdgeInsets = .init(top: 71, left: 32, bottom: 51, right: 32)
    static let PAGE_TAP_AREA_RATE_LEFT: CGFloat = 1 / 5
    static let PAGE_TAP_AREA_RATE_RIGHT: CGFloat = 4 / 5

    var pageContentFrame: CGRect {
        //        let pageRect = CGRect(x: 0, y: 0, width: pageSize.width, height: pageSize.height).inset(by: Paginator.PAGE_INSERT)
        //        let pageFrame = CGRect(x: 0, y: 0, width: pageRect.width, height: pageRect.height).inset(by: Paginator.PAGE_INSERT)
        //        let pageFrame = CGRect(x: 0, y: 0,
        //                               width: pageSize.width - Paginator.PAGE_INSERT.left - Paginator.PAGE_INSERT.right,
        //                               height: pageSize.height - Paginator.PAGE_INSERT.top - Paginator.PAGE_INSERT.bottom)
        //        let pageFrame = CGRect(x: 0, y: 0, width: pageSize.width, height: pageSize.height).inset(by: Paginator.PAGE_INSERT)

        let pageInsert = Paginator.PAGE_INSETS
        return CGRect(x: 0, y: 0, width: pageSize.width - pageInsert.left - pageInsert.right, height: pageSize.height - pageInsert.top - pageInsert.bottom)
    }

    var pageContentSize: CGSize {
        let pageInsert = Paginator.PAGE_INSETS
        return CGSize(width: pageSize.width - pageInsert.left - pageInsert.right, height: pageSize.height - pageInsert.top - pageInsert.bottom)
    }

    var maxImageSize: CGSize {
        let pageSize = pageContentSize
        if Device.current.isOneOf(
            [
                .iPodTouch7, .simulator(.iPodTouch7),
                .iPhoneSE2, .simulator(.iPhoneSE2),
                .iPhoneSE, .simulator(.iPhoneSE),
                .iPhone8, .simulator(.iPhone8),
                .iPhone7, .simulator(.iPhone7),
                .iPhone6, .simulator(.iPhone6),
                .iPhone8Plus, .simulator(.iPhone8Plus),
                .iPhone7Plus, .simulator(.iPhone7Plus),
                .iPhone6Plus, .simulator(.iPhone6Plus),
            ]
        ) {
            return CGSize(width: pageSize.width - 15, height: pageSize.height - 15)
        } else if Device.current.isPad {
            return CGSize(width: pageSize.width - 150, height: pageSize.height - 150)
        } else {
            return pageSize
        }
    }

    var queue = OperationQueue()
    private var pageSize: CGSize

    // MARK: 包含不同字体分页的配置。

    static var patinators: [String: Paginator] = [:] // 暂未使用。性能好，不需要分开paginator。
    static var currentPationatorKey: String = ""
    static var current: Paginator!

    // MARK: 每章数据

    /// 章索引和章
    @Atomic var chapterFileDic: [Int: Chapter] = .init()

    // MARK:

    /// 目录和页码关系
    @Atomic var resourcePageNumberDic = [String: Int]() // 资源锚点对应的页码数。key: href，href#fragmentid。value:pagenum。
//    @Atomic var pages:[Page] = [Page]() //影响范围，reloaddata数据，总页码数量，当前页码不大于总页码校验，上一页，下一页，跳页，阅读进度计算。(内存占用过大，废弃)
    var indexPathRecords: [IndexPath] = .init()
    var pageCount: Int? // 全部加载完毕后，有值。重新加载前清空。

    var currentIndexPath: IndexPath = .init(item: 0, section: 0) {
        didSet {
//            Log.d("调适:\(currentIndexPath)")
            guard indexPathRecords.last != currentIndexPath else {
                return
            }

            if indexPathRecords.count >= 2 {
                indexPathRecords.removeFirst()
            }
            indexPathRecords.append(currentIndexPath)
        }
    }

    var isAllLoad = false { // 第一次isAllLoad之后设为true。重新加载（换字体，换字号）设为false
        didSet {
            NotificationCenter.default.post(name: Paginator.isAllLoadChangedNotification, object: isAllLoad)
        }
    }

//    let queuePages:DispatchQueue = DispatchQueue(label: "ChapterPages_Queue", qos: .userInteractive)
    let queuePagesLock: NSLock = .init()

    required init(pageSize: CGSize) {
        self.pageSize = pageSize
    }

    func setPageSize(_ size: CGSize) {
        pageSize = size
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    func stop() {
        chainMutexThreadPool?.stop()
    }

    // 分段算法
    func splitPage(attrString: NSAttributedString, maxPageLength: Int = 10000, logId: Int = 0) -> [CTPage] {
        if attrString.length <= maxPageLength {
            var pageIndex = 0
            return splitPage(pageIndex: &pageIndex, locationOnFull: 0, attr: attrString, logId: logId)
        }

        var textPos = 0
        var pages = [CTPage]()
        var pageIndex = 0

        while textPos < attrString.length {
            let segmentLength = min(maxPageLength, attrString.length - textPos)
            let segmentRange = NSRange(location: textPos, length: segmentLength)
            var segmentAttrStr = NSMutableAttributedString(attributedString: attrString.attributedSubstring(from: segmentRange))
            // 首行缩进
            segmentAttrStr = adjustFirstLineIndentIfNeeded(segmentAttrString: segmentAttrStr, segmentRange: segmentRange, fullAttrString: attrString)

            var segmentPages = splitPage(pageIndex: &pageIndex, locationOnFull: segmentRange.location, attr: segmentAttrStr)
            if let lastCTFrame = segmentPages.last?.ctFrame {
                let frameRange = CTFrameGetVisibleStringRange(lastCTFrame)
                if textPos + frameRange.location + frameRange.length < attrString.length {
                    segmentPages.removeLast()
                    pages.append(contentsOf: segmentPages)
                    textPos = textPos + frameRange.location
                    continue
                }
            }

            pages.append(contentsOf: segmentPages)
            textPos += segmentLength
        }

        return pages
    }

    func applyRandomColor(to attributedString: NSMutableAttributedString) {
        let color = UIColor.randomColor() // 使用前面定义的randomColor函数
        attributedString.addAttribute(.foregroundColor, value: color, range: NSRange(location: 0, length: attributedString.length))
    }

    func log(_ logId: Int, _ content: String) {
        if logId == 0 {
            return
        }
        Log.d(">>>>>\(logId) \(content)")
    }

    func splitPage(pageIndex: inout Int, locationOnFull: Int, attr: NSAttributedString, logId: Int = 0) -> [CTPage] {
        var attrString = NSMutableAttributedString(attributedString: attr)
        log(logId, "splitPage(attr) -> [CTPage]")
        log(logId, "origin attr is \(attrString), pageContentFrame.size is (\(pageContentFrame.size.width), \(pageContentFrame.size.height))")

        // 调整部分图片部分iphone显示不出来
        if Device.current.isPhone {
            if UIDevice.isSmailScreenPhone {
                attrString = attrString.resizedImages(maxSize: CGSize(width: pageContentFrame.size.width, height: pageContentFrame.size.height - 60))
            } else {
                if pageContentFrame.height > pageContentFrame.width {
                    log(logId, "竖屏较大iphone，will call attr = attr.resizedImages(maxSize: (\(pageContentFrame.size.width - 10), \(pageContentFrame.size.height - 10)))")
                    attrString = attrString.resizedImages(maxSize: CGSize(width: pageContentFrame.size.width - 10, height: pageContentFrame.size.height - 10), logId: logId)
                    log(logId, "after resizedImages(maxSize), attr.length is \(attrString.length)")
                } else {
                    log(logId, "横屏较大iphone，will call attr = attr.resizedImages(maxSize: (\(pageContentFrame.size.width - 40), \(pageContentFrame.size.height - 40)))")
                    attrString = attrString.resizedImages(maxSize: CGSize(width: pageContentFrame.size.width - 40, height: pageContentFrame.size.height - 40), logId: logId)
                    log(logId, "after resizedImages(maxSize), attr.length is \(attrString.length)")
                }
            }
        }

        let orignAttrString = attrString

        let path = CGMutablePath()
        path.addRect(pageContentFrame)
        let cfAttrString = attrString as CFAttributedString

        let orignFramesetter = CTFramesetterCreateWithAttributedStringOnMain(cfAttrString)
        var framesetter = orignFramesetter
        let originMaxSize = pageContentFrame.size
        var maxSize = originMaxSize

        var loopNum = 0
        var maxLengthInRange = 0 // 0代表到文本结束

        var textPos = 0
        var pages = [CTPage]()
        log(logId, "初始化一些变量：cfAttrString:\(cfAttrString), maxSize:(\(maxSize.width),\(maxSize.height)), maxLengthInRange:\(maxLengthInRange), textPos:\(textPos), pageIndex:\(pageIndex), pages:[]")

        while textPos < attrString.length {
            loopNum += 1
            log(logId, "while loopNum: \(loopNum) textPos:\(textPos) attrString.length:\(attrString.length) maxLengthInRange:\(maxLengthInRange)")
            let ctframe = CTFramesetterCreateFrame(framesetter, CFRangeMake(textPos, maxLengthInRange), path, nil)
            log(logId, "ctframe = CTFramesetterCreateFrame(framesetter, range(\(textPos),\(maxLengthInRange)), path) is \(ctframe)")

            let imageAttachments = attachImages(attrString: attrString, ctframe: ctframe)
            log(logId, "imageAttachments = attachImages(attrString:attrString,ctframe: ctframe) is \(imageAttachments.count) items")
            let frameRange = CTFrameGetVisibleStringRange(ctframe)
            log(logId, "frameRange = CTFrameGetVisibleStringRange(ctframe) is (location: \(frameRange.location), len:\(frameRange.length))")

            // 矫正纯图片书籍显示不出来。
            if frameRange.location == 0 && frameRange.length == 0 {
                log(logId, "frameRange.location == 0 && frameRange.length == 0, will reduce maxSize")
                maxSize = CGSize(width: maxSize.width - 10, height: maxSize.height - 10)
                log(logId, "maxSize reduce to (\(maxSize.width), \(maxSize.height))")
                if maxSize.width <= 0 || maxSize.height <= 0 {
                    textPos += 1
                    log(logId, "maxSize.width or height <=0, add textPos to \(textPos), will recover attrString,framesetter,maxSize to origin values")
                    attrString = orignAttrString
                    framesetter = orignFramesetter
                    maxSize = originMaxSize
                    log(logId, "continue while")
                    continue
                }
                attrString = attrString.resizedImages(maxSize: maxSize, logId: logId)
                log(logId, "use new maxSize call attr = attr.resizedImages(maxSize: maxSize)")
                framesetter = CTFramesetterCreateWithAttributedStringOnMain(attrString as CFAttributedString)
                log(logId, "use new attr call framesetter = CTFramesetterCreateWithAttributedStringOnMain(attr as CFAttributedString)")
                log(logId, "continue while")
                continue
            }

            // 矫正纯图片书籍分页的的位置
            if imageAttachments.count > 2 && attrString.attributedSubstring(from: NSRange(location: Int(frameRange.location), length: Int(frameRange.length))).isEffectivelyEmpty {
                maxLengthInRange = 2
                log(logId, "if (imageAttachments.count > 2 && attrString.attributedSubstring(from: NSRange(location: Int(frameRange.location), length: Int(frameRange.length))).isEffectivelyEmpty) is true, change maxLengthInRange to 2")
                log(logId, "continue while")
                continue
            }
            maxLengthInRange = 0
            log(logId, "call maxLengthInRange = 0;")
            textPos += frameRange.length
            log(logId, "textPos += frameRange.length is \(frameRange.length)")
            if frameRange.length == 0 {
                textPos += 1
                log(logId, "frameRange.length == 0, textPos += 1")
            }

            let page = CTPage(ctFrame: ctframe, pageIndex: pageIndex, ctFrameRange: NSRange(location: frameRange.location, length: frameRange.length), segmentLocationOnFull: locationOnFull, imageAttachments: imageAttachments)
            pages.append(page)
            log(logId, "add page = CTPage(ctFrame: ctframe, pageIndex: \(pageIndex),ctFrameRange: NSRange(location: \(frameRange.location), length: \(frameRange.length)), segmentLocationOnFull: \(locationOnFull), imageAttachments: \(imageAttachments.count) items)")

            pageIndex += 1
            log(logId, "pageIndex += 1")
            log(logId, "continue while")
        }
        return pages
    }

    private func adjustFirstLineIndentIfNeeded(segmentAttrString: NSMutableAttributedString, segmentRange: NSRange, fullAttrString: NSAttributedString) -> NSMutableAttributedString {
        var effectiveRange = NSRange()
        let searchRangeStart = max(segmentRange.location - 2000, 0)
        let searchRangeEnd = min(segmentRange.location + 2000, segmentRange.location + segmentRange.length)
        let searchRange = NSRange(location: searchRangeStart, length: searchRangeEnd - searchRangeStart)

        let paragraphStyle = fullAttrString.attribute(.paragraphStyle, at: segmentRange.location, longestEffectiveRange: &effectiveRange, in: searchRange) as? NSParagraphStyle

        if let mutableParagraphStyle = paragraphStyle?.mutableCopy() as? NSMutableParagraphStyle,
           effectiveRange.location < segmentRange.location
        {
            // 移除首行缩进
            mutableParagraphStyle.firstLineHeadIndent = 0
            mutableParagraphStyle.headIndent = 0 // 可能还需要调整这个

            // 计算新的属性应用范围，确保不会超出 segmentAttrString 的范围
            let newRangeLength = min(effectiveRange.length - (segmentRange.location - effectiveRange.location), segmentAttrString.length)
            let newRange = NSRange(location: 0, length: newRangeLength)

            // 应用调整后的段落样式到整个分页的段落中
            segmentAttrString.addAttribute(.paragraphStyle, value: mutableParagraphStyle, range: newRange)
        }
        return segmentAttrString
    }

    private func attachImages(attrString _: NSAttributedString, ctframe: CTFrame) -> [ImageAttachment] {
        var imageAttachments = [ImageAttachment]()

        let lines = CTFrameGetLines(ctframe) as NSArray
        // 2行的起始点坐标复制到origi数组中。
        // 用CTFrameGetOrigins去复制ctframe的行初始点坐标到origins数组中。通过设置长度为0的range，CTFrameGetOrigins将知道要遍历整个CTFrame。
        var origins = [CGPoint](repeating: .zero, count: lines.count)
        CTFrameGetLineOrigins(ctframe, CFRangeMake(0, 0), &origins)

        for lineIndex in 0 ..< lines.count {
            let line = lines[lineIndex] as! CTLine
            var lineAscent: CGFloat = 0
            var lineDescent: CGFloat = 0
            var lineLeading: CGFloat = 0
            let _ = CTLineGetTypographicBounds(line, &lineAscent, &lineDescent, &lineLeading)
            // 如果这一行的字形、文件名和图片文件名都存在的话，则遍历这一行的字形。
            if let glyphRuns = CTLineGetGlyphRuns(line) as? [CTRun] {
                for run in glyphRuns {
                    if let attributes = CTRunGetAttributes(run) as? [String: Any] {
                        // 2、通过attributes属性的key获取数据
                        if let attachment = attributes[NSAttributedString.Key.attachment.rawValue] as? DTImageTextAttachment {
                            // 1 纸书页码
                            if let isTagSpanClassPage = attributes[NSAttributedString.Key(rawValue: TagSpanClassPage).rawValue] as? Bool, isTagSpanClassPage, let image = attachment.image {
                                let xOffset = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location, nil)
                                let imgBounds = CGRect(x: origins[lineIndex].x + xOffset, y: origins[lineIndex].y - lineDescent, width: image.size.width, height: image.size.height)
                                imageAttachments.append(ImageAttachment(image: attachment.image, frame: imgBounds))
                            } else {
                                // 2 svg图片
                                if let isTagSVG = attributes[NSAttributedString.Key(rawValue: TagSVG).rawValue] as? Bool, isTagSVG,
                                   let baseURL = attributes[NSAttributedString.Key(rawValue: BasePath).rawValue] as? URL
                                {
                                    if let imageData = getImage(basePath: baseURL.path, href: attachment.imagePath) {
                                        attachment.image = imageData
                                    }
                                }
                                if let image = attachment.image {
                                    // 3 注释图片和其他图片
                                    var imgBounds: CGRect = .zero

                                    // 通常图片计算位置
                                    // 用CTLineGetOffsetForStringIndex获取线的x偏移，然后将其添加到imgBounds的起点坐标。
                                    let xOffset = CTLineGetOffsetForStringIndex(line, CTRunGetStringRange(run).location, nil)
                                    imgBounds.origin.x = origins[lineIndex].x + xOffset
                                    imgBounds.origin.y = origins[lineIndex].y - lineDescent / 2

                                    // 使用CTRunGetTypographicBounds计算图像宽度，并将高度设置为ascent。
                                    // run的descent和leading都是0。所以使用lineDescent，lineLeading
                                    var ascent: CGFloat = 0
                                    var descent: CGFloat = 0
                                    var leading: CGFloat = 0
                                    imgBounds.size.width = CGFloat(CTRunGetTypographicBounds(run, CFRangeMake(0, 0), &ascent, &descent, &leading)) // 内容宽，包含内容两边的间隙。
                                    imgBounds.size.height = lineAscent // 行高

                                    // 特殊图片计算位置
                                    if image.isFontNote ?? false {
                                        if let obj = attributes[(kCTRunDelegateAttributeName as NSAttributedString.Key).rawValue] {
                                            let ctRunDelegate = obj as! CTRunDelegate

                                            imgBounds.origin.x = origins[lineIndex].x + xOffset
                                            imgBounds.origin.y = origins[lineIndex].y

                                            let imgPointer = CTRunDelegateGetRefCon(ctRunDelegate)
                                            let d = imgPointer.assumingMemoryBound(to: RunStruct.self)
                                            imgBounds.size.width = d.pointee.width
                                            imgBounds.size.height = d.pointee.ascent + d.pointee.descent // 方形。（不一定是方形）
                                        }
                                    }
                                    imageAttachments.append(ImageAttachment(image: image, frame: imgBounds))
                                }
                            }
                        }
                    }
                }
            }
        }
        return imageAttachments
    }

    private func getImage(basePath: String, href: String) -> UIImage? {
        let baseURL = URL(fileURLWithPath: basePath)
        let EPUBDir = baseURL.lastPathComponent
        let epubFileURL = baseURL.deletingLastPathComponent()
        let imagePath = EPUBDir + "/" + href

        var unzipImagePath = epubFileURL
        unzipImagePath = unzipImagePath.deletingLastPathComponent()
        unzipImagePath.appendPathComponent(href)

        guard let archive = Archive(url: epubFileURL, accessMode: .read) else {
            return nil
        }
        guard let entry = archive[imagePath] else {
            return nil
        }
        do {
            try archive.extract(entry, to: unzipImagePath)
        } catch {
            print("Extracting entry from archive failed with error:\(error)")
        }

        let image = UIImage(contentsOfFile: unzipImagePath.path)
        try? FileManager.default.removeItem(at: unzipImagePath)
        return image
    }

    var chainMutexThreadPool: ThreadPool?
    func chainMutexThreadPoolSplitPage(oneTaskComplete: ((_ key: String?) -> Void)? = nil, allTasksComplete: @escaping () -> Void) {
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            self?.chainMutexThreadPool = ThreadPool()
            self?.chainMutexThreadPool?.initData()

            self?.chainMutexThreadPool?.setOneCompletionHandler { key in
                DispatchQueue.main.async {
                    oneTaskComplete?(key)
                }
            }
            // 测试线程：延迟2s
            //                    DispatchQueue.global(qos: .userInteractive).asyncAfter(deadline: .now() + 2, execute: {
            self?.chainMutexThreadPool?.setAllCompletionHandler { [weak self] in
                //                    self.pagesCompleteForLog()

                // 构造pages数组和每章首page在数组中的页码。比较耗时
                self?.computePageWhenComplete()

                DispatchQueue.main.async { [weak self] in
                    self?.isAllLoad = true
                    if self != nil {
                        allTasksComplete()
                    }
                }
            }

            self?.execute()
            //                    })
        }
    }

    // Test测试代码，调整优先级。-- begin
    var priorityTimer: Timer?
    var keyNum: Int = 1000
    private func startPriorityTimer() {
        priorityTimer?.invalidate()
        //        autoscrollTimer = Timer.scheduledTimer(withTimeInterval: 3, repeats: false) {  _ in self.scrollToNextElement() }
        priorityTimer = Timer(timeInterval: 5, repeats: true, block: { [weak self] _ in
            guard let self = self else { return }
            self.chainMutexThreadPool?.resetPriorityHigh(key: String(self.keyNum - 50))
            self.keyNum -= 50
            if self.keyNum < 0 {
                self.stopPriorityTimer()
            }
        })
        RunLoop.main.add(priorityTimer!, forMode: .common)
    }

    private func stopPriorityTimer() {
        priorityTimer?.invalidate()
        priorityTimer = nil
    }

    // Test测试代码，调整优先级。-- end

    func resetPriorityHigh(key: String) {
        guard let chapterIndex = Int(key) else {
            return
        }

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            if self.chapterFileDic[chapterIndex]?.loadState == .paged ||
                self.chapterFileDic[chapterIndex]?.pages.count == 0 ||
                !(self.chainMutexThreadPool?.resetPriorityHigh(key: key) ?? false)
            {
                self.chainMutexThreadPool?.executeAndResetPriorityHiggh(key: key, task: { [weak self] in
                    if let self = self {
                        self.parse(chapterIndex: chapterIndex)
                    }
                })
            }
        }
    }

    private func execute() {
        for i in 0 ..< WDReaderCenter.shared.fbBook.spine.spineReferences.count {
            chainMutexThreadPool?.execute(key: String(i), task: { [weak self] in
                if let self = self {
                    self.parse(chapterIndex: i)
                }
            })
        }

        // startPriorityTimer()
    }

    /// 再次计算，重设字体大小时候调用。
    /// after：开始执行之后。并未完成
    func reExecute(after: @escaping () -> Void) {
        // 不设置新的 setOneCompletionHandler和setAllCompletionHandler。

        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else {
                return
            }
            self.chainMutexThreadPool?.clearTasks()

//            let chapterDic = self.chaptorFileDic
            self.clearChapterData()
            self.clearStoreData()

            for i in 0 ..< WDReaderCenter.shared.fbBook.spine.spineReferences.count {
                self.chainMutexThreadPool?.execute(key: String(i), task: { [weak self] in
                    if let self = self {
                        // 如果解析过，那么重设attributedstring。此处并没有处理行间距。这部分在DTCoreText设置中。
//                        if let chapter = chapterDic[i]{
//                            let displayedAttributedString = chapter.displayedAttributedString.resetFootSize()
//                            let ctPagesInOneChaptor = self.splitPage(attrString: displayedAttributedString)
//                            let pagesInOneChaptor = ctPagesInOneChaptor.enumerated().map{(index,ctPage) in return Page(ctPage: ctPage, chapterIndex: i, pageIndexInChapter: index)}
//                            self.addChapter(chapterIndex: i, pages: pagesInOneChaptor, resource: chapter.resource, sourceHTML: chapter.sourceHTML, originalAttributedString: chapter.originalAttributedString,displayedAttributedString: displayedAttributedString)
//                        //如果未解析过，开始解析
//                        }else{
                        self.parse(chapterIndex: i)
//                        }
                    }
                })
            }
            DispatchQueue.main.async {
                after()
            }
        }
    }

    // 解析单个HTML
    private func parse(chapterIndex: Int) {
        let resource = WDReaderCenter.shared.fbBook.spine.spineReferences[chapterIndex].resource

        var html: String?
        if WDReaderConfig.hasCode {
            readCryptorSemaphore.wait()
            html = WDCryptor.getBookText(WDReaderCenter.shared.fbBook.bookBasePath!, pagePath: resource.href)
            readCryptorSemaphore.signal()
        } else {
            html = try? String(contentsOfFile: resource.fullHref, encoding: String.Encoding.utf8)
        }

        // 希伯来文问题。写死要处理的html。
//        html = HTMLCommonUtils.hebrewTextNotWork
        //        html = HTMLCommonUtils.hebrewTextWorked
        guard var contentHtml = html else {
            return
        }

        //        Log.d("处理章节：\(chapterIndex), \(resource.href),第一章内容:\(chapterIndex == 0 ? html : "")")

        // 计时
        //        self.loadFileTime += CFAbsoluteTimeGetCurrent() - operationStart
        //        let replaceStart = CFAbsoluteTimeGetCurrent()
        //

        // 调试
        //        if contentHtml.contains("给我画一只绵羊。"){
        //            Log.d("debug")
        //        }
//                if resource.href.contains("EPUB/titlepage.xhtml"){
//                    Log.d("debug")
//                }

        // 去掉图片。
        //        str = str.replacingOccurrences(of: "<img[^>]*>", with: "",options: [.regularExpression])
        //        str = str.replacingOccurrences(of: "<img alt=", with: "<!-- img alt=")
        //                .replacingOccurrences(of: "jpeg\" class=\"calibre10\"/>", with: "jpeg\" class=\"calibre10\"/ -->")

        //        2020-09-29 16:51:50.074497+0800 WDReaderExample[31908:605716] nil host used in call to allowsSpecificHTTPSCertificateForHost
        //        2020-09-29 16:51:50.074716+0800 WDReaderExample[31908:605716] nil host used in call to allowsAnyHTTPSCertificateForHost:
        // 去掉css
        //            str = html.replacingOccurrences(of: "<link href", with: "<!-- link href")
        //                .replacingOccurrences(of: "\"text/css\"/>", with: "\"text/css\"/ -->")

        //            self.modifyHTMLTime += CFAbsoluteTimeGetCurrent() - replaceStart
        //            let attributedStringStart = CFAbsoluteTimeGetCurrent()

        // 配置段间距。 reset不会更改
        let tag = "<style>p{margin:4 auto}</style>"
        //        let tag = "<style>p {margin: \(WDReaderConfig.currentFontSize/2)px 0px \(WDReaderConfig.currentFontSize/2)px 0px;}</style>"
        if !contentHtml.contains(tag) {
            contentHtml = tag.appending(contentHtml)
        }

//        let startPresT = CFAbsoluteTimeGetCurrent()

        // 系统html解析：必须在主线程才能运行，否则卡死？ 必须是串行？。
        //        let attributedString = try! NSAttributedString(data: contentHtml.data(using: .utf8)!,
        //                                                            options: [NSAttributedString.DocumentReadingOptionKey.documentType: NSAttributedString.DocumentType.html,
        //                                                                      NSAttributedString.DocumentReadingOptionKey.characterEncoding:String.Encoding.utf8.rawValue], documentAttributes: nil)

        // TDCoreText
        // 使用build和option可以使 kCTForegroundColorAttributeName -> NSForegroundColorAttributeName
        //        The reason is setting above attribute to true makes use of NSForegroundColorAttributeName (available iOS 6.0 onwards only) instead of the default kCTForegroundColorAttributeName.

        // DTCoreText版本
        let options = [DTUseiOS6Attributes: true,
                       DTDefaultFontFamily: WDReaderConfig.currentFontFamilyName,
                       DTDefaultFontName: WDReaderConfig.currentFontName,
                       DTDefaultFontSize: WDReaderConfig.currentFontSize, // 会受到html影响？？
                       DTDefaultLineHeightMultiplier: 1.1, // 行间距乘数
                       DTDefaultTextAlignment: CTTextAlignment.justified.rawValue, // 会全局生效
                       DTDefaultFirstLineHeadIndent: WDReaderConfig.currentFontSize * 2, // 左缩进，首次受影响，resetFormat()失效。
                       DTDefaultTextColor: dynamicTextColor3,
                       DTDefaultLinkColor: dynamicLinkColor, // 此处修改html<a color的颜色，解析后只有单一颜色。在DTCoreText内部没找到给AttributedString颜色赋值的位置，所以放在了NSAttributedString.resetFormat()中修改颜色。
                       //             DTDefaultLinkHighlightColor:dynamicLinkColor,
                       DTMaxImageSize: maxImageSize,
                       DefaultPageSize: pageContentSize,
                       Support2ColumnsTable: true,
//                       NSBaseURLDocumentOption:URL(fileURLWithPath: resource.basePath()),
                       BasePath: URL(fileURLWithPath: resource.basePath())] as [String: Any]
//        debugPrint("阅读器 加载 开始解析章:\(chapterIndex), href:\(resource.href!)")
        let stringBuilder = DTHTMLAttributedStringBuilder(html: contentHtml.data(using: .utf8)!, options: options, documentAttributes: nil)
        let attributedString = stringBuilder?.generatedAttributedString() ?? NSAttributedString(string: "") // 防止nil崩溃

        //        let attributedString = NSAttributedString(htmlData: str.data(using: .utf8)!,options:nil, documentAttributes: nil)!

//        Log.d("阅读器 加载 解析完毕：\(chapterIndex)，耗时\(CFAbsoluteTimeGetCurrent() - startPresT), href:\(resource.href!)Thread:\(Thread.current)")

        //        self.attributTime += atttime
        //        self.attributTimeMaxOne = max(self.attributTimeMaxOne, atttime)

        let startFormatT = CFAbsoluteTimeGetCurrent()
        var displayedAttributedString = NSMutableAttributedString(attributedString: attributedString).resetFormat()
//        Log.d("阅读器 加载 重设格式: \(chapterIndex)，耗时:\(CFAbsoluteTimeGetCurrent() - startFormatT), href:\(resource.href!) 内容:\(displayedAttributedString.string.prefix(10))")
        // 如果首页是空页面，那么第二页左对齐。
        // 如果首页不是空页面，那么首页左对齐。
        // TODO：如果多个空页面，需要跳过，怎么处理？
        ///            过滤首页空页面，返回
        ///            首页格式：如果第0个是 空   //处理第1个
        ///            首页格式：如果第0个不是空  //处理第0个
        ///            正文布局

        if chapterIndex == 0 && !displayedAttributedString.isEmpty() {
            displayedAttributedString = displayedAttributedString.resetParagraphStyleForFirstChapter()
        } else if chapterIndex == 1 && displayedAttributedString.isEmpty() {
            displayedAttributedString = displayedAttributedString.resetParagraphStyleForFirstChapter()
        } else {
            displayedAttributedString = displayedAttributedString.resetParagraphStyle(alignmentJustified: true)
        }

//        // Process RTL text like Hebrew to ensure proper selection and highlighting
//        // This needs to happen after paragraph styles are set but before pagination
//        displayedAttributedString = displayedAttributedString.prepareForRTLTextSelection() as! NSMutableAttributedString

        var logId = 0
        if chapterIndex == 0 {
            // 排查封面图片不显示问题，logId != 0会记录日志
            logId = Int(arc4random_uniform(1000))
        }
        let ctPagesInOneChaptor = splitPage(attrString: displayedAttributedString, logId: logId)
        let pagesInOneChaptor = ctPagesInOneChaptor.enumerated().map { index, ctPage in Page(ctPage: ctPage, chapterIndex: chapterIndex, pageIndexInChapter: index) }

        addChapter(chapterIndex: chapterIndex, pages: pagesInOneChaptor, resource: resource, sourceHTML: html!, originalAttributedString: attributedString, displayedAttributedString: displayedAttributedString)
    }

    // 用来log
    func pagesCompleteForLog() {
        //        Log.d(String(format: "加载文件时间：%.2f,修改html时间：%.2f,解析时间：%.2f,解析最大文件时间：%.2f,分页时间：%.2f", loadFileTime,modifyHTMLTime,attributTime,attributTimeMaxOne,splitPageTime))
        loadFileTime = 0.0; modifyHTMLTime = 0.0; attributTime = 0.0; attributTimeMaxOne = 0.0; splitPageTime = 0.0
    }

    // 执行时机
    // 加载完一章处理,翻页，换字体，换字号处理。
    func dynamicRelease(chapterIndex: Int, currentChapterIndex: Int) {
        // 如果当前不在中间或者前后两章节，则清除。
        // 如果在，则不做处理。
        if (currentChapterIndex - 1) ... (currentChapterIndex + 1) ~= chapterIndex {
//            Log.d("阅读器 加载：当前章:\(currentChapterIndex),加载章:\(chapterIndex),不释放")

        } else {
//            Log.d("阅读器 加载：当前章:\(currentChapterIndex),加载章:\(chapterIndex),释放")
            chapterFileDic[chapterIndex]?.loadState = .paged
            chapterFileDic[chapterIndex]?.pages.removeAll()
        }
    }

    func dynamicRelease(currentChapterIndex: Int) {
        let rangeToKeep = (currentChapterIndex - 1) ... (currentChapterIndex + 1)
        for key in chapterFileDic.keys {
            if !rangeToKeep.contains(key),
               let dic = chapterFileDic[key],
               dic.loadState == .loaded
            {
                chapterFileDic[key]?.loadState = .paged
                chapterFileDic[key]?.pages.removeAll()
            }
        }
    }

    func CTFramesetterCreateWithAttributedStringOnMain(_ attrString: CFAttributedString) -> CTFramesetter {
        let semaphore = DispatchSemaphore(value: 0)
        var framesetter: CTFramesetter!

        DispatchQueue.main.async {
            framesetter = CTFramesetterCreateWithAttributedString(attrString)
            semaphore.signal()
        }

        semaphore.wait()
        return framesetter
    }
}

extension UIImage {
    /// 调整图片大小以适应最大尺寸，保持图片的纵横比。
    func resizedToFit(maxSize: CGSize) -> UIImage {
        let originalSize = size
        let widthRatio = maxSize.width / originalSize.width
        let heightRatio = maxSize.height / originalSize.height
        let ratio = min(widthRatio, heightRatio)

        if ratio >= 1 {
            return self
        }

        let newSize = CGSize(width: originalSize.width * ratio, height: originalSize.height * ratio)
        UIGraphicsBeginImageContextWithOptions(newSize, false, scale)
        draw(in: CGRect(origin: .zero, size: newSize))
        let resizedImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return resizedImage ?? self
    }
}
