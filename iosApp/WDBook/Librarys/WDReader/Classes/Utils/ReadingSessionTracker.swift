//
//  ReadingSessionTracker.swift
//  WDBook
//
//  Created by <PERSON> on 2025/05/28.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

// MARK: - Reading Session Data Models

struct ReadingSessionData: Codable {
    let sessionId: String
    let resourceId: String
    let startTime: Int64 // Unix timestamp in milliseconds
    var endTime: Int64? // Unix timestamp in milliseconds, nil if ongoing
    var isCompleted: Bool = false
    
    func isValidReading() -> Bool {
        guard let endTime = endTime else { return false }
        let duration = endTime - startTime
        return duration >= 300_000 // 5 minutes in milliseconds
    }
    
    func getSessionDate() -> String {
        let date = Date(timeIntervalSince1970: TimeInterval(startTime / 1000))
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd"
        formatter.timeZone = TimeZone.current
        return formatter.string(from: date)
    }
}

struct ReadingEventAttributes {
    let resourceId: String
    let startTime: Int64
    let endTime: Int64
    let sessionDate: String
    let readingDurationSeconds: Int
    
    func toDictionary() -> [String: Any] {
        return [
            "resource_id": resourceId,
            "start_time": startTime,
            "end_time": endTime,
            "session_date": sessionDate,
            "reading_duration_seconds": readingDurationSeconds
        ]
    }
}

// MARK: - Reading Session Tracker

class ReadingSessionTracker {
    static let shared = ReadingSessionTracker()
    
    private let userDefaults = UserDefaults.standard
    private let currentSessionKey = "current_reading_session"
    
    private var currentSession: ReadingSessionData?
 
    // MARK: - Session Management
    
    func startSession() {
        // End any existing session first
        if currentSession != nil {
            endSession()
        }
        
        // Get resourceId from WDReaderConfig
        let resourceId = WDReaderConfig.resourceId ?? ""
        
        // Don't start session if resourceId is empty
        guard !resourceId.isEmpty else {
            Log.d("ReadingSessionTracker: Cannot start session - resourceId is empty")
            return
        }
        
        let sessionId = UUID().uuidString
        let startTime = Int64(Date().timeIntervalSince1970 * 1000)
        
        currentSession = ReadingSessionData(
            sessionId: sessionId,
            resourceId: resourceId,
            startTime: startTime
        )
        
        // Persist current session
        saveCurrentSession()
        
        Log.d("ReadingSessionTracker: Started session \(sessionId) for resource \(resourceId)")
    }
    
    func endSession() {
        guard var session = currentSession else {
            Log.d("ReadingSessionTracker: No active session to end")
            return
        }
        
        let endTime = Int64(Date().timeIntervalSince1970 * 1000)
        session.endTime = endTime
        session.isCompleted = true
        
        // Clear current session from persistence
        clearCurrentSession()
        currentSession = nil
        
        // Report if valid reading session
        if session.isValidReading() {
            reportSession(session)
        } else {
            let duration = endTime - session.startTime
            Log.d("ReadingSessionTracker: Session \(session.sessionId) too short (\(duration/1000)s), not reporting")
        }
    }
    
    // MARK: - Abnormal Termination Handling
    
    func handleAbnormalTermination() {
        guard let sessionData = userDefaults.data(forKey: currentSessionKey) else {
            return
        }
        
        guard var session = try? JSONDecoder().decode(ReadingSessionData.self, from: sessionData) else {
            // If session data is corrupted or can't be decoded, clean it up
            Log.d("ReadingSessionTracker: Found corrupted session data, cleaning up")
            clearCurrentSession()
            return
        }
        
        Log.d("ReadingSessionTracker: Found incomplete session from previous launch")
        
        let now = Int64(Date().timeIntervalSince1970 * 1000)
        let duration = now - session.startTime
        if duration >= 300_000 { // More than 5 minutes
            session.endTime = now
            session.isCompleted = true
            reportSession(session)
        } 
        
        clearCurrentSession()
    }
    
    // MARK: - Analytics Reporting
    
    private func reportSession(_ session: ReadingSessionData) {
        guard let endTime = session.endTime else {
            Log.d("ReadingSessionTracker: Cannot report session without end time")
            return
        }
        
        let duration = endTime - session.startTime
        let durationSeconds = Int(duration / 1000)
        
        let eventAttributes = ReadingEventAttributes(
            resourceId: session.resourceId,
            startTime: session.startTime,
            endTime: endTime,
            sessionDate: session.getSessionDate(),
            readingDurationSeconds: durationSeconds
        )
        
        // Convert to array format expected by AnalysisUtils.logEvent
        let eventParams = [
            "resource_id", eventAttributes.resourceId,
            "start_time", String(eventAttributes.startTime),
            "end_time", String(eventAttributes.endTime),
            "session_date", eventAttributes.sessionDate,
            "reading_duration_seconds", String(eventAttributes.readingDurationSeconds)
        ]
        
        // Report to analytics
        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V2_READING_SESSION_COMPLETE, params: eventParams)
        
        Log.d("ReadingSessionTracker: Reported session \(session.sessionId) - Duration: \(durationSeconds)s")
    }
    
    // MARK: - Persistence
    
    private func saveCurrentSession() {
        guard let session = currentSession,
              let data = try? JSONEncoder().encode(session) else {
            return
        }
        userDefaults.set(data, forKey: currentSessionKey)
    }
    
    private func clearCurrentSession() {
        userDefaults.removeObject(forKey: currentSessionKey)
    }
}
