//
//  Paginator+Reader.swift
//  WDReader
//
//  Created by <PERSON> on 2021/3/19.
//  Copyright © 2021 <PERSON>. All rights reserved.
//

import Foundation

// MARK：页码相关方法
extension Paginator {
    static func clear() {
        patinators = [:]
        currentPationatorKey = ""
        current = nil
    }

    func clear() {
        Paginator.clear()
        clearStoreData()
        indexPathRecords = [IndexPath]()
        currentIndexPath = IndexPath(item: 0, section: 0)
        clearChapterData()
    }

    func clearChapterData() {
        chapterFileDic = [Int: Chapter]()
        isAllLoad = false
        pageCount = nil
    }

    func clearStoreData() {
//        pages = [Page]()
        resourcePageNumberDic = [String: Int]()
    }

    // 解析中添加每章的数据
    func addChapter(chapterIndex: Int, pages: [Page], resource: FRResource, sourceHTML: String, originalAttributedString: NSAttributedString, displayedAttributedString: NSAttributedString) {
//        queuePages.sync {
        defer { queuePagesLock.unlock() }
        queuePagesLock.lock()

        if chapterFileDic[chapterIndex] == nil {
            chapterFileDic[chapterIndex] = Chapter(pages: pages, resource: resource, sourceHTML: sourceHTML, originalAttributedString: originalAttributedString, displayedAttributedString: displayedAttributedString)
        } else {
            chapterFileDic[chapterIndex]?.copyFrom(pages: pages, resource: resource, sourceHTML: sourceHTML, originalAttributedString: originalAttributedString, displayedAttributedString: displayedAttributedString)
        }
//        }
    }

    /// 解析完epub之后，计算资源。
    func computePageWhenComplete() {
        Log.d("阅读器 加载 所有页面计算完毕")
        clearStoreData()

        // 是否已经设置第一页左对齐。
        //        var doneFirstChapterParagraphStyle = false
//        var ps = [Page]()
        var totalPageCount = 0
        var pageNum = 1
        for i in 0 ..< chapterFileDic.count {
            guard let chapter = chapterFileDic[i] else {
                continue
            }

            ////            如果第0个是 空   //处理第1个
            ////            如果第0个不是空  //处理第0个
            //
            //            //处理第0个
            //            if i == 0 && !chapter.originalAttributedString.isEmpty(){
            //                chapterFileDic[0]!.displayedAttributedString = chapter.displayedAttributedString.resetParagraphStyleForFirstChapter()
            //            }
            //            //处理第1个
            //            if i == 1 && chapterFileDic[0]!.displayedAttributedString.isEmpty(){
            //                chapterFileDic[i]!.displayedAttributedString = chapterFileDic[i]!.displayedAttributedString.resetParagraphStyleForFirstChapter()
            //            }

            // 跳过空页面
            if WDReaderConfig.skipEmptyTextHTML && chapter.originalAttributedString.isEmpty() {
                continue
            }
            //            if !doneFirstChapterParagraphStyle{
            //                chapterFileDic[i]!.displayedAttributedString = chapterFileDic[i]!.displayedAttributedString.resetParagraphStyleForFirstChapter()
            //                doneFirstChapterParagraphStyle = true
            //            }else{
            //                chapterFileDic[i]!.displayedAttributedString = chapterFileDic[i]!.displayedAttributedString.resetParagraphStyle(alignmentJustified: true)
            //            }

            // 保存页码
            let resource = chapter.resource

            chapterFileDic[i]?.firstPageNumInWholeBook = pageNum
            resourcePageNumberDic[resource.href] = pageNum

            for (key, value) in chapter.resourcePageIndexDic {
                resourcePageNumberDic[key] = pageNum + value
            }

//            for pageIndex in 0 ..< chapter.pages.count{
//                let p = chapter.pages[pageIndex]
//
//                //获取页中的所有fragments。
//                let fragmentIds = p.ctPage.ctFrame.fragmentIds()
//                //resource.id + # + fragment 做key保存pagenum。(如果保存过，不再保存;只保存第一个）
//                for fragmentId in fragmentIds.keys {
//                    let resourceHrefAndFragmentId = "\(resource.href!)#\(fragmentId)"
//                    if resourcePageNumberDic[resourceHrefAndFragmentId] == nil {
//                        resourcePageNumberDic[resourceHrefAndFragmentId] = pageNum + pageIndex
//                    }
//                }
//            }

            totalPageCount += chapter.pageCount
            pageNum = totalPageCount + 1

//            let pagesInOnechapter = chapter.pages
//            ps.append(contentsOf: pagesInOnechapter)
        }
//        pages = ps
    }

    /// MARK: Get 函数
    /// MARK: IndexPath相关get函数
    func getChapterIndex(href: String) -> Int {
        let references = WDReaderCenter.shared.fbBook.spine.spineReferences
        for i in 0 ..< references.count {
            let item = references[i]
            let resource = item.resource
            if resource.href == href {
                return i
            }
        }
        return 0
    }

    func getChapterHref(chapterIndex: Int) -> String {
        let references = WDReaderCenter.shared.fbBook.spine.spineReferences
        if (0 ..< references.count).contains(chapterIndex) {
            let item = references[chapterIndex]
            let resource = item.resource
            return resource.href
        }
        return ""
    }

    func getIndexPath(chapterIndex: Int, contentOffset: Int) -> IndexPath? {
        guard let chapter = chapterFileDic[chapterIndex] else {
            return nil
        }
        if WDReaderConfig.skipEmptyTextHTML && chapter.originalAttributedString.isEmpty() {
            return nil
        }

        for pageIndex in 0 ..< chapter.pages.count {
            let p = chapter.pages[pageIndex]
            if p.ctPage.range.contains(contentOffset) {
                return IndexPath(item: pageIndex, section: chapterIndex)
            }
        }

        return nil
    }

    /// 获取上一次加载的页面
    var lastRecordIndexPath: IndexPath? {
        if indexPathRecords.count > 1 {
            return indexPathRecords.first
        }
        return nil
    }

    func getIndexPath(pageNum: Int) -> IndexPath? {
        guard pageNum >= 0 else {
            return nil
        }
        var cursorPageNum = 0
        for i in 0 ..< chapterFileDic.count {
            guard let chapter = chapterFileDic[i] else {
                continue
            }
            // 跳过空页面
            if WDReaderConfig.skipEmptyTextHTML && chapter.originalAttributedString.isEmpty() {
                continue
            }
            if chapter.pageCount > 0 {
                if pageNum > cursorPageNum + chapter.pageCount {
                    cursorPageNum += chapter.pageCount
                    continue
                } else {
                    let index = pageNum - cursorPageNum - 1
                    return IndexPath(item: index, section: i)
                }
            }
        }
        return nil
    }

    // MARK: 页码相关

    func hasPagedForChapter(chapterIndex: Int) -> Bool {
        guard let chapter = Paginator.current.chapterFileDic[chapterIndex] else {
            return false
        }
        return chapter.loadState == .paged
    }

    var currentPageNum: Int {
        getPageNum(indexPath: currentIndexPath)
    }

    /// 获取页码
    func getPageNum(indexPath: IndexPath) -> Int {
        Log.d("翻页测试：性能测试：getPageNum")
        let pnum = getCurrentPageNum(indexPath: indexPath)
        Log.d("翻页测试：性能测试：getPageNum2")
        return pnum

        var pageNum = 0
        Log.d("翻页测试：性能测试：getPageNum3")
        let count = chapterFileDic.count
        for i in 0 ..< count {
            guard let chapter = chapterFileDic[i] else {
                continue
            }
            if WDReaderConfig.skipEmptyTextHTML && chapter.originalAttributedString.isEmpty() {
                continue
            }

            if i < indexPath.section {
                pageNum += chapter.pageCount
            } else if i == indexPath.section {
                pageNum += indexPath.item + 1
                break
            }
        }
        Log.d("翻页测试：性能测试：getPageNum4")
        if pageNum == 0 {
            return 1
        } else {
            return pageNum
        }
    }

    /// 获取页码
    func getPageNum(hrefAndFragmentId: String) -> Int {
        resourcePageNumberDic[hrefAndFragmentId] ?? 1
    }

    /// 获取页码
    func getCurrentPageNum(indexPath: IndexPath) -> Int {
        if let chapter = chapterFileDic.filter({ $0.key == indexPath.section }).first?.value {
            return chapter.firstPageNumInWholeBook + indexPath.item
        } else {
            return 1
        }
    }

    /// 获取总页码
    func getPagesCount() -> Int {
        if let c = pageCount {
            return c
        }

        Log.d("翻页测试：getPagesCount 0：")
        pageCount = chapterFileDic.values.reduce(0) { result, chapter -> Int in
            if WDReaderConfig.skipEmptyTextHTML && chapter.originalAttributedString.isEmpty() {
                return result
            }
            return result + chapter.pageCount
        }
        Log.d("翻页测试：getPagesCount 1：")
        return pageCount ?? 0
    }

    /// 获取当前页码和总页码，如果有合法的，返回
    func getCurrentPageAndTotalNum(indexPath: IndexPath) -> String {
        // 如果当前页算的是1，但是真实不是1，那么返回空字符串。
        if let chapter = chapterFileDic.filter({ $0.key == indexPath.section }).first?.value {
            if chapter.hasSettedFirstPageNumInWholeBook {
                return "\(chapter.firstPageNumInWholeBook + indexPath.item)/\(getPagesCount())"
            } else {
                return ""
            }
        } else {
            return "1/\(getPagesCount())"
        }
    }

    /// 获取页码
    func getPageNum(chapterHref: String, contentOffset: Int) -> Int? {
        let chapterIndex = getChapterIndex(href: chapterHref)
        let indexPath = getIndexPath(chapterIndex: chapterIndex, contentOffset: contentOffset)
        guard let ip = indexPath else {
            return nil
        }
        return getPageNum(indexPath: ip)
    }

    // MARK: 章相关get函数

    func getChapterFilesNotEmptyCount() -> Int {
        chapterFileDic.values.filter { chapter -> Bool in
            if WDReaderConfig.skipEmptyTextHTML && chapter.originalAttributedString.isEmpty() {
                return false
            }
            return true
        }.count
    }

    /// 获取当前章
    var currentChapter: Chapter? {
        chapterFileDic[currentIndexPath.section]
    }

    /*
     /// 获取章名字。只支持一级目录。
     /// - Parameter pageNum: <#pageNum description#>
     /// - Returns: <#description#>
     @available(*, deprecated, renamed: "getSubLevelChapterName", message: "")
     func getChapterName(pageNum:Int) -> String?{
         if let chapter = getChapter(pageNum: pageNum),
            let tocRes = WDReaderCenter.shared.getTOCResource(resource: chapter.resource){
             return tocRes.title
         }
         return nil
     }
     @available(*, deprecated, renamed: "getSubLevelChapterName", message: "")
     func getChapterName(indexPath:IndexPath) -> String?{
         if let chapter = getChapter(chapterIndex: indexPath.section),
            let tocRes = WDReaderCenter.shared.getTOCResource(resource: chapter.resource){
             return tocRes.title
         }
         return nil
     }
      */

    /// 获取章名字。同页面只返回最子级的目录。
    /// - Parameter pageNum: <#pageNum description#>
    /// - Returns: <#description#>
    func getSubLevelChapterName(pageNum: Int) -> String? {
        var searchTitle: String?
        let allTocs = WDReaderCenter.shared.fbBook.getFlatAllTableOfContents()
        for i in 0 ..< allTocs.count {
            let item = allTocs[i]

            if let resource = item.resource,
               let searchPNum = resourcePageNumberDic[resource.href]
            {
                if searchPNum <= pageNum { // 选择最接近pageNum并<=的title。
                    searchTitle = item.title
                } else {
                    break
                }
            }
        }

        return searchTitle
    }

    /// 获取章名字。同页面只返回最子级的目录。
    /// - Parameter pageNum: <#pageNum description#>
    /// - Returns: <#description#>
    func getSubLevelChapterName(indexPath: IndexPath) -> String? {
        let chapter = getChapter(chapterIndex: indexPath.section)
        var searchTitle: String?
        let allTocs = WDReaderCenter.shared.fbBook.getFlatAllTableOfContents()
        for i in 0 ..< allTocs.count {
            let item = allTocs[i]

            if let resource = item.resource,
               let searchPageIndex = chapter?.resourcePageIndexDic[resource.href]
            {
                if searchPageIndex <= indexPath.item { // 选择最接近searchPageIndex并<=的title。
                    searchTitle = item.title
                } else {
                    break
                }
            }
        }

        return searchTitle
    }

    /*
     /// 获取属于章节的资源。按照资源排序，非目录排序。
     /// - Parameter pageNum: 当前页码
     /// - Returns: 当前页码属于章的资源
     func getChapter(pageNum:Int) -> chapter? {
         let page = pages[pageNum - 1]
         let i = page.chapterIndex
         guard i >= 0,i < chapterFileDic.count else {
             return nil
         }
         let chapter = chapterFileDic[i]
         return chapter
     }
      */

    func getChapter(chapterIndex: Int) -> Chapter? {
        return chapterFileDic[chapterIndex]
    }

    func getChapter(href: String) -> Chapter? {
        chapterFileDic.values.filter { $0.resource.href == href }.first
    }

    /*
     func getPreChapter(pageNum:Int) -> chapter?{
         let page = pages[pageNum - 1]
         let i = page.chapterIndex - 1
         guard i >= 0,i < chapterFileDic.count else {
             return nil
         }
         let chapter = chapterFileDic[i]
         return chapter
     }

     func getNextChapter(pageNum:Int) -> chapter?{
         let page = pages[pageNum - 1]
         let i = page.chapterIndex + 1
         guard i >= 0,i < chapterFileDic.count else {
             return nil
         }
         let chapter = chapterFileDic[i]
         return chapter
     }
      */

    /// 获取章节的起始页码。（只含一级目录）按照目录排序，非资源排序。
    /// 算法：通过当前页，找到当前章，找到前一章和后一章，获取起始页码。
    /// - Parameter pageNum: 当前页码
    /// - Returns: (当前页码所在的章的前一章的起始页码，当前页码所在的章的后一章的起始页码)
    @available(*, deprecated, message: "newMethod")
    func getTOCStartPageNumForChapter(pageNum: Int) -> (Int?, Int?) {
        var preChapterStartPageNum: Int?
        var tempPageNum: Int? // 中间保存值
        var nextChapterStartPageNum: Int?

        for item in WDReaderCenter.shared.fbBook.tableOfContents {
            if let resource = item.resource,
               let pNum = resourcePageNumberDic[resource.href]
            {
                if pNum <= pageNum {
                    if tempPageNum != nil {
                        preChapterStartPageNum = tempPageNum
                    }
                    tempPageNum = pNum
                } else {
                    nextChapterStartPageNum = pNum
                    break
                }
            }
        }
        return (preChapterStartPageNum, nextChapterStartPageNum)
    }

    /// 获取章节的起始页码。（同页码情况下，只包含最子级的目录）按照目录排序，非资源排序。
    /// 算法：通过当前页，找到当前章，找到前一章和后一章，获取起始页码。
    /// - Parameter pageNum: 当前页码
    /// - Returns: (当前页码所在的章的前一章的起始页码，当前页码所在的章的后一章的起始页码)
    @available(*, deprecated, message: "newMethod")
    func getTOCStartPageNumForSubLevelChapter(pageNum: Int) -> (Int?, Int?) {
        var searchIndex = 0 // 符合条件的最后一个目录。
        var searchPageNum = 0

        var preChapterStartPageNum: Int?
        var nextChapterStartPageNum: Int?

        let allTocs = WDReaderCenter.shared.fbBook.getFlatAllTableOfContents()
        for i in 0 ..< allTocs.count {
            let item = allTocs[i]

            if let resource = item.resource,
               let searchPNum = resourcePageNumberDic[resource.href]
            {
                if searchPNum <= pageNum {
                    searchIndex = i
                    searchPageNum = searchPNum
                    //                    preChapterStartPageNum = searchPageNum //防止空
                    //                    nextChapterStartPageNum = searchPageNum
                } else {
                    break
                }
            }
        }

        var preIndex = searchIndex - 1
        while preIndex >= 0 {
            let preItem = allTocs[preIndex]
            if let resource = preItem.resource,
               let prePageNum = resourcePageNumberDic[resource.href]
            {
                if prePageNum == searchPageNum {
                    preIndex -= 1
                    continue
                } else {
                    preChapterStartPageNum = prePageNum
                    break
                }
            }
        }

        var nextIndex = searchIndex + 1
        while nextIndex < allTocs.count {
            let nextItem = allTocs[nextIndex]
            if let resource = nextItem.resource,
               let nextPageNum = resourcePageNumberDic[resource.href]
            {
                if nextPageNum == searchPageNum {
                    nextIndex += 1
                    continue
                } else {
                    nextChapterStartPageNum = nextPageNum
                    break
                }
            }
        }

        return (preChapterStartPageNum, nextChapterStartPageNum)
    }

    /// 获取前一章和后一章的(索引，href,title)。//TODO: 加载完毕所有章后，算法崩溃！！
    func getTOCStartIndexPathForSubLevelChapter(indexPath: IndexPath) -> ((Int, String, String)?, (Int, String, String)?) {
        let currentChapter = Paginator.current.currentChapter!

        var searchTocIndex: Int!
        var preChapterStartPageIndex: Int?
        var nextChapterStartPageIndex: Int?

        // 条件A: 同一href并且href和fragmengid的index相同

        // 不满足过A:继续找下一个，直到满足过A：继续找下一个满足A的，下一个不满足则立刻跳出。
        var foundMatch = false
        let allTocs = WDReaderCenter.shared.fbBook.getFlatAllTableOfContents()
        for i in 0 ..< allTocs.count {
            let item = allTocs[i]

            if foundMatch {
                if let resource = item.resource,
                   resource == currentChapter.resource,
                   let pageIndex = currentChapter.resourcePageIndexDic[item.resourceHrefAndFragmentId()],
                   pageIndex <= indexPath.item
                {
                    foundMatch = true
                    searchTocIndex = i
                } else {
                    break
                }
            } else {
                searchTocIndex = i

                if let resource = item.resource,
                   resource == currentChapter.resource,
                   let pageIndex = currentChapter.resourcePageIndexDic[item.resourceHrefAndFragmentId()],
                   pageIndex <= indexPath.item
                {
                    foundMatch = true
                }
            }
        }

        // 从中间位置向前找，满足条件A，则选前一个。不满足则返回
        var anchorIndex = searchTocIndex!
        var preChapterIndex = searchTocIndex - 1
        while preChapterIndex >= 0 {
            let anchorItem = allTocs[anchorIndex]
            let preItem = allTocs[preChapterIndex]
            // 如果满足B，则向前
            if let preResource = preItem.resource,
               let anchorResource = anchorItem.resource,
               preResource == anchorResource,
               let preChapter = Paginator.current.getChapter(href: preResource.href),
               let anchorChapter = Paginator.current.getChapter(href: anchorResource.href),
               let prePageIndex = preChapter.resourcePageIndexDic[preItem.resourceHrefAndFragmentId()],
               let anchorPageIndex = anchorChapter.resourcePageIndexDic[anchorItem.resourceHrefAndFragmentId()],
               prePageIndex == anchorPageIndex
            {
                anchorIndex -= 1
                preChapterIndex -= 1
                continue
                // 否则，break
            } else {
                preChapterStartPageIndex = 0
                break
            }
        }

        var preData: (Int, String, String)?
        if preChapterStartPageIndex != nil && preChapterIndex >= 0 {
            let preItem = allTocs[preChapterIndex]
            let preFileChapterIndex = WDReaderCenter.shared.getFileChapterIndex(preItem)
            preData = (preFileChapterIndex, preItem.resourceHrefAndFragmentId(), preItem.title)
        }

        // 从中间位置向后找，满足条件A，则选后一个。不满足则返回
        anchorIndex = searchTocIndex!
        var nextChapterIndex = searchTocIndex + 1
        while nextChapterIndex < allTocs.count {
            let anchorItem = allTocs[anchorIndex]
            let nextItem = allTocs[nextChapterIndex]
            // 如果满足B，则向前
            if let nextResource = nextItem.resource,
               let anchorResource = anchorItem.resource,
               nextResource == anchorResource,
               let nextChapter = Paginator.current.getChapter(href: nextResource.href),
               let anchorChapter = Paginator.current.getChapter(href: anchorResource.href),
               let nextPageIndex = nextChapter.resourcePageIndexDic[nextItem.resourceHrefAndFragmentId()],
               let anchorPageIndex = anchorChapter.resourcePageIndexDic[anchorItem.resourceHrefAndFragmentId()],
               nextPageIndex == anchorPageIndex
            {
                anchorIndex += 1
                nextChapterIndex += 1
                continue
                // 否则，break
            } else {
                nextChapterStartPageIndex = 0
                break
            }
        }

        var nextData: (Int, String, String)?
        if nextChapterStartPageIndex != nil && nextChapterIndex >= 0 {
            let nextItem = allTocs[nextChapterIndex]
            let nextFileChapterIndex = WDReaderCenter.shared.getFileChapterIndex(nextItem)
            nextData = (nextFileChapterIndex, nextItem.resourceHrefAndFragmentId(), nextItem.title)
        }

        return (preData, nextData)
    }

    // MARK: 注脚

    /*
     func getFootNoteOnCurrentChapter(URL: URL, currentPageNumber:Int) -> FootNote? {
         if let attributedString = getChapter(pageNum: currentPageNumber)?.originalAttributedString{
             if let footNote = attributedString.getEpubFootNote(id: URL.absoluteString),
                let chapter = getChapter(pageNum: currentPageNumber){
                 //计算注脚页码。
                 var linkPageNum:Int?
                 for p in chapter.pages{
                     if p.ctPage.range.contains(footNote.location!) {
                         linkPageNum = chapter.firstPageNumInWholeBook + p.pageIndexInChapter
                         break
                     }
                 }

                 if let num = linkPageNum{
                     print("脚注location:\(footNote.location),位置：\(footNote.numInChapter),内容:\(footNote.content)")

                     return footNote
                     //在WDReaderVC的话 滚动到注脚位置。
                     //                        currentPageNumber = num
                     //                        changePage(page: currentPageNumber,animated: true)
                 }

             }
         }
         return nil
     }*/

    func pageCount(href: String, range: NSRange) -> Int {
        var count = 0
        if let chapter = getChapter(href: href) {
            for page in chapter.pages {
                if let intersectionRange = page.ctPage.range.intersection(range), intersectionRange.length > 0 {
                    count += 1
                }
            }
        }
        return count
    }
}
