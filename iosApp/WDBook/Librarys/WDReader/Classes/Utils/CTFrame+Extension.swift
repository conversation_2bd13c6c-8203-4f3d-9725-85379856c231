//
//  CTFrame+Extension.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/22.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import CoreText
import DTCoreText
import Foundation

struct CTTapedInfo {
    let url: URL
    let range: NSRange
    let location: CGPoint
}

extension CTFrame {
    // MARK: 实现获取点击的位置文字

    func isTapLink(point: CGPoint) -> CTTapedInfo? {
        var location = point
        let lineArr = CTFrameGetLines(self) as NSArray
        let ctLinesArray = lineArr as Array
        var originsArray = [CGPoint].init(repeating: CGPoint.zero, count: ctLinesArray.count)
        CTFrameGetLineOrigins(self, CFRangeMake(0, 0), &originsArray) // 每一行的起始坐标。即(x=0 ,y递增)

        // 遍历ctline
//        for i in 0 ..< CFArrayGetCount(lineArr) {
        for (i, l) in lineArr.enumerated() {
            let line = l as! CTLine
            let origin = originsArray[i]

            // 获取整个CTFrame的大小
            let path = CTFrameGetPath(self)
            let rect = path.boundingBox

            // 坐标转换，把每行的原点坐标转换为UIKit的坐标体系
            // y代表 ctline的maxY
            let y = rect.origin.y + rect.size.height - origin.y

            // 判断点击的位置处于那一行范围内
            if location.y <= y && location.x >= origin.x {
                // 修改偏移量，找到对应的位置
                location.x -= origin.x
                let index = CTLineGetStringIndexForPosition(line, location)

                // 获取点击的位置文字
                Log.d("——————————————")
                Log.d("Clicked the character index(location) of current attributedtext = \(index), on location(point) of current cframe: \(location)")

                let runs = CTLineGetGlyphRuns(line)
                let ctRuns = runs as Array
                for j in 0 ..< CFArrayGetCount(runs) {
                    let run = ctRuns[j] as! CTRun

                    let range = CTRunGetStringRange(run)
//                    print("Range of ctrun: \(range)")

                    if let attributes = CTRunGetAttributes(run) as? [String: Any],
                       let link = attributes[NSAttributedString.Key.link.rawValue] as? URL,
                       index >= range.location && index <= range.location + range.length
                    {
                        Log.d("点击的ctrun: \(run),连接地址：\(link)")
                        return CTTapedInfo(url: link, range: NSRange(location: index, length: 1), location: location)
                    }
                }
                break
            }
        }

        return nil
    }

    // TODO: 暂未使用
    func contains(fragmentId _: String) -> Bool {
        var location = CGPoint.zero
        let lineArr = CTFrameGetLines(self) as NSArray
        let ctLinesArray = lineArr as Array
        var originsArray = [CGPoint].init(repeating: CGPoint.zero, count: ctLinesArray.count)
        CTFrameGetLineOrigins(self, CFRangeMake(0, 0), &originsArray) // 每一行的起始坐标。即(x=0 ,y递增)

        // 遍历ctline
//        for i in 0 ..< CFArrayGetCount(lineArr) {
        for (i, l) in lineArr.enumerated() {
            let line = l as! CTLine

            let runs = CTLineGetGlyphRuns(line)
            let ctRuns = runs as Array
            for j in 0 ..< CFArrayGetCount(runs) {
                let run = ctRuns[j] as! CTRun

                let range = CTRunGetStringRange(run)
//                print("Range of ctrun: \(range)")

                if let attributes = CTRunGetAttributes(run) as? [String: Any] // ,
//                   let idAttribute = attributes[IDAttribute] as? String,
//                   idAttribute == fragmentId
                {
                    return true
                }
            }
        }

        return false
    }

    func fragmentIds() -> [String: String] {
        var location = CGPoint.zero
        let lineArr = CTFrameGetLines(self) as NSArray
        let ctLinesArray = lineArr as Array
        var originsArray = [CGPoint].init(repeating: CGPoint.zero, count: ctLinesArray.count)
        CTFrameGetLineOrigins(self, CFRangeMake(0, 0), &originsArray) // 每一行的起始坐标。即(x=0 ,y递增)

        var ids = [String: String]()
        // 遍历ctline
//        for i in 0 ..< CFArrayGetCount(lineArr) {
        for (i, l) in lineArr.enumerated() {
            let line = l as! CTLine

            let runs = CTLineGetGlyphRuns(line)
            let ctRuns = runs as Array
            for j in 0 ..< CFArrayGetCount(runs) {
                let run = ctRuns[j] as! CTRun

                let range = CTRunGetStringRange(run)
//                print("Range of ctrun: \(range)")

                if let attributes = CTRunGetAttributes(run) as? [String: Any],
                   let idAttribute = attributes[TagIDAttribute] as? String
//                   idAttribute == fragmentId
                {
                    ids[idAttribute] = idAttribute
                }
            }
        }

        return ids
    }

    // 去最前面的一段文字
//    func summary() -> String{
//
//        var location = CGPoint.zero
//        let lineArr = CTFrameGetLines(self)  as NSArray
//        let ctLinesArray = lineArr as Array
//        var originsArray = [CGPoint].init(repeating: CGPoint.zero, count: ctLinesArray.count)
//        CTFrameGetLineOrigins(self, CFRangeMake(0, 0),&originsArray) //每一行的起始坐标。即(x=0 ,y递增)
//
//        var str = ""
//        //遍历ctline
    ////        for i in 0 ..< CFArrayGetCount(lineArr) {
//        for (i, l) in lineArr.enumerated() {
//            let line = l as! CTLine
//
//            let runs = CTLineGetGlyphRuns(line)
//            let ctRuns = runs as Array
//            for j in 0 ..< CFArrayGetCount(runs){
//                let run = ctRuns[j] as! CTRun
//
//                let range = CTRunGetStringRange(run)
//                print("Range of ctrun: \(range)")
//                CTRun
//                if let attributes = CTRunGetAttributes(run) as? [String: Any]
//                   ,let idAttribute = attributes[TagIDAttribute] as? String
    ////                   idAttribute == fragmentId
//                {
//
//
//                }
//            }
//
//        }
//
//        return str
//    }
}

// MARK: 选中

extension CTFrame {
    /// 获取点击位置的1个字符的range
    func getTouchLocationRange(point: CGPoint, viewFrame: CGRect, isAutoAdjust: Bool = true) -> (NSRange, Bool) {
        let ctFrame = self
        var resultRange = NSRange(location: 0, length: 0)

        var lines = CTFrameGetLines(self) as Array
        var origins = [CGPoint](repeating: CGPoint.zero, count: lines.count)
        CTFrameGetLineOrigins(ctFrame, CFRange(location: 0, length: 0), &origins)

        for i in 0 ..< lines.count {
            let line = lines[i] as! CTLine
            let origin = origins[i]

            var ascent: CGFloat = 0
            var descent: CGFloat = 0

            CTLineGetTypographicBounds(line, &ascent, &descent, nil)

            let lineRect = CGRect(x: origin.x, y: viewFrame.height - origin.y - (ascent + descent), width: CTLineGetOffsetForStringIndex(line, 100_000, nil), height: ascent + descent)

            var isRightOrBottomEdge = false
            var pointInLineRect: CGPoint = point
            if i == 0 && point.y < lineRect.minY {
                Log.d("CTFrame ---- 上边沿，  point:\(point)  ---  lineRect:\(lineRect)")
                if !isAutoAdjust {
                    return (NSRange(location: 0, length: 0), false)
                }
                pointInLineRect = CGPoint(x: lineRect.origin.x + 1, y: lineRect.minY + 1)
            } else if i == lines.count - 1 && point.y > lineRect.maxY {
                Log.d("CTFrame ---- 下边沿，  point:\(point)  ---  lineRect:\(lineRect)")
                if !isAutoAdjust {
                    return (NSRange(location: 0, length: 0), false)
                }
                pointInLineRect = CGPoint(x: lineRect.maxX - 1, y: lineRect.maxY - 1)
                isRightOrBottomEdge = true
            } else if point.y >= lineRect.minY && point.y <= lineRect.maxY {
                if point.x < lineRect.minX {
                    Log.d("CTFrame ---- 在第:\(i)行,左边沿，  point:\(point)  ---  lineRect:\(lineRect)")
                    if !isAutoAdjust {
                        return (NSRange(location: 0, length: 0), false)
                    }
                    pointInLineRect.x = lineRect.origin.x + 1
                }

                if point.x > lineRect.maxX {
                    Log.d("CTFrame ---- 在第:\(i)行,右边沿，  point:\(point)  ---  lineRect:\(lineRect)")
                    if !isAutoAdjust {
                        return (NSRange(location: 0, length: 0), false)
                    }
                    pointInLineRect.x = lineRect.maxX - 1
                    isRightOrBottomEdge = true
                }
            }

            if lineRect.contains(pointInLineRect) {
                Log.d("CTFrame ---- 在第:\(i)行内部，  point:\(pointInLineRect)  ---  lineRect:\(lineRect)")

                let lineRange = CTLineGetStringRange(line)

                // Check if line might contain RTL text
                let hasRTLText = checkLineForRTLText(line)
                let runs = CTLineGetGlyphRuns(line) as Array

                // If we have RTL text, we need to find the exact run first
                if hasRTLText {
                    // Find the run containing our touch point
                    for j in 0 ..< runs.count {
                        let run = runs[j] as! CTRun
                        let runRange = CTRunGetStringRange(run)

                        // Get the bounds of this run
                        var runAscent: CGFloat = 0
                        var runDescent: CGFloat = 0
                        var runLeading: CGFloat = 0

                        let runWidth = CTRunGetTypographicBounds(run, CFRange(location: 0, length: 0), &runAscent, &runDescent, &runLeading)

                        // Calculate x position
                        var runPositions = [CGPoint](repeating: .zero, count: 1)
                        CTRunGetPositions(run, CFRange(location: 0, length: 1), &runPositions)
                        let xPosition = origin.x + runPositions[0].x

                        let runRect = CGRect(
                            x: xPosition,
                            y: viewFrame.height - origin.y - (ascent + descent),
                            width: CGFloat(runWidth),
                            height: ascent + descent
                        )

                        if runRect.contains(pointInLineRect) {
                            // Check if this run is RTL text
                            if let attributes = CTRunGetAttributes(run) as? [String: Any],
                               (attributes[NSAttributedString.Key.paragraphStyle.rawValue] as? NSParagraphStyle)?.baseWritingDirection == .rightToLeft ||
                               attributes[NSAttributedString.Key.writingDirection.rawValue] != nil
                            {
                                // For RTL, calculate position within the run
                                let relativeX = pointInLineRect.x - xPosition
                                let fraction = relativeX / CGFloat(runWidth)

                                // For RTL, we need to invert the index calculation (right-most = lowest index)
                                let runGlyphCount = CTRunGetGlyphCount(run)
                                let glyphIndex = min(max(Int(Double(runGlyphCount) * (1.0 - Double(fraction))), 0), runGlyphCount - 1)

                                // Map glyph index to string index
                                var stringIndices = [CFIndex](repeating: 0, count: 1)
                                CTRunGetStringIndices(run, CFRange(location: glyphIndex, length: 1), &stringIndices)
                                let stringIndex = stringIndices[0]

                                resultRange = NSRange(location: stringIndex, length: 1)
                                Log.d("CTFrame ---- RTL 选中范围：\(resultRange)")
                                return (resultRange, isRightOrBottomEdge)
                            }
                        }
                    }
                }

                // Standard LTR handling or fallback
                for j in 0 ..< lineRange.length {
                    let index = lineRange.location + j

                    var offsetX = CTLineGetOffsetForStringIndex(line, index, nil)
                    var offsetX2 = CTLineGetOffsetForStringIndex(line, index + 1, nil)

                    offsetX += origin.x
                    offsetX2 += origin.x

                    // Normal flow for LTR text
                    for k in 0 ..< runs.count {
                        let run = runs[k] as! CTRun
                        let runRange = CTRunGetStringRange(run)

                        if runRange.location <= index && index <= (runRange.location + runRange.length - 1) {
                            // 说明在当前的run中
                            var ascent: CGFloat = 0
                            var descent: CGFloat = 0

                            CTRunGetTypographicBounds(run, CFRange(location: 0, length: 0), &ascent, &descent, nil)

                            // Make sure the width is at least 1 point to avoid zero width frames
                            let width = max((offsetX2 - offsetX) * 1, 1.0)
                            let frame = CGRect(x: offsetX, y: viewFrame.height - origin.y - (ascent + descent), width: width, height: ascent + descent)

                            if frame.contains(pointInLineRect) {
                                resultRange = NSRange(location: index, length: 1)

                                Log.d("CTFrame ---- 选中范围：\(resultRange)")
                                return (resultRange, isRightOrBottomEdge)
                            }
                        }
                    }
                }
            }
        }
        return (resultRange, false)
    }

    /// 获取range所占用的rects
    func getRangeRects(range: NSRange) -> [CGRect] {
        let ctFrame = self
        var rects = [CGRect]()
        guard range.location != NSNotFound else { return rects }

        let lines = CTFrameGetLines(ctFrame) as Array
        var origins = [CGPoint](repeating: CGPoint.zero, count: lines.count)
        CTFrameGetLineOrigins(ctFrame, CFRange(location: 0, length: 0), &origins)

        for i in 0 ..< lines.count {
            let line = lines[i] as! CTLine
            let origin = origins[i]
            let lineCFRange = CTLineGetStringRange(line)

            if lineCFRange.location != NSNotFound {
                let lineRange = NSRange(location: lineCFRange.location, length: lineCFRange.length)

                if lineRange.location + lineRange.length > range.location && lineRange.location < (range.location + range.length) {
                    var ascent: CGFloat = 0
                    var descent: CGFloat = 0
                    var startX: CGFloat = 0

                    var contentRange = NSRange(location: range.location, length: 0)
                    let end = min(lineRange.location + lineRange.length, range.location + range.length)
                    contentRange.length = end - contentRange.location

                    CTLineGetTypographicBounds(line, &ascent, &descent, nil)

                    let y = origin.y - descent

                    // Check if this line contains RTL text
                    let hasRTLText = checkLineForRTLText(line)

                    // Get startX
                    startX = CTLineGetOffsetForStringIndex(line, contentRange.location, nil)

                    // Get endX, properly accounting for RTL direction if needed
                    var endX = CTLineGetOffsetForStringIndex(line, contentRange.location + contentRange.length, nil)

                    // For RTL text, if the width is too small, manually check all glyph positions
                    if hasRTLText && (endX - startX <= 1.0 || endX < startX) {
                        // Find the proper rect through CTRun analysis
                        let rect = getRTLRangeRect(in: line, origin: origin, y: y, range: contentRange, ascent: ascent, descent: descent)
                        if !rect.isEmpty {
                            rects.append(rect)
                            continue
                        }
                    }

                    // Normal LTR or fallback case
                    let rect = CGRect(x: origin.x + startX, y: y, width: max(endX - startX, 1.0), height: ascent + descent)
                    Log.d("The rect is \(rect)")
                    rects.append(rect)
                }
            }
        }
        Log.d("CTFrame ---- 选中范围：\(rects)")
        return rects
    }

    /// Helper method to check if a CTLine contains RTL text by examining its CTRuns.
    private func checkLineForRTLText(_ line: CTLine) -> Bool {
        guard let runs = CTLineGetGlyphRuns(line) as? [CTRun] else {
            // If there are no runs, it cannot contain RTL text runs.
            return false
        }

        // Check the status of each run.
        for run in runs {
            let status = CTRunGetStatus(run)
            // Check if the RightToLeft flag is set in the run's status.
            // kCTRunStatusRightToLeft indicates the run direction is RTL.
            if status.contains(.rightToLeft) {
                // Found at least one RTL run, so the line contains RTL text.
                return true
            }
        }

        // No RTL runs were found after checking all runs.
        return false
    }

    // Helper function to get typographic bounds as rect
    private func CTRunGetTypeographicBoundsAsRect(_ run: CTRun, _ range: CFRange) -> CGRect? {
        var ascent: CGFloat = 0
        var descent: CGFloat = 0
        var leading: CGFloat = 0
        let width = CTRunGetTypographicBounds(run, range, &ascent, &descent, &leading)
        return CGRect(x: 0, y: -descent, width: CGFloat(width), height: ascent + descent)
    }

    /// Get the correct rectangle for RTL text
    private func getRTLRangeRect(in line: CTLine, origin: CGPoint, y: CGFloat, range: NSRange, ascent: CGFloat, descent: CGFloat) -> CGRect {
        let runs = CTLineGetGlyphRuns(line) as Array
        var minX = CGFloat.greatestFiniteMagnitude
        var maxX: CGFloat = 0
        var foundMatch = false

        for i in 0 ..< runs.count {
            let run = runs[i] as! CTRun
            let runRange = CTRunGetStringRange(run)

            // Check if the run overlaps with our range
            if runRange.location + runRange.length > range.location &&
                runRange.location < range.location + range.length
            {
                // Get the bounds of this run
                var runAscent: CGFloat = 0
                var runDescent: CGFloat = 0
                var runLeading: CGFloat = 0

                let runWidth = CTRunGetTypographicBounds(run, CFRange(location: 0, length: 0), &runAscent, &runDescent, &runLeading)

                // Calculate x position
                var runPositions = [CGPoint](repeating: .zero, count: 1)
                CTRunGetPositions(run, CFRange(location: 0, length: 1), &runPositions)
                let xOffset = runPositions[0].x

                let runBounds = CGRect(x: origin.x + xOffset, y: y, width: CGFloat(runWidth), height: ascent + descent)

                minX = min(minX, runBounds.minX)
                maxX = max(maxX, runBounds.maxX)
                foundMatch = true
            }
        }

        if foundMatch {
            return CGRect(x: minX, y: y, width: maxX - minX, height: ascent + descent)
        }

        return CGRect.zero
    }

    func getRangeBorderEntryRects(frame: CGRect, range: NSRange) -> [CGRect] {
        let ctFrame = self
        var rects = [CGRect]()
        guard range.location != NSNotFound else { return rects }

        var lines = CTFrameGetLines(ctFrame) as Array
        var origins = [CGPoint](repeating: CGPoint.zero, count: lines.count)
        CTFrameGetLineOrigins(ctFrame, CFRange(location: 0, length: 0), &origins)

        for i in 0 ..< lines.count {
            let line = lines[i] as! CTLine
            let origin = origins[i]
            let lineCFRange = CTLineGetStringRange(line)

            if lineCFRange.location != NSNotFound {
                let lineRange = NSRange(location: lineCFRange.location, length: lineCFRange.length)

                if lineRange.location + lineRange.length > range.location && lineRange.location < (range.location + range.length) {
                    var ascent: CGFloat = 0
                    var descent: CGFloat = 0
                    var startX: CGFloat = 0

                    var contentRange = NSRange(location: range.location, length: 0)
                    let end = min(lineRange.location + lineRange.length, range.location + range.length)
                    contentRange.length = end - contentRange.location

                    CTLineGetTypographicBounds(line, &ascent, &descent, nil)

                    let y = origin.y - descent

                    startX = CTLineGetOffsetForStringIndex(line, contentRange.location, nil)

                    let endX = CTLineGetOffsetForStringIndex(line, contentRange.location + contentRange.length, nil)

//                    let rect = CGRect(x: origin.x + startX, y: y, width: endX - startX, height: ascent + descent)

                    // 右边沿
                    let contentLineHeight = ascent + descent
                    let rect = CGRect(x: frame.maxX + 10, y: y + (contentLineHeight - 16) / 2, width: 16, height: 16)
//                    let rect = CGRect(x: frame.maxX + 10, y: y, width: 16, height: ascent + descent)
                    rects.append(rect)
                }
            }
        }
        return rects
    }
}
