//
//  SelectedRangeView.swift
//  WDBook
//
//  Created by <PERSON> on 2021/4/24.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit

protocol SelectedRangeViewDelegate: AnyObject {
    /// 选择文本区域改变。（选中翻页状态下调用）
    /// - Parameters:
    ///   - selectedRangeView: <#selectedRangeView description#>
    ///   - isNext: 是否下一页
    func selectedRangeViewSelectedTextChangePage(_ selectedRangeView: SelectedRangeView, isNext: Bool)

    /// 获得当前pagecell。
    func selectedRangeViewGetCurrentPageCell() -> BookReaderPageCell?
}

class SelectedRangeView: UIView {
    weak var delegate: SelectedRangeViewDelegate?

    weak var page: Page?

    // 选择区域
    private var rects: [CGRect] = .init()

    private var leftCursor: SelectedCursorView?
    private var rightCursor: SelectedCursorView?

    init(frame: CGRect, page: Page) {
        self.page = page
        super.init(frame: frame)
        backgroundColor = UIColor.clear
        addGestureRecognizer(UIPanGestureRecognizer(target: self, action: #selector(panAction(g:))))

        Log.d("pangesture: SelectedRangeView: 初始化self:\(self)。page索引:\(self.page!.pageIndexInChapter))")
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }

        context.textMatrix = .identity
        context.translateBy(x: 0, y: bounds.size.height)
        context.scaleBy(x: 1.0, y: -1.0)

        // 处理光标
        guard rects.count > 0 else { return }
        let lineRects = rects.map { rect in
            CGRect(x: rect.origin.x, y: rect.origin.y, width: rect.width, height: rect.height)
        }
        let fillPath = CGMutablePath()
        UIColor(hex: 0x005AFF, alpha: 0.15).setFill()
        fillPath.addRects(lineRects)
        context.addPath(fillPath)
        context.fillPath()
    }

    func update(page: Page) {
        self.page = page
        Log.d("pangesture: SelectedRangeView: self:\(self)。更新page。page索引:\(self.page!.pageIndexInChapter))")
    }

    func update(by originPoint: CGPoint) {
        // 初始化区域
        let touchLocationRange = page!.ctPage.getTouchLocationRange(point: originPoint, viewFrame: frame).0
        SelectedRangeManager.setTouchOriginRange(touchLocationRange, chapterPath: page!.href)

        if touchLocationRange.length > 0 {
            rects = page?.ctPage.getRangeRects(range: SelectedRangeManager.range) ?? []
            Log.d("pangesture: SelectedRangeView: self:\(self)。page索引:\(page!.pageIndexInChapter),区域个数 \(rects.count)")
            // 显示光标
            showCursorView()
            setNeedsDisplay()
        }
    }

    func update(by range: NSRange) {
        // 初始化区域
        if range.length > 0 {
            rects = page?.ctPage.getRangeRects(range: range) ?? []
            Log.d("pangesture: SelectedRangeView: self:\(self)。page索引:\(page!.pageIndexInChapter),区域个数 \(rects.count)")
            // 显示光标
            showCursorView()
            setNeedsDisplay()
        }
    }

    @objc func panAction(g: UIPanGestureRecognizer) {
        Log.d("pangesture: SelectedRangeView: \(g.state.rawValue)")
        // 获取当前页面view，而不是手势最初设置事件的cell的view对象。
//        guard let currentPageCell = delegate?.selectedRangeViewGetCurrentPageCell(),
//              let currentSelectedRangeView = currentPageCell.ctCellView?.selectedRangeView else {
//            return
//        }
//
//        let point = g.location(in: currentSelectedRangeView)
        ////        Log.d("状态:\(g.state.rawValue),触发对象:\(g.view),点:\(g.location(in: self)),当前对象:\(currentSelectedRangeView),点:\(point)")
//        Log.d("状态:\(g.state.rawValue),点:\(point)")

        switch g.state {
        case .began:
            panBegan(g: g)
        case .changed:
            panChangeed(g: g)
        case .ended:
            panEnded(g: g)
        case .cancelled:
            panCancelled(g: g)
        default:
            panDefault(g: g)
        }
    }

    // MARK：手势函数。可外层调用。
    func panBegan(g: UIGestureRecognizer) {
        guard let currentPageCell = delegate?.selectedRangeViewGetCurrentPageCell(),
              let currentSelectedRangeView = currentPageCell.ctCellView?.selectedRangeView
        else {
            return
        }

        let point = g.location(in: currentSelectedRangeView)
        Log.d("SelectedRangeView.pan.begin, point: \(point)")

        if let left = leftCursor, let right = rightCursor, left.frame.minY == right.frame.minY, left.frame.insetBy(dx: -30, dy: -30).intersects(right.frame.insetBy(dx: -30, dy: -30)) {
            if left.frame.minX == right.frame.minX {
                SelectedRangeManager.setIsChangingRightCursor(true)
                SelectedRangeManager.isTouchCursor = true
            } else {
                let distance = right.frame.minX - left.frame.maxX
                var leftTouchRect = left.frame.insetBy(dx: -30, dy: -30)
                leftTouchRect.size.width = leftTouchRect.width - 30 + distance / 2

                var rightTouchRect = right.frame.insetBy(dx: -30, dy: -30)
                rightTouchRect.origin.x = rightTouchRect.origin.x + 30 - distance / 2

                if rightTouchRect.contains(point) {
                    SelectedRangeManager.setIsChangingRightCursor(true)
                    SelectedRangeManager.isTouchCursor = true
                } else if leftTouchRect.contains(point) {
                    SelectedRangeManager.setIsChangingRightCursor(false)
                    SelectedRangeManager.isTouchCursor = true
                } else {
                    return
                }
            }

        } else if let left = leftCursor, left.frame.insetBy(dx: -30, dy: -30).contains(point) {
            SelectedRangeManager.setIsChangingRightCursor(false)
            SelectedRangeManager.isTouchCursor = true
        } else if let right = rightCursor, right.frame.insetBy(dx: -30, dy: -30).contains(point) {
            SelectedRangeManager.setIsChangingRightCursor(true)
            SelectedRangeManager.isTouchCursor = true
        } else {
            return
        }

        setNeedsDisplay()
        // TODO: changed事件对CurrentSelectedRangeView的操作，做封装。
    }

    func panChangeed(g: UIGestureRecognizer) {
        guard let currentPageCell = delegate?.selectedRangeViewGetCurrentPageCell(),
              let currentSelectedRangeView = currentPageCell.ctCellView?.selectedRangeView,
              let currentPage = currentSelectedRangeView.page
        else {
            return
        }

        let point = g.location(in: currentSelectedRangeView)
        let frame = currentSelectedRangeView.frame

//        var isChangePaged = false
//        let startPageLocation = (g.view as! SelectedRangeView).page?.ctPage.range.location
//        let currentPageLocation = currentSelectedRangeView.page?.ctPage.range.location
//        if currentPageLocation > startPageLocation {
//            //rightCursor可以移动
//            isChangePaged = true
//        }else if currentPageLocation < startPageLocation{
//            //leftCursor可以移动
//            isChangePaged = true
//        }else{
//            //当前页,都可以移动。
//        }

        Log.d("SelectedRangeView.pan.changed, point: \(point)")

        SelectMenu.hide()
//            Log.d("CTView.pan.changed, point: \(g.location(in: self)), 最小点:\(frame.origin),最大点:\(nextPageFloatPoint)")
        checkPageChange(point: point)

        if isInPrePageArea(point: point) {
            Log.d("左上角")
        } else if isInNextPageArea(point: point) {
            Log.d("右下角")
        } else if frame.contains(point) {
            Log.d("内部")
        } else if point.x < frame.origin.x && point.y > frame.origin.y && point.y < frame.maxY {
            Log.d("左边沿")
        } else if point.x > frame.origin.x && point.y > frame.origin.y && point.y < frame.maxY {
            Log.d("右边沿")
        } else {
            Log.d("其他")
        }

//        if isChangePaged {
//            Log.d("滑动中换页了。")
//        }

        if SelectedRangeManager.isTouchCursor {
            let (finalRange, isRightOrBottomEdge) = currentPage.ctPage.getTouchLocationRange(point: point, viewFrame: frame)

            guard finalRange.location != NSNotFound,
                  finalRange.location >= 0,
                  finalRange.length > 0
            else {
                Log.d("没有找到触发的ctframe的range")
                return
            }

            if isRightOrBottomEdge {
                SelectedRangeManager.updateRange(chapterPath: currentPageCell.page!.href, newLocation: finalRange.location + finalRange.length)
            } else {
                let range = SelectedRangeManager.computeRange(chapterPath: currentPageCell.page!.href, newLocation: finalRange.location)
                if finalRange.location >= range.location + range.length {
                    SelectedRangeManager.updateRange(chapterPath: currentPageCell.page!.href, newLocation: finalRange.location + 1)
                } else {
                    SelectedRangeManager.updateRange(chapterPath: currentPageCell.page!.href, newLocation: finalRange.location)
                }
            }
            currentSelectedRangeView.rects = currentPage.ctPage.getRangeRects(range: SelectedRangeManager.range) ?? []
            Log.d("pangesture: SelectedRangeView: self:\(self)。page索引:\(currentPage.pageIndexInChapter),区域个数 \(rects.count)")
            // 显示光标
            currentSelectedRangeView.showCursorView()
        }
        currentSelectedRangeView.setNeedsDisplay()
    }

    func panEnded(g: UIGestureRecognizer) {
        // 获取当前页面view，而不是手势最初设置事件的cell的view对象。
        guard let currentPageCell = delegate?.selectedRangeViewGetCurrentPageCell(),
              let currentSelectedRangeView = currentPageCell.ctCellView?.selectedRangeView
        else {
            return
        }

        let point = g.location(in: currentSelectedRangeView)

        Log.d("SelectedRangeView.pan.ended, point: \(point)")
        SelectedRangeManager.isTouchCursor = false
        SelectedRangeManager.setTouchOriginRange(SelectedRangeManager.range, chapterPath: currentPageCell.page!.href)
        SelectedRangeManager.endDrag()
        cancelPageChange()

        if let chapterPath = SelectedRangeManager.chapterPath, let chapter = Paginator.current.getChapter(href: chapterPath) {
            let range = SelectedRangeManager.range
            let summary = chapter.displayedAttributedString.attributedSubstring(from: range).resetImageToBlank().string
            if summary.count == 0 || summary == " " {
                currentSelectedRangeView.clear()
                panCancelled(g: g)
                return
            }
        }

        if let firstRect = currentSelectedRangeView.rects.first {
            let minY = frame.height - firstRect.maxY
            let pointInCTView = CGPoint(x: firstRect.minX, y: minY)
            let pointInCell = CGPoint(x: pointInCTView.x + Paginator.PAGE_INSETS.left, y: pointInCTView.y + Paginator.PAGE_INSETS.top)

//                if let selectedNote = HighlightRangeManager.containsNote(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range){
            ////                    currentPageCell.ctCellView?.clearSelectedState()
//                    SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: currentPageCell, mode: .modify,selectedNote: selectedNote)
//                }else if SelectedRangeManager.range.length > 0{
//                    SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: currentPageCell, mode: .add)
//                }
            Log.d("pan显示菜单，point")
            SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: currentPageCell, mode: .add)
        } else {
            Log.d("pan未显示菜单，point")
        }
    }

    func panCancelled(g _: UIGestureRecognizer) {
        Log.d("SelectedRangeView.pan.cancelled, point: \(point)")
        guard let currentPageCell = delegate?.selectedRangeViewGetCurrentPageCell() else {
            return
        }

        SelectedRangeManager.isTouchCursor = false
        SelectedRangeManager.setTouchOriginRange(SelectedRangeManager.range, chapterPath: currentPageCell.page!.href)
        SelectedRangeManager.endDrag()
        cancelPageChange()
    }

    func panDefault(g: UIGestureRecognizer) {
        Log.d("SelectedRangeView.pan.default, point，\(g.state)")
        cancelPageChange()
    }

    // MARK：选中状态下滑动，是否翻页
    var currentPoint: CGPoint?
    var timer: Timer?
    func checkPageChange(point: CGPoint) {
        currentPoint = point
        if isInPrePageArea(point: point) {
            dispatchDelay(orignPoint: point)
        } else if isInNextPageArea(point: point) {
            dispatchDelay(orignPoint: point)
        } else {
            cancelPageChange()
        }
    }

    func dispatchDelay(orignPoint: CGPoint) {
        if timer == nil {
            timer = Timer(timeInterval: 1, repeats: true, block: { [weak self] _ in
                guard let self = self else { return }
                guard let point = self.currentPoint else { return }

                if self.isInPrePageArea(point: orignPoint), self.isInPrePageArea(point: point) {
                    self.delegate?.selectedRangeViewSelectedTextChangePage(self, isNext: false)
                    Log.d("尝试翻到上一页")
                } else if self.isInNextPageArea(point: point) {
                    self.delegate?.selectedRangeViewSelectedTextChangePage(self, isNext: true)
                    Log.d("尝试翻到下一页")
                }
            })
            RunLoop.main.add(timer!, forMode: .common)
        }
    }

    func cancelPageChange() {
        timer?.invalidate()
        timer = nil
    }

    // MARK: 判断区域

    func isInPrePageArea(point: CGPoint) -> Bool {
        let prePageFloatArea = CGRect(origin: CGPoint.zero, size: CGSize(width: point.x, height: point.y))
        if prePageFloatArea.contains(point) || (point.x < frame.origin.x && point.y < frame.origin.y + 30) || (point.y < frame.origin.y && point.x < frame.origin.x + 30) {
            return true
        }
        return false
    }

    func isInNextPageArea(point: CGPoint) -> Bool {
        let nextPageFloatPoint = CGPoint(x: frame.maxX, y: frame.maxY)
        if (point.x > nextPageFloatPoint.x && point.y > nextPageFloatPoint.y) || (point.x > nextPageFloatPoint.x && point.y > nextPageFloatPoint.y - 30) || (point.y > nextPageFloatPoint.y && point.x > nextPageFloatPoint.x - 30) {
            return true
        }
        return false
    }
}

// Cursor相关
extension SelectedRangeView {
    func clear() {
        rects.removeAll()
        hideCursorView()
        setNeedsDisplay()
    }

    func showCursorView() {
        guard rects.count > 0 else {
            hideCursorView()
            return
        }

        let leftRect = rects.first!
        let rightRect = rects.last!
        let cursorWidth: CGFloat = SelectedCursorView.CIRCLE_WIDTH
        let cursorLeftHeight: CGFloat = leftRect.height + cursorWidth * 2 // 20 应该等于字体大小 + 间距
        let cursorRightHeight: CGFloat = rightRect.height + cursorWidth * 2 // 20 应该等于字体大小 + 间距
//        let cursorHeight:CGFloat = CGFloat(WDReaderConfig.currentFontSize) + cursorWidth * 2
        let uiFont = UIFont.systemFont(ofSize: CGFloat(WDReaderConfig.currentFontSize))
        let font = CTFontCreateWithName(uiFont.fontName as CFString, uiFont.pointSize, nil)
        let cursorHeight = CTFontGetAscent(font) + CTFontGetDescent(font) + CTFontGetLeading(font) + 6 + cursorWidth * 2

        let selectedRange = SelectedRangeManager.range
        if let page = page, page.ctPage.range.location <= selectedRange.location {
            let leftCursorRect = CGRect(x: leftRect.minX - cursorWidth / 2, y: bounds.height - leftRect.origin.y - leftRect.height - cursorWidth, width: cursorWidth, height: cursorHeight)
            if leftCursor == nil {
                leftCursor = SelectedCursorView(frame: leftCursorRect, circleOnBottom: false)
                addSubview(leftCursor!)
            } else {
                leftCursor?.frame = leftCursorRect
                leftCursor?.draw(leftCursorRect)
            }
        } else {
            leftCursor?.removeFromSuperview()
            leftCursor = nil
        }

        if let page = page, page.ctPage.range.location + page.ctPage.range.length >= selectedRange.location + selectedRange.length {
            let rightCursorRect = CGRect(x: rightRect.maxX - cursorWidth / 2, y: bounds.height - rightRect.origin.y - rightRect.height - cursorWidth, width: cursorWidth, height: cursorHeight)
            if rightCursor == nil {
                rightCursor = SelectedCursorView(frame: rightCursorRect, circleOnBottom: true)
                addSubview(rightCursor!)
            } else {
                rightCursor?.frame = rightCursorRect
                rightCursor?.draw(rightCursorRect)
            }
        } else {
            rightCursor?.removeFromSuperview()
            rightCursor = nil
        }
    }

    func hideCursorView() {
        leftCursor?.removeFromSuperview()
        leftCursor = nil
        rightCursor?.removeFromSuperview()
        rightCursor = nil
    }
}

class SelectedCursorView: UIView {
    static let CIRCLE_WIDTH: CGFloat = 7
    static let LINE_WIDTH: CGFloat = 1.5
    // 圈圈在下边
    private var circleOnBottom: Bool = false

    // 光标颜色
    var cursorColor: UIColor = .init(hex: 0x007AFF) {
        didSet {
            setNeedsDisplay()
        }
    }

    init(frame: CGRect, circleOnBottom: Bool) {
        super.init(frame: frame)

        self.circleOnBottom = circleOnBottom
        backgroundColor = UIColor.clear
//        backgroundColor = UIColor.red
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    override func draw(_ rect: CGRect) {
        let context = UIGraphicsGetCurrentContext()
        cursorColor.set()

        let cursorWidth = rect.width
        let cursorHeight = rect.height

        context?.addRect(CGRect(x: (cursorWidth - Self.LINE_WIDTH) / 2, y: cursorWidth, width: Self.LINE_WIDTH, height: bounds.height - cursorWidth * 2))
        context?.fillPath()

        if circleOnBottom {
            context?.addEllipse(in: CGRect(x: 0, y: bounds.height - cursorWidth, width: cursorWidth, height: cursorWidth))
        } else {
            context?.addEllipse(in: CGRect(x: 0, y: 0, width: cursorWidth, height: cursorWidth))
        }

        context?.fillPath()
        cursorColor.set()
    }
}
