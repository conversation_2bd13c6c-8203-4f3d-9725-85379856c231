//
//  CTView.swift
//  CoreTextMagazine
//
//  Created by <PERSON> on 2020/4/16.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import CoreText
import UIKit
import WebKit

extension CGImage {
    /// 计算图片等比例缩放后填满指定矩形所需的CGRect
    /// - Parameters:
    ///   - rect: 目标矩形
    /// - Returns: 调整后的CGRect，使图片等比例缩放并填满目标矩形
    func scaledRectToFit(in rect: CGRect) -> CGRect {
        let imageSize = CGSize(width: CGFloat(width), height: CGFloat(height))
        let imageAspectRatio = imageSize.width / imageSize.height
        let rectAspectRatio = rect.width / rect.height

        let scale: CGFloat
        // 无论图片是横向还是竖向，都基于宽高比较小的一侧进行缩放，以确保图片不会被拉伸
        if imageAspectRatio > rectAspectRatio {
            // 图片宽度相对于高度更大，或目标矩形较窄，基于宽度缩放
            scale = rect.width / imageSize.width
        } else {
            // 图片高度相对于宽度更大，或目标矩形较高，基于高度缩放
            scale = rect.height / imageSize.height
        }

        let scaledWidth = imageSize.width * scale
        let scaledHeight = imageSize.height * scale

        // 创建一个新的CGRect，使缩放后的图片在目标矩形中居中显示
        let scaledRect = CGRect(
            x: rect.midX - (scaledWidth / 2),
            y: rect.midY - (scaledHeight / 2),
            width: scaledWidth,
            height: scaledHeight
        )

        return scaledRect
    }
}

enum TapPosition {
    case left
    case center
    case right
}

protocol CTViewDelegate: AnyObject {
    func ctView(_ ctView: CTView, interactWith URL: URL, in characterRange: NSRange, location: CGPoint)
    func ctViewDidSelected(_ ctView: CTView, position: TapPosition)
    func ctViewHideNavAndToolBar()

    // 选择文本相关
    func ctViewSelectdModeChanged(_ ctView: CTView, isSelected: Bool)
    func ctViewSelectedTextChangePage(_ ctView: CTView, isNext: Bool)
    func ctViewGetCurrentPageCell() -> BookReaderPageCell?
    func ctViewGetSelfPageCell() -> BookReaderPageCell

    func ctViewShowPhotoBrowser(_ ctView: CTView, photo: UIImage, sourceView: UIView, sourceFrame: CGRect)
}

class CTView: UIView {
    weak var delegate: CTViewDelegate?

    var page: Page!
    var ctFrame: CTFrame!
    var imageAttchaments: [ImageAttachment]?
    var selectedRangeView: SelectedRangeView?

    enum RenderType {
        case ctframe
        case textview
        case webview
        case textkit
    }

    var readerType = RenderType.ctframe
    var textView: UITextView?
    var webView: WKWebView?

    var textStorage: NSTextStorage?
    var layoutManager: NSLayoutManager?
    var textContainer: NSTextContainer?

    required init(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)!
    }

    required init(frame: CGRect, page: Page, ctFrame: CTFrame, imageAttchaments: [ImageAttachment]? = nil) {
        super.init(frame: frame)
        self.page = page
        self.ctFrame = ctFrame
        self.imageAttchaments = imageAttchaments
//        backgroundColor = dynamicBackgroundColor1
        backgroundColor = UIColor.clear
        addEvents()
        updateSelectedArea()
    }

    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        guard readerType == .ctframe else { return }

        context.textMatrix = .identity
        context.translateBy(x: 0, y: bounds.size.height)
        context.scaleBy(x: 1.0, y: -1.0)

        CTFrameDraw(ctFrame, context)

//        Log.d("闪退测试：第\(page.chapterIndex)章，第\(page.pageIndexInChapter)页，内容是否为空:\(page.getPageAttributedString().isEffectivelyEmpty)。包含图片个数:\(String(describing: imageAttchaments?.count)),是否横屏：\(rect.width > rect.height)")

        if imageAttchaments?.count == 1 && page.getPageAttributedString().isEffectivelyEmpty {
            if let image = imageAttchaments?.first?.image.cgImage {
                let targetRect = image.scaledRectToFit(in: rect)
                imageAttchaments?[0].displayFrame = targetRect
                context.draw(image, in: targetRect)
            }
        } else if imageAttchaments?.count == 2 && page.getPageAttributedString().isEffectivelyEmpty && rect.width > rect.height {
            if let leftImg = imageAttchaments?.first?.image.cgImage {
                let leftRect = CGRect(x: rect.minX, y: rect.minY, width: rect.width / 2, height: rect.height)
                let targetRect = leftImg.scaledRectToFit(in: leftRect)
                imageAttchaments?[0].displayFrame = targetRect
                context.draw(leftImg, in: targetRect)
            }

            if let rightImg = imageAttchaments?.last?.image.cgImage {
                let rightRect = CGRect(x: rect.midX, y: rect.minY, width: rect.width / 2, height: rect.height)
                let targetRect = rightImg.scaledRectToFit(in: rightRect)
                imageAttchaments?[1].displayFrame = targetRect
                context.draw(rightImg, in: targetRect)
            }

        } else {
            for imageAttachment in imageAttchaments ?? [] {
                if let image = imageAttachment.image.cgImage {
                    let imgBounds = imageAttachment.frame
                    context.draw(image, in: imgBounds)
                }
            }
        }
    }

    func resetCTPage(page: Page, ctFrame: CTFrame, imageAttchaments: [ImageAttachment]? = nil) {
        self.page = page
        self.ctFrame = ctFrame
        self.imageAttchaments = imageAttchaments
        updateSelectedArea()

        setNeedsDisplay()

        if readerType == .textview {
            if textView == nil {
                textView = UITextView()
                textView?.frame = CGRect(x: 0, y: 0, width: frame.width, height: frame.height)
                addSubview(textView!)
            }
            textView?.attributedText = page.chapter!.displayedAttributedString
        } else if readerType == .webview {
            if webView == nil {
                webView = WKWebView(frame: CGRect(x: 0, y: 0, width: frame.width, height: frame.height))
                addSubview(webView!)
            }
//            webView?.loadHTMLString(HTMLCommonUtils.hebrewTextNotWork, baseURL: nil) //正常
            webView?.loadHTMLString(page.getPageAttributedString().htmlString(), baseURL: nil) // 希伯来错误；格式错误；
        } else if readerType == .textkit {
            if textStorage == nil {
                // 创建 TextKit 组件
                textStorage = NSTextStorage()
                let newLayoutManager = NSLayoutManager()
                textStorage?.addLayoutManager(newLayoutManager)
                layoutManager = newLayoutManager

                let s = CGSize(width: bounds.size.width, height: CGFloat.greatestFiniteMagnitude)
                let newTextContainer = NSTextContainer(size: s)
//                let newTextContainer = NSTextContainer(size: bounds.size)
                layoutManager?.addTextContainer(newTextContainer)
                textContainer = newTextContainer

                if textView == nil {
                    // 创建一个基于 TextKit 组件的 UITextView
                    textView = UITextView(frame: bounds, textContainer: textContainer)
                    textView?.isScrollEnabled = true
                    addSubview(textView!)
                }
            }
            // 将章节内容设置到 NSTextStorage
            textStorage?.setAttributedString(page.chapter!.displayedAttributedString)
        }
    }

    func updateSelectedArea() {
        if let chapterPath = SelectedRangeManager.chapterPath {
            let selectedRange = SelectedRangeManager.range
            showSelectedRangeView(range: selectedRange)
        } else {
            hideSelectedRangeView()
        }
    }
}

extension CTView: SelectedRangeViewDelegate {
    func selectedRangeViewSelectedTextChangePage(_: SelectedRangeView, isNext: Bool) {
        delegate?.ctViewSelectedTextChangePage(self, isNext: isNext)
    }

    func selectedRangeViewGetCurrentPageCell() -> BookReaderPageCell? {
        delegate?.ctViewGetCurrentPageCell()
    }
}

extension CTView {
    func addEvents() {
        addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapAction(_:))))
        addGestureRecognizer(UILongPressGestureRecognizer(target: self, action: #selector(longPressAction(g:))))
    }

    @objc func tapAction(_ g: UIGestureRecognizer) {
        // 选择区域
        guard selectedRangeView == nil else {
            clearSelectedState()
            return
        }

        if SelectMenu.isShowing(), let tips = SelectMenu.menu?.noteTips {
            let pointInTips = g.location(in: tips)
            if tips.bounds.contains(pointInTips) {
                SelectMenu.menu?.selectedNoteOnModifyMode()
                SelectMenu.hide()
                return
            }
        }

        // 正文中链接(url,目录跳转，笔记脚注)
        let point = g.location(in: self)
        if let tappedInfo = page.ctPage.isTapLink(point: point) {
            if SelectMenu.isShowing() {
                SelectMenu.hide()
            }
            delegate?.ctView(self, interactWith: tappedInfo.url, in: tappedInfo.range, location: tappedInfo.location)
            return
        }

        // 高亮笔记区域
        if let cell = delegate?.ctViewGetSelfPageCell(),
           let highlightView = cell.highlightView,
           let notes = HighlightRangeManager.chapterNotesDic?[page.href]
        {
            // 首先检查点击位置是否在实际文本内容区域
            let pointInHighlightView = g.location(in: highlightView)
            let touchRange = highlightView.page!.ctPage.getTouchLocationRange(point: pointInHighlightView, viewFrame: highlightView.frame, isAutoAdjust: false).0
            
            // 只有当点击位置确实在文本内容上时才进行高亮检测
            if touchRange.length > 0 && touchRange.location != NSNotFound {
                // 额外检查：确保点击位置确实在某个文本行的边界内
                let ctFrame = highlightView.page!.ctPage.ctFrame
                let lines = CTFrameGetLines(ctFrame) as Array
                var origins = [CGPoint](repeating: CGPoint.zero, count: lines.count)
                CTFrameGetLineOrigins(ctFrame, CFRange(location: 0, length: 0), &origins)
                
                var isPointInTextBounds = false
                for i in 0 ..< lines.count {
                    let line = lines[i] as! CTLine
                    let origin = origins[i]
                    
                    var ascent: CGFloat = 0
                    var descent: CGFloat = 0
                    CTLineGetTypographicBounds(line, &ascent, &descent, nil)
                    
                    let lineRect = CGRect(x: origin.x, y: highlightView.frame.height - origin.y - (ascent + descent), width: CTLineGetOffsetForStringIndex(line, 100_000, nil), height: ascent + descent)
                    
                    if lineRect.contains(pointInHighlightView) {
                        isPointInTextBounds = true
                        break
                    }
                }
                
                // 只有当点击确实在文本行边界内时才继续处理高亮
                if isPointInTextBounds {
                    var selectedNote: Note?
                    for note in notes.sorted(by: { $0.createTime > $1.createTime }) {
                        let range = note.showRange
                        if let intersectionRange = range.intersection(touchRange), intersectionRange.location >= 0 && intersectionRange.length > 0 {
                            selectedNote = note
                            break
                        }
                    }
                    if selectedNote != nil {
                        Log.d("点击了高亮，弹出高亮菜单")
                        let rects = page.ctPage.getRangeRects(range: selectedNote!.showRange)
                        if let firstRect = rects.first {
                            let minY = frame.height - firstRect.maxY
                            let pointInCTView = CGPoint(x: firstRect.minX, y: minY)
                            let pointInCell = CGPoint(x: pointInCTView.x + Paginator.PAGE_INSETS.left, y: pointInCTView.y + Paginator.PAGE_INSETS.top)

                            delegate?.ctViewHideNavAndToolBar()
                            SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: cell, mode: .modify, selectedNote: selectedNote)
                        }
                        return
                    }
                }
            }
        }

        if SelectMenu.isShowing() {
            SelectMenu.hide()
            return
        }

        // 中文中其他，包含左右区域参数。
        var position = TapPosition.center
        if point.x <= (UIScreen.main.bounds.width * Paginator.PAGE_TAP_AREA_RATE_LEFT) - Paginator.PAGE_INSETS.left {
            position = .left
        } else if point.x >= (UIScreen.main.bounds.width * Paginator.PAGE_TAP_AREA_RATE_RIGHT) - Paginator.PAGE_INSETS.left {
            position = .right
        }

        if position == .center {
            if let (photo, imageFrame) = tappedImage(point: point) {
                delegate?.ctViewShowPhotoBrowser(self, photo: photo, sourceView: UIView(), sourceFrame: imageFrame)
                return
            }
        }

        delegate?.ctViewDidSelected(self, position: position)
    }

    @objc func longPressAction(g: UILongPressGestureRecognizer) {
        Log.d("longgesture: CTView : \(g.state.rawValue)")
        let touchPoint = g.location(in: self)
        let touchLocationRange = ctFrame.getTouchLocationRange(point: touchPoint, viewFrame: frame).0
        guard touchLocationRange.location >= 0, touchLocationRange.length > 0 else {
            return
        }

        // selectedRangeView?.panxxx函数是：确定一个位置，调整另一个位置版本。
        switch g.state {
        case .began:
            Log.d("CTView.longPress.began, point: \(g.location(in: self))")
            // 显示选择区域，选中一个区域。显示放大镜
            showSelectedRangeView(point: g.location(in: self))

            selectedRangeView?.panBegan(g: g)
            SelectedRangeManager.setIsChangingRightCursor(true)
//            Log.d("开始：原区域：\(originRange),选中区域：\(selectedRange),frame：\(rects)")
        case .changed:
            Log.d("CTView.longPress.changed, point: \(g.location(in: self))")

            // 选中的这个1个区域移动。显示放大镜的版本。
//            changeSelectedRangeView(point: g.location(in: self))

            selectedRangeView?.panChangeed(g: g)
            SelectMenu.hide()
//            Log.d("移动：原区域：\(originRange),选中区域：\(selectedRange),frame：\(rects)")
        case .ended:
            Log.d("CTView.longPress.ended, point: \(g.location(in: self))")

            // 选中的这个1个区域移动。显示放大镜的版本。
//            let rects = ctFrame.getRangeRects(range: SelectedRangeManager.range)
//            if let firstRect = rects.first, let pageCell = delegate?.ctViewGetCurrentPageCell(){
//
//                let minY = self.frame.height - firstRect.maxY
//                let pointInCTView = CGPoint(x: firstRect.minX, y: minY)
//                let pointInCell = CGPoint(x: pointInCTView.x + Paginator.PAGE_INSERT.left, y: pointInCTView.y + Paginator.PAGE_INSERT.top)
//
            ////                if let selectedNote = HighlightRangeManager.containsNote(href: pageCell.page?.href ?? "", range: SelectedRangeManager.range){
            //////                    delegate?.ctViewHideNavAndToolBar()
            //////                    clearSelectedState()
            ////                    SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: pageCell, mode: .modify,selectedNote: selectedNote)
            ////                }else if SelectedRangeManager.range.length > 0{
            ////                    SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: pageCell, mode: .add)
            ////                }
//
//                SelectMenu.show(point: CGRect(origin: pointInCell, size: firstRect.size), on: pageCell, mode: .add)
//            }

            selectedRangeView?.panEnded(g: g)
        case .cancelled:
            Log.d("CTView.longPress.cancelled, point: \(g.location(in: self))")
            selectedRangeView?.panCancelled(g: g)
        case .failed:
            Log.d("CTView.longPress.failed, point: \(g.location(in: self))")
        case .possible:
            Log.d("CTView.longPress.possible, point: \(g.location(in: self))")
        default:
            Log.d("CTView.longPress.default, point: \(g.location(in: self))")
            selectedRangeView?.panDefault(g: g)
        }
    }

    // MARK: 选中

    func clearSelectedState(isHideMenu: Bool = true) {
        selectedRangeView?.clear()
        hideSelectedRangeView()

        SelectedRangeManager.clear()
        if isHideMenu {
            SelectMenu.hide()
        }

        delegate?.ctViewSelectdModeChanged(self, isSelected: false)
    }

    func showSelectedRangeView(point: CGPoint) {
        if selectedRangeView == nil {
            selectedRangeView = SelectedRangeView(frame: CGRect(origin: CGPoint.zero, size: frame.size), page: page)
            selectedRangeView?.delegate = self
            addSubview(selectedRangeView!)

            delegate?.ctViewSelectdModeChanged(self, isSelected: true)
        }
        selectedRangeView?.update(page: page)
        selectedRangeView?.update(by: point)
    }

    func changeSelectedRangeView(point: CGPoint) {
        if selectedRangeView == nil {
            selectedRangeView = SelectedRangeView(frame: CGRect(origin: CGPoint.zero, size: frame.size), page: page)
            selectedRangeView?.delegate = self
            addSubview(selectedRangeView!)

            delegate?.ctViewSelectdModeChanged(self, isSelected: true)
        }
        selectedRangeView?.update(page: page)
        selectedRangeView?.update(by: point)
    }

    func hideSelectedRangeView() {
        if selectedRangeView != nil {
            selectedRangeView?.removeFromSuperview()
            selectedRangeView = nil
        }
    }

    func showSelectedRangeView(range: NSRange) {
        if selectedRangeView == nil {
            selectedRangeView = SelectedRangeView(frame: CGRect(origin: CGPoint.zero, size: frame.size), page: page)
            selectedRangeView?.delegate = self
            addSubview(selectedRangeView!)

            delegate?.ctViewSelectdModeChanged(self, isSelected: true)
        }
        selectedRangeView?.update(page: page)
        selectedRangeView?.update(by: range)
    }

    // MARK: 判断是否是点击图片

    func tappedImage(point: CGPoint) -> (UIImage, CGRect)? {
        for imageAttachment in imageAttchaments ?? [] {
//          if let image = imageAttachment.image.drawOutlie(color: UIColor.red)?.cgImage{
            let image = imageAttachment.image
            guard image.disableTap == nil || image.disableTap == false else { continue }

            if let _ = image.cgImage {
                var imageFrame = imageAttachment.displayFrame
                imageFrame.origin.y = frame.height - imageFrame.origin.y - imageFrame.size.height
                if imageFrame.contains(point) {
                    print("图片被点击了！,\(point)")
                    return (image, imageFrame)
                }
            }
        }
        return nil
    }

//    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
//        super.touchesBegan(touches, with: event)
//        Log.d("CTView.touchesBegan, point: \(touches.first?.location(in: self))")
//
//        guard let point = touches.first?.location(in: self),
//              let left = leftCursor,
//              let right = rightCursor else { return }
//
//        if left.frame.insetBy(dx: -30, dy: -30).contains(point) {
//            touchRightCursor = true
//            isTouchCursor = true
//        } else if right.frame.insetBy(dx: -30, dy: -30).contains(point) {
//            touchRightCursor = false
//            isTouchCursor = true
//        }
//
//        touchOriginRange = selectedRange
//
    ////        let touch = event?.allTouches?.first
    ////        if touch?.view == ctView {
    ////            let point = touch?.location(in: touch!.view)
    ////            print("touchesBegan: \(point)")
    ////            if let point = point{
    ////                     //检查是否点击在图片上，如果在，优先响应图片事件
    ////                if self.checkIsClickImageViewWith(point: CGPoint(x: point.x, y: point.y)) {
    ////                         return;
    ////                     }
    ////            }
    ////            self.isTapLink(point: point!)//响应字符串事件
    ////        }
//    }

//    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
//        super.touchesMoved(touches, with: event)
//        Log.d("CTView.touchesMoved, point: \(touches.first?.location(in: self))")
//        guard let point = touches.first?.location(in: self),
//              leftCursor != nil,
//              rightCursor != nil else { return }
//
//        if isTouchCursor {
//            let finalRange = ctFrame.getTouchLocationRange(point: point, viewFrame: frame)
//
//            guard finalRange.location != NSNotFound,finalRange.location > 0, finalRange.length > 0 else { return }
//
//            var range = NSRange(location: 0, length: 0)
//
//            if touchRightCursor { // 移动右边光标
//                if finalRange.location >= touchOriginRange.location {
//                    range.location = touchOriginRange.location
//                    range.length = finalRange.location - touchOriginRange.location + 1
//                } else {
//                    range.location = finalRange.location
//                    range.length = touchOriginRange.location - range.location
//                }
//            } else {  // 移动左边光标
//
//                if finalRange.location <= touchOriginRange.location {
//                    range.location = finalRange.location
//                    range.length = touchOriginRange.location - finalRange.location + touchOriginRange.length
//
//                } else if finalRange.location > touchOriginRange.location {
//
//                    if finalRange.location <= touchOriginRange.location + touchOriginRange.length - 1 {
//                        range.location = finalRange.location
//                        range.length = touchOriginRange.location + touchOriginRange.length - finalRange.location
//                    } else {
//                        range.location = touchOriginRange.location + touchOriginRange.length
//                        range.length = finalRange.location - range.location
//                    }
//                }
//            }
//
//            selectedRange = range
//            rects = ctFrame.getRangeRects(range: selectedRange)
//
//            // 显示光标
//            showCursorView()
//            setNeedsDisplay()
//        }
//    }
//
//    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
//        super.touchesEnded(touches, with: event)
//        Log.d("CTView.touchesEnded, point: \(touches.first?.location(in: self))")
//        isTouchCursor = false
//        touchOriginRange = selectedRange
//    }
//
//    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
//        super.touchesCancelled(touches, with: event)
//        Log.d("CTView.touchesCancelled, point: \(touches.first?.location(in: self))")
//    }
}
