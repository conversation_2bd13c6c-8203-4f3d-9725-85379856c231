//
//  HighlightRangeView.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/5/21.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit

protocol HighlightRangeViewDelegate:AnyObject {
    
    /// 选择文本区域改变。（选中翻页状态下调用）
    /// - Parameters:
    ///   - selectedRangeView: <#selectedRangeView description#>
    ///   - isNext: 是否下一页
    func selectedRangeViewSelectedTextChangePage(_ selectedßRangeView: SelectedRangeView, isNext:Bool)

    /// 获得当前pagecell。
    func selectedRangeViewGetCurrentPageCell() -> BookReaderPageCell?
}

class HighlightRangeView: UIView {
    weak var delegate:HighlightRangeViewDelegate?

    weak var page:Page?
    private var noteRects: [(Note,[CGRect])] = [(Note,[CGRect])]()
    
    init(frame: CGRect, page:Page) {
        self.page = page
        super.init(frame: frame)
        backgroundColor = UIColor.clear
//        backgroundColor = UIColor.red.alpha(0.3)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }

        context.textMatrix = .identity
        context.translateBy(x: 0, y: bounds.size.height)
        context.scaleBy(x: 1.0, y: -1.0)

        //TODO:文字区域上下紧贴；最左边最右边区域更大；线比文字低2个像素。
        guard noteRects.count > 0 else { return }
        for (note,rects) in noteRects {
            let lineRects = rects.map { rect -> CGRect in
                if note.style.isUnderline{
                    return CGRect(x: rect.origin.x, y: rect.origin.y - 1.5, width: rect.width, height: 1.2)
                }else{
                    return CGRect(x: rect.origin.x, y: rect.origin.y, width: rect.width, height: rect.height)
                }
            }
            let fillPath = CGMutablePath()
            note.style.showColor.setFill()
            fillPath.addRects(lineRects)
            context.addPath(fillPath)
            context.fillPath()
        }
        
        //计算每个笔记在右边的入口。
        //计算所有入口，重叠的向下排列，可以超越屏幕。不关心行的矫正。
        //如果是横屏，高亮在左半，笔记入口从左半排列。高亮在右半，笔记入口从右半排列。
//        var noteEntranceDic = [Int:(Note,CGRect)]()
//        var noteEntrances = [(Note,CGRect)]()
//        if let pageRange = page?.ctPage.range{
//            let allRects = ctFrame.getRangeBorderEntryRects(frame:frame,range: pageRange)
//
//            for note in notes {
//                for rect in allRects{
//
//                    if rect.minY >= note.rects.first!.minY && noteEntranceDic[Int(rect.minY)] == nil{
//                        noteEntranceDic[Int(rect.minY)] = (note,rect)
//                        noteEntrances.append((note,rect))
//                    }
//                }
//            }
//
//        }
//
//        for (note,rect) in noteEntrances{
//            let lineRects = note.rects.map { rect -> CGRect in
////                if note.style.isUnderline{
////                    return CGRect(x: rect.origin.x, y: rect.origin.y - 1.5, width: rect.width, height: 1)
////                }else{
////                    return CGRect(x: rect.origin.x, y: rect.origin.y, width: rect.width, height: rect.height)
////                }
//                return CGRect(x: rect.maxX + 10, y: rect.origin.y, width: 16, height: 16)
//            }
//            let fillPath = CGMutablePath()
//            note.style.color.setFill()
//            fillPath.addRects(lineRects)
//            context.addPath(fillPath)
//            context.fillPath()
//        }
    }
    
    func update(page:Page){
        self.page = page
    }
    
    func update(by notes:[Note]){
        noteRects = [(Note,[CGRect])]()
        
        for i in 0 ..< notes.count{
            let note = notes[i]
            let range = note.showRange
            //笔记包含当前页
            if let intersectionRange = range.intersection(page!.ctPage.range) ,intersectionRange.length > 0{
                let rectsInOneNoteOnOnePage = page!.ctPage.getRangeRects(range: range)
                noteRects.append((note,rectsInOneNoteOnOnePage))
            }
        }
        
        setNeedsDisplay()
    }
    
    //MARK: 判断区域
    func isInPrePageArea(point:CGPoint) -> Bool{
        let prePageFloatArea = CGRect(origin: CGPoint.zero, size: CGSize(width: point.x, height: point.y))
        if prePageFloatArea.contains(point) || (point.x < frame.origin.x && point.y < frame.origin.y + 30) || (point.y < frame.origin.y && point.x < frame.origin.x + 30){
            return true
        }
        return false
    }
    
    func isInNextPageArea(point:CGPoint) -> Bool{
        let nextPageFloatPoint = CGPoint(x: frame.maxX, y: frame.maxY)
        if (point.x > nextPageFloatPoint.x && point.y > nextPageFloatPoint.y) || (point.x > nextPageFloatPoint.x && point.y > nextPageFloatPoint.y - 30) || (point.y > nextPageFloatPoint.y && point.x > nextPageFloatPoint.x - 30){
            return true
        }
        return false
    }
}

extension HighlightRangeView{
    func clear() {
        noteRects.removeAll()
        setNeedsDisplay()
    }
}
