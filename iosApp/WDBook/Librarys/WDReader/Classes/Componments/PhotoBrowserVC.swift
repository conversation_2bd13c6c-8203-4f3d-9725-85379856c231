//
//  PhotoBrowserVC.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2021/9/22.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import UIKit
import SnapKit
import AVFoundation

class PhotoBrowserVC: UIViewController {
    var initImageFrame:CGRect!
    let closeImageView:UIImageView
    let closeButton:UIButton
    let imageView:UIImageView
    let image:UIImage
    var startPoint:CGPoint!
    var endPoint:CGPoint!
    
    init(photo:UIImage,sourceView:UIView?) {
        closeImageView = UIImageView(image: UIImage(named: "close"))
        closeButton = UIButton()
        image = photo
        imageView = UIImageView(image: self.image)
        
        imageView.contentMode = .scaleAspectFit
        super.init(nibName: nil, bundle: nil)
        view.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        imageView.isUserInteractionEnabled = true
        let pinchGuesture = UIPinchGestureRecognizer(target: self, action:#selector(pinchGestureHandler(_:)))
        imageView.addGestureRecognizer(pinchGuesture)
        let panGuesture = UIPanGestureRecognizer(target: self, action:#selector(panGestureHandler(_:)))
        imageView.addGestureRecognizer(panGuesture)
        
        view.addSubview(closeImageView)
        closeImageView.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide.snp.top).offset(31.5)
            make.trailing.equalTo(view.safeAreaLayoutGuide.snp.trailing).offset(-23.5)
            make.width.height.equalTo(24)
        }
        
        view.addSubview(closeButton)
        closeButton.snp.makeConstraints { make in
            make.top.trailing.equalTo(view.safeAreaLayoutGuide)
            make.width.equalTo(31.5*2 + 24)
            make.height.equalTo(23.5*2 + 24)
        }
        closeButton.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        initImageFrame = AVMakeRect(aspectRatio: imageView.image!.size, insideRect: imageView.frame)
    }
    
    @objc func tapClose(){
        self.dismiss()
    }
    
    @objc func pinchGestureHandler(_ recognizer:UIPinchGestureRecognizer)
    {
        Log.d("pinch: state:\(recognizer.state.rawValue), scale:\(recognizer.scale),velocity: \(recognizer.velocity), imageScale:\(image.scale),\(imageView.transform)")
        
        switch recognizer.state{
        case .began:
            startPoint = recognizer.location(in: recognizer.view)
            endPoint = startPoint
            break
        case .changed:
            endPoint = recognizer.location(in: recognizer.view)
            let newScale = recognizer.scale
            if let transform = recognizer.view?.transform.scaledBy(x: newScale,y: newScale){
                
                let offsetX = (endPoint!.x - startPoint!.x)/2
                let offsetY = (endPoint!.y - startPoint!.y)/2
                recognizer.view?.transform = transform.translatedBy(x: offsetX * newScale, y: offsetY * newScale)
            }
            recognizer.scale = 1
            break
        case .ended:
            endPoint = recognizer.location(in: recognizer.view)
            let newScale = recognizer.scale
            if recognizer.view?.transform.a < 1{
                UIView.animate(withDuration: 0.25) { [weak self] in
                    guard let self = self else{return}
                    recognizer.view?.transform = CGAffineTransform.identity
                }
            }else if recognizer.view?.transform.a > 4{
                if var transform = recognizer.view?.transform.scaledBy(x: newScale,y: newScale){
                    //目标位置
                    let offsetX = (endPoint!.x - startPoint!.x)/2
                    let offsetY = (endPoint!.y - startPoint!.y)/2
                    let scaleOffsetX = offsetX * newScale
                    let scaleOffsetY = offsetY * newScale
                    transform = transform.translatedBy(x: scaleOffsetX, y: scaleOffsetY)
                    
                    //返回位置
                    let returnScale = 4.0 / transform.d
                    let returnScaleTX = transform.tx * returnScale
                    let returnScaleTY = transform.ty * returnScale
                    transform = transform.scaledBy(x: returnScale,y: returnScale)
                    transform = CGAffineTransform(a: transform.a, b: transform.b, c: transform.c, d: transform.d, tx: returnScaleTX, ty: returnScaleTY)
                    
                    //调整边界
                    if !aligmentEdge(transform: transform, targetView: recognizer.view){
                        UIView.animate(withDuration: 0.25) {
                            recognizer.view?.transform = transform
                        }
                    }
                }
            }else{
                aligmentEdge(transform: recognizer.view!.transform, targetView: recognizer.view)
            }
            recognizer.scale = 1
            break
//        case .possible: break
//        case .cancelled: break
//        case .failed: break
        @unknown default: break
            
        }
    }
    
    @objc func panGestureHandler(_ recognizer:UIPanGestureRecognizer)
    {
        Log.d("pan: state:\(recognizer.state.rawValue), point: \(recognizer.location(in: recognizer.view)),initImageFrame:\(String(describing: initImageFrame)), size:\(image.size),\(imageView.transform)")
        
        switch recognizer.state {
        case .began:
            startPoint = recognizer.location(in: recognizer.view)
            endPoint = startPoint
            break
        case .changed:
            endPoint = recognizer.location(in: recognizer.view)
            let offsetX = (endPoint!.x - startPoint!.x)/2
            let offsetY = (endPoint!.y - startPoint!.y)/2

            if let transform = recognizer.view?.transform.translatedBy(x: offsetX, y: offsetY){
                recognizer.view?.transform = transform
            }
            break
        case .ended:
            endPoint = recognizer.location(in: recognizer.view)
            let offsetX = (endPoint!.x - startPoint!.x)/2
            let offsetY = (endPoint!.y - startPoint!.y)/2
            if let transform = recognizer.view?.transform.translatedBy(x: offsetX, y: offsetY){
                aligmentEdge(transform: transform, targetView: recognizer.view)
            }
            break
        case .cancelled:
            break
        default:
            break
        }
    }
    
    func aligmentEdge(transform:CGAffineTransform,targetView:UIView?) -> Bool{
        //可视区域在屏幕之外。
        let actualImageFrame =  CGRect(x: -((initImageFrame.size.width * transform.a - UIScreen.main.bounds.width ) / 2) + transform.tx, y: -((initImageFrame.size.height * transform.d - UIScreen.main.bounds.height ) / 2) + transform.ty, width: initImageFrame.size.width * transform.a, height: initImageFrame.size.height * transform.d)
        guard !actualImageFrame.contains(UIScreen.main.bounds) else {
            return false
        }
        
        var tx:CGFloat = 0
        var ty:CGFloat = 0
        
        if initImageFrame.size.height * transform.d <= UIScreen.main.bounds.height{
            Log.d("高度小于屏幕")
            ty = 0
        }else if initImageFrame.size.height * transform.d > UIScreen.main.bounds.height{
            if actualImageFrame.minY > 0{
                Log.d("高度大于屏幕, 贴近上边")
                ty = (initImageFrame.size.height * transform.d - UIScreen.main.bounds.height ) / 2
            }else if actualImageFrame.maxY < UIScreen.main.bounds.height{
                Log.d("高度大于屏幕, 贴近下边")
                ty = -((initImageFrame.size.height * transform.d - UIScreen.main.bounds.height ) / 2)
            }else{
                Log.d("高度大于屏幕, 中间")
                ty = transform.ty
            }
        }
        
        if initImageFrame.size.width * transform.a <= UIScreen.main.bounds.width{
            Log.d("宽度小于屏幕")
            tx = 0
        }else if initImageFrame.size.width * transform.a > UIScreen.main.bounds.width{
            
            if actualImageFrame.minX > 0{
                Log.d("宽度大于屏幕, 贴近左边")
                tx = (initImageFrame.size.width * transform.a - UIScreen.main.bounds.width ) / 2
            }else if actualImageFrame.maxX < UIScreen.main.bounds.width{
                Log.d("宽度大于屏幕, 贴近右边")
                tx = -((initImageFrame.size.width * transform.a - UIScreen.main.bounds.width ) / 2)
            }else{
                Log.d("宽度大于屏幕, 中间")
                tx = transform.tx
            }
        }
        
        UIView.animate(withDuration: 0.25) {
            targetView?.transform = CGAffineTransform(a: transform.a, b: transform.b, c: transform.c, d: transform.d, tx: tx, ty: ty)
        }
        return true
    }
    
    //MARK: 转向相关
    public override func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        
        coordinator.animate {[weak self] context in
            guard let self = self else {return}
            self.imageView.transform = CGAffineTransform.identity
            self.initImageFrame = AVMakeRect(aspectRatio: self.imageView.image!.size, insideRect: self.imageView.frame)
        }
    }
}
