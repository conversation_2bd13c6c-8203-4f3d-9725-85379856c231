//
//  BookSearchBar.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/12.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct BookSearchBar: View {
    @ObservedObject private var searchState = SearchState.shared
    @State private var isSearchFocused: Bool = false
    @State private(set) var focusWorkItem: DispatchWorkItem?

    var dismissAction: () -> Void
    var shouldAutoFocus: Bool
    var autoFocusDelay: TimeInterval
    var preserveStateOnDisappear: Bool
    var minLength: Int
    var maxLength: Int

    init(
        dismissAction: @escaping () -> Void,
        shouldAutoFocus: Bool = true,
        autoFocusDelay: TimeInterval = 0.5,
        preserveStateOnDisappear: Bool = false,
        minLength: Int = 2,
        maxLength: Int = 50
    ) {
        self.dismissAction = dismissAction
        self.shouldAutoFocus = shouldAutoFocus
        self.autoFocusDelay = autoFocusDelay
        self.preserveStateOnDisappear = preserveStateOnDisappear
        self.minLength = minLength
        self.maxLength = maxLength
    }

    // Extracted function to show the invalid search toast message.
    private func showInvalidSearchToast() {
        Toaster.showToast(message: "search_invalid_length_message".localized)
    }

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)
                        .frame(width: 24)
                        .padding(.leading, 8)

                    SearchTextField(
                        text: $searchState.searchText,
                        isFocused: $isSearchFocused,
                        onSubmit: {
                            if !searchState.searchText.isEmpty {
                                if searchState.searchText.isValidSearchLength(minLength: self.minLength, maxLength: self.maxLength) {
                                    isSearchFocused = false
                                    searchState.performSearch(with: searchState.searchText)
                                } else {
                                    self.showInvalidSearchToast()
                                }
                            }
                        },
                        onInvalidInput: {
                            self.showInvalidSearchToast()
                        },
                        minLength: minLength,
                        maxLength: maxLength
                    )
                    .frame(height: 36)
                    .layoutPriority(1)

                    if !searchState.searchText.isEmpty {
                        Button(action: {
                            searchState.clearSearch()
                            isSearchFocused = true
                        }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(.gray)
                                .padding(.trailing, 8)
                        }
                    }
                }
                .frame(height: 44)
                .background(Color(.systemGray6))
                .cornerRadius(8)

                Button("cancel_button".localized) {
                    searchState.clearSearch()
                    dismissAction()
                }
                .foregroundColor(Color("wd_orange"))
            }
            .frame(height: 44)
            .padding(.horizontal)
            .padding(.vertical, 4)

            Rectangle()
                .fill(Color(.systemGray5))
                .frame(height: 1)
        }
        .onAppear {
            handleAutoFocus()
        }
        .onDisappear {
            // Always cancel pending focus requests
            focusWorkItem?.cancel()
            focusWorkItem = nil

            // Only clear focus state if we're not preserving state
            if !preserveStateOnDisappear {
                isSearchFocused = false
            }
        }
        .onReceive(searchState.$pendingSearchTerm) { searchTerm in
            if let term = searchTerm {
                isSearchFocused = false
                searchState.searchText = term
                searchState.performSearch(with: term)
                searchState.pendingSearchTerm = nil
            }
        }
        .onReceive(searchState.$shouldUnfocusSearchBar) { shouldUnfocus in
            if shouldUnfocus {
                isSearchFocused = false
                // Reset the flag in SearchState after handling
                searchState.shouldUnfocusSearchBar = false
            }
        }
    }

    private func handleAutoFocus() {
        guard shouldAutoFocus else { return }

        // Cancel any existing work item
        focusWorkItem?.cancel()

        // Create and schedule new work item
        let workItem = createFocusWorkItem()
        focusWorkItem = workItem

        DispatchQueue.main.asyncAfter(
            deadline: .now() + autoFocusDelay,
            execute: workItem
        )
    }

    private func createFocusWorkItem() -> DispatchWorkItem {
        return DispatchWorkItem { [self] in
            isSearchFocused = true
        }
    }
}

// UIKit wrapper for TextField to handle focus
struct SearchTextField: UIViewRepresentable {
    @Binding var text: String
    @Binding var isFocused: Bool
    var onSubmit: () -> Void
    var onInvalidInput: () -> Void
    var minLength: Int
    var maxLength: Int

    func makeCoordinator() -> Coordinator {
        Coordinator(self, minLength: minLength, maxLength: maxLength)
    }

    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        textField.placeholder = "search_book_placeholder".localized
        textField.delegate = context.coordinator
        textField.returnKeyType = .search
        configureTextField(textField)
        textField.tintColor = UIColor(named: "wd_orange")
        return textField
    }

    private func configureTextField(_ textField: UITextField) {
        // Set fixed height
        textField.heightAnchor.constraint(equalToConstant: 22).isActive = true

        // Configure layout priorities
        textField.setContentHuggingPriority(.defaultLow, for: .horizontal)
        textField.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)

        // Configure text behavior
        textField.adjustsFontSizeToFitWidth = false
        textField.textAlignment = .left
        textField.borderStyle = .none

        // Configure font
        textField.font = UIFont.systemFont(ofSize: 16)
    }

    func updateUIView(_ uiView: UITextField, context _: Context) {
        uiView.text = text
        if isFocused && !uiView.isFirstResponder {
            uiView.becomeFirstResponder()
        } else if !isFocused && uiView.isFirstResponder {
            uiView.resignFirstResponder()
        }
    }

    class Coordinator: NSObject, UITextFieldDelegate {
        var parent: SearchTextField
        let minLength: Int
        let maxLength: Int

        init(_ parent: SearchTextField, minLength: Int, maxLength: Int) {
            self.parent = parent
            self.minLength = minLength
            self.maxLength = maxLength
        }

        func textFieldDidChangeSelection(_ textField: UITextField) {
            DispatchQueue.main.async {
                self.parent.text = textField.text ?? ""
            }
        }

        func textFieldDidBeginEditing(_: UITextField) {
            DispatchQueue.main.async {
                self.parent.isFocused = true
            }
        }

        func textFieldDidEndEditing(_: UITextField) {
            DispatchQueue.main.async {
                self.parent.isFocused = false
            }
        }

        func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
            let currentText = textField.text ?? ""
            guard let stringRange = Range(range, in: currentText) else { return false }

            let updatedText = currentText.replacingCharacters(in: stringRange, with: string)

            // Check if the updated text exceeds the maximum length
            if updatedText.count > maxLength {
                // Truncate the text to the maximum length
                let truncatedText = String(updatedText.prefix(maxLength))

                // Manually update the text field's text
                textField.text = truncatedText

                // Update the parent's text binding and set cursor asynchronously
                DispatchQueue.main.async {
                    // Check if the parent's text actually needs updating
                    if self.parent.text != truncatedText {
                        self.parent.text = truncatedText
                    }
                    // Manually set the cursor position to the end AFTER updating parent state
                    if let newPosition = textField.position(from: textField.beginningOfDocument, offset: truncatedText.count) {
                        textField.selectedTextRange = textField.textRange(from: newPosition, to: newPosition)
                    }
                }

                // Notify that the input was modified (truncated)
                parent.onInvalidInput()

                // Return false because we manually handled the text update
                return false
            }

            // The change is valid and within the length limit, allow the text field to handle it.
            // The parent.text will be updated via textFieldDidChangeSelection.
            return true
        }

        func textFieldShouldReturn(_ textField: UITextField) -> Bool {
            if let text = textField.text, !text.isEmpty {
                if !text.isValidSearchLength(minLength: minLength, maxLength: maxLength) {
                    parent.onInvalidInput()
                    return false
                }
                parent.onSubmit()
            }
            return true
        }
    }
}

#Preview {
    BookSearchBar(
        dismissAction: {},
        shouldAutoFocus: true,
        autoFocusDelay: 0.5,
        preserveStateOnDisappear: false,
        minLength: 2,
        maxLength: 10
    )
}
