//
//  ReaderViewSearchResultNavigator.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/27.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ReaderViewSearchResultNavigator: View {
    @StateObject private var searchState = SearchState.shared
    var onNavigate: (SearchResult) -> Void
    var onShowSearchView: (() -> Void)? = nil

    private let disabledColor = Color("searchNavigatorButtonDisabled")

    var body: some View {
        HStack(spacing: 0) {
            // Previous button
            Button(action: {
                if let prevResult = searchState.previousResult() {
                    onNavigate(prevResult)
                }
            }) {
                HStack(spacing: 4) {
                    Image(systemName: "chevron.left")
                        .font(.system(size: 16))
                    Text("reader_navigator_previous".localized)
                        .font(.system(size: 14))
                }
                .foregroundColor(searchState.isAtFirstResult ? disabledColor : .primary)
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
            }
            .disabled(searchState.isAtFirstResult)

            Spacer()

            // Search result title - now opens search view when tapped
            But<PERSON>(action: {
                onShowSearchView?()
            }) {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .font(.system(size: 16))
                    Text("reader_navigator_search_result".localized)
                        .font(.system(size: 14))
                }
                .padding(.horizontal, 12)
                .padding(.vertical, 10)
            }
            .foregroundColor(.primary)

            Spacer()

            // Next button
            Button(action: {
                if searchState.isAtLastResult, searchState.hasMoreResults {
                    // Load more results when at the end and more are available
                    searchState.performSearch(with: searchState.searchText, loadMore: true) { result in
                        switch result {
                        case .success:
                            // Navigate to next result after successfully loading more results
                            if let nextResult = searchState.nextResult() {
                                onNavigate(nextResult)
                            }
                        case .failure, .cancelled:
                            // Do nothing on failure or cancellation
                            break
                        }
                    }
                } else if let nextResult = searchState.nextResult() {
                    // If not at last result or no more results to load, navigate directly
                    onNavigate(nextResult)
                }
            }) {
                HStack(spacing: 4) {
                    if searchState.footerRefreshing {
                        // Show loading indicator when loading more results
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle())
                            .scaleEffect(0.8)
                    } else {
                        Text("reader_navigator_next".localized)
                            .font(.system(size: 14))
                        Image(systemName: "chevron.right")
                            .font(.system(size: 16))
                    }
                }
                .foregroundColor((searchState.footerRefreshing || (searchState.isAtLastResult && !searchState.hasMoreResults)) ? disabledColor : .primary)
                .frame(height: 20) // Keep consistent height when loading
                .padding(.horizontal, 12)
                .padding(.vertical, 15)
            }
            .disabled(searchState.footerRefreshing || (searchState.isAtLastResult && !searchState.hasMoreResults))
        }
        .background(
            RoundedRectangle(cornerRadius: 10)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 4, x: 0, y: 2)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 10)
                .stroke(Color(.systemGray5), lineWidth: 0.5)
        )
        .padding(.horizontal, 20)
    }
}

// Extension to use this component in a reader view
extension ReaderViewSearchResultNavigator {
    // Helper method to create the navigator with a jump action
    static func createForReaderView(jumpAction: @escaping (SearchResult) -> Void, showSearchAction: @escaping () -> Void) -> ReaderViewSearchResultNavigator {
        return ReaderViewSearchResultNavigator(
            onNavigate: { result in
                jumpAction(result)
            },
            onShowSearchView: showSearchAction
        )
    }
}

#Preview {
    // Set up the search state with sample data
    SearchState.shared.searchText = "示例文本"
    SearchState.shared.results = [
        SearchSection(
            title: "第一章",
            results: [
                SearchResult(content: "示例内容", chapterIndex: 0, fileId: 1, fileUuid: "", startOffset: 10, endOffset: 20),
            ]
        ),
    ]

    return VStack {
        Spacer()
        ReaderViewSearchResultNavigator(
            onNavigate: { _ in },
            onShowSearchView: {}
        )
        .padding(.bottom, 20)
    }
    .background(Color(.systemGray6))
}
