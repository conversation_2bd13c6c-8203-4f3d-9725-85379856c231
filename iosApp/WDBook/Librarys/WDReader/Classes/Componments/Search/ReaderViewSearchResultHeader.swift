//
//  ReaderViewSearchResultHeader.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/27.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ReaderViewSearchResultHeader: View {
    @StateObject private var searchState = SearchState.shared
    let clearAction: () -> Void
    var showSearchView: (() -> Void)? = nil

    var body: some View {
        HStack {
            Spacer()

            VStack(alignment: .center, spacing: 4) {
                Text("reader_view_search_result_header".localizedFormat(searchState.searchText))
                    .font(.system(size: 12, weight: .light))
                    .multilineTextAlignment(.center)

                Text(searchState.currentSectionTitle)
                    .font(.system(size: 10))
                    .foregroundColor(.secondary)
                    .multilineTextAlignment(.center)
            }
            .padding(.vertical, 12)
            .contentShape(Rectangle())
            .onTapGesture {
                showSearchView?()
            }
            .frame(maxWidth: .infinity)

            Spacer()

            Button(action: {
                withAnimation {
                    clearAction()
                }
            }) {
                Image(systemName: "xmark.circle.fill")
                    .foregroundColor(Color(hex: 0xBCBCBC))
                    .font(.system(size: 20))
            }
            .padding(.trailing, 8)
        }
        .padding(.horizontal, 16)
        .background(
            RoundedRectangle(cornerRadius: 12)
                .fill(Color(.systemBackground))
                .shadow(color: Color.black.opacity(0.1), radius: 5, x: 0, y: 2)
        )
        .padding(.horizontal, 16)
    }
}

#Preview {
    // Set up the search state with sample data
    SearchState.shared.searchText = "示例文本"
    SearchState.shared.results = [
        SearchSection(
            title: "第一章",
            results: [
                SearchResult(content: "示例内容", chapterIndex: 0, fileId: 0, fileUuid: "", startOffset: 0, endOffset: 0),
            ]
        ),
    ]

    return ReaderViewSearchResultHeader(
        clearAction: {
            // Preview clear action
        },
        showSearchView: {
            // Preview show search action
        }
    )
}
