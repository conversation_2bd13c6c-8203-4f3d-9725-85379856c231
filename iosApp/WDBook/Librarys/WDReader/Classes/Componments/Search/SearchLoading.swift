//
//  SearchLoading.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/13.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct SearchLoading: View {
    var body: some View {
        VStack(spacing: 20) {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle())
                .scaleEffect(1.5)

            Text("search_loading_message".localized)
                .font(.system(size: 16))
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
}

#Preview {
    SearchLoading()
}
