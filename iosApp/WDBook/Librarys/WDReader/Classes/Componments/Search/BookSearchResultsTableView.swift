//
//  BookSearchResultsTableView.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/12.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit

/// A UITableView-based implementation of the search results list for better performance with large result sets.
struct BookSearchResultsTableView: UIViewRepresentable {
    @ObservedObject private var searchState = SearchState.shared
    let onItemSelected: (SearchResult) -> Void

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    func makeUIView(context: Context) -> UITableView {
        // Create a custom table view that handles didMoveToWindow
        let tableView = SearchResultsTableView(frame: .zero, style: .grouped)
        tableView.delegate = context.coordinator
        tableView.dataSource = context.coordinator
        tableView.backgroundColor = .clear
        tableView.separatorStyle = .none
        tableView.contentInset = UIEdgeInsets(top: 0, left: 0, bottom: 20, right: 0)
        tableView.isUserInteractionEnabled = true
        tableView.allowsSelection = true

        // Register cell and header
        tableView.register(SearchResultTableViewCell.self, forCellReuseIdentifier: "SearchResultCell")
        tableView.register(SearchSectionHeaderView.self, forHeaderFooterViewReuseIdentifier: "SectionHeader")

        // Configure refresh control for pull-to-refresh
        let refreshControl = UIRefreshControl()
        refreshControl.addTarget(context.coordinator, action: #selector(Coordinator.handleRefresh(_:)), for: .valueChanged)
        tableView.refreshControl = refreshControl

        // Create a simple loading footer
        let footerContainer = UIView(frame: CGRect(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 44))
        footerContainer.backgroundColor = .clear

        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.translatesAutoresizingMaskIntoConstraints = false
        activityIndicator.hidesWhenStopped = true

        let label = UILabel()
        label.translatesAutoresizingMaskIntoConstraints = false
        label.text = "加载更多".localized
        label.textColor = .secondaryLabel
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center

        footerContainer.addSubview(activityIndicator)
        footerContainer.addSubview(label)

        NSLayoutConstraint.activate([
            activityIndicator.centerXAnchor.constraint(equalTo: footerContainer.centerXAnchor),
            activityIndicator.topAnchor.constraint(equalTo: footerContainer.topAnchor, constant: 8),

            label.centerXAnchor.constraint(equalTo: footerContainer.centerXAnchor),
            label.topAnchor.constraint(equalTo: activityIndicator.bottomAnchor, constant: 4),
        ])

        tableView.tableFooterView = footerContainer
        context.coordinator.loadingIndicator = activityIndicator
        context.coordinator.loadingLabel = label

        return tableView
    }

    func updateUIView(_ tableView: UITableView, context: Context) {
        // Update the table view when data changes
        tableView.reloadData()

        // Update footer visibility
        if !searchState.results.isEmpty {
            if searchState.footerRefreshing {
                context.coordinator.loadingIndicator?.startAnimating()
                context.coordinator.loadingLabel?.text = "加载中...".localized
            } else if !searchState.hasMoreResults {
                context.coordinator.loadingIndicator?.stopAnimating()
                context.coordinator.loadingLabel?.text = "没有更多".localized
            } else {
                context.coordinator.loadingIndicator?.stopAnimating()
                context.coordinator.loadingLabel?.text = "加载更多".localized
            }
            tableView.tableFooterView?.isHidden = false
        } else {
            tableView.tableFooterView?.isHidden = true
        }

        // Update refresh control state
        if searchState.isSearching {
            tableView.refreshControl?.beginRefreshing()
        } else {
            tableView.refreshControl?.endRefreshing()
        }
    }

    class Coordinator: NSObject, UITableViewDelegate, UITableViewDataSource {
        var parent: BookSearchResultsTableView
        var loadingIndicator: UIActivityIndicatorView?
        var loadingLabel: UILabel?

        init(_ parent: BookSearchResultsTableView) {
            self.parent = parent
        }

        // MARK: - UITableViewDataSource

        func numberOfSections(in _: UITableView) -> Int {
            return parent.searchState.results.count
        }

        func tableView(_: UITableView, numberOfRowsInSection section: Int) -> Int {
            guard section < parent.searchState.results.count else { return 0 }
            return parent.searchState.results[section].results.count
        }

        func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
            guard let cell = tableView.dequeueReusableCell(withIdentifier: "SearchResultCell", for: indexPath) as? SearchResultTableViewCell else {
                return UITableViewCell()
            }

            guard indexPath.section < parent.searchState.results.count,
                  indexPath.row < parent.searchState.results[indexPath.section].results.count
            else {
                return cell
            }

            let result = parent.searchState.results[indexPath.section].results[indexPath.row]
            let isSelected = parent.searchState.hasSelectedResult &&
                parent.searchState.selectedSectionIndex == indexPath.section &&
                parent.searchState.selectedResultIndex == indexPath.row

            // Configure the cell with the result and selection state
            cell.configure(with: result, isSelected: isSelected)

            // Set up the selection callback
            cell.onSelect = { [weak self] in
                guard let self = self else { return }

                // Provide haptic feedback
                let generator = UIImpactFeedbackGenerator(style: .light)
                generator.impactOccurred()

                // Update selection state
                self.parent.searchState.selectResult(sectionIndex: indexPath.section, resultIndex: indexPath.row)

                // Notify parent
                self.parent.onItemSelected(result)
            }

            return cell
        }

        // MARK: - UITableViewDelegate

        func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
            guard let headerView = tableView.dequeueReusableHeaderFooterView(withIdentifier: "SectionHeader") as? SearchSectionHeaderView,
                  section < parent.searchState.results.count
            else {
                return nil
            }

            let title = parent.searchState.results[section].title
            headerView.configure(with: title)
            return title.isEmpty ? nil : headerView
        }

        func tableView(_: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
            guard section < parent.searchState.results.count else { return 0 }
            return parent.searchState.results[section].title.isEmpty ? 0 : 44
        }

        func tableView(_: UITableView, heightForRowAt _: IndexPath) -> CGFloat {
            return UITableView.automaticDimension
        }

        func tableView(_: UITableView, estimatedHeightForRowAt _: IndexPath) -> CGFloat {
            return 80
        }

        func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
            tableView.deselectRow(at: indexPath, animated: true)

            guard indexPath.section < parent.searchState.results.count,
                  indexPath.row < parent.searchState.results[indexPath.section].results.count
            else {
                return
            }

            let result = parent.searchState.results[indexPath.section].results[indexPath.row]

            // Provide haptic feedback
            let generator = UIImpactFeedbackGenerator(style: .light)
            generator.impactOccurred()

            // Update selection state
            parent.searchState.selectResult(sectionIndex: indexPath.section, resultIndex: indexPath.row)

            // Highlight the selected cell
            if let cell = tableView.cellForRow(at: indexPath) as? SearchResultTableViewCell {
                cell.configure(with: result, isSelected: true)
            }

            // Notify parent
            parent.onItemSelected(result)
        }

        // MARK: - Refresh Control

        @objc func handleRefresh(_: UIRefreshControl) {
            // Perform a new search with the current search text
            parent.searchState.performSearch(with: parent.searchState.searchText)
        }

        // MARK: - UIScrollViewDelegate

        func scrollViewDidScroll(_ scrollView: UIScrollView) {
            // Check if we need to load more results
            let offsetY = scrollView.contentOffset.y
            let contentHeight = scrollView.contentSize.height
            let height = scrollView.frame.size.height

            if offsetY > contentHeight - height * 1.2 {
                if parent.searchState.hasMoreResults && !parent.searchState.footerRefreshing {
                    parent.searchState.footerRefreshing = true
                    parent.searchState.performSearch(with: parent.searchState.searchText, loadMore: true)
                }
            }
        }

        func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate _: Bool) {
            // Check if we need to load more results
            let offsetY = scrollView.contentOffset.y
            let contentHeight = scrollView.contentSize.height
            let height = scrollView.frame.size.height

            if offsetY > contentHeight - height * 1.2 {
                if parent.searchState.hasMoreResults && !parent.searchState.footerRefreshing {
                    parent.searchState.footerRefreshing = true
                    parent.searchState.performSearch(with: parent.searchState.searchText, loadMore: true)
                }
            }
        }
    }
}

// Custom UITableView subclass to handle didMoveToWindow
class SearchResultsTableView: UITableView {
    override func didMoveToWindow() {
        super.didMoveToWindow()

        // Only proceed if we're being added to the window (not removed)
        if window != nil {
            // Get the search state
            let searchState = SearchState.shared

            // Check if we need to restore scroll position using selected indices
            if searchState.hasSelectedResult && !searchState.footerRefreshing {
                let indexPath = IndexPath(row: searchState.selectedResultIndex, section: searchState.selectedSectionIndex)

                if indexPath.section < searchState.results.count &&
                    indexPath.row < searchState.results[indexPath.section].results.count
                {
                    // First reload the data to ensure cells are properly configured
                    reloadData()

                    // Force an initial layout
                    layoutIfNeeded()

                    // Use a single delay with a completion callback
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                        // Scroll without animation to avoid visual glitches
                        self.scrollToRow(at: indexPath, at: .middle, animated: false)

                        // Force layout to finalize cell positions
                        self.layoutIfNeeded()

                        // Update all visible cells in one pass
                        self.updateAllVisibleCellsSelectionState(searchState: searchState)
                    }
                }
            }
        }
    }

    // Helper method to update all visible cells selection state
    private func updateAllVisibleCellsSelectionState(searchState: SearchState) {
        for visibleCell in visibleCells {
            if let searchCell = visibleCell as? SearchResultTableViewCell,
               let cellIndexPath = indexPath(for: visibleCell)
            {
                let cellIsSelected = searchState.hasSelectedResult &&
                    searchState.selectedSectionIndex == cellIndexPath.section &&
                    searchState.selectedResultIndex == cellIndexPath.row

                if let cellResult = cellIndexPath.section < searchState.results.count &&
                    cellIndexPath.row < searchState.results[cellIndexPath.section].results.count ?
                    searchState.results[cellIndexPath.section].results[cellIndexPath.row] : nil
                {
                    searchCell.configure(with: cellResult, isSelected: cellIsSelected)
                }
            }
        }
    }
}

// MARK: - Supporting Views

class SearchResultTableViewCell: UITableViewCell {
    private let contentLabel = UILabel()
    private let containerView = UIView()
    private let innerView = UIView()
    var onSelect: (() -> Void)?
    private var isSelectedState: Bool = false

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupViews() {
        backgroundColor = .clear
        selectionStyle = .none
        isUserInteractionEnabled = true

        containerView.translatesAutoresizingMaskIntoConstraints = false
        containerView.layer.cornerRadius = 12
        containerView.layer.masksToBounds = false // Allow shadow to be visible
        containerView.isUserInteractionEnabled = true

        // Configure shadow
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.08
        containerView.layer.shadowRadius = 8
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)

        // Create an inner view for the content with masksToBounds
        innerView.translatesAutoresizingMaskIntoConstraints = false
        innerView.backgroundColor = UIColor(named: "searchResultBackground") ?? .systemBackground
        innerView.layer.cornerRadius = 12
        innerView.layer.masksToBounds = true

        // Add tap gesture recognizer
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap))
        containerView.addGestureRecognizer(tapGesture)

        contentLabel.translatesAutoresizingMaskIntoConstraints = false
        contentLabel.numberOfLines = 3 // Limit to 3 lines
        contentLabel.font = .systemFont(ofSize: 16)
        contentLabel.textColor = .label
        contentLabel.isUserInteractionEnabled = false
        contentLabel.textAlignment = .natural // Use natural text alignment for mixed scripts
        contentLabel.allowsDefaultTighteningForTruncation = false // Prevent truncation issues with RTL text
        contentLabel.lineBreakMode = .byWordWrapping // Better wrapping for mixed text

        // Use dynamic type to improve readability
        contentLabel.adjustsFontForContentSizeCategory = true

        containerView.addSubview(innerView)
        innerView.addSubview(contentLabel)
        contentView.addSubview(containerView)

        NSLayoutConstraint.activate([
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 6),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -6),

            innerView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor),
            innerView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor),
            innerView.topAnchor.constraint(equalTo: containerView.topAnchor),
            innerView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor),

            contentLabel.leadingAnchor.constraint(equalTo: innerView.leadingAnchor, constant: 16),
            contentLabel.trailingAnchor.constraint(equalTo: innerView.trailingAnchor, constant: -16),
            contentLabel.topAnchor.constraint(equalTo: innerView.topAnchor, constant: 12),
            contentLabel.bottomAnchor.constraint(equalTo: innerView.bottomAnchor, constant: -12),
        ])
    }

    @objc private func handleTap() {
        onSelect?()
    }

    func configure(with result: SearchResult, isSelected: Bool) {
        // Store the selected state
        isSelectedState = isSelected

        // Create attributed string with highlights
        let attributedString = NSMutableAttributedString(string: result.content)

        // Apply base attributes with line spacing
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineSpacing = 4 // Reduce line spacing
        paragraphStyle.lineBreakMode = .byTruncatingTail // Ensure ellipsis at the end
        paragraphStyle.alignment = .natural // Use natural text alignment

        // Check if content starts with Hebrew
        let startsWithHebrew = result.content.first.flatMap { RTLTextProcessor.isRTLText(String($0)) } ?? false

        // Set the base writing direction based on whether the content starts with Hebrew
        paragraphStyle.baseWritingDirection = startsWithHebrew ? .rightToLeft : .leftToRight

        attributedString.addAttributes([
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: UIColor.label,
            .paragraphStyle: paragraphStyle,
        ], range: NSRange(location: 0, length: result.content.count))

        // Find RTL text for more accurate highlighting
        var correctedHighlights: [(NSRange, String)] = []
        if result.highlights.contains(where: { RTLTextProcessor.isRTLText($0.text) }) {
            correctedHighlights = RTLTextProcessor.findActualRangesForRTLHighlights(in: result.content, highlights: result.highlights)
        }

        // Apply highlight attributes, accounting for bidirectional text
        // Use either the corrected highlights if available, or the original ones
        if !correctedHighlights.isEmpty {
            for (correctedRange, text) in correctedHighlights {
                // Apply highlighting for corrected ranges
                var attributes: [NSAttributedString.Key: Any] = [
                    .foregroundColor: UIColor.systemOrange,
                ]

                // Add RTL direction for RTL text
                if RTLTextProcessor.isRTLText(text) {
                    attributes[.writingDirection] = [NSWritingDirection.rightToLeft.rawValue | NSWritingDirectionFormatType.override.rawValue]
                }

                attributedString.addAttributes(attributes, range: correctedRange)
            }
        } else {
            // Use original highlights if no corrections were made
            for highlight in result.highlights {
                // Ensure the range is valid before applying
                if highlight.range.location != NSNotFound &&
                    highlight.range.location + highlight.range.length <= result.content.count
                {
                    let needsDirectionalOverride = RTLTextProcessor.isRTLText(highlight.text)

                    var attributes: [NSAttributedString.Key: Any] = [
                        .foregroundColor: UIColor.systemOrange,
                    ]

                    if needsDirectionalOverride {
                        // Use override instead of embedding for RTL text to ensure proper display
                        attributes[.writingDirection] = [NSWritingDirection.rightToLeft.rawValue | NSWritingDirectionFormatType.override.rawValue]
                    }

                    attributedString.addAttributes(attributes, range: highlight.range)
                }
            }
        }

        contentLabel.attributedText = attributedString
        contentLabel.textAlignment = .natural // Ensure natural text alignment at the label level

        // Update appearance based on selection state
        updateSelectionAppearance()
    }

    private func updateSelectionAppearance() {
        if isSelectedState {
            // Remove border for selected cells
            containerView.layer.borderWidth = 0
            // Slightly increase shadow for selected state
            containerView.layer.shadowOpacity = 0.15
            // Update inner view background color for selected state
            innerView.backgroundColor = UIColor(named: "searchResultSelectedBackground") ?? .systemBackground
        } else {
            containerView.layer.borderWidth = 0
            containerView.layer.shadowOpacity = 0.08
            // Update inner view background color for non-selected state
            innerView.backgroundColor = UIColor(named: "searchResultBackground") ?? .systemBackground
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        // Reset the cell's state
        isSelectedState = false
        updateSelectionAppearance()
    }
}

class SearchSectionHeaderView: UITableViewHeaderFooterView {
    private let titleLabel = UILabel()
    private var widthConstraint: NSLayoutConstraint?
    private var heightConstraint: NSLayoutConstraint?

    override init(reuseIdentifier: String?) {
        super.init(reuseIdentifier: reuseIdentifier)
        setupViews()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupViews() {
        titleLabel.translatesAutoresizingMaskIntoConstraints = false
        titleLabel.font = UIFont.systemFont(ofSize: 18, weight: .medium)
        titleLabel.textColor = .label

        contentView.addSubview(titleLabel)

        // Store constraints that we might need to deactivate
        widthConstraint = contentView.widthAnchor.constraint(equalToConstant: 0)
        heightConstraint = contentView.heightAnchor.constraint(equalToConstant: 0)

        // Add constraints for the label
        NSLayoutConstraint.activate([
            titleLabel.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 16),
            titleLabel.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -16),
            titleLabel.topAnchor.constraint(equalTo: contentView.topAnchor, constant: 16),
            titleLabel.bottomAnchor.constraint(equalTo: contentView.bottomAnchor, constant: -8),
        ])
    }

    func configure(with title: String) {
        titleLabel.text = title

        if title.isEmpty {
            // Deactivate size constraints and hide the label
            widthConstraint?.isActive = true
            heightConstraint?.isActive = true
            titleLabel.isHidden = true
        } else {
            // Activate size constraints and show the label
            widthConstraint?.isActive = false
            heightConstraint?.isActive = false
            titleLabel.isHidden = false
        }
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        titleLabel.text = nil
        titleLabel.isHidden = true
        widthConstraint?.isActive = true
        heightConstraint?.isActive = true
    }
}
