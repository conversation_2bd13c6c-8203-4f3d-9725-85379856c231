//
//  BookSearchHistory.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/12.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit

struct BookSearchHistory: View {
    @ObservedObject private var searchState = SearchState.shared
    @State private var showingDeleteConfirmation = false
    @State private var totalHeight: CGFloat = .zero

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // History Header
            HStack {
                Text("book_search_history_title".localized)
                    .font(.headline)
                Spacer()
                Button(action: {
                    showingDeleteConfirmation = true
                    searchState.unfocusSearchBar()
                }) {
                    Image(systemName: "trash")
                        .foregroundColor(.primary)
                        .padding(8)
                }
            }
            .padding(.horizontal)
            .padding(.top, 8)

            // History Tags - using the existing FlowLayout for vertical scrolling
            ScrollView(.vertical, showsIndicators: true) {
                FlowLayout(
                    mode: .scrollable,
                    items: searchState.searchHistory,
                    itemSpacing: 8
                ) { _, item in
                    Text(item)
                        .padding(.top, 6)
                        .padding(.bottom, 6)
                        .padding(.leading, 8)
                        .padding(.trailing, 8)
                        .frame(height: 32)
                        .font(.init(UIFont.primaryTextRegular))
                        .background(Color("SearchHistoryItemBackground"))
                        .foregroundColor(Color("SearchHistoryItemText"))
                        .cornerRadius(19)
                        .lineLimit(1)
                        .onTapGesture {
                            searchState.pendingSearchTerm = item
                        }
                }
                .padding(.horizontal)
                .padding(.vertical, 8)
            }
        }
        .alert(isPresented: $showingDeleteConfirmation) {
            Alert(
                title: Text("confirm_deletion_title".localized),
                message: Text("confirm_deletion_message".localized),
                primaryButton: .destructive(Text("delete_button".localized)) {
                    searchState.clearSearchHistory()
                },
                secondaryButton: .cancel(Text("cancel_button".localized))
            )
        }
    }
}

#Preview {
    BookSearchHistory()
}
