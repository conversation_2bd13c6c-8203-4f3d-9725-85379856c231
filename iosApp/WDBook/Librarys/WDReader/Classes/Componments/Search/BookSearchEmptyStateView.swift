//
//  BookSearchEmptyStateView.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/12.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct BookSearchEmptyStateView: View {
    var body: some View {
        VStack(spacing: 12) {
            Text("book_search_empty_title".localized)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.primary)

            Text("book_search_empty_subtitle".localized)
                .font(.system(size: 14))
                .foregroundColor(.gray)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor.systemBackground))
    }
}

#Preview {
    BookSearchEmptyStateView()
}
