//
//  ReuseView.swift
//  WDReader
//
//  Created by <PERSON> on 2021/3/3.
//  Copyright © 2021 <PERSON>. All rights reserved.
//

import Foundation
import UIKit
import SnapKit

class ReuseViewFlowLayout {
    var minimumLineSpacing: CGFloat
    var minimumInteritemSpacing: CGFloat
    var itemSize: CGSize
    var scrollDirection: UICollectionView.ScrollDirection
    init(itemSize:CGSize,scrollDirection: UICollectionView.ScrollDirection = .horizontal,minimumLineSpacing:CGFloat = 0.0,minimumInteritemSpacing:CGFloat = 0.0) {
        self.itemSize = itemSize
        self.scrollDirection = scrollDirection
        self.minimumLineSpacing = minimumLineSpacing
        self.minimumInteritemSpacing = minimumInteritemSpacing
    }
}

//添加接口num，section。
protocol ReuseViewDelegate:UIScrollViewDelegate {
    //预加载
    func reuseView(_ reuseView: ReuseView, prefetchItemsAt indexPaths: [IndexPath])
    
    //获取IndexPath
    func reuseViewCurrentIndexPath(_ reuseView: ReuseView) -> IndexPath
    func reuseView(_ reuseView: ReuseView, previousIndexPathWithCurrentIndexPath currentIndexPath:IndexPath) -> IndexPath?
    func reuseView(_ reuseView: ReuseView, nextIndexPathWithCurrentIndexPath currentIndexPath:IndexPath) -> IndexPath?
    
    //通过IndexPath获取和更新Cell
    func reuseView(_ reuseView: ReuseView, cellForItemAt indexPath: IndexPath) -> ReuseViewCell
    func reuseView(_ reuseView: ReuseView, updateCell cell: ReuseViewCell, forItemAt indexPath: IndexPath, needPrefetch:Bool)
    func reuseView(_ reuseView: ReuseView, willDisplay cell: ReuseViewCell, forItemAt indexPath: IndexPath)
    func reuseView(_ reuseView: ReuseView, didEndDisplaying cell: ReuseViewCell, forItemAt indexPath: IndexPath)
    
    //重新布局：专项的时候
    func reuseViewLayout(_ reuseView: ReuseView, cell:ReuseViewCell, indexPath:IndexPath, size:CGSize)
}

extension ReuseViewDelegate{
    func reuseViewLayout(_ reuseView: ReuseView, cell:ReuseViewCell, indexPath:IndexPath, size:CGSize){
        
    }
}

class ReuseViewCell:UIView{
    var indexPath:IndexPath?
}

class ReuseView: UIView , UIScrollViewDelegate{
    //    |--|--|--|
    //          |--|
    //    0-0.5
    //    0.5-1.5
    //    1.5-2.5
    let layout:ReuseViewFlowLayout
    var scrollView:UIScrollView
    var delegate:ReuseViewDelegate?

    //当前选择（阅读）的位置。每次换页手动更新。
    var selectedIndex:Int = 0  //0,1,2
    var selectedIndexPath:IndexPath = IndexPath()
    
    private let maxNum:Int = 3
    private var contentViews:[ReuseViewCell] = [ReuseViewCell]()
    
    var isScrolling = true
    var lastContentOffset:CGPoint = CGPoint.zero
    var scrollDirection:ScrollDirection = .none
    var ignoreScrollViewDidScroll = true
    var isDidWillLoadData = false //每次滚动都处理
    
    init(frame: CGRect,layout:ReuseViewFlowLayout) {
        self.layout = layout
        scrollView = UIScrollView(frame: .zero)
        scrollView.isPagingEnabled = true
        scrollView.clipsToBounds = false
        scrollView.decelerationRate = .fast
        scrollView.showsHorizontalScrollIndicator = false
        super.init(frame: frame)
        setUpUI()
    }
    
    private func setUpUI() {
        scrollView.frame = CGRect(x: 0, y: 0, width: self.frame.size.width, height: self.frame.size.height)
        scrollView.delegate = self
        self.addSubview(scrollView)
    }
    
    required init?(coder aDecoder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    //初始化数据和UI。
    func initData() {
        guard self.maxNum > 0 else { return }
        
        var indexPaths = [IndexPath]()
        let currentIndexPath = delegate?.reuseViewCurrentIndexPath(self) ?? IndexPath(item: 0, section: 0)
        let previousIndexPath = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: currentIndexPath)
        let nextIndexPath = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: currentIndexPath)
        
        var initIndex:Int = 0
        //最左边
        if previousIndexPath == nil {
            let nextIndexPath2 = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: nextIndexPath ?? currentIndexPath)
            indexPaths.append(currentIndexPath)
            indexPaths.append(nextIndexPath ?? currentIndexPath)
            indexPaths.append(nextIndexPath2 ?? currentIndexPath)
            initIndex = 0
            //最右边
        }else if nextIndexPath == nil {
            let previousIndexPath2 = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: previousIndexPath ?? currentIndexPath)
            indexPaths.append(previousIndexPath2 ?? currentIndexPath)
            indexPaths.append(previousIndexPath ?? currentIndexPath)
            indexPaths.append(currentIndexPath)
            initIndex = maxNum - 1
            //中间
        }else {
            indexPaths.append(previousIndexPath ?? currentIndexPath)
            indexPaths.append(currentIndexPath)
            indexPaths.append(nextIndexPath ?? currentIndexPath)
            initIndex = (maxNum - 1) / 2
        }
        
        for i in 0 ..< indexPaths.count {
            let indexPath = indexPaths[i]
            if let delegate {
                let contentView:ReuseViewCell = delegate.reuseView(self, cellForItemAt: indexPath)
                contentViews.append(contentView)
                addViewToIndex(view: contentView, index: i)
            }
        }
        
        scrollView.contentSize = CGSize(width: CGFloat(maxNum) * scrollView.frame.size.width, height: scrollView.frame.size.height)
        scrollToIndex(index: initIndex, complete: nil)
    }
    
    func isInitData() -> Bool{
        scrollView.contentSize.width > 0
    }
    
    private func addViewToIndex(view:UIView, index:Int) {
        //        view.translatesAutoresizingMaskIntoConstraints = false
        scrollView.addSubview(view)
        view.frame = CGRect(x: CGFloat(index)*scrollView.frame.size.width, y: 0, width: scrollView.frame.size.width, height: scrollView.frame.size.height)
    }
    
    func getCurrentCell() -> UIView?{
        if contentViews.count > selectedIndex {
            return contentViews[selectedIndex]
        }
        return nil
    }
    
    func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
        isScrolling = true
        lastContentOffset = scrollView.contentOffset
        isDidWillLoadData = false
        delegate?.scrollViewWillBeginDragging?(scrollView)
    }
    
    //    [a]，(b),{c}
    //    默认
    //    cellfoitem a,b,c
    //
    //    左滑
    //    willdisplay 2
    //    prefetch 3
    //
    //    左滑结束
    //    (b),{c},[...]
    //    didenddisplay 0
    //    换位置
    //
    //    再次左滑
    //    willdisplay d
    //
    //    -----
    //    右滑
    //    willdisplay 0
    //    prefetch -1
    //
    //    右滑结束
    //    {...},[a]，(b),
    //    didenddisplay 2
    //    换位置
    //
    //    再次右滑
    //    willdisplay 0
    //    prefetch -1
    
    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        Log.d("翻页测试：scrollViewDidScroll-开始。目标页\(targetIndex),目标页索引\(targetIndexPath)")
        scrollDirection = scrollView.contentOffset.x > lastContentOffset.x ? .left : .right
        
        if !ignoreScrollViewDidScroll {
            Log.d("翻页测试：scrollViewDidScroll-ignoreScrollViewDidScroll。目标页\(targetIndex),目标页索引\(targetIndexPath)")
            if !isDidWillLoadData{
                Log.d("翻页测试：scrollViewDidScroll-isDidWillLoadData。目标页\(targetIndex),目标页索引\(targetIndexPath)")
                if scrollDirection == .left {
                    willLoadNext()
                }else if scrollDirection == .right{
                    willLoadPre()
                }
                isDidWillLoadData = true
            }
        }
        ignoreScrollViewDidScroll = false
        Log.d("翻页测试：scrollViewDidScroll-结束。目标页\(targetIndex),目标页索引\(targetIndexPath)")
        delegate?.scrollViewDidScroll?(scrollView)
    }
    
    var targetContentOffset:CGPoint = CGPointZero
    func scrollViewWillEndDragging(_ scrollView: UIScrollView, withVelocity velocity: CGPoint, targetContentOffset: UnsafeMutablePointer<CGPoint>) {
        self.targetContentOffset = targetContentOffset.pointee
        
        targetIndex = Int((self.targetContentOffset.x / scrollView.frame.size.width) + 0.5)
        targetIndexPath = contentViews[targetIndex].indexPath ?? IndexPath(item: 0, section: 0)
        
        Log.d("翻页测试：scrollViewWillEndDragging目标页\(targetIndex),目标页索引\(targetIndexPath)")
        delegate?.scrollViewWillEndDragging?(scrollView, withVelocity: velocity, targetContentOffset: targetContentOffset)
    }
    
    func scrollViewDidEndDragging(_ scrollView: UIScrollView, willDecelerate decelerate: Bool) {
        delegate?.scrollViewDidEndDragging?(scrollView, willDecelerate: decelerate)
    }
    
    var targetIndex:Int = 0
    var targetIndexPath:IndexPath = IndexPath(item: 0, section: 0)
    func scrollViewWillBeginDecelerating(_ scrollView: UIScrollView) {
        Log.d("翻页测试：scrollViewWillBeginDecelerating目标页\(targetIndex),目标页索引\(targetIndexPath)")
        delegate?.scrollViewWillBeginDecelerating?(scrollView)
    }
    
    ///是在索引0...3之间滚动的，4只是占位，并未参与滚动
    ///索引 0 , 1 , 2 , 3 , 4
    ///内容 2 , 0 , 1 , 2 , 0
    func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        isScrolling = false
        isDidWillLoadData = false
        
        //right滑
        if targetIndex == 0{ //最左边位置
            if let preIndexPath = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: selectedIndexPath){
                Log.d("翻页测试：上一个滑动结束：有上一页。目标页\(targetIndex),目标页索引\(targetIndexPath),上一页\(preIndexPath)")
                didLoadPre()
                //不是边界
                if let _ = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: preIndexPath){
                    switchPositionWhenPre()
                    scrollToCenter()
                    Log.d("翻页测试：打印当前indexPath：\(contentViews.map({$0.indexPath}))")
                    delegate?.scrollViewDidEndDecelerating?(scrollView)
                    return
                }else{
                    Log.d("翻页测试：上一个滑动结束：没有有上一页。目标页\(targetIndex),目标页索引\(targetIndexPath)")
                    self.selectedIndex = targetIndex
                    self.selectedIndexPath = targetIndexPath
                    delegate?.scrollViewDidEndDecelerating?(scrollView)
                    return
                }
            }
            
            //left滑动
        }else if targetIndex == maxNum - 1 { //最右边位置
            if let nextIndexPath = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: selectedIndexPath){
                Log.d("翻页测试：下一个滑动结束：有下一页。目标页\(targetIndex),目标页索引\(targetIndexPath),下一页\(nextIndexPath)")
                didLoadNext()
                if let _ = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: nextIndexPath){
                    switchPositionWhenNext()
                    scrollToCenter()
                    Log.d("翻页测试：打印当前indexPath：\(contentViews.map({$0.indexPath}))")
                    delegate?.scrollViewDidEndDecelerating?(scrollView)
                    return
                }else{
                    Log.d("翻页测试：下一个滑动结束：没有下一页。目标页\(targetIndex),目标页索引\(targetIndexPath)")
                    self.selectedIndex = targetIndex
                    self.selectedIndexPath = targetIndexPath
                    delegate?.scrollViewDidEndDecelerating?(scrollView)
                    return
                }
            }
        }
        
        self.selectedIndex = targetIndex
        self.selectedIndexPath = targetIndexPath
        
        Log.d("翻页测试：打印当前indexPath：\(contentViews.map({$0.indexPath}))")
        delegate?.scrollViewDidEndDecelerating?(scrollView)
    }
    
    private func willLoadPre() {
        //向前查找的时候，会遇见preIndexPath，preIndexPath2属于不同的section情况。所以willDisplay和prefetchItemsAt没有数据的时候，都需要prefetch。
        if let preIndexPath = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: selectedIndexPath){
            delegate?.reuseView(self, willDisplay: contentViews[max(selectedIndex - 1, 0)], forItemAt: preIndexPath)
            
            if let preIndexPath2 = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: preIndexPath){
                delegate?.reuseView(self, prefetchItemsAt: [preIndexPath2])
            }
        }
    }
    
    private func didLoadPre() {
        guard let lastView = contentViews.last, let indexPath = lastView.indexPath else { return }
        delegate?.reuseView(self, didEndDisplaying: lastView, forItemAt: indexPath)
    }
    
    private func switchPositionWhenPre() {
        for i in 0 ..< contentViews.count{
            if i == contentViews.count - 1 {
                contentViews[i].frame.origin.x = 0
            }else{
                contentViews[i].frame.origin.x = scrollView.frame.size.width * CGFloat(i + 1)
            }
        }
        contentViews = contentViews.sorted { (v0, v1) -> Bool in
            v0.frame.minX < v1.frame.minX
        }
        //此时第一个位置的cell和indexpath都是原来最大的那个。当下次willLoadPre或willLoadNext的时候,才会把值赋值过去。
    }
    
    private func willLoadNext() {
        Log.d("翻页测试：下一页0：")
        if let nextIndexPath = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: selectedIndexPath){
            Log.d("翻页测试：下一页1：\(nextIndexPath)")
            delegate?.reuseView(self, willDisplay: contentViews[min(selectedIndex + 1, maxNum - 1)], forItemAt: nextIndexPath)
            Log.d("翻页测试：下一页2：\(nextIndexPath)")
            if let nextIndexPath2 = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: nextIndexPath){
                Log.d("翻页测试：下一页3：\(nextIndexPath)")
                delegate?.reuseView(self, prefetchItemsAt: [nextIndexPath2])
                Log.d("翻页测试：下一页4：\(nextIndexPath)")
            }
        }
    }
    
    private func didLoadNext(){
        guard let firstView = contentViews.first, let indexPath = firstView.indexPath else { return }
        delegate?.reuseView(self, didEndDisplaying: firstView, forItemAt: indexPath)
    }
    
    private func switchPositionWhenNext(){
        for i in 0 ..< contentViews.count{
            if i == 0 {
                contentViews[i].frame.origin.x = scrollView.frame.size.width * 2
            }else{
                contentViews[i].frame.origin.x = scrollView.frame.size.width * CGFloat(i - 1)
            }
        }
        contentViews = contentViews.sorted { (v0, v1) -> Bool in
            v0.frame.minX < v1.frame.minX
        }
        //此时最后一个位置的cell和indexpath都是原来最小的那个。当下次willLoadNext或willLoadPre的时候,才会把值赋值过去。
    }
}

extension ReuseView{
    
    //重新加载indexpath，会触发cellForItemAt方法
    func reload(indexPath:IndexPath,replaceIndexPath:IndexPath? = nil){
        let oldIndexPath = replaceIndexPath ?? indexPath
        for cell in contentViews{
            if cell.indexPath == oldIndexPath {
                delegate?.reuseView(self, updateCell: cell, forItemAt: indexPath, needPrefetch: true)
            }
        }
    }
    //加载当前page，使用新的indexpath，会触发cellForItemAt方法
    func reloadAndGoto(indexPath:IndexPath){
        guard let firstView = contentViews.first, let lastView = contentViews.last, contentViews.count > 1  else { return }
        let index1View = contentViews[1]
        let previousIndexPath = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: indexPath)
        let nextIndexPath = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: indexPath)
        
        var toIndex:Int = 0
        //最左边
        if previousIndexPath == nil{
            let nextIndexPath2 = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: nextIndexPath ?? indexPath)
            
            delegate?.reuseView(self, updateCell: firstView, forItemAt: indexPath, needPrefetch: true)
            delegate?.reuseView(self, updateCell: index1View, forItemAt: nextIndexPath ?? indexPath, needPrefetch: false)
            delegate?.reuseView(self, updateCell: lastView, forItemAt: nextIndexPath2 ?? indexPath, needPrefetch: false)
            
            delegate?.reuseView(self, prefetchItemsAt: [nextIndexPath ?? indexPath,nextIndexPath2 ?? indexPath])
            toIndex = 0
        //最右边
        }else if nextIndexPath == nil{
            let previousIndexPath2 = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: previousIndexPath ?? indexPath)
            delegate?.reuseView(self, updateCell: firstView, forItemAt: previousIndexPath2 ?? indexPath, needPrefetch: false)
            delegate?.reuseView(self, updateCell: index1View, forItemAt: previousIndexPath ?? indexPath, needPrefetch: false)
            delegate?.reuseView(self, updateCell: lastView, forItemAt: indexPath, needPrefetch: true)
            
            delegate?.reuseView(self, prefetchItemsAt: [previousIndexPath2 ?? indexPath,previousIndexPath ?? indexPath])
            toIndex = maxNum - 1
        //中间
        }else{
            delegate?.reuseView(self, updateCell: firstView, forItemAt: previousIndexPath ?? indexPath, needPrefetch: false)
            delegate?.reuseView(self, updateCell: index1View, forItemAt: indexPath, needPrefetch: true)
            delegate?.reuseView(self, updateCell: lastView, forItemAt: nextIndexPath ?? indexPath, needPrefetch: false)
            
            delegate?.reuseView(self, prefetchItemsAt: [previousIndexPath ?? indexPath,nextIndexPath ?? indexPath])
            toIndex = (maxNum - 1) / 2
        }
        scrollToIndex(index: toIndex, complete: nil)
    }
    
    func layout(size:CGSize){
        scrollView.frame = CGRect(x: 0, y: 0, width: size.width, height: size.height)
        scrollView.contentSize = CGSize(width: CGFloat(maxNum) * size.width, height: size.height)
        for i in 0 ..< contentViews.count{
            let cell = contentViews[i]
            cell.frame = CGRect(x: size.width * CGFloat(i), y: 0, width: size.width, height: size.height)
            if let indexPath = cell.indexPath {
                delegate?.reuseViewLayout(self, cell: cell, indexPath: indexPath,size:size)
            }
        }
        scrollToIndex(index: selectedIndex, complete: nil)
    }
    
    //跳转换页
    func gotoPrevious(animated: Bool = false,complete:(()->())?){
        //最后一页向前
        if selectedIndex == maxNum - 1{
            if let _ = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: selectedIndexPath){
                willLoadPre()
                didLoadPre()
                scrollToPre(animated: animated, complete: complete)
            }
        //中间的某页向前
        }else{
            if let preIndexPath = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: selectedIndexPath){
                willLoadPre()
                didLoadPre()
                if let _ = delegate?.reuseView(self, previousIndexPathWithCurrentIndexPath: preIndexPath){
                    switchPositionWhenPre()
                    scrollToCenter(animated: animated, complete: complete)
                }else{
                    scrollToPre(animated: animated, complete: complete)
                }
            }
        }
    }
    
    func gotoNext(animated: Bool = false,complete:(()->())?){
        if selectedIndex == 0{
            if let _ = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: selectedIndexPath){
                willLoadNext()
                didLoadNext()
                scrollToNext(animated: animated, complete: complete)
            }
        }else{
            if let nextIndexPath = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: selectedIndexPath){
                willLoadNext()
                didLoadNext()
                if let _ = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: nextIndexPath){
                    switchPositionWhenNext()
                    scrollToCenter(animated: animated, complete: complete)
                }else{
                    scrollToNext(animated: animated, complete: complete)
                }
            }
        }
//        scrollToNextPage(complete: complete)
    }
    
    //暂未使用。
    //TODO:滑动效果翻页。
    func scrollToNextPage(complete:(()->())?){
        if selectedIndex == 0{
            if let _ = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: selectedIndexPath){
                willLoadNext()
                didLoadNext()
                UIView.animate(withDuration: 0.5) {
                    self.scrollToNext(animated: false, complete: nil)
                } completion: { finished in
                    complete?()
                }
            }
        }else{
            if let nextIndexPath = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: selectedIndexPath){
                willLoadNext()
                didLoadNext()
                if let _ = delegate?.reuseView(self, nextIndexPathWithCurrentIndexPath: nextIndexPath){
                    switchPositionWhenNext()
                    UIView.animate(withDuration: 0.5) {
                        self.scrollToCenter(animated: false, complete: nil)
                    } completion: { finished in
                        complete?()
                    }
                }else{
                    UIView.animate(withDuration: 0.5) {
                        self.scrollToNext(animated: false, complete: nil)
                    } completion: { finished in
                        complete?()
                    }
                }
            }
        }
    }
    
    //翻页或跳页工具函数
    private func scrollToCenter(animated:Bool = false,complete:(()->())? = nil) {
        scrollToIndex(index: (maxNum - 1)/2,animated: animated, complete: complete)
    }
    
    private func scrollToPre(animated:Bool = false,complete:(()->())?) {
        scrollToIndex(index: selectedIndex - 1,animated: animated, complete: complete)
    }
    
    private func scrollToNext(animated:Bool = false,complete:(()->())?) {
        scrollToIndex(index: selectedIndex + 1,animated: animated, complete: complete)
    }
    
    private func scrollToIndex(index:Int,animated: Bool = false,complete:(()->())?) {
        ignoreScrollViewDidScroll = true
        
        selectedIndex = index
        if let ip = indexPathOfIndex(index: index){
            selectedIndexPath = ip
        }else{
            selectedIndex = 0
            selectedIndexPath = indexPathOfIndex(index: 0) ?? IndexPath()
        }
        
        scrollView.contentOffset = CGPoint(x: self.scrollView.frame.size.width * CGFloat(index), y: 0)
        if animated{
            UIView.transition(with: self.scrollView, duration: 0.12, options:.transitionCrossDissolve, animations: { () -> Void in
                
            }) { (finished) -> Void in
                complete?()
            }
        }
    }
    
    private func indexPathOfIndex(index:Int) -> IndexPath?{
        if index >= 0 && index < contentViews.count{
            return contentViews[index].indexPath
        }else{
            return nil
        }
    }
}

