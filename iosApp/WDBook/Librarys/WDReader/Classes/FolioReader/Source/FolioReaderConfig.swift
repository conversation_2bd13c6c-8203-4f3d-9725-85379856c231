//
//  FolioReaderConfig.swift
//  FolioReaderKit
//
//  Created by <PERSON><PERSON><PERSON> on 08/04/15.
//  Copyright (c) 2015 Folio Reader. All rights reserved.
//

import UIKit


// MARK: - FolioReaderScrollDirection

/// Defines the Reader scrolling direction
///
/// - vertical: Section and content scroll on vertical.
/// - horizontal: Section and content scroll on horizontal.
/// - horizontalWithVerticalContent: Sections scroll horizontal and content scroll on vertical.
/// - defaultVertical: The default scroll direction, if not overridden; works as .vertical.
public enum FolioReaderScrollDirection: Int {
    case vertical
    case horizontal

    /// The current scroll direction
    ///
    /// - Returns: Returns `UICollectionViewScrollDirection`
    func collectionViewScrollDirection() -> UICollectionView.ScrollDirection {
        switch self {
        case .vertical:
            return .vertical
        case .horizontal:
            return .horizontal
        }
    }
}


// MARK: - FolioReaderConfig

/**
 Defines the Reader custom configuration
 */
open class FolioReaderConfig: NSObject {

    /// Used to distinguish between multiple or different reader instances. The content of the user defaults (font settings etc.) depends on this identifier. The default is `nil`.
    open var identifier: String?

    /// If `canChangeScrollDirection` is `true` it will be overrided by user's option.
    open var scrollDirection: FolioReaderScrollDirection = .horizontal
    
    public convenience init(withIdentifier identifier: String) {
        self.init()

        self.identifier = identifier
    }

    /**
     Simplify attibution of values based on direction, basically is to avoid too much usage of `switch`,
     `if` and `else` statements to check. So basically this is like a shorthand version of the `switch` verification.

     For example:
     ```
     let pageOffsetPoint = readerConfig.isDirection(CGPoint(x: 0, y: pageOffset), CGPoint(x: pageOffset, y: 0), CGPoint(x: 0, y: pageOffset))
     ```

     As usually the `vertical` direction and `horizontalContentVertical` has similar statements you can basically hide the last
     value and it will assume the value from `vertical` as fallback.
     ```
     let pageOffsetPoint = readerConfig.isDirection(CGPoint(x: 0, y: pageOffset), CGPoint(x: pageOffset, y: 0))
     ```

     - parameter vertical:                  Value for `vertical` direction
     - parameter horizontal:                Value for `horizontal` direction
     - parameter horizontalContentVertical: Value for `horizontalWithVerticalContent` direction, if nil will fallback to `vertical` value

     - returns: The right value based on direction.
     */
    func isDirection<T> (_ vertical: T, _ horizontal: T) -> T {
        switch self.scrollDirection {
        case .vertical:       return vertical
        case .horizontal:                       return horizontal
        }
    }
}
