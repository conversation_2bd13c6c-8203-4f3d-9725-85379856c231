//
//  FRBook.swift
//  FolioReaderKit
//
//  Created by <PERSON><PERSON><PERSON> on 09/04/15.
//  Extended by <PERSON> on 12/30/15
//  Copyright (c) 2015 Folio Reader. All rights reserved.
//

import UIKit

open class FRBook: NSObject {
    var metadata = FRMetadata()
    var spine = FRSpine()
    var smils = FRSmils()
    var version: Double?
    
    public var opfResource: FRResource!
    public var tocResource: FRResource?
    public var uniqueIdentifier: String?
    public var coverImage: FRResource?
    public var name: String?
    public var bookBasePath: String! //added by zk
    public var resources = FRResources()
    public var tableOfContents: [FRTocReference]!  //源目录，含层级关系。（ncx文件。html文件全，锚点id不全）
    public var flatTableOfContents: [FRTocReference]!  //展平第一，二级目录。第三级目录不展平。
    public var bibleTableOfContents: [FRTocReference] = [FRTocReference]()
    
    fileprivate var flatAllTableOfContents:[FRTocReference]?

    var hasAudio: Bool {
        return smils.smils.count > 0
    }

    var title: String? {
        return metadata.titles.first
    }

    var authorName: String? {
        return metadata.creators.first?.name
    }

    // MARK: - Media Overlay Metadata
    // http://www.idpf.org/epub/301/spec/epub-mediaoverlays.html#sec-package-metadata

    var duration: String? {
        return metadata.find(byProperty: "media:duration")?.value
    }

    var activeClass: String {
        guard let className = metadata.find(byProperty: "media:active-class")?.value else {
            return "epub-media-overlay-active"
        }
        return className
    }

    var playbackActiveClass: String {
        guard let className = metadata.find(byProperty: "media:playback-active-class")?.value else {
            return "epub-media-overlay-playing"
        }
        return className
    }

    // MARK: - Media Overlay (SMIL) retrieval

    /**
     Get Smil File from a resource (if it has a media-overlay)
     */
    func smilFileForResource(_ resource: FRResource?) -> FRSmilFile? {
        guard let resource = resource, let mediaOverlay = resource.mediaOverlay else { return nil }

        // lookup the smile resource to get info about the file
        guard let smilResource = resources.findById(mediaOverlay) else { return nil }

        // use the resource to get the file
        return smils.findByHref(smilResource.href)
    }

    func smilFile(forHref href: String) -> FRSmilFile? {
        return smilFileForResource(resources.findByHref(href))
    }

    func smilFile(forId ID: String) -> FRSmilFile? {
        return smilFileForResource(resources.findById(ID))
    }
    
    // @NOTE: should "#" be automatically prefixed with the ID?
    func duration(for ID: String) -> String? {
        return metadata.find(byProperty: "media:duration", refinedBy: ID)?.value
    }
}


extension FRBook{
    func getFlatAllTableOfContents() -> [FRTocReference]{
        initFlatAllTOC()
        return flatAllTableOfContents!
    }
    
    private func initFlatAllTOC() {
        if flatAllTableOfContents == nil{
            flatAllTableOfContents = flatTOC(tableOfContents)
        }
    }
    
    private func flatTOC(_ reference:[FRTocReference]) -> [FRTocReference] {
        var tocItems = [FRTocReference]()

        for item in reference {
            tocItems.append(item)
            tocItems.append(contentsOf: tocChildren(item))
        }
        return tocItems
    }

    private func tocChildren(_ item: FRTocReference) -> [FRTocReference] {
        var tocItems = [FRTocReference]()

        item.children.forEach {
            tocItems.append($0)
            tocItems.append(contentsOf: tocChildren($0))
        }
        return tocItems
    }
    
}
