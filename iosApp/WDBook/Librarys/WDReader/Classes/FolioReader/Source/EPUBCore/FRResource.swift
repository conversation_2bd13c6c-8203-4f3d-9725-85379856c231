//
//  FRResource.swift
//  FolioReaderKit
//
//  Created by <PERSON><PERSON><PERSON> on 29/04/15.
//  Copyright (c) 2015 Folio Reader. All rights reserved.
//

import UIKit

open class FRResource: NSObject {
    var id: String!
    var properties: String?
    var mediaType: MediaType!
    var mediaOverlay: String?
    
    public var href: String!
    public var fullHref: String!

    func basePath() -> String! {
        if href == nil || href.isEmpty { return nil }
        var paths = fullHref.components(separatedBy: "/")
        paths.removeLast()
        return paths.joined(separator: "/")
    }
    
    func equalHref(other:FRResource) -> Bool {
        return href == other.href
    }
}

// MARK: Equatable

func ==(lhs: FRResource, rhs: FRResource) -> Bool {
//    return lhs.id == rhs.id && lhs.href == rhs.href
    return lhs.href == rhs.href
}

