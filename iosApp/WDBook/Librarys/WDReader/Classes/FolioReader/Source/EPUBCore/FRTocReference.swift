//
//  FRTocReference.swift
//  FolioReaderKit
//
//  Created by <PERSON><PERSON><PERSON> on 06/05/15.
//  Copyright (c) 2015 Folio Reader. All rights reserved.
//

import UIKit

open class FRTocReference: NSObject {
    var children: [FRTocReference]!

    public var title: String!
    public var resource: FRResource?
    public var fragmentID: String?
    
    convenience init(title: String, resource: FRResource?, fragmentID: String = "") {
        self.init(title: title, resource: resource, fragmentID: fragmentID, children: [FRTocReference]())
    }

    init(title: String, resource: FRResource?, fragmentID: String, children: [FRTocReference]) {
        self.resource = resource
        self.title = title
        self.fragmentID = fragmentID
        self.children = children
    }
    
    //只判断resource和framentID是否相等的方法。从bible目录 -> toc目录 -> page。
    func equalHref(other:FRTocReference) -> Bool{
        guard let res = resource,
              let otherRes = other.resource else {
            return false
        }
        return res.equalHref(other: otherRes)
    }
    
    func resourceHrefAndFragmentId() -> String{
        if let r = resource, let href = r.href{
            if let fid = fragmentID, !fid.isEmpty{
                return "\(href)#\(fid)"
            }else{
                return href
            }
        }else{
            return ""
        }
    }
    
    func equal(other:FRTocReference) -> Bool{
        return equalHref(other: other) && resourceHrefAndFragmentId() == other.resourceHrefAndFragmentId()
    }
}

// MARK: Equatable

func ==(lhs: FRTocReference, rhs: FRTocReference) -> Bool {
    return lhs.title == rhs.title && lhs.fragmentID == rhs.fragmentID
}


