//
//  WDReaderCenter.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/30.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import UIKit

class WDReaderCenter{
    private init(){
        
    }
    static let shared:WDReaderCenter = WDReaderCenter()
    var fbBook:FRBook!
    
    ///滚动相关
    var pointNow = CGPoint.zero
    var isScrolling = false
    var pageScrollDirection = ScrollDirection()
    
    func loadEpub(epubPath:String){
        guard (epubPath.isEmpty == false) else {
            print("Epub path is nil.")
            
            return
        }
        let parsedBook = try? FREpubParser().readEpub(epubPath: epubPath, removeEpub: false, unzipPath: nil)
        fbBook = parsedBook
    }
    

    var chapterFilesMaxCount:Int{
        fbBook.spine.spineReferences.count
    }
    
    /// 通过资源得到目录。返回一级目录。
    /// - Parameter resource: <#resource description#>
    /// - Returns: <#description#>
    func getTOCResource(resource:FRResource) -> FRTocReference?{
        
        var resultResource:FRTocReference?
        
        for item in fbBook.tableOfContents {
            resultResource = item
            if resource == item.resource{
                return resultResource
            } else if let children = item.children, children.isEmpty == false {
                if searchResource(resource: resource, tocs: children){
                    return resultResource
                }
            }
        }
        
        return nil
    }
    
    /// 递归查找是否存在
    private func searchResource(resource:FRResource,tocs:[FRTocReference]) -> Bool{
        var foundResource:FRTocReference?
        func search(_ items: [FRTocReference]){
            for item in items {
                guard foundResource == nil else { break }

                if resource == item.resource{
                    foundResource = item
                    break
                } else if let children = item.children, children.isEmpty == false {
                    search(children)
                }
            }
        }
        search(tocs)
        return foundResource != nil
    }
    
    /// 包含子级别且展开的目录，顺序排列目录。（保存上次对目录操作的版本。使用fbBook.tableOfContents）
    /// - Returns: <#description#>
//    func resetTocItems() -> [FRTocReference] {
//        var tocItems = [FRTocReference]()
//
//        for item in fbBook.tableOfContents {
//            tocItems.append(item)
//            if item.isExpand {
//                tocItems.append(contentsOf: getTocChildren(item,outlineLevel:1))
//            }
//
//        }
//        return tocItems
//    }

    func getTocChildren(_ reference: FRTocReference,outlineLevel:Int) -> [FRTocReference] {
        var tocItems = [FRTocReference]()

        for item in reference.children {
            item.outlineLevel = outlineLevel
            tocItems.append(item)
            if item.isExpand {
                tocItems.append(contentsOf: getTocChildren(item,outlineLevel: outlineLevel + 1))
            }
        }
        
        return tocItems
    }
    
    /// 通过href对比，获取tocReference。（bible目录到epub目录的转换）
    public func getTocReference(href:String) -> FRTocReference? {
        var toc: FRTocReference?
        
        func search(_ items: [FRTocReference]) {
            for item in items {
                guard toc == nil else { break }
                
                if let resource = item.resource,
                   resource.href == href{
                    toc = item
                } else if let children = item.children, children.isEmpty == false {
                    search(children)
                }
            }
        }
        search(fbBook.tableOfContents)
        
        return toc
    }
    

    public func getFileChapterIndex(_ reference:FRTocReference) -> Int {
        let references = WDReaderCenter.shared.fbBook.spine.spineReferences
        for i in 0 ..< references.count{
            let item = references[i]
            let resource = item.resource
            if resource == reference.resource{
                return i
            }
        }
        return 0
    }
    
    public func getFileChapterIndex(href:String) -> Int {
        let references = WDReaderCenter.shared.fbBook.spine.spineReferences
        for i in 0 ..< references.count{
            if references[i].resource.href == href{
                return i
            }
        }
        return 0
    }
    
    //MARK:滚动相关
    func updatePageScrollDirection(inScrollView scrollView: UIScrollView, forScrollType scrollType: ScrollType = .page) {
       
        let config =  WDReaderConfig.folioReaderConfig
        
        let scrollViewContentOffsetForDirection = scrollView.contentOffset.forDirection(withConfiguration: config, scrollType: scrollType)
        let pointNowForDirection = pointNow.forDirection(withConfiguration: config, scrollType: scrollType)
        // The movement is either positive or negative. This happens if the page change isn't completed. Toggle to the other scroll direction then.
        let isCurrentlyPositive = (pageScrollDirection == .left || pageScrollDirection == .up)

        if (scrollViewContentOffsetForDirection < pointNowForDirection) {
            pageScrollDirection = .negative(withConfiguration: config, scrollType: scrollType)
        } else if (scrollViewContentOffsetForDirection > pointNowForDirection) {
            pageScrollDirection = .positive(withConfiguration: config, scrollType: scrollType)
        } else if (isCurrentlyPositive == true) {
            pageScrollDirection = .negative(withConfiguration: config, scrollType: scrollType)
        } else {
            pageScrollDirection = .positive(withConfiguration: config, scrollType: scrollType)
        }
    }
    
    func getCurrentIndexPath(collectionView:UICollectionView) -> IndexPath {
        let indexPaths = collectionView.indexPathsForVisibleItems
        var indexPath = IndexPath()

        if indexPaths.count > 1 {
            let first = indexPaths.first!
            let last = indexPaths.last!

            switch self.pageScrollDirection {
            case .up, .left:
                if first.compare(last) == .orderedAscending {
                    indexPath = last
                } else {
                    indexPath = first
                }
            default:
                if first.compare(last) == .orderedAscending {
                    indexPath = first
                } else {
                    indexPath = last
                }
            }
        } else {
            indexPath = indexPaths.first ?? IndexPath(item: 0, section: 0)
        }

        return indexPath
    }
}
