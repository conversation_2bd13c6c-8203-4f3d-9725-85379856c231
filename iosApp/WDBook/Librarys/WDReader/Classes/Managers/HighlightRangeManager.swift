//
//  HighlightRangeManager.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/5/21.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import CoreGraphics
import Foundation
import shared

class HighlightRangeManager {
    static var defaultStyle: HighlightStyle {
        HighlightStyle.defaultStyle
    }

    private(set) static var chapterNotesDic: [String: [Note]]? // <href:[Note]>

    static func loadNotes(chapterPath: String) {
        let noteEntityList = WDBookUserSDK.shared.getNoteEntityListByPagePath(resourceId: WDReaderConfig.resourceId, pagePath: chapterPath)
        Log.d("生命周期测试:笔记:\(WDReaderConfig.resourceId),\(chapterPath),\(noteEntityList)")
        if chapterNotesDic == nil {
            chapterNotesDic = [String: [Note]]()
        }
        chapterNotesDic?[chapterPath] = noteEntityList.map { entity in
            Note(noteEntity: entity)
        }.sorted { $0.createTime < $1.createTime }
        Log.d(String(describing: chapterNotesDic))
    }

    static func add(note: Note, chapterPath: String, isForMerge: Bool = false) {
        if chapterNotesDic == nil {
            chapterNotesDic = [String: [Note]]()
        }
        if chapterNotesDic?[chapterPath] == nil {
            chapterNotesDic?[chapterPath] = [Note]()
        }

        let noteEntity = NoteEntity()
        noteEntity.tocTitle = note.tocTitle
        noteEntity.summary = note.summary // 引用内容
        noteEntity.noteText = note.noteText
        noteEntity.resourceId = WDReaderConfig.resourceId
        noteEntity.pagePath = note.chapterPath
        noteEntity.filePosition = Int32(note.filePosition)
        noteEntity.highlightColorType = note.style.type
        noteEntity.markStyle = note.style.isUnderline ? 1 : 0
        noteEntity.wordStartOffset = Int32(note.range.location)
        noteEntity.wordEndOffset = Int32(note.range.location + note.range.length)
        //        noteEntity.createTime = Int64(note.date.timeIntervalSince1970 * 1000) //TODO:乘以1000？
        //        noteEntity.noteLabel = //保留
        noteEntity.dataStatus = DataStatus.normal.value
        WDBookUserSDK.shared.saveNote(noteEntity: noteEntity)
        note.noteEntity = noteEntity
        note.createTime = Date(timeIntervalSince1970: Double(noteEntity.createTime) / 1000)
        if isForMerge {
            for mergeNote in note.mergeNotes ?? [Note]() {
                remove(note: mergeNote, chapterPath: note.chapterPath, isForce: true)
            }
        }

        HighlightStyle.defaultStyle = note.style

        chapterNotesDic?[chapterPath]?.append(note)
        // 排序
        chapterNotesDic?[chapterPath]?.sort(by: { l, r in
            if l.range.location == r.range.location {
                return l.range.length < r.range.length
            } else {
                return l.range.location < r.range.location
            }
        })
    }

    static func update(note: Note) {
        if let entity = note.noteEntity {
            entity.noteText = note.noteText
            entity.highlightColorType = note.style.type
            entity.markStyle = note.style.isUnderline ? 1 : 0
            WDBookUserSDK.shared.saveNote(noteEntity: entity)
            entity.conflictRemoteId = ""
            HighlightStyle.defaultStyle = note.style
            note.createTime = Date(timeIntervalSince1970: Double(entity.createTime) / 1000)
        }
    }

    static func remove(note: Note, chapterPath: String, isForce: Bool = false) {
        // 删除db
        if let entity = note.noteEntity, let dataId = entity.dataId {
            if isForce {
                WDBookUserSDK.shared.deleteNote(dataId: dataId)
            } else {
                WDBookUserSDK.shared.removeNote(dataId: dataId)
            }
        }

        // 删除内存
        if let hrefArr = chapterNotesDic?[chapterPath], let index = hrefArr.firstIndex(where: { $0.range == note.range }) {
            chapterNotesDic?[chapterPath]?.remove(at: index)
        }
    }

    static func containsNote(href: String, range: NSRange) -> Note? {
        if let notes = chapterNotesDic?[href] {
            let sortedNotes = notes.sorted { $0.createTime > $1.createTime }
            for note in sortedNotes {
                if note.showRange.contains(range) {
                    return note
                }
            }
        }
        return nil
    }

    static func isNeedMerge(href: String, range: NSRange) -> Bool {
//        //超过两条需要合并。
//               相交的数量>=2
//        //没超过两条：
//             区域大于， contains, length > （合并）
//                相等， contains， equal  (不合并)
//                相交， intersection, intersection.length < 双方 （合并）
//                小于， contains，length > （不合并）
        var intersectionCount = 0
        if let notes = chapterNotesDic?[href] {
            for note in notes {
                let intersectionRange = note.showRange.safeIntersection(with: range)
                if intersectionRange.location >= 0, intersectionRange.length > 0 {
                    intersectionCount += 1
                    if intersectionCount >= 2 {
                        return true
                    }
                    if (range.contains(note.showRange) && range.length > note.showRange.length)
                        || (intersectionRange.length < note.showRange.length && intersectionRange.length < range.length)
                    {
                        return true
                    }
                }
            }
        }
        return false
    }

    static func generateMergeNote(href: String, range: NSRange) -> Note {
        var resultNotes = [Note]()
        var combinRange = range
        if let notes = chapterNotesDic?[href] {
            for note in notes {
                let intersectionRange = note.showRange.safeIntersection(with: range)
                if intersectionRange.location >= 0, intersectionRange.length > 0 {
                    resultNotes.append(note)
                    combinRange = combinRange.union(note.showRange)
                }
            }
        }
        resultNotes.sort { $0.createTime < $1.createTime }

        // Make sure the combined range is within bounds of the current chapter
        var summary = ""
        if let chapter = Paginator.current.currentChapter {
            let safeRange = combinRange.constrainedTo(length: chapter.displayedAttributedString.length)
            summary = chapter.displayedAttributedString.safeAttributedSubstring(from: safeRange).resetImageToBlank().string
        }

        let newNote = Note(chapterPath: href, range: combinRange, summary: summary)
        newNote.mergeNotes = [Note]()

//        合并笔记内容规则：按创建时间升序，每条笔记内容为 "yyyy/MM/dd HH:mm"+"\n"+"笔记内容" 笔记之间用 "\n\n" 分割
        var content = ""
        for i in 0 ..< resultNotes.count {
            let note = resultNotes[i]
            newNote.mergeNotes?.append(note)

            if !note.noteText.isEmpty {
                if i != 0 && content.count > 0 {
                    content += "\n\n"
                }
                content += note.createTime.yyyymmddhhmm + "\n" + note.noteText
            }
        }

        newNote.noteText = content
        return newNote
    }

    static func isNeedMergeForHighlight(href: String, range: NSRange) -> Bool {
        isNeedMerge(href: href, range: range) && isAllNoteAreHighlight(href: href, range: range)
    }

    private static func isAllNoteAreHighlight(href: String, range: NSRange) -> Bool {
        var resultNotes = [Note]()
        var combinRange = range
        if let notes = chapterNotesDic?[href] {
            for note in notes {
                let intersectionRange = note.showRange.safeIntersection(with: range)
                if intersectionRange.location >= 0, intersectionRange.length > 0 {
                    resultNotes.append(note)
                    combinRange = combinRange.union(note.showRange)
                }
            }
        }
        resultNotes.sort { $0.createTime < $1.createTime }

        for i in 0 ..< resultNotes.count {
            let note = resultNotes[i]
            if !note.noteText.isEmpty {
                return false
            }
        }
        return true
    }

    static func generateMergeHighlight(href: String, range: NSRange) -> Note {
        var resultNotes = [Note]()
        var combinRange = range
        if let notes = chapterNotesDic?[href] {
            for note in notes {
                let intersectionRange = note.showRange.safeIntersection(with: range)
                if intersectionRange.location >= 0, intersectionRange.length > 0 {
                    resultNotes.append(note)
                    combinRange = combinRange.union(note.showRange)
                }
            }
        }
        resultNotes.sort { $0.createTime < $1.createTime }

        // Make sure the combined range is within bounds of the current chapter
        var summary = ""
        if let chapter = Paginator.current.currentChapter {
            let safeRange = combinRange.constrainedTo(length: chapter.displayedAttributedString.length)
            summary = chapter.displayedAttributedString.safeAttributedSubstring(from: safeRange).resetImageToBlank().string
        }

        let newNote = Note(chapterPath: href, range: combinRange, summary: summary)
        newNote.mergeNotes = [Note]()
        for i in 0 ..< resultNotes.count {
            let note = resultNotes[i]
            newNote.mergeNotes?.append(note)
        }
        newNote.noteText = ""
        return newNote
    }

    static func clear() {
        chapterNotesDic = nil
    }
}
