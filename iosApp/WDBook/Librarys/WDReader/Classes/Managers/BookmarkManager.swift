//
//  BookmarkManager.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2021/6/9.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared

class BookmarkManager {
    
    static func get(chapterPath:String, range:NSRange) -> BookmarkEntity? {
        WDBookUserSDK.shared.getBookmarkEntity(resourceId: WDReaderConfig.resourceId, pagePath: chapterPath, firstWordLocation: range.location, lastWordLocation: range.location + range.length - 1)
    }
    
    //range是页面range。
    static func add(chapterPath:String, range:NSRange) {
        let bookmarkEntity = BookmarkEntity()
        bookmarkEntity.tocTitle = Paginator.current.getSubLevelChapterName(indexPath: Paginator.current.currentIndexPath) ?? ""
        bookmarkEntity.resourceId = WDReaderConfig.resourceId
        bookmarkEntity.pagePath = chapterPath
        bookmarkEntity.pathName = chapterPath
        bookmarkEntity.filePosition = Int32(WDReaderCenter.shared.getFileChapterIndex(href: chapterPath))
        bookmarkEntity.firstWordOffset = Int32(range.location)
        let summaryRange = range.intersection(NSRange(location: range.location, length: range.location + Int(Constants().BOOKMARK_SUMMARY_DATA_LENGTH)))!
        bookmarkEntity.summary = Paginator.current.currentChapter?.displayedAttributedString.attributedSubstring(from: summaryRange).resetImageToBlank().string ?? ""
        //        bookmarkEntity.createTime = Int64(note.date.timeIntervalSince1970 * 1000) //TODO:乘以1000？
        //        bookmarkEntity.localStatus = //默认0，-1为已删除。
        WDBookUserSDK.shared.saveBookmark(entity: bookmarkEntity)
    }
    
    static func remove(bookmark:BookmarkEntity) {
        WDBookUserSDK.shared.deleteBookmark(dataId: bookmark.dataId ?? "")
    }
}
