//
//  SelectedRangeManager.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/5/12.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import CoreGraphics

class SelectedRangeManager {
    static let MAX_SELECTED_PAGE_COUNT = 2
    static private(set) var chapterPath:String?
    static var range:NSRange{
        let minValue = min(leftLocation, rightLocation)
        let maxValue = max(leftLocation, rightLocation)
        return NSRange(location: minValue, length: maxValue - minValue)
//        return NSRange(location: 0, length: 1)
    }
    
    static private(set) var touchOriginRange:NSRange?
    static private(set) var isChangingRightCursor:Bool = false
    static  var isTouchCursor = false
    
    static private(set) var leftLocation:Int = 0
    static private(set) var rightLocation:Int = 0
    static private(set) var fiexdPosition:Int = 0
    
    static func setTouchOriginRange(_ range:NSRange, chapterPath:String? = nil) {
        touchOriginRange = range
        leftLocation = range.location
        rightLocation = range.location + range.length
        self.chapterPath = chapterPath
    }
    
    static func setIsChangingRightCursor(_ b:Bool) {
        isChangingRightCursor = b
        if isChangingRightCursor{
            fiexdPosition = leftLocation
        }else{
            fiexdPosition = rightLocation
        }
    }
    
    static func updateRange(chapterPath:String?, newLocation:Int) {
        self.chapterPath = chapterPath
        if isChangingRightCursor{
            return rightLocation = newLocation
        }else{
            return leftLocation = newLocation
        }
    }
    
    static func computeRange(chapterPath:String?, newLocation:Int) -> NSRange{
        var tmpLeftLocation = leftLocation
        var tmpRightLocation = rightLocation
        if isChangingRightCursor{
            tmpRightLocation = newLocation
        }else{
            tmpLeftLocation = newLocation
        }
        
        let minValue = min(tmpLeftLocation, tmpRightLocation)
        let maxValue = max(tmpLeftLocation, tmpRightLocation)
        return NSRange(location: minValue, length: maxValue - minValue)
    }
    static func endDrag() {
        
    }
//    static func update(href:String?,range:NSRange?){
////        self.href = "EPUB/54642.xhtml"
////        self.range = NSRange(location: 1505,length: 500)
//        self.href = href
//        self.range = range
//        Log.d("更新选中区域:\(self.href),\(self.range)")
//    }
    
    static func isEmpty() -> Bool{
        chapterPath == nil || range.length == 0
    }
    
    static func clear(){
        touchOriginRange = nil
        chapterPath = nil
        leftLocation = 0
        rightLocation = 0
        fiexdPosition = 0
    }
}

extension SelectedRangeManager{
    static func pageCount(chapterPath:String, newLocation:Int) -> Int {
        Paginator.current.pageCount(href: chapterPath,range: calculateRange(chapterPath: chapterPath, newLocation: newLocation))
    }
    
    private static func calculateRange(chapterPath:String?, newLocation:Int) -> NSRange {
        if isChangingRightCursor{
            let rightLocationTemp = newLocation
            
            let minv = min(leftLocation, rightLocationTemp)
            let maxv = max(leftLocation, rightLocationTemp)
            return NSRange(location: minv, length: maxv - minv)
        }else{
            let leftLocationTemp = newLocation
            
            let minv = min(leftLocationTemp, rightLocation)
            let maxv = max(leftLocationTemp, rightLocation)
            return NSRange(location: minv, length: maxv - minv)
        }
    }
}
