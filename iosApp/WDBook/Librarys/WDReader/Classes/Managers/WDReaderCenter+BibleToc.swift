//
//  WDReaderCenter+BibleToc.swift
//  WDReader
//
//  Created by <PERSON> on 2021/1/7.
//  Copyright © 2021 <PERSON>. All rights reserved.
//

import Foundation

struct VerseNode {
    let usfm:String
    let chapterNum:Int
    let verseNum:Int
}

extension BibleTocItem{
    //书卷名相同才比较
    func compare(node:VerseNode)->Int{
        if chapterNum < node.chapterNum {
            return -1
        }else if chapterNum == node.chapterNum{
            if verseNum < node.verseNum {
                return -1
            }else if verseNum == node.verseNum{
                return 0
            }else{
                return 1
            }
        }else{
            return 1
        }
    }
}

extension WDReaderCenter{
    //获取圣经目录信息 (经文目录中包含的条目) 用于判断是否有数据，是否展示UI。
    func getBibleToc() -> [BibleTocItem] {
        var tocItems = [BibleTocItem]()

        // 1、从目录文件biblereferences数据-> 中间数据structDic
        //  [书卷英文简写:[章索引:[节索引:["href":xxxx,"fragmentid":xxxxx]]]]
        var structDic = [String:[Int:[Int:[String:String]]]]()
        if fbBook != nil{
            for item in fbBook.bibleTableOfContents {
                var biblefullname = item.title
                biblefullname = String(biblefullname!.replacingOccurrences(of: "wdbible://bible/", with: "").split(separator: "-").first!)
                let array = biblefullname!.split(separator: ".")
                let structName = String(array.first!)
                if structDic[structName] == nil {
                    structDic[structName] = [Int:[Int:[String:String]]]()
                }
                let chapterNum:Int = array.count >= 2 ? Int(array[1]) ?? 1 : 1
                if structDic[structName]![chapterNum] == nil {
                    structDic[structName]![chapterNum] = [Int:[String:String]]()
                }
                let verseNum:Int = array.count >= 3 ? Int(array[2]) ?? 1 : 1
                if structDic[structName]![chapterNum]![verseNum] == nil {
                    structDic[structName]![chapterNum]![verseNum] = [String:String]()
                    structDic[structName]![chapterNum]![verseNum]!["href"] = item.resource!.href
                    structDic[structName]![chapterNum]![verseNum]!["fragmentid"] = item.fragmentID
                }
            }
        }
        
        //2、中间数据structDic -> 构造目录对象数组BibleTocItem
        var structureList = [BibleTocItem]()
        //圣经json。获得human和abbr
        let bookJsons = bibleStructureJsons
        for (structureName,chapters) in structDic{
            if let bookJson = bookJsons.books.filter({$0.usfm == structureName.uppercased()}).first{
                var chapterList = [BibleTocItem]()
                for (chapterNum,verses) in chapters{
                    var verseList = [BibleTocItem]()
                    for (verseNum,verseDic) in verses{
                        let resourceHref = verseDic["href"]
                        let fragmentid = verseDic["fragmentid"]
                        verseList.append(BibleTocItem(structureName: bookJson.human,structureAbbreviation:bookJson.abbr,usfm: bookJson.usfm.lowercased(),chapterNum: chapterNum,verseNum: verseNum,level: .verse, num: verseNum, title: "节", abbreviation: String(verseNum), resuorceHref: resourceHref, fragmentId: fragmentid))
                    }
                    let sortedverseList = verseList.sorted(by: { $0.num < $1.num })
                    chapterList.append(BibleTocItem(structureName: bookJson.human,structureAbbreviation:bookJson.abbr,usfm: bookJson.usfm.lowercased(),level: .chapter, num: chapterNum, title: "章", abbreviation: String(chapterNum), children: sortedverseList))
                }
                let sortedchapterList = chapterList.sorted(by: { $0.num < $1.num })
                let structureIndex = bibleStructureJsons.books.firstIndex{$0.usfm.lowercased() == structureName} ?? 0
                structureList.append(BibleTocItem(structureName: bookJson.human,structureAbbreviation:bookJson.abbr,usfm: bookJson.usfm.lowercased(),level: .structure, num: structureIndex, title: bookJson.human, abbreviation: bookJson.abbr, children: sortedchapterList))
            }
        }
        
        let sortedstructureListt = structureList.sorted(by: { $0.num < $1.num })
        tocItems = sortedstructureListt

        return tocItems
    }
    
    /// 获取对应书卷中所有章节信息。用于显示UI中的数据。
    /// db查询出列表，不设置跳转href和fragmentid。等跳转的时刻，再计算anchor.xml相应经文，得到href和fragmentid跳转位置。
    func getBibleStructureToc() -> [BibleTocItem] {
        var tocItems = [BibleTocItem]()

        // 1、获取经文目录的数据
        //  [书卷英文简写:[章索引:[节索引:["href":xxxx,"fragmentid":xxxxx]]]]
        var structTocDic = [String:[Int:[Int:[String:String?]]]]()
        for item in fbBook.bibleTableOfContents {
            var biblefullname = item.title
            biblefullname = String(biblefullname!.replacingOccurrences(of: "wdbible://bible/", with: "").split(separator: "-").first!)
            let array = biblefullname!.split(separator: ".")
            let structName = String(array.first!)
            if structTocDic[structName] == nil {
                structTocDic[structName] = [Int:[Int:[String:String?]]]()
            }
            let chapterNum:Int = array.count >= 2 ? Int(array[1]) ?? 1 : 1
            if structTocDic[structName]![chapterNum] == nil {
                structTocDic[structName]![chapterNum] = [Int:[String:String?]]()
            }
            let verseNum:Int = array.count >= 3 ? Int(array[2]) ?? 1 : 1
            if structTocDic[structName]![chapterNum]![verseNum] == nil {
                structTocDic[structName]![chapterNum]![verseNum] = [String:String?]()
                structTocDic[structName]![chapterNum]![verseNum]!["href"] = item.resource!.href
                structTocDic[structName]![chapterNum]![verseNum]!["fragmentid"] = item.fragmentID
            }
        }
        
        // 2、获取经文目录对应所有书卷中，所有章节的数据。
        //  [书卷英文简写:[章索引:[节索引:["href":xxxx,"fragmentid":xxxxx]]]]
        var structDic = [String:[Int:[Int:[String:String?]]]]()
        for (key,chapters) in structTocDic{
            let structName = key
            //获取书卷圣经数据库
            let bibleContents = BibleContent.findBibleContents(bookName: structName)
            
            for item in bibleContents {
                if structDic[structName] == nil {
                    structDic[structName] = [Int:[Int:[String:String?]]]()
                }
                let chapterNum:Int = Int(item.USFM.split(separator: ".").last ?? "1") ?? 1
                if structDic[structName]![chapterNum] == nil {
                    structDic[structName]![chapterNum] = [Int:[String:String?]]()
                }
                let verseNum:Int = Int(item.verse)
                if structDic[structName]![chapterNum]![verseNum] == nil {
                    structDic[structName]![chapterNum]![verseNum] = [String:String?]()
                    structDic[structName]![chapterNum]![verseNum]!["href"] = structTocDic[structName]?[chapterNum]?[verseNum]?["href"]
                    structDic[structName]![chapterNum]![verseNum]!["fragmentid"] = structTocDic[structName]?[chapterNum]?[verseNum]?["fragmentid"]
                }
            }
        }
        
        
        //3、中间数据structDic -> 构造目录对象数组BibleTocItem
        var structureList = [BibleTocItem]()
        //圣经json。获得human和abbr
        let bookJsons = bibleStructureJsons
        for (structureName,chapters) in structDic{
            let bookJson = bookJsons.books.filter{$0.usfm == structureName.uppercased()}.first!
            var chapterList = [BibleTocItem]()
            for (chapterNum,verses) in chapters{
                var verseList = [BibleTocItem]()
                for (verseNum,verseDic) in verses{
                    let resourceHref = verseDic["href"]
                    let fragmentid = verseDic["fragmentid"]
                    verseList.append(BibleTocItem(structureName: bookJson.human,structureAbbreviation:bookJson.abbr, usfm: bookJson.usfm.lowercased(),chapterNum: chapterNum,verseNum: verseNum, level: .verse, num: verseNum, title: "节", abbreviation: String(verseNum), resuorceHref: resourceHref ?? nil, fragmentId: fragmentid ?? nil))
                }
                let sortedverseList = verseList.sorted(by: { $0.num < $1.num })
                chapterList.append(BibleTocItem(structureName: bookJson.human,structureAbbreviation:bookJson.abbr,usfm: bookJson.usfm.lowercased(),level: .chapter, num: chapterNum, title: "章", abbreviation: String(chapterNum), children: sortedverseList))
            }
            let sortedchapterList = chapterList.sorted(by: { $0.num < $1.num })
            let structureIndex = bibleStructureJsons.books.firstIndex{$0.usfm.lowercased() == structureName} ?? 0
            structureList.append(BibleTocItem(structureName: bookJson.human,structureAbbreviation:bookJson.abbr,usfm: bookJson.usfm.lowercased(),level: .structure, num: structureIndex, title: bookJson.human, abbreviation: bookJson.abbr, children: sortedchapterList))
        }
        
        let sortedstructureListt = structureList.sorted(by: { $0.num < $1.num })
        tocItems = sortedstructureListt

        return tocItems
    }

    func getBibleFRTocReference(bibleToc:BibleTocItem)->FRTocReference?{
        if bibleToc.fragmentId != nil && bibleToc.fragmentId!.count > 0 {
            let r = FRResource()
            r.href = bibleToc.resuorceHref
            let tocR = FRTocReference(title: "", resource: r)
            tocR.fragmentID = bibleToc.fragmentId
            return tocR
        }
        
        //寻找匹配的，否则找上一个。
        var preItem:FRTocReference?
        for i in 0 ..< fbBook.bibleTableOfContents.count {
            if i == fbBook.bibleTableOfContents.count - 2{
                Log.d("调适")
            }
            let item = fbBook.bibleTableOfContents[i]
            
            //不是同一书卷的跳过
            if !item.title.contains(bibleToc.usfm.lowercased()) {
                continue
            }
            
            let result = compare(bibleToc: bibleToc,toc: item)
            if result == 0{
                //相等
                return item
            }else if result < 0{
                //小于
                if i == 0{
                    return item
                }
                return preItem ?? FRTocReference(title: "", resource: FRResource()) //fbBook.bibleTableOfContents[i - 1]
            }else{
                preItem = item
                //大于
                if i == fbBook.bibleTableOfContents.count - 1{
                    return item
                }
                continue
            }
            
//        a    "wdbible://bible/gen.1.2"
//        b    "wdbible://bible/gen.1.3"          //1.3 >=b, <c
//        c    "wdbible://bible/gen.1.4-gen.1.5"  //1.5 >=c, <d
//        d    "wdbible://bible/gen.1.6-gen.1.8"  //1.9 >=d, <e
//        e    "wdbible://bible/gen.1.11-gen.1.12"
//        f    "wdbible://bible/gen.1.14-gen.1.19"
//            "wdbible://bible/gen.1.20"
//            "wdbible://bible/gen.1.21-gen.1.22"
//            "wdbible://bible/gen.1.24"
//            "wdbible://bible/gen.1.26"
//            "wdbible://bible/gen.1.27"
//            "wdbible://bible/gen.1.28"
//            "wdbible://bible/gen.1.29-gen.1.30"
//        m    "wdbible://bible/gen.1.31"         //1.32 >=m, <n
//        n    "wdbible://bible/gen.2.1-gen.2.3"
//        x    "wdbible://bible/gen.2.4-gen.2.6"
//        y    "wdbible://bible/gen.2.5-gen.2.6"
            
        }
        return preItem ?? FRTocReference(title: "", resource: FRResource())
    }

    private func compare(bibleToc:BibleTocItem,toc:FRTocReference)->Int{
        let nodes = getVerseNodes(toc: toc)
        if nodes.count == 1{
            let node = nodes.first!
            return bibleToc.compare(node: node)
        }else if nodes.count == 2{
            let first = nodes.first!
            let last = nodes.last!
            let result1 = bibleToc.compare(node: first)
            if result1 < 0{
                return result1
            }else if result1 == 0{
                return result1
            }else{
                let result2 = bibleToc.compare(node: last)
                if result2 < 0{
                    return 0
                }else if result2 == 0{
                    return 0
                }else{
                    return 1
                }
            }
        }
        
        return 0
    }

    private func getVerseNodes(toc:FRTocReference)->[VerseNode]{
        let nodes = toc.title!.replacingOccurrences(of: "wdbible://bible/", with: "").split(separator: "-")
        var biblefullname = String(nodes.first!)
        
        //经文区域
        if nodes.count == 2{
            let array1 = String(nodes.first!).split(separator: ".")
            let structName1 = String(array1.first!)
            let chapterNum1:Int = array1.count >= 2 ? Int(array1[1]) ?? 1 : 1
            let verseNum1:Int = array1.count >= 3 ? Int(array1[2]) ?? 1 : 1
            
            let array2 = String(nodes.last!).split(separator: ".")
            let structName2 = String(array2.first!)
            let chapterNum2:Int = array2.count >= 2 ? Int(array2[1]) ?? 1 : 1
            let verseNum2:Int = array2.count >= 3 ? Int(array2[2]) ?? 1 : 1
            return [VerseNode(usfm:structName1,chapterNum:chapterNum1,verseNum:verseNum1),VerseNode(usfm:structName2,chapterNum:chapterNum2,verseNum:verseNum2)]
        }
        //只有一个经文
        else{
            let array = String(nodes.first!).split(separator: ".")
            let structName = String(array.first!)
            let chapterNum:Int = array.count >= 2 ? Int(array[1]) ?? 1 : 1
            let verseNum:Int = array.count >= 3 ? Int(array[2]) ?? 1 : 1
            
            return [VerseNode(usfm:structName,chapterNum:chapterNum,verseNum:verseNum)]
        }
    }
    
    

 
}
