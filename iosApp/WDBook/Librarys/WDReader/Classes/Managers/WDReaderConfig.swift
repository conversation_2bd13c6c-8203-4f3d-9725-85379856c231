//
//  WDReaderConfig.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/8.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import SwiftyUserDefaults
import UIKit

// 配置
// 字体相关整个app统一配置。当前页码只和bookid关联； 和用户id，和字体大小不关联。页码超过总页码就认为是最大页码！
class WDReaderConfig {
    static var hasCode = true
    static var code: String = ""
    static var skipEmptyTextHTML = true // 跳过单个html内容为空的页面。（可能是一张local图片，跳过不处理）
    static var isDynamicLoadingMode = true // true动态加载模式,边加载边释放。false一次性加载完所有章。

    static var resourceId: String = ""
    static var fileId: String = ""
    static var chapterPath: String?
    static var contentOffset: Int?
    static var appBrightness = Double(UIScreen.main.brightness)

    static let FootNoteFizePlus = [2, 2, 2, 2, 4, 4, 4, 4, 6, 6] // 注脚字体相对于正文减少的差值
    static let FontSizeCorrectingValue = 0 // 字体校正值
    static let DefaultFontId = 1 // 0,系统字体，1，思源宋体。
    static var folioReaderConfig: FolioReaderConfig = .init()

    static let DefaultFontSizeIndex: Int = 3

    static var StyleFontSizes: [Int] {
        if UIDevice.current.userInterfaceIdiom == .pad {
            return [18, 20, 22, 24, 27, 30, 33, 36, 40, 44]
        } else {
            return [14, 16, 18, 20, 22, 24, 26, 28, 32, 36]
        }
    }

    static var currentFontFamilyName: String {
        let fontId = WDReaderConfig.getCurrentFontId()
        if fontId == 0 {
            return UIFont.regular().familyName
        } else {
            return UIFont.sourceHanSerif().familyName
        }
    }

    static var currentFontName: String {
        let fontId = WDReaderConfig.getCurrentFontId()
        if fontId == 0 {
            return UIFont.regular().fontName
        } else {
            return UIFont.sourceHanSerif().fontName
        }
    }

    static var getCurrentFont: UIFont {
        let fontId = getCurrentFontId()
        if fontId == 0 {
            return UIFont.regular()
        } else {
            return UIFont.sourceHanSerif()
        }
    }

    // 默认字体大小
    static var DefaultFontSize: Int {
        StyleFontSizes[DefaultFontSizeIndex]
    }

    // 当前字体和默认字体的差值
    static var currentFontSizePlus: Int {
        return StyleFontSizes[getCurrentFontSizeIndex()] - StyleFontSizes[DefaultFontSizeIndex] + FontSizeCorrectingValue
    }

    // 当前字体大小
    static var currentFontSize: Int {
        return StyleFontSizes[getCurrentFontSizeIndex()]
    }

    // 当前脚注字体大小
    static var currentFootNoteSize: Int {
        return StyleFontSizes[getCurrentFontSizeIndex()] - FootNoteFizePlus[getCurrentFontSizeIndex()]
    }

    // MARK: 字体大小索引存取

    static func setCurrentFontSizeIndex(bookid _: String, footSizeIndex: Int) {
//        let currentFootSizeIndexKey = DefaultsKey<Int>("WDReader_currentFontSizeIndex_\(bookid)", defaultValue: DefaultFontSizeIndex)
        let currentFootSizeIndexKey = DefaultsKey<Int>("WDReader_currentFontSizeIndex", defaultValue: DefaultFontSizeIndex)
        Defaults[key: currentFootSizeIndexKey] = footSizeIndex
    }

    static func getCurrentFontSizeIndex() -> Int {
//        let bookid = WDReaderCenter.shared.fbBook.name!  //"ysqm_2010.epub"
//        let currentPageNumKey = DefaultsKey<Int>("WDReader_currentFontSizeIndex_\(bookid)", defaultValue: DefaultFontSizeIndex)
        let currentFontSizeKey = DefaultsKey<Int>("WDReader_currentFontSizeIndex", defaultValue: DefaultFontSizeIndex)
        return Defaults[key: currentFontSizeKey]
    }

    static func setCurrentFontId(fontId: Int) {
        let currentFootKey = DefaultsKey<Int>("WDReader_currentFontId", defaultValue: DefaultFontId)
        Defaults[key: currentFootKey] = fontId
    }

    static func getCurrentFontId() -> Int {
        let currentFootKey = DefaultsKey<Int>("WDReader_currentFontId", defaultValue: DefaultFontId)
        return Defaults[key: currentFootKey]
    }

    static func setUserBrightness(brightness: Double) {
        let brightnessKey = DefaultsKey<Double>("WDReader_brightness", defaultValue: Double(UIScreen.main.brightness))
        Defaults[key: brightnessKey] = brightness
    }

    static func getUserBrightness() -> Double {
        let brightnessKey = DefaultsKey<Double>("WDReader_brightness", defaultValue: Double(UIScreen.main.brightness))
        return Defaults[key: brightnessKey]
    }
}
