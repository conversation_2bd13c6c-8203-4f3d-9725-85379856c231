//
//  SearchState.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/19.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI

class SearchState: ObservableObject {
    // Published properties
    @Published var searchText: String = ""
    @Published var searchHistory: [String] = []
    @Published var showingResults = false
    @Published var isSearching = false
    @Published var results: [SearchSection] = []
    @Published var pendingSearchTerm: String? = nil
    @Published var hasMoreResults = false
    @Published var currentPage = 1
    @Published var footerRefreshing: Bool = false
    @Published var selectedSectionIndex: Int = 0
    @Published var selectedResultIndex: Int = 0
    @Published var hasSelectedResult: Bool = false
    @Published var showSearchNavigation: Bool = false {
        didSet {
            if oldValue != showSearchNavigation {
                NotificationCenter.default.post(name: NSNotification.Name("SearchStateDidChange"), object: nil)
                // Clear cache when search navigation visibility changes
                if !showSearchNavigation {
                    searchNotesCache.clear()
                }
            }
        }
    }

    /// Flag to trigger unfocusing the search bar from external components
    /// Set to true when keyboard focus should be removed, will be reset to false automatically
    @Published var shouldUnfocusSearchBar: Bool = false

    // Private properties
    private let itemsPerPage = 22
    private let searchHistoryLimit: Int64 = 8
    private var searchNotesCache = LRUCache<String, [Note]>(maxSize: 50)

    // Static properties
    static let shared = SearchState()

    // Initializer
    private init() {
        loadSearchHistory()
    }

    private func resetSearchState() {
        showingResults = false
        results = []
        pendingSearchTerm = nil
        currentPage = 1
        hasMoreResults = false
        selectedSectionIndex = 0
        selectedResultIndex = 0
        hasSelectedResult = false
        showSearchNavigation = false
        searchNotesCache.clear()
    }

    func clearSearch() {
        searchText = ""
        resetSearchState()
    }

    /// Triggers the search bar to lose focus.
    func unfocusSearchBar() {
        shouldUnfocusSearchBar = true
    }

    func performSearch(with text: String, loadMore: Bool = false, completion: ((SearchPerformResult) -> Void)? = nil) {
        guard NetReachability.isReachability() else {
            Toaster.showToast(message: "network_unavailable".localized)
            completion?(.cancelled)
            return
        }

        let trimmedText = text.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedText.isEmpty else {
            completion?(.cancelled)
            return
        }

        prepareForSearch(text: trimmedText, loadMore: loadMore)

        WDBookStoreSDK.shared.getSentenceSearchResultList(resourceId: WDReaderConfig.resourceId, text: trimmedText, page: currentPage, pageSize: itemsPerPage) { result in
            switch result {
            case let .success(searchResultEntity):
                if let entity = searchResultEntity {
                    self.processSearchResults(entity, loadMore: loadMore)
                    completion?(.success)
                } else {
                    completion?(.failure(nil))
                }

            case let .failure(error):
                self.handleSearchError(error)
                completion?(.failure(error))
            }

            self.finishLoading()
        }
    }

    private func prepareForSearch(text: String, loadMore: Bool) {
        if !loadMore {
            currentPage = 1
            results = []
            WDBookUserSDK.shared.saveSearchText(resourceId: WDReaderConfig.resourceId, searchText: text)
            loadSearchHistory()
        }

        isSearching = true
        if loadMore {
            footerRefreshing = true
        }
    }

    private func finishLoading() {
        footerRefreshing = false
        isSearching = false
    }

    private func processSearchResults(_ entity: SentenceSearchResultEntity, loadMore: Bool) {
        let newResults = convertToSearchSections(entity)

        if !loadMore {
            results = newResults
            // Reset selection state for new search results
            selectedSectionIndex = 0
            selectedResultIndex = 0
            hasSelectedResult = false
            searchNotesCache.clear()
        } else {
            results = appendNewResults(newResults)
            // Clear cache when results change
            searchNotesCache.clear()
        }

        hasMoreResults = entity.currentPage < entity.totalPage
        currentPage = Int(entity.currentPage + 1)
        showingResults = true
    }

    private func handleSearchError(_ error: Error) {
        Log.d("Search error occurred: \(error)")
        if error is SDKException {
            Toaster.showToast(message: "search_failed".localized)
        } else {
            let nsError = error as NSError
            if nsError.code == -1009 {
                Toaster.showToast(message: "network_unavailable".localized)
            } else {
                Toaster.showToast(message: "search_failed".localized)
            }
        }
    }

    private func convertToSearchSections(_ entity: SentenceSearchResultEntity) -> [SearchSection] {
        return entity.dataContents.map { chapter in
            let results = chapter.sentences.map { sentence in
                SearchResult(
                    content: sentence.contentWithTag,
                    chapterIndex: Int(chapter.chapterIndex),
                    fileId: chapter.fileId,
                    fileUuid: chapter.fileUuid,
                    startOffset: Int(sentence.startOffset),
                    endOffset: Int(sentence.endOffset)
                )
            }

            return SearchSection(
                title: chapter.fileName.attributeStringFromHTML().string,
                results: results
            )
        }
    }

    private func appendNewResults(_ newResults: [SearchSection]) -> [SearchSection] {
        var updatedSections = results

        for newSection in newResults {
            if let existingIndex = updatedSections.firstIndex(where: { $0.title == newSection.title }) {
                // Append results to existing section
                updatedSections[existingIndex].results.append(contentsOf: newSection.results)
            } else {
                // Add new section
                updatedSections.append(newSection)
            }
        }

        return updatedSections
    }

    func loadSearchHistory() {
        // Use the constant here
        let history = WDBookUserSDK.shared.getSearchHistory(resourceId: WDReaderConfig.resourceId, limit: searchHistoryLimit)
        DispatchQueue.main.async {
            self.searchHistory = history.map { $0.keyWords }
        }
    }

    func clearSearchHistory() {
        WDBookUserSDK.shared.clearSearchHistory(resourceId: WDReaderConfig.resourceId)
        loadSearchHistory()
    }

    // Add these new functions for navigation

    // Get the current section title
    var currentSectionTitle: String {
        guard !results.isEmpty,
              selectedSectionIndex >= 0,
              selectedSectionIndex < results.count
        else {
            return ""
        }
        return results[selectedSectionIndex].title
    }

    // Get the current result
    var currentResult: SearchResult? {
        guard !results.isEmpty,
              selectedSectionIndex >= 0,
              selectedSectionIndex < results.count,
              selectedResultIndex >= 0,
              selectedResultIndex < results[selectedSectionIndex].results.count
        else {
            return nil
        }
        return results[selectedSectionIndex].results[selectedResultIndex]
    }

    // Set the selected result by its indices
    func selectResult(sectionIndex: Int, resultIndex: Int) {
        guard sectionIndex >= 0, sectionIndex < results.count,
              resultIndex >= 0, resultIndex < results[sectionIndex].results.count
        else {
            return
        }
        selectedSectionIndex = sectionIndex
        selectedResultIndex = resultIndex
        hasSelectedResult = true
        showSearchNavigation = true
    }

    // Navigate to the next result
    func nextResult() -> SearchResult? {
        guard !results.isEmpty else { return nil }

        var nextSectionIndex = selectedSectionIndex
        var nextResultIndex = selectedResultIndex + 1

        // Check if we need to move to the next section
        if nextResultIndex >= results[nextSectionIndex].results.count {
            nextSectionIndex += 1
            nextResultIndex = 0

            // If we've reached the end of all sections, return nil instead of cycling
            if nextSectionIndex >= results.count {
                return nil
            }
        }

        selectedSectionIndex = nextSectionIndex
        selectedResultIndex = nextResultIndex

        // Force fresh highlighting calculations by clearing related cache entries
        clearResultSpecificCache()

        return currentResult
    }

    // Navigate to the previous result
    func previousResult() -> SearchResult? {
        guard !results.isEmpty else { return nil }

        var prevSectionIndex = selectedSectionIndex
        var prevResultIndex = selectedResultIndex - 1

        // Check if we need to move to the previous section
        if prevResultIndex < 0 {
            prevSectionIndex -= 1

            // If we've reached the beginning of all sections, return nil instead of cycling
            if prevSectionIndex < 0 {
                return nil
            }

            prevResultIndex = results[prevSectionIndex].results.count - 1
        }

        selectedSectionIndex = prevSectionIndex
        selectedResultIndex = prevResultIndex

        // Force fresh highlighting calculations by clearing related cache entries
        clearResultSpecificCache()

        return currentResult
    }

    // Add these computed properties to check if we're at the first or last result
    var isAtFirstResult: Bool {
        guard !results.isEmpty else { return true }
        return selectedSectionIndex == 0 && selectedResultIndex == 0
    }

    var isAtLastResult: Bool {
        guard !results.isEmpty else { return true }

        let lastSectionIndex = results.count - 1
        let lastResultIndex = results[lastSectionIndex].results.count - 1

        return selectedSectionIndex == lastSectionIndex && selectedResultIndex == lastResultIndex
    }

    /// Returns an array of highlighted notes for the specified chapter path based on the current search result.
    ///
    /// This method extracts highlight information from the current search result and converts it into
    /// `Note` objects that can be displayed in the reader. Each note corresponds to a highlighted text match
    /// in the search results and is styled with a yellow highlight.
    ///
    /// - Parameter chapterPath: The path of the chapter to get search highlight notes for.
    ///
    /// - Returns: An array of `Note` objects containing the highlight information if all of the following conditions are met:
    ///   - Search navigation is currently active (`showSearchNavigation` is true)
    ///   - A valid current result exists
    ///   - The current result's chapter path matches the provided chapter path
    ///   - The current result contains at least one highlight
    ///   - Returns `nil` otherwise.
    ///
    /// - Note: Each returned note will be styled with the yellow highlight style by default.
    func getSearchNotes(chapterPath: String) -> [Note]? {
        guard showSearchNavigation,
              let result = currentResult,
              result.href == chapterPath,
              result.highlights.count > 0 else { return nil }

        // Create a unique cache key that includes both chapter path and result ID
        let cacheKey = cacheKeyFor(chapterPath: chapterPath, resultId: result.id)

        // Check if notes for this chapter path and result ID are already in the cache
        if let cachedNotes = searchNotesCache.get(cacheKey) {
            Log.d("DEBUG: Returning cached notes for chapter: \(chapterPath) and result: \(result.id)")
            return cachedNotes
        }

        // Calculate and update adjusted offset if needed
        calculateAndUpdateAdjustedOffset(for: result, in: chapterPath)

        var notes = [Note]()
        notes.reserveCapacity(result.highlights.count)

        // Use the stored adjusted offset if available, otherwise fall back to startOffset
        let offsetToUse = result.adjustedStartOffset >= 0 ? result.adjustedStartOffset : result.startOffset

        // --- DEBUG: Fetch displayed chapter content for range verification ---
        var displayString: String?
        if let chapter = Paginator.current.getChapter(href: chapterPath) {
            displayString = chapter.displayedAttributedString.string
        }
        // --- END DEBUG ---

        for highlight in result.highlights {
            let highlightText = highlight.text
            let relativeHighlightRange = highlight.range

            var finalNoteRange = NSRange(location: NSNotFound, length: 0)
            var foundText: String?
            var searchStrategy = "Initialization Failed"

            if let content = displayString, offsetToUse >= 0, !highlightText.isEmpty {
                // Strategy 1: Verify Offset + Relative Range
                let absoluteHighlightStart = offsetToUse + relativeHighlightRange.location
                let candidateRange = NSRange(location: absoluteHighlightStart, length: relativeHighlightRange.length)

                // Check if candidateRange is valid within the displayString
                if let swiftRange = Range(candidateRange, in: content) {
                    let textAtCandidateRange = String(content[swiftRange])
                    // Use contains as a slightly more lenient check than exact equality
                    if textAtCandidateRange == highlightText || textAtCandidateRange.contains(highlightText) || highlightText.contains(textAtCandidateRange) {
                        finalNoteRange = candidateRange
                        searchStrategy = "Verified Offset + RelativeRange"
                        foundText = textAtCandidateRange // Store the actually found text
                    } else {
                        searchStrategy = "Verification Failed ('\(textAtCandidateRange)' != '\(highlightText)')"
                    }
                } else {
                    searchStrategy = "Verification Failed (Range \(candidateRange) invalid)"
                }

                // Strategy 2: Search near offset if Verification Failed
                if finalNoteRange.location == NSNotFound {
                    // Define a search window around the expected start
                    // Ensure search window starts at a valid position
                    let searchWindowStart = max(0, min(absoluteHighlightStart - 20, content.count - 1))
                    // Ensure search window ends at a valid position
                    let searchWindowEnd = min(content.count, max(searchWindowStart + 1, absoluteHighlightStart + relativeHighlightRange.length + 20))

                    if searchWindowStart < searchWindowEnd {
                        // Debug the indices calculation
                        Log.d("DEBUG: Before indexing - searchWindowStart: \(searchWindowStart), searchWindowEnd: \(searchWindowEnd)")

                        // Safely create indices with bounds checking
                        let safeStartOffset = min(searchWindowStart, content.count)
                        let safeEndOffset = min(searchWindowEnd, content.count)

                        // Create the indices
                        let startIndex = content.utf16.index(content.utf16.startIndex, offsetBy: safeStartOffset)
                        let endIndex = content.utf16.index(content.utf16.startIndex, offsetBy: safeEndOffset)

                        // Convert UTF16 indices back to String indices
                        if let stringStartIndex = String.Index(startIndex, within: content),
                           let stringEndIndex = String.Index(endIndex, within: content)
                        {
                            let searchRange = stringStartIndex ..< stringEndIndex

                            #if DEBUG
                                Log.d("DEBUG: Safe offsets - start: \(safeStartOffset), end: \(safeEndOffset)")
                                Log.d("DEBUG: Created indices - start position: \(content.distance(from: content.startIndex, to: stringStartIndex)), end position: \(content.distance(from: content.startIndex, to: stringEndIndex))")
                                Log.d("DEBUG: Search Window Content: '\(String(content[searchRange]))'")
                                Log.d("DEBUG: Looking for: '\(highlightText)'")
                            #endif

                            if let foundSwiftRange = content.range(of: highlightText, options: [], range: searchRange) {
                                finalNoteRange = NSRange(foundSwiftRange, in: content)
                                searchStrategy += " -> Found Near Offset"
                                // Extract text for debug print
                                if let verifiedRange = Range(finalNoteRange, in: content) {
                                    foundText = String(content[verifiedRange])
                                }
                            } else {
                                searchStrategy += " -> Not Found Near Offset"
                            }
                        } else {
                            Log.d("Failed to convert UTF-16 indices to String indices")
                            searchStrategy += " -> UTF-16 Index Conversion Failed"
                        }
                    } else {
                        searchStrategy += " -> Invalid Search Window [\(searchWindowStart), \(searchWindowEnd))"
                    }
                }

                // Strategy 3: Search full string if still not found
                if finalNoteRange.location == NSNotFound {
                    if let foundSwiftRange = content.range(of: highlightText) {
                        finalNoteRange = NSRange(foundSwiftRange, in: content)
                        searchStrategy += " -> Found in Full String"
                        // Extract text for debug print
                        if let verifiedRange = Range(finalNoteRange, in: content) {
                            foundText = String(content[verifiedRange])
                        }
                    } else {
                        searchStrategy += " -> Not Found in Full String"
                    }
                }

            } else {
                searchStrategy = "Prerequisites Failed (content=\(displayString != nil), offset=\(offsetToUse), hlText=\(!highlightText.isEmpty))"
            }

            // Strategy 4: Fallback Calculation (Last Resort)
            if finalNoteRange.location == NSNotFound {
                // Only use fallback if offset was valid initially
                if offsetToUse >= 0 {
                    finalNoteRange = NSRange(location: offsetToUse + relativeHighlightRange.location,
                                             length: relativeHighlightRange.length)
                    searchStrategy += " -> Fallback Calculation"
                } else {
                    // Keep NSNotFound if offset was invalid from the start
                    searchStrategy += " -> Fallback Skipped (Invalid Offset)"
                }
            }

            // Create the note using the determined range
            let note = Note(
                chapterPath: chapterPath,
                range: finalNoteRange, // Use the determined range
                summary: highlightText
            )
            note.style = HighlightStyle(id: 0, type: .colorOrange, showColor: dynamicHighlightSearchResultColor)
            notes.append(note)

            #if DEBUG
                // --- DEBUG: Print check results ---
                // Update foundText if it wasn't set during successful search strategies
                if foundText == nil, let content = displayString, finalNoteRange.location != NSNotFound, let range = Range(finalNoteRange, in: content) {
                    foundText = String(content[range])
                } else if foundText == nil, displayString == nil {
                    foundText = "Error: Display string nil"
                } else if foundText == nil {
                    foundText = "Error: Range \(finalNoteRange) invalid or text not extractable"
                }

                Log.d("DEBUG: Highlight Check - Strategy: \(searchStrategy)")
                Log.d("  - Sentence: `\(result.content)`")
                Log.d("  - Expected: '\(highlightText)' (Rel Range: \(relativeHighlightRange))")
                Log.d("  - Found:    '\(foundText ?? "N/A")'")
                Log.d("  - Range:    \(finalNoteRange)")
                Log.d("  - Offset:   \(offsetToUse)")
                Log.d("-------------------------------------")
                // --- END DEBUG ---
            #endif
        }

        // Add notes to cache with LRU tracking
        searchNotesCache.set(cacheKeyFor(chapterPath: chapterPath, resultId: result.id), value: notes)

        return notes
    }

    // Helper method to create a unique cache key for search notes
    private func cacheKeyFor(chapterPath: String, resultId: UUID) -> String {
        return "\(chapterPath)_\(resultId.uuidString)"
    }

    // Update the adjusted offset for a search result
    func updateAdjustedOffset(for searchResult: SearchResult, adjustedOffset: Int) {
        // Find the search result in our results array
        for (sectionIndex, section) in results.enumerated() {
            if let resultIndex = section.results.firstIndex(where: { $0.id == searchResult.id }) {
                updateAdjustedOffset(sectionIndex: sectionIndex, resultIndex: resultIndex, adjustedOffset: adjustedOffset)
                // Clear search notes cache for affected chapters when adjusting offsets
                if let result = currentResult {
                    searchNotesCache.remove(cacheKeyFor(chapterPath: result.href, resultId: result.id))
                }
                return
            }
        }

        Log.d("Could not find search result to update adjusted offset")
    }

    // Update the adjusted offset for a specific search result
    func updateAdjustedOffset(sectionIndex: Int, resultIndex: Int, adjustedOffset: Int) {
        guard sectionIndex >= 0 else {
            Log.d("Section index out of bounds: \(sectionIndex) is negative")
            return
        }

        guard sectionIndex < results.count else {
            Log.d("Section index out of bounds: \(sectionIndex), results count: \(results.count)")
            return
        }

        guard resultIndex >= 0 else {
            Log.d("Result index out of bounds: \(resultIndex) is negative for section \(sectionIndex)")
            return
        }

        guard resultIndex < results[sectionIndex].results.count else {
            Log.d("Result index out of bounds: \(resultIndex), results count for section \(sectionIndex): \(results[sectionIndex].results.count)")
            return
        }

        // Since SearchResult is a struct (value type), we need to modify it and reassign
        var updatedResult = results[sectionIndex].results[resultIndex]
        updatedResult.adjustedStartOffset = adjustedOffset

        // Update the result in the results array
        results[sectionIndex].results[resultIndex] = updatedResult

        Log.d("Updated adjusted offset for result at [\(sectionIndex), \(resultIndex)]: \(adjustedOffset)")
    }

    // Helper method to get the most up-to-date search result from the results array
    func getUpdatedSearchResult(for searchResult: SearchResult) -> SearchResult {
        // Find the search result in our results array by matching the ID
        for section in results {
            if let updatedResult = section.results.first(where: { $0.id == searchResult.id }) {
                return updatedResult
            }
        }

        // If not found, return the original result
        return searchResult
    }

    // Helper method to calculate the adjusted offset based on content without tags
    func calculateAdjustedOffset(for searchResult: SearchResult, in displayedString: String) -> Int {
        // Check cache and validate inputs
        if searchResult.adjustedStartOffset >= 0 {
            return searchResult.adjustedStartOffset
        }

        let content = searchResult.content

        // Check for empty displayed string
        guard !displayedString.isEmpty else {
            Log.d("Displayed string is empty - Using original offset: \(searchResult.startOffset)")
            return searchResult.startOffset
        }

        // Check for empty content
        guard !content.isEmpty else {
            Log.d("Search content is empty - Using original offset: \(searchResult.startOffset)")
            return searchResult.startOffset
        }

        let safeStartOffset = validateStartOffset(searchResult.startOffset, displayedString.count)

        // Try different matching strategies
        if let exactMatch = findExactMatch(content: content, in: displayedString, startingFrom: safeStartOffset) {
            return exactMatch
        }

        if let fuzzyMatch = findFuzzyMatch(content: content, in: displayedString, startingFrom: safeStartOffset) {
            return fuzzyMatch
        }

        // If no match found, return the original offset
        Log.d("No match found - Using original offset: \(searchResult.startOffset)")
        return searchResult.startOffset
    }

    // Validate and ensure startOffset is within valid range
    private func validateStartOffset(_ startOffset: Int, _ stringLength: Int) -> Int {
        let safeStartOffset = min(max(0, startOffset), stringLength)

        guard safeStartOffset < stringLength else {
            Log.d("Start offset out of range - Using original offset: \(startOffset)")
            return -1 // Signal that offset is invalid
        }

        return safeStartOffset
    }

    // Try to find an exact match for the content
    private func findExactMatch(content: String, in displayedString: String, startingFrom offset: Int) -> Int? {
        // If offset is invalid, skip this matching strategy
        guard offset >= 0 else {
            return nil
        }

        let startIndex = displayedString.index(displayedString.startIndex, offsetBy: offset)
        let searchRange = startIndex ..< displayedString.endIndex

        // Try exact match first within the search range
        if let range = displayedString.range(of: content, range: searchRange) {
            // Convert the Swift string range to an NSRange for offset calculation
            let nsRange = NSRange(range, in: displayedString)
            let adjustedOffset = nsRange.location
            Log.d("Exact match - Adjusted offset: \(adjustedOffset) (original: \(offset))")
            return adjustedOffset
        }

        // If no match found in the range from startOffset, try the whole string as fallback
        if let range = displayedString.range(of: content) {
            let nsRange = NSRange(range, in: displayedString)
            let adjustedOffset = nsRange.location
            Log.d("Exact match (full content) - Adjusted offset: \(adjustedOffset) (original: \(offset))")
            return adjustedOffset
        }

        return nil
    }

    // Try to find a fuzzy match for the content
    private func findFuzzyMatch(content: String, in displayedString: String, startingFrom offset: Int) -> Int? {
        // If offset is invalid, skip this matching strategy
        guard offset >= 0 else {
            return nil
        }

        let minContentLength = 10

        // Only try fuzzy matching for longer content
        guard content.count > minContentLength else {
            Log.d("Content too short for fuzzy matching - Using original offset: \(offset)")
            return nil
        }

        let trimAmount = min(5, content.count / 4)
        let startIndex = displayedString.index(displayedString.startIndex, offsetBy: offset)
        let searchRange = startIndex ..< displayedString.endIndex

        // Create a substring by trimming from both ends
        let substringStart = content.index(content.startIndex, offsetBy: trimAmount)
        let substringEnd = content.index(content.endIndex, offsetBy: -trimAmount)

        guard substringStart < substringEnd else {
            Log.d("""
            Invalid substring range for fuzzy matching:
            - Content: "\(content.prefix(20))\(content.count > 20 ? "..." : "")"
            - Content length: \(content.count)
            - Trim amount: \(trimAmount)
            - Substring start position: \(content.distance(from: content.startIndex, to: substringStart))
            - Substring end position: \(content.distance(from: content.startIndex, to: substringEnd))
            - Error: Start position (\(content.distance(from: content.startIndex, to: substringStart)) >= End position (\(content.distance(from: content.startIndex, to: substringEnd))
            """)
            return nil
        }

        let substring = String(content[substringStart ..< substringEnd])

        // Try to find the substring first from the startOffset
        if let range = displayedString.range(of: substring, range: searchRange) {
            let nsRange = NSRange(range, in: displayedString)
            let adjustedOffset = max(0, nsRange.location - trimAmount)
            Log.d("Fuzzy match from startOffset - Adjusted offset: \(adjustedOffset) (original: \(offset))")
            return adjustedOffset
        }

        // If not found, try the whole content
        if let range = displayedString.range(of: substring) {
            let nsRange = NSRange(range, in: displayedString)
            let adjustedOffset = max(0, nsRange.location - trimAmount)
            Log.d("Fuzzy match (full content) - Adjusted offset: \(adjustedOffset) (original: \(offset))")
            return adjustedOffset
        }

        Log.d("No match found - Using original offset: \(offset)")
        return nil
    }

    // Helper method to calculate and update adjusted offset if needed
    func calculateAndUpdateAdjustedOffset(for searchResult: SearchResult, in chapterPath: String) {
        // Only calculate if not already done and a valid chapter is provided
        guard searchResult.adjustedStartOffset == -1, let chapter = Paginator.current.getChapter(href: chapterPath) else {
            // Adjusted offset already calculated or chapter not found/loaded
            if searchResult.adjustedStartOffset != -1 {
                Log.d("Adjusted offset already calculated for \(chapterPath): \(searchResult.adjustedStartOffset)")
            } else {
                Log.d("Chapter not found or loaded for offset calculation: \(chapterPath)")
            }
            return
        }

        // --- REMOVED Chapter Loading Logic ---
        // Chapter is guaranteed to be available here by the calling context (afterLoadOneChapter)

        let content = chapter.displayedAttributedString.string
        let adjustedOffset = calculateAdjustedOffset(for: searchResult, in: content)
        updateAdjustedOffset(for: searchResult, adjustedOffset: adjustedOffset)

        // Clear cache for this chapter path when updating offset, as highlight ranges might change
        searchNotesCache.remove(cacheKeyFor(chapterPath: chapterPath, resultId: searchResult.id))
    }

    // Helper method to clear cache entries related to the current result
    private func clearResultSpecificCache() {
        if let result = currentResult {
            searchNotesCache.remove(cacheKeyFor(chapterPath: result.href, resultId: result.id))
        }
    }
}
