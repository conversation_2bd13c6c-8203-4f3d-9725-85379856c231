//
//  PermenantThreadTask.swift
//  EpubDemo
//
//  Created by <PERSON> on 2020/5/27.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
typealias PermenantThreadTaskBlock = () -> Void
typealias PermenantThreadTaskCompleteBlock = (String) -> Void

class PermenantThreadTask:NSObject {
    var key:String? //可以为空，用来查找，理论上不重复
    var isResetPriorityHigh:Bool = false //是否设置过优先级
    var executeBlock:PermenantThreadTaskBlock
    var completeBlock:PermenantThreadTaskCompleteBlock? //暂未使用
    
    init(key:String? = nil,block:@escaping PermenantThreadTaskBlock,complete:PermenantThreadTaskCompleteBlock? = nil) {
        self.key = key
        self.executeBlock = block
        self.completeBlock = complete
    }
    
    func execute(){
        self.executeBlock()
    }
}
