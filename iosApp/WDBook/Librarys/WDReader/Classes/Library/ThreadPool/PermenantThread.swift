//
//  LivingThread.swift
//  EpubDemo
//
//  Created by <PERSON> on 2020/5/26.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation

class DeallocThread:Thread{
    deinit {
        print("file:\(#file.lastPathComponent) function:\(#function)")
    }
}

enum PermenantThreadState {
    case created
    case ready
    case executing
    case finished
}
class PermenantThread:NSObject{
    var innerThread:DeallocThread?
    
    var state:PermenantThreadState = .created
    
    private var completeBlock:((String?)->())?
    
    override init() {

    }
    
    deinit {
        print("file:\(#file.lastPathComponent) function:\(#function)")

        stop()
    }
    
    func setCompleteBlock(_ block:((String?)->())? = nil){
        self.completeBlock = block
    }
    /**
    开启线程
    */
    func run() {
        innerThread = DeallocThread(block: {[unowned self] in
            print("---thread---begin---")
            // 创建上下文（要初始化一下结构体）
            var context = CFRunLoopSourceContext()

            // 创建source
            let source = CFRunLoopSourceCreate(kCFAllocatorDefault, CFIndex(0), &context)

            // 往Runloop中添加source
            CFRunLoopAddSource(CFRunLoopGetCurrent(), source, CFRunLoopMode.defaultMode)

            // 销毁source
            //CFRelease(source);

            // 启动
//            while self.innerThread != nil && !self.innerThread!.isCancelled {
                print("---thread---ready---")
//            while self.state != .finish {
                // 第3个参数：returnAfterSourceHandled，设置为true，代表执行完source后就会退出当前loop
                CFRunLoopRunInMode(CFRunLoopMode.defaultMode, 1.0e10, false)
//            }
            print("---thread---end---")
        })
        
        innerThread?.start()
        self.state = .ready
    }

    /**
    在当前子线程执行一个任务
    */
    func execute(_ task: PermenantThreadTask) {
        guard let thread = innerThread else{
            return
        }
        
//        print("---thread---\(thread)---开始执行")
        state = .executing
        perform(#selector(__execute(_:)), on: thread, with: task, waitUntilDone: false) //如果是true，会有偶尔提前判断所有任务执行完毕的情况。导致时间很短。但ture的时间平均比false的长。
//        state = .ready
//        print("---thread---\(thread)---执行完毕")
//        completeBlock?()
    }
    
    /**
    结束线程
    */
    func stop() {
        guard let thread = innerThread else{
            return
        }
        state = .executing
        perform(#selector(__stop), on: thread, with: nil, waitUntilDone: true)
        state = .finished
    }

    func setReady(){
        state = .ready
    }
//    var isCancel:Bool{
//        return innerThread?.isCancelled ?? true
//    }
    
    var isReady:Bool{
        return state == .ready
    }
    
    var isFinished:Bool{
        return state == .ready || state == .finished
//        return innerThread?.isFinished ?? false
    }
    
    var isExecuting:Bool{
        return state == .executing
//        return innerThread?.isExecuting ?? false
    }

    // MARK: - private methods
    @objc private func __stop() {
        CFRunLoopStop(CFRunLoopGetCurrent())
        innerThread?.cancel()
        innerThread = nil
    }

    @objc private func __execute(_ task:PermenantThreadTask) {
        task.execute()
        state = .ready
        theadDebugPrint("----self---\(self)---thread---\(innerThread)---执行完毕。 设置ready状态。")
        completeBlock?(task.key)
    }
}
