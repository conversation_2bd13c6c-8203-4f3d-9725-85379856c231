//
//  ThreadPool.swift
//  EpubDemo
//
//  Created by <PERSON> on 2020/6/4.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation

class ThreadPool:NSObject {
    private var taskQueue:Queue<PermenantThreadTask> = Queue<PermenantThreadTask>()
    
    private var serialQueue:DispatchQueue = DispatchQueue(label: "serial")
    
    private var threads:Set<PermenantThread> = Set<PermenantThread>()
    private var maxConcurrentThreadCount = 4
    
    private(set) var state:PermenantThreadState = .created
    private var poolThread:DeallocThread?
    
    var mutex:pthread_mutex_t!
    var cond:pthread_cond_t!
    
    var tryCompleteAll = false //开始计算所有任务是否完成
    
    private var oneTaskCompleteHandler:((String?)->())?
    private var allTasksCompleteHandler:(()->())?
    
    private var willSetPriorityHighKey:String? //即将要设置的提高优先级的key
    
    override init() {
        // 初始化属性
        var attr = pthread_mutexattr_t()
        pthread_mutexattr_init(&attr)
        pthread_mutexattr_settype(&attr, PTHREAD_MUTEX_RECURSIVE)
        // 初始化锁
        mutex = pthread_mutex_t()
        pthread_mutex_init(&mutex, &attr)
        // 销毁属性
        pthread_mutexattr_destroy(&attr)

        // 初始化条件
        cond = pthread_cond_t()
        pthread_cond_init(&cond, nil)
    }
    
    deinit {
        print("file:\(#file.lastPathComponent) function:\(#function)")
        stop()
    }
    func oneThreadComplete(key:String? = nil){
        oneTaskCompleteHandler?(key)
        tryCompleteAll = true
        pthread_mutex_lock(&mutex)
        pthread_cond_signal(&cond)
        pthread_mutex_unlock(&mutex)
        
    }
    
    func getOptimalThreadPoolSize() -> Int {
        let processorCount = ProcessInfo.processInfo.processorCount
        let activeProcessorCount = ProcessInfo.processInfo.activeProcessorCount

        let optimalThreadPoolSize = (2 * activeProcessorCount) + 1
        return max(1, min(processorCount, optimalThreadPoolSize))
    }
    
    func getOptimalThreadPoolSize(num:Int) -> Int {
        let processorCount = ProcessInfo.processInfo.processorCount

        return max(1, min(processorCount, num))
    }
    
    func getOptimalThreadPoolSizeByDevice() -> Int{
        if UIDevice.isOldDevice() {
            return 1
        }else{
            return 2
        }
    }
    
    //执行任务  放在while中失败。因此在适当时刻执行任务。
    //1、每一个thread执行完判断
    //2、queue每变化一次，执行
    /**
    开启线程
    */
    func initData(maxConcurrentThreadCount:Int? = nil) {
        if let maxCount = maxConcurrentThreadCount{
            self.maxConcurrentThreadCount = getOptimalThreadPoolSize(num: maxCount)
        }else{
            self.maxConcurrentThreadCount = getOptimalThreadPoolSize()
        }
        
        self.maxConcurrentThreadCount = getOptimalThreadPoolSizeByDevice()
        
        for _ in 0 ..< self.maxConcurrentThreadCount {
            let thread = PermenantThread()
            thread.setCompleteBlock { key in
                self.serialQueue.async {[weak self] in
                    theadDebugPrint("thread 处理完成")
                    self?.oneThreadComplete(key:key)
//                    self?.__executeOneLoop()
//                    self?.condition?.lock()
//                    self?.condition?.signal()
//                    self?.condition?.unlock()
                }
            }
            threads.insert(thread)
            thread.run()
        }
        

        poolThread = DeallocThread(block: { [weak self] in
            print("---thread pool---begin---")
            // 创建上下文（要初始化一下结构体）
            var context = CFRunLoopSourceContext()

            // 创建source
            let source = CFRunLoopSourceCreate(kCFAllocatorDefault, CFIndex(0), &context)

            // 往Runloop中添加source
            CFRunLoopAddSource(CFRunLoopGetCurrent(), source, CFRunLoopMode.defaultMode)

            // 销毁source
            //CFRelease(source);

            // 启动
//            while let self = self, self.state != .finished {
                print("---thread pool ---开启")  //只会输出一次

                // 第3个参数：returnAfterSourceHandled，设置为true，代表执行完source后就会退出当前loop
                CFRunLoopRunInMode(CFRunLoopMode.defaultMode, 1.0e10, false)
                
//            }
            
            
            print("---thread pool---end---")
        })
        
        poolThread?.start()
        state = .ready
        
        executePoolTask()
    }

    func executePoolTask() {
        guard let thread = poolThread else{
            return
        }

        print("---thread pool---\(thread)---开始执行")
        state = .executing
        perform(#selector(__execute), on: thread, with: nil, waitUntilDone: false)
        state = .ready
        print("---thread pool---\(thread)---执行完毕")
    }
    
    func getAReadyThread() -> PermenantThread?{
        return threads.filter{$0.isReady}.first
    }
    
    func isAllFinished() -> Bool{
        return threads.filter{$0.isFinished}.count == maxConcurrentThreadCount
    }
    
    func finidhedThreadsCount() -> Int{
        return threads.filter{$0.isFinished}.count
    }
    
    func execute(key:String? = nil, task:@escaping PermenantThreadTaskBlock){
        _addTask(key:key,task:task)
//        serialQueue.async {[weak self] in
//            self?._addTask(key:key,task:task)
//        }

    }
    
    func _addTask(key:String? = nil,task:@escaping PermenantThreadTaskBlock){
        pthread_mutex_lock(&mutex)
        
        taskQueue.enqueue(PermenantThreadTask(key:key,block: task))
        theadDebugPrint("添加任务：\(taskQueue.count), thread:\(Thread.current)")

        if state == .created {
//        if !poolThread!.isExecuting {
            poolThread?.start()
            state = .ready
        }
        
        pthread_mutex_unlock(&mutex)
        pthread_cond_signal(&cond)
        
//        serialQueue.async {[weak self] in
//            self?.taskQueueSemaphore.signal()
//        }
    }

    @discardableResult
    func resetPriorityHigh(key:String) -> Bool{
        pthread_mutex_lock(&mutex)
        defer{
            pthread_mutex_unlock(&mutex)
        }
        willSetPriorityHighKey = key
        
        return checkResetPriorityHigh()
    }
    
    func executeAndResetPriorityHiggh(key:String? = nil, task:@escaping PermenantThreadTaskBlock){
        pthread_mutex_lock(&mutex)
        defer{
            pthread_mutex_unlock(&mutex)
            pthread_cond_signal(&cond)
        }
        
        taskQueue.elements.insert(PermenantThreadTask(key:key,block: task), at: 0)
        theadDebugPrint("添加并提高任务优先级：\(taskQueue.count), thread:\(Thread.current)")

        if state == .created {
//        if !poolThread!.isExecuting {
            poolThread?.start()
            state = .ready
        }
    }
    
    private func checkResetPriorityHigh() -> Bool{
        guard let key = willSetPriorityHighKey else{
            return false
        }
        
        var elements:[PermenantThreadTask] = taskQueue.elements
        var foundIndex:Int?
        for i in 0 ..< elements.count{
            let element = elements[i]
            if element.key == key {
                foundIndex = i
                break
            }
        }
        
        if foundIndex != nil && foundIndex != 0 {
            
            let foundElement = elements.remove(at: foundIndex!)
            elements.insert(foundElement, at: 0)
            theadDebugPrint("优先级测试：提前任务\(key)到首位")
            
//            TheadDebugPrint("优先级测试：交换任务\(key)到首位，延迟任务\(elements[0].key ?? "")")
//            elements.swapAt(0, foundIndex!)
            taskQueue.elements = elements
            willSetPriorityHighKey = nil
            return true
        }else{
            theadDebugPrint("优先级测试：还未找到任务\(key)，无法提前提前到首位。当前个数:\(taskQueue.elements.count)")
            return false
        }
    }
    
    func setOneCompletionHandler(_ complete:((String?)->())? = nil){
        self.oneTaskCompleteHandler = complete
    }
    
    func setAllCompletionHandler(_ complete:(()->())? = nil){
        self.allTasksCompleteHandler = complete
    }
    
    /// 清除任务
    func clearTasks(){
        pthread_mutex_lock(&mutex)
        taskQueue.elements = []
        pthread_mutex_unlock(&mutex)
    }
    /**
    结束线程
    */
    func stop() {
        guard let thread = poolThread else{
            return
        }
        state = .executing
        perform(#selector(__stop), on: thread, with: nil, waitUntilDone: false) //如果true，会等其他任务运行，导致这个__stop插不进来。
        state = .finished
    }
    
    func execute() {
//        __executeCheckAndRun()
        
        guard let thread = poolThread else{
            return
        }

//        print("---thread pool---\(thread)---开始执行")
        state = .executing
        perform(#selector(__execute), on: thread, with: nil, waitUntilDone: false)
        state = .ready
//        print("---thread pool---\(thread)---执行完毕")
    }
    
    // MARK: - private methods
    @objc private func __execute() {
        print("---thread pool ---执行循环")  //只会输出一次
        while self.state != .finished {
            self.__executeOneLoop()
        }
    }
    
    @objc private func __executeOneLoop(){
        pthread_mutex_lock(&mutex)
//        if self.taskQueue.count == 0{
//            print("状态等待：\(self.taskQueue.count), \(Thread.current)")
//            self.condition?.wait()
//        }
//        sleep(1)
//        let task = self.taskQueue.dequeue()
//        self.condition?.unlock()
//        print("状态解锁：\(self.taskQueue.count), \(Thread.current)")
//        return
//
        //TODO: 也可以等待所有添加完毕，然后执行，这样不容易出现提前结束的情况。
        if tryCompleteAll && taskQueue.count == 0 && isAllFinished() {
            theadDebugPrint("任务全部完成")
            allTasksCompleteHandler?()
            tryCompleteAll = false
            pthread_mutex_unlock(&mutex)
//            stop()  不停止。
            return
        }

        if !(taskQueue.count > 0 && getAReadyThread() != nil) {
//            if self.isAllFinished() {
//                self.state = .finished
//                self.allTasksCompleteHandler?()
////                return
//            }
             theadDebugPrint("状态等待：\(taskQueue.count)")
            pthread_cond_wait(&cond, &mutex)
//            print("状态跳过：\(self.taskQueue.count)")  //不会执行
//            self.condition?.unlock()
//            return
        }

        checkResetPriorityHigh()
        
        //wait后重新算一次条件是否满足。
        if taskQueue.count > 0, let thread = getAReadyThread() {
            theadDebugPrint("状态满足开始执行。任务数：\(taskQueue.count)")
            let task = taskQueue.dequeue()
            if task != nil {
                theadDebugPrint("优先级测试：减少一个任务Key:\(task!.key ?? "")。当前等待任务数：\(taskQueue.count)")
                thread.execute(task!)
            }
            theadDebugPrint("状态执行了结束：\(taskQueue.count)")
            pthread_mutex_unlock(&mutex)
        }else{
//            theadDebugPrint("状态未执行结束：\(taskQueue.count)") //打印太多。
            pthread_mutex_unlock(&mutex)
        }

    }
    
    
    @objc private func __stop() {
        threads.forEach{$0.stop()}
        CFRunLoopStop(CFRunLoopGetCurrent())
        if mutex != nil{
            pthread_mutex_destroy(&mutex)
        }
        if cond != nil{
            pthread_cond_destroy(&cond)
        }
        
        poolThread = nil
    }
    
}

func theadDebugPrint(_ msg:String){
//    debugPrint("\(CFAbsoluteTimeGetCurrent()):ThreadPool测试：\(msg)")
}
