/*
  First-in first-out queue (FIFO)

  New elements are added to the end of the queue. Dequeuing pulls elements from
  the front of the queue.

  Enqueuing is an O(1) operation, dequeuing is O(n). Note: If the queue had been
  implemented with a linked list, then both would be O(1).
*/

public struct Queue<T> {
  fileprivate var array = [T]()

  public var count: Int {
    return array.count
  }

  public var isEmpty: Bool {
    return array.isEmpty
  }

  public mutating func enqueue(_ element: T) {
    array.append(element)
  }

  public mutating func dequeue() -> T? {
    if isEmpty {
      return nil
    } else {
      return array.removeFirst()
    }
  }

  public var front: T? {
    return array.first
  }
}

extension Queue{
    var elements:[T]{
        get{
            return array
        }
        set{
            array = newValue
        }
    }
}

//
///*
//  First-in first-out queue (FIFO)
//
//  New elements are added to the end of the queue. Dequeuing pulls elements from
//  the front of the queue.
//
//  Enqueuing and dequeuing are O(1) operations.
//*/
//public struct Queue<T> {
//  fileprivate var array = [T?]()
//  fileprivate var head = 0
//
//  public var isEmpty: Bool {
//    return count == 0
//  }
//
//  public var count: Int {
//    return array.count - head
//  }
//
//  public mutating func enqueue(_ element: T) {
//    array.append(element)
//  }
//
//  public mutating func dequeue() -> T? {
//    guard let element = array[guarded: head] else { return nil }
//
//    array[head] = nil
//    head += 1
//
//    let percentage = Double(head)/Double(array.count)
//    if array.count > 50 && percentage > 0.25 {
//      array.removeFirst(head)
//      head = 0
//    }
//
//    return element
//  }
//
//  public var front: T? {
//    if isEmpty {
//      return nil
//    } else {
//      return array[head]
//    }
//  }
//}
//
//extension Array {
//    subscript(guarded idx: Int) -> Element? {
//        guard (startIndex..<endIndex).contains(idx) else {
//            return nil
//        }
//        return self[idx]
//    }
//}
