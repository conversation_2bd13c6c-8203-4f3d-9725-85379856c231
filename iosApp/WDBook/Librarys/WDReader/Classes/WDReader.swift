//
//  WDReader.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/29.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import UIKit

// let FrameworkBundle = Bundle(for: HTMLCommonUtils.self)
//
// let ResourceBundle = Bundle.init(path: FrameworkBundle.path(forResource: "WDReaderResource", ofType: "bundle") ?? "")
//
// extension UIImage{
//    convenience init?(named:String) {
//        self.init(named: named, in: ResourceBundle, compatibleWith: nil)
//    }
// }

public func enterReadBrightness() {
    WDReaderConfig.appBrightness = Double(UIScreen.main.brightness)
    UIScreen.main.brightness = CGFloat(WDReaderConfig.getUserBrightness())
}

public func exitReadeBrigtness() {
    UIScreen.main.brightness = CGFloat(WDReaderConfig.appBrightness)
}

struct CTPage {
    let ctFrame: CTFrame
    let pageIndex: Int // 当前分段(章）内的索引。(NSAttributedString分页后的索引)。目前并未使用

    let ctFrameRange: NSRange
    // ctFrame在整个AttributedString的range。在segmentLocationOnFull为0时，与ctFrameRange相等
    let range: NSRange
    let segmentLocationOnFull: Int // 所属分段的第一个位置在整个AttributedString的位置
    let imageAttachments: [ImageAttachment]

    init(ctFrame: CTFrame, pageIndex: Int, ctFrameRange: NSRange, segmentLocationOnFull: Int, imageAttachments: [ImageAttachment]) {
        self.ctFrame = ctFrame
        self.pageIndex = pageIndex
        self.ctFrameRange = ctFrameRange
        range = NSRange(location: ctFrameRange.location + segmentLocationOnFull, length: ctFrameRange.length)
        self.segmentLocationOnFull = segmentLocationOnFull
        self.imageAttachments = imageAttachments
    }

    // 显示相关
    func isTapLink(point: CGPoint) -> CTTapedInfo? {
        ctFrame.isTapLink(point: point).map { CTTapedInfo(url: $0.url, range: NSRange(location: $0.range.location - segmentLocationOnFull, length: $0.range.length), location: $0.location) }
    }

    func getRangeRects(range: NSRange) -> [CGRect] {
        ctFrame.getRangeRects(range: NSRange(location: range.location - segmentLocationOnFull, length: range.length))
    }

    func getRangeBorderEntryRects(frame: CGRect, range: NSRange) -> [CGRect] {
        ctFrame.getRangeBorderEntryRects(frame: frame, range: NSRange(location: range.location - segmentLocationOnFull, length: range.length))
    }

    // 选中相关
    func getTouchLocationRange(point: CGPoint, viewFrame: CGRect, isAutoAdjust _: Bool = true) -> (NSRange, Bool) {
        let (r, b) = ctFrame.getTouchLocationRange(point: point, viewFrame: viewFrame, isAutoAdjust: true)
        return (NSRange(location: r.location + segmentLocationOnFull, length: r.length), b)
    }
}

struct ImageAttachment {
    let image: UIImage
    let frame: CGRect
    var displayFrame: CGRect
    init(image: UIImage, frame: CGRect) {
        self.image = image
        self.frame = frame
        displayFrame = frame
    }
}

class Page {
    let ctPage: CTPage

    let chapterIndex: Int // 当前章在所有章中的index。
    var pageIndexInChapter: Int // 在当前章中所有页面中的索引

    weak var chapter: Chapter?

    init(ctPage: CTPage, chapterIndex: Int, pageIndexInChapter: Int, chapter: Chapter? = nil) {
        self.ctPage = ctPage
        self.chapterIndex = chapterIndex
        self.pageIndexInChapter = pageIndexInChapter
        self.chapter = chapter
    }

    var href: String {
        chapter?.resource.href ?? ""
    }

    func getPageAttributedString() -> NSAttributedString {
        chapter?.displayedAttributedString.attributedSubstring(from: ctPage.range) ?? NSAttributedString()
    }
}

class Chapter {
    enum LoadState {
        case none // 未加载
        case paged // 加载完了当前章，后清空了pages。
        case loaded // 加载完了当前章，pages有数据。
    }

    var loadState: LoadState = .none

    var sourceHTML: String // 解密过的html原始数据
    var originalAttributedString: NSAttributedString // 原始数据
    var displayedAttributedString: NSAttributedString // 显示的；处理过的
    var resource: FRResource

    var pageCount: Int = 0

    // 当前章第一页 是整本书所有页面中的第几页。已经跳过空页面了。
    var firstPageNumInWholeBook: Int = 1 {
        didSet {
            hasSettedFirstPageNumInWholeBook = true
        }
    }

    private(set) var hasSettedFirstPageNumInWholeBook = false

    // loaded状态需要的数据
    var pages: [Page]
    @Atomic var resourcePageIndexDic = [String: Int]() // 资源锚点对应的页码索引。key: href，href#fragmentid。value:pageNum。

    init(pages: [Page],
         resource: FRResource,
         sourceHTML: String,
         originalAttributedString: NSAttributedString,
         displayedAttributedString: NSAttributedString)
    {
        self.pages = pages
        pageCount = pages.count
        self.resource = resource
        self.sourceHTML = sourceHTML
        self.originalAttributedString = originalAttributedString
        self.displayedAttributedString = displayedAttributedString

        resetData()
    }

    func copyFrom(pages: [Page], resource: FRResource, sourceHTML: String, originalAttributedString: NSAttributedString, displayedAttributedString: NSAttributedString) {
        self.pages = pages
        pageCount = pages.count
        self.resource = resource
        self.sourceHTML = sourceHTML
        self.originalAttributedString = originalAttributedString
        self.displayedAttributedString = displayedAttributedString

        resetData()
    }

    private func resetData() {
        for page in pages {
            page.chapter = self
        }

        // 跳过空页面
//        if !(WDReaderConfig.skipEmptyTextHTML && originalAttributedString.isEmpty()){
        // 保存页码
        resourcePageIndexDic.removeAll()
        resourcePageIndexDic[resource.href] = 0
        for pageIndex in 0 ..< pages.count {
            let page = pages[pageIndex]
            // 获取页中的所有fragments。
            let fragmentIds = page.ctPage.ctFrame.fragmentIds()
            // resource.id + # + fragment 做key保存pagenum。(如果保存过，不再保存;只保存第一个）
            for fragmentId in fragmentIds.keys {
                let resourceHrefAndFragmentId = "\(resource.href!)#\(fragmentId)"
                if resourcePageIndexDic[resourceHrefAndFragmentId] == nil {
                    resourcePageIndexDic[resourceHrefAndFragmentId] = pageIndex
                }
            }
        }
//        }
    }

    var title: String? {
        WDReaderCenter.shared.getTOCResource(resource: resource)?.title
    }

    var href: String {
        resource.href
    }
}

////UI上是否reloadSection
// private var WDREADER_CHAPTER_RELOAD: UInt8 = 0
// extension Chaptor{
//    var isReload:Bool{
//        set{
//            objc_setAssociatedObject(self, &WDREADER_CHAPTER_RELOAD, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_ASSIGN)
//        }
//        get{
//            return objc_getAssociatedObject(self, &WDREADER_CHAPTER_RELOAD) as? Bool ?? false
//        }
//    }
// }

struct FootNote {
    enum `Type` {
        case epub // epub格式注脚
        case bible // 圣经经文注脚
    }

    /// 注脚类型
    let type: Type

    /// 标题
    var title: String

    /// 内容：所有内容拼接的字符串
    let content: String

    // MARK: 脚注类型独有

    /// 位置：第一个内容的位置。
    let location: Int?
    /// 在当前章的注脚序号>=1
    var numInChapter: Int?
}

private var UIVIEW_ACTIVITY_INDICATOR_VIEW_KEY: UInt8 = 0
extension UIView {
    var indicatorView: UIActivityIndicatorView? {
        set {
            objc_setAssociatedObject(self, &UIVIEW_ACTIVITY_INDICATOR_VIEW_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get {
            return objc_getAssociatedObject(self, &UIVIEW_ACTIVITY_INDICATOR_VIEW_KEY) as? UIActivityIndicatorView
        }
    }

    func showLoading() {
        if indicatorView == nil {
            indicatorView = UIActivityIndicatorView()
            indicatorView!.isHidden = false
            //        indicatorView.style = .large
            indicatorView!.translatesAutoresizingMaskIntoConstraints = false
            addSubview(indicatorView!)

            // Auto layout
            let horizontalConstraint = indicatorView!
                .centerXAnchor.constraint(equalTo: centerXAnchor)
            let verticalConstraint = indicatorView!
                .centerYAnchor.constraint(equalTo: centerYAnchor)
            NSLayoutConstraint.activate([horizontalConstraint, verticalConstraint])
            indicatorView!.startAnimating()
        }
    }

    func hideLoading() {
        indicatorView?.removeFromSuperview()
        indicatorView = nil
    }
}

// 加边框
extension UIImage {
    func drawOutlie(imageKeof: CGFloat = 1.01, color: UIColor) -> UIImage? {
        let outlinedImageRect = CGRect(x: 0.0, y: 0.0,
                                       width: size.width * imageKeof,
                                       height: size.height * imageKeof)

        let imageRect = CGRect(x: size.width * (imageKeof - 1) * 0.5,
                               y: size.height * (imageKeof - 1) * 0.5,
                               width: size.width,
                               height: size.height)

        UIGraphicsBeginImageContextWithOptions(outlinedImageRect.size, false, imageKeof)

        draw(in: outlinedImageRect)

        guard let context = UIGraphicsGetCurrentContext() else { return nil }
        context.setBlendMode(.sourceIn)
        context.setFillColor(color.cgColor)
        context.fill(outlinedImageRect)
        draw(in: imageRect)

        let newImage = UIGraphicsGetImageFromCurrentImageContext()
        UIGraphicsEndImageContext()

        return newImage
    }
}

// 加阴影
extension CALayer {
    func applySketchShadow(
        color: UIColor = UIColor(hex: 0x32335E),
        alpha: Float = 0.18,
        x: CGFloat = 0,
        y: CGFloat = 5,
        blur: CGFloat = 20,
        spread: CGFloat = 0
    ) {
        shadowColor = color.cgColor
        shadowOpacity = alpha
        shadowOffset = CGSize(width: x, height: y)
        shadowRadius = blur / 2.0
        if spread == 0 {
            shadowPath = nil
        } else {
            let dx = -spread
            let rect = bounds.insetBy(dx: dx, dy: dx)
            shadowPath = UIBezierPath(rect: rect).cgPath
        }
    }
}

// JSON加载
struct BibleBookJson: Codable {
    let books: [BibleStructureJson]
}

struct BibleStructureJson: Codable {
    let usfm: String // 英文简写大写
    let human: String // 书卷全名
    let abbr: String // 书卷简称
    let start_id: Int
    let end_id: Int
}

let bibleStructureJsons: BibleBookJson = load("cunps.json")

// 原子操作
@propertyWrapper
struct NSLockAtomic<Value> {
    private var value: Value
    private let lock = NSLock()

    init(wrappedValue value: Value) {
        self.value = value
    }

    var wrappedValue: Value {
        get { return load() }
        set { store(newValue: newValue) }
    }

    func load() -> Value {
        lock.lock()
        defer { lock.unlock() }
        return value
    }

    mutating func store(newValue: Value) {
        lock.lock()
        defer { lock.unlock() }
        value = newValue
    }
}

@propertyWrapper
struct Atomic<Value> {
    private var value: Value
    private var mutex: pthread_mutex_t = {
        var mutex = pthread_mutex_t()
        pthread_mutex_init(&mutex, nil)
        return mutex
    }()

    init(wrappedValue value: Value) {
        self.value = value
    }

    var wrappedValue: Value {
        mutating get { load() }
        set { store(newValue: newValue) }
    }

    mutating func load() -> Value {
        pthread_mutex_lock(&mutex)
        defer { pthread_mutex_unlock(&mutex) }
        return value
    }

    mutating func store(newValue: Value) {
        pthread_mutex_lock(&mutex)
        defer { pthread_mutex_unlock(&mutex) }
        value = newValue
    }
}

// 死锁？？！！
@propertyWrapper
struct RWAtomic<Value> {
    private var value: Value
    private var rwlock: pthread_rwlock_t = {
        var rwlock = pthread_rwlock_t()
        pthread_rwlock_init(&rwlock, nil)
        return rwlock
    }()

    init(wrappedValue value: Value) {
        self.value = value
    }

    var wrappedValue: Value {
        mutating get { load() }
        set { store(newValue: newValue) }
    }

    mutating func load() -> Value {
        pthread_rwlock_rdlock(&rwlock)
        defer { pthread_rwlock_unlock(&rwlock) }
        return value
    }

    mutating func store(newValue: Value) {
        pthread_rwlock_wrlock(&rwlock)
        defer { pthread_rwlock_unlock(&rwlock) }
        value = newValue
    }
}
