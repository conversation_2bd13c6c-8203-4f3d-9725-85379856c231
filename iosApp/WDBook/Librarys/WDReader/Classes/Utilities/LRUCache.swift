//
//  LRUCache.swift
//  WDBook
//
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import Foundation

/// A generic Least Recently Used (LRU) cache implementation.
/// Automatically evicts the least recently accessed items when capacity is reached.
class LRUCache<Key: Hashable, Value> {
    private var cache: [Key: Value] = [:]
    private var accessOrder: [Key] = []
    private let maxSize: Int

    /// Initialize a new LRU cache with the specified maximum size
    /// - Parameter maxSize: The maximum number of items to store in the cache
    init(maxSize: Int) {
        self.maxSize = maxSize
    }

    /// Get a value from the cache by key. Updates access order when item is accessed.
    /// - Parameter key: The key to look up
    /// - Returns: The cached value if present, nil otherwise
    func get(_ key: Key) -> Value? {
        guard let value = cache[key] else { return nil }
        updateAccessOrder(for: key)
        return value
    }

    /// Set a value in the cache. If the cache is full, removes the least recently used item.
    /// - Parameters:
    ///   - key: The key to associate with the value
    ///   - value: The value to store
    func set(_ key: Key, value: Value) {
        if cache[key] != nil {
            updateAccessOrder(for: key)
        } else if cache.count >= maxSize, let leastRecentKey = accessOrder.first {
            cache.removeValue(forKey: leastRecentKey)
            accessOrder.removeFirst()
        }

        cache[key] = value
        accessOrder.append(key)
    }

    /// Remove a specific item from the cache
    /// - Parameter key: The key of the item to remove
    /// - Returns: The removed value if it was in the cache, nil otherwise
    @discardableResult
    func remove(_ key: Key) -> Value? {
        guard let value = cache.removeValue(forKey: key) else { return nil }

        if let index = accessOrder.firstIndex(of: key) {
            accessOrder.remove(at: index)
        }

        return value
    }

    /// Clear all items from the cache
    func clear() {
        cache.removeAll()
        accessOrder.removeAll()
    }

    /// Update the access order for a key, marking it as most recently used
    private func updateAccessOrder(for key: Key) {
        if let index = accessOrder.firstIndex(of: key) {
            accessOrder.remove(at: index)
            accessOrder.append(key)
        }
    }

    /// Check if the cache contains a key
    /// - Parameter key: The key to check
    /// - Returns: True if the key exists in the cache
    func contains(_ key: Key) -> Bool {
        return cache[key] != nil
    }

    /// The number of items currently in the cache
    var count: Int {
        return cache.count
    }
}
