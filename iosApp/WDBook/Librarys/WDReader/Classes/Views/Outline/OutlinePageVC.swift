//
//  OutlinePageVC.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/10.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import UIKit
import SnapKit

protocol OutlinePageVCDelegate:AnyObject {
    func outlinePageVCChapterList(tocReference reference: FRTocReference)
}

class OutlinePageVC: UIViewController {

    var navBar:UIView!
    var segmentedControl: UISegmentedControl!
    var viewList = [UIViewController]()
    var segmentedControlItems:[String] = ["书籍目录".localized, "经文目录".localized]
    
    var pageVC:UIPageViewController!
    var viewControllerOne: UIViewController!
    var viewControllerTwo: UIViewController!
    var index: Int = 0

    weak var delegate:OutlinePageVCDelegate?
    
    override func viewDidLoad() {
        super.viewDidLoad()
                self.index = 0//self.folioReader.currentMenuIndex
               
        //        self.edgesForExtendedLayout = UIRectEdge()
        //        self.extendedLayoutIncludesOpaqueBars = true
        
        navigationController?.setNavigationBarHidden(true, animated: false)
        self.view.backgroundColor = dynamicBackgroundColor1
        
        //头部bar
        navBar = UIView()
        view.addSubview(navBar)
        navBar.snp.makeConstraints { (make) in
            make.top.leading.trailing.equalTo(0)
            make.height.equalTo(60)
        }
        
        //title
        if WDReaderCenter.shared.getBibleToc().count == 0{
            let wd_navigation = ReaderNavigationBar(frame: CGRect.zero, title:"目录".localized, hasBack: false, hasClose: false, hasBottomLine: false)
            navBar.addSubview(wd_navigation)
            wd_navigation.snp.makeConstraints { (make) in
                make.edges.equalToSuperview()
            }
        }
        
        //segmentedControl
        segmentedControlItems = WDReaderCenter.shared.getBibleToc().count > 0 ? ["书籍目录".localized, "经文目录".localized] : ["目录".localized]
        segmentedControl = UISegmentedControl(items: segmentedControlItems)
        segmentedControl.isHidden = WDReaderCenter.shared.getBibleToc().count == 0
        segmentedControl.addTarget(self, action: #selector(OutlinePageVC.didSwitchMenu(_:)), for: UIControl.Event.valueChanged)
        segmentedControl.selectedSegmentIndex = index
        segmentedControl.setTitleTextAttributes([
            NSAttributedString.Key.font: UIFont.medium(),
            NSAttributedString.Key.foregroundColor: dynamicSegmentTitleColor
        ], for: .normal)
        
        segmentedControl.setTitleTextAttributes([
            NSAttributedString.Key.font : UIFont.semibold(),
            NSAttributedString.Key.foregroundColor: dynamicSegmentTitleColor
        ], for: .selected)
        
        navBar.addSubview(segmentedControl)
        segmentedControl.snp.makeConstraints { (make) in
            make.width.equalTo(247)
            make.height.equalTo(32)
            make.center.equalTo(navBar)
        }
        
        let closeBtn = UIButton()
        closeBtn.setImage(UIImage(named: "reader_btn_close"), for: .normal)
        closeBtn.addTarget(self, action:  #selector(dismiss as () -> Void), for: .touchUpInside)
        navBar.addSubview(closeBtn)
        closeBtn.snp.makeConstraints { (make) in
            make.width.height.equalTo(24)
            make.top.equalTo(16)
            make.trailing.equalTo(-16)
        }
        
//        self.navigationItem.rightBarButtonItem = UIBarButtonItem(image: closeImage, style: .plain, target: self, action: #selector(dismiss as () -> Void))
//        self.navigationItem.rightBarButtonItem?.tintColor = UIColor.black
        
        viewControllerOne = FolioReaderChapterList(book: WDReaderCenter.shared.fbBook, delegate: self)
        viewControllerTwo = BibleTocStructureVC(book: WDReaderCenter.shared.fbBook,navigationVC: navigationController,delegate: self) //readerConfig
        viewList = [viewControllerOne, viewControllerTwo]
        viewControllerOne.didMove(toParent: self)
        viewControllerTwo.didMove(toParent: self)

        pageVC = UIPageViewController(transitionStyle: .scroll, navigationOrientation: .horizontal, options: nil)
        pageVC.delegate = self
        pageVC.dataSource = self
        view.addSubview(pageVC.view)
        pageVC.view.snp.makeConstraints { (make) in
            make.top.equalTo(navBar.snp.bottom)
            make.leading.trailing.bottom.equalToSuperview()
        }
        
        pageVC.setViewControllers([viewList[index]], direction: .forward, animated: false, completion: nil)

        // FIXME: This disable scroll because of highlight swipe to delete, if you can fix this would be awesome
        for view in pageVC.view.subviews {
            if view is UIScrollView {
                let scroll = view as! UIScrollView
                scroll.bounces = false
            }
        }
//        self.setCloseButton()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        NotificationCenter.default.post(name: Noti_Remove_StatusBarBG, object: nil)
    }
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        NotificationCenter.default.post(name: Noti_Add_StatusBarBG, object: nil)
    }

    // MARK: - Segmented control changes
    @objc func didSwitchMenu(_ sender: UISegmentedControl) {
        self.index = sender.selectedSegmentIndex
        let direction: UIPageViewController.NavigationDirection = (index == 0 ? .reverse : .forward)
        pageVC.setViewControllers([viewList[index]], direction: direction, animated: true, completion: nil)
//        self.folioReader.currentMenuIndex = index
    }
}

// MARK: UIPageViewControllerDelegate
extension OutlinePageVC: UIPageViewControllerDelegate {
    func pageViewController(_ pageViewController: UIPageViewController, didFinishAnimating finished: Bool, previousViewControllers: [UIViewController], transitionCompleted completed: Bool) {
        if finished && completed {
            let viewController = pageViewController.viewControllers?.last
            segmentedControl.selectedSegmentIndex = viewList.firstIndex(of: viewController!)!
        }
    }
}

// MARK: UIPageViewControllerDataSource

extension OutlinePageVC: UIPageViewControllerDataSource {
    func pageViewController(_ pageViewController: UIPageViewController, viewControllerAfter viewController: UIViewController) -> UIViewController? {

        let index = viewList.firstIndex(of: viewController)!
        if index == viewList.count - 1 {
            return nil
        }

        self.index = self.index + 1
        return viewList[self.index]
    }

    func pageViewController(_ pageViewController: UIPageViewController, viewControllerBefore viewController: UIViewController) -> UIViewController? {

        let index = viewList.firstIndex(of: viewController)!
        if index == 0 {
            return nil
        }

        self.index = self.index - 1
        return viewList[self.index]
    }
}


extension OutlinePageVC: FolioReaderChapterListDelegate {
    func chapterList(_ chapterList: FolioReaderChapterList, didSelectRowAtIndexPath indexPath: IndexPath, withTocReference reference: FRTocReference) {
        delegate?.outlinePageVCChapterList(tocReference: reference)
        self.navigationController?.dismiss()
    }

    func chapterList(didDismissedChapterList chapterList: FolioReaderChapterList) {
        
    }
}

extension OutlinePageVC: BibleTocStructureVCDelegate{
    func bibleTocStructureVCDidSelected(tocReference reference: FRTocReference) {
        delegate?.outlinePageVCChapterList(tocReference: reference)
        self.navigationController?.dismiss()
    }
}
