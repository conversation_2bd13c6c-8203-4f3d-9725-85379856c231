//
//  BibleTocStructureCell.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/27.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import UIKit

class BibleTocStructureCell: UICollectionViewCell {
    var titleLabel:UILabel = {
        let l = UILabel()
        l.textAlignment = .center
        l.font = UIFont.medium(size: 21)
        l.textColor = dynamicTitleColor2
        l.snp.makeConstraints { (make) in
            make.height.equalTo(21)
        }
        return l
    }()
    
    var detailLabel:UILabel = {
        let l = UILabel()
        l.textAlignment = .center
        l.font = UIFont.regular(size: 9)
        l.textColor = dynamicTextColor2
        return l
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        let stack = UIStackView(arrangedSubviews: [titleLabel,detailLabel])
        stack.axis = .vertical
        stack.distribution = .fill
        stack.alignment = .center
        stack.spacing = 8
        contentView.addSubview(stack)
        stack.snp.makeConstraints { (make) in
            make.height.equalTo(21 + 8 + 9)
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        layer.borderWidth = 1
        layer.borderColor = dynamicBorderColor.cgColor
        
        ////在书卷和章目录有效；但进入经文节目录后，无效。
//        layerBorderColorDynimic = dynamicBorderColor
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func updateData(title:String,details:String){
        titleLabel.text = title
        detailLabel.text = details
    }
    
    override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        layer.borderColor = dynamicBorderColor.cgColor
    }
}
