//
//  FolioReaderChapterListCell.swift
//  FolioReaderKit
//
//  Created by <PERSON><PERSON><PERSON> on 07/05/15.
//  Copyright (c) 2015 Folio Reader. All rights reserved.
//

import UIKit
import SnapKit

protocol FolioReaderChapterListCellDelegate:AnyObject {
    func folioReaderChapterListCellTapExpand(tocReference:FRTocReference)
}

class FolioReaderChapterListCell: UITableViewCell {
    var expandIV:UIImageView
    var expandBtn:UIButton
    
    var titleLabel: UILabel
    var pageNumLabel:UILabel

    var resource:FRTocReference!
    
    weak var delegate:FolioReaderChapterListCellDelegate?
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        
        expandIV = UIImageView(image: UIImage(named: "caret_down"))
        expandBtn = UIButton()
        
        let titleL = UILabel()
        titleL.textColor = dynamicTitleColor2
        titleL.font = UIFont.systemFont(ofSize: 16)
        titleL.lineBreakMode = .byTruncatingTail
        titleL.numberOfLines = 0

        titleL.translatesAutoresizingMaskIntoConstraints = false
        titleLabel = titleL
        
        let pageNumL = UILabel()
        pageNumL.textColor = dynamicTextColor2
        pageNumL.font = UIFont.systemFont(ofSize: 12)
        pageNumL.textAlignment = .right
        pageNumL.translatesAutoresizingMaskIntoConstraints = false
        pageNumLabel = pageNumL
        
        
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        
//        expandIV.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapExpand)))
        contentView.addSubview(expandIV)
        expandIV.snp.makeConstraints { (make) in
            make.centerY.equalToSuperview()
            make.leading.equalTo(16)
            make.width.height.equalTo(24)
        }
        expandIV.isHidden = true
        
        expandBtn.addTarget(self, action: #selector(tapExpand), for: .touchUpInside)
        contentView.addSubview(expandBtn)
        expandBtn.snp.makeConstraints { (make) in
            make.centerY.equalToSuperview()
            make.leading.equalTo(8)
            make.width.height.equalTo(40)
        }
        expandBtn.isHidden = true
        
        contentView.addSubview(pageNumLabel)
        pageNumLabel.snp.makeConstraints { (make) in
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
            make.trailing.equalTo(-24)
            make.width.equalTo(40)
        }
        
        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { (make) in
            make.centerY.equalToSuperview()
            make.height.equalTo(24)
            make.leading.equalTo(expandIV.snp.trailing).offset(4)
            make.trailing.equalTo(pageNumLabel.snp.leading).offset(-10)
        }
        
        let line = UIView()
        line.backgroundColor = dynamicSpanLineColor2
        addSubview(line)
        line.snp.makeConstraints { (make) in
            make.leading.equalTo(44)
            make.trailing.equalTo(-24)
            make.bottom.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("storyboards are incompatible with truth and beauty")
    }
    
    func updateData(resource:FRTocReference) {
        self.resource = resource
        
        expandIV.isHidden = true
        expandBtn.isHidden = true
        if resource.children.count > 0 {
            if resource.isExpand{
                expandIV.image = UIImage(named: "caret_down")
//                "▷"
            }else{
                expandIV.image = UIImage(named: "caret_right")
//                "▽"
            }

            expandIV.isHidden = false
            expandBtn.isHidden = false
        }
        expandIV.snp.updateConstraints { (make) in
            make.leading.equalTo(16*(resource.outlineLevel + 1))
        }
        expandBtn.snp.updateConstraints { (make) in
            make.leading.equalTo(16*(resource.outlineLevel + 1) - 8)
        }
        
        titleLabel.text = resource.title//.trimmingCharacters(in: .whitespacesAndNewlines)
        titleLabel.textColor = resource.isLastReadRecord ? UIColor(hex: 0xE9973E) : dynamicTitleColor2

        //希伯来文强制从左到右
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.lineBreakMode = .byTruncatingTail
        paragraphStyle.baseWritingDirection = .leftToRight
        let attributedString = NSAttributedString(string: titleLabel.text!, attributes: [
            .font: UIFont.systemFont(ofSize: 16),
            .foregroundColor: titleLabel.textColor!,
            .paragraphStyle: paragraphStyle
        ])
        titleLabel.attributedText = attributedString
        
        if let pageNum = Paginator.current.resourcePageNumberDic[resource.resourceHrefAndFragmentId()] {
            pageNumLabel.text = String(pageNum)
        }else{
            pageNumLabel.text = ""
            //测试数据
//            pageNumLabel.text = String(resource.resource!.href.split(separator: "/").last!) // ""
//            pageNumLabel.font = UIFont.systemFont(ofSize: 6)
        }

        layoutMargins = UIEdgeInsets.zero
        preservesSuperviewLayoutMargins = false
//        cell.contentView.backgroundColor = isSection ? UIColor(white: 0.7, alpha: 0.1) : UIColor.clear
        backgroundColor = UIColor.clear
    }
    
    @objc func tapExpand() {
        delegate?.folioReaderChapterListCellTapExpand(tocReference: resource)
    }
}
