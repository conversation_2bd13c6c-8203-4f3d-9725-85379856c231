//
//  BibleTocStructureVC.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/27.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import UIKit

/// 卷： title: 书卷名，abbreviation：书卷简写，num 序号，1开始，无特别意义。
/// 章： title = “章”, abbreviation = ""，num：章序号
/// 节： title = "节", abbreviation = ""，num：节序号
struct BibleTocItem{
    enum Level:Int {
        case structure = 0
        case chapter
        case verse
    }

    let level:Level
    let num:Int //顺序 >= 1
    let title:String //书卷名字|章|节
    let abbreviation:String //书卷简写|章号|节号
    var children:[BibleTocItem]?
    let resuorceHref:String?
    let fragmentId:String?

    //经文目录辅助属性
    let structureName:String //书卷名字
    let structureAbbreviation:String //书卷简写
    let usfm:String //书卷英文简写小写
    let chapterNum:Int! //章号(verse类型包含）
    let verseNum:Int! //节号（verse类型包含）
    
    init(structureName:String,structureAbbreviation:String,usfm:String,chapterNum:Int = 1,verseNum:Int = 1,level:Level,num:Int,title:String,abbreviation:String,children:[BibleTocItem]? = nil, resuorceHref:String? = nil,fragmentId:String? = nil) {        
        self.level = level
        self.num = num
        self.title = title
        self.abbreviation = abbreviation
        self.children = children
        self.resuorceHref = resuorceHref
        self.fragmentId = fragmentId

        self.structureName = structureName
        self.structureAbbreviation = structureAbbreviation
        self.usfm = usfm
        self.chapterNum = chapterNum
        self.verseNum = verseNum
    }
}

protocol BibleTocStructureVCDelegate:AnyObject {
    func bibleTocStructureVCDidSelected(tocReference reference: FRTocReference)
}

class BibleTocStructureVC: UIViewController {
    
    weak var delegate:BibleTocStructureVCDelegate?
    
    fileprivate let NUM_ON_ROW = 5
    private var collectionView:UICollectionView!
    var structures:[BibleTocItem] = [BibleTocItem]()
    fileprivate var book: FRBook
    fileprivate weak var navigationVC:UINavigationController?
    
    init(book:FRBook,navigationVC:UINavigationController? ,delegate:BibleTocStructureVCDelegate?) {
        self.book = book
        self.navigationVC = navigationVC
        self.delegate = delegate
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 3.0
        layout.minimumInteritemSpacing = 3.0
        
        collectionView = UICollectionView(frame: view.frame,collectionViewLayout: layout)
        collectionView.register(BibleTocStructureCell.self, forCellWithReuseIdentifier: NSStringFromClass(BibleTocStructureCell.self))
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = dynamicBackgroundColor1
        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { (make) in
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
            make.leading.equalTo(14)
            make.trailing.equalTo(-14)
        }

        //获取数据源
        if structures.isEmpty {
            view.showLoading()
            DispatchQueue.global().async {[weak self] in
                //            structures = WDReaderCenter.shared.getBibleToc()
                self?.structures = WDReaderCenter.shared.getBibleStructureToc()
                DispatchQueue.main.async {
                    self?.collectionView.reloadData()
                    self?.collectionView.performBatchUpdates {
                    } completion: {(success) in
                        self?.view.hideLoading()
                    }
                }
            }
        }
    }

    //转向后重新layout collection
    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        collectionView.collectionViewLayout.invalidateLayout()
    }
}


extension BibleTocStructureVC: UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return structures.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let reuseableCell = collectionView.dequeueReusableCell(withReuseIdentifier: NSStringFromClass(BibleTocStructureCell.self), for: indexPath) as! BibleTocStructureCell
        let structure = structures[indexPath.row]
        reuseableCell.updateData(title: structure.abbreviation, details: structure.title)
        return reuseableCell
    }
    
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let itemWidth = ((collectionView.frame.size.width - 14.0*2) - (CGFloat(NUM_ON_ROW) - 1)*3 ) / CGFloat(NUM_ON_ROW)
        return CGSize(width:itemWidth,height:itemWidth)
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: false)
        
        let structure = structures[indexPath.row]
        let vc = BibleTocChapterVC(book: book)
        vc.delegate = delegate
        vc.title = structure.title
        vc.structures = structure.children ?? [BibleTocItem]()
        navigationVC?.pushViewController(vc, animated: true)
    }
}
