//
//  FolioReaderChapterList.swift
//  FolioReaderKit
//
//  Created by <PERSON><PERSON><PERSON> on 15/04/15.
//  Copyright (c) 2015 Folio Reader. All rights reserved.
//

import UIKit

/// Table Of Contents delegate
@objc protocol FolioReaderChapterListDelegate: AnyObject {
    /**
     Notifies when the user selected some item on menu.
     */
    func chapterList(_ chapterList: FolioReaderChapterList, didSelectRowAtIndexPath indexPath: IndexPath, withTocReference reference: FRTocReference)

    //TODO:此协议没有使用
    /**
     Notifies when chapter list did totally dismissed.
     */
    func chapterList(didDismissedChapterList chapterList: FolioReaderChapterList)
}

class FolioReaderChapterList: UITableViewController,FolioReaderChapterListCellDelegate {

    weak var delegate: FolioReaderChapterListDelegate?
    fileprivate var tableOfContentsCopyItems = [FRTocReference]() //分层的原始数据。每次从fbBook.tableOfContents copy过来。加上了expand属性，isLastReadRecord属性。
    fileprivate var tocShowItems = [FRTocReference]() //展示目录，实际看到的目录顺序。
    fileprivate var book: FRBook
//    fileprivate var readerConfig: FolioReaderConfig
//    fileprivate var folioReader: FolioReader

    
    init( book: FRBook, delegate: FolioReaderChapterListDelegate?) {
        self.delegate = delegate
        self.book = book

        super.init(style: UITableView.Style.plain)
    }

    required init?(coder aDecoder: NSCoder) {
        fatalError("init with coder not supported")
    }

    override func viewDidLoad() {
        super.viewDidLoad()

        self.tableView.register(FolioReaderChapterListCell.self, forCellReuseIdentifier: kReuseCellIdentifier)
        self.tableView.separatorInset = UIEdgeInsets.zero
        self.tableView.separatorStyle = .none
        self.tableView.rowHeight = 50
        self.tableView.estimatedRowHeight = 50
        self.tableView.isHidden = true

        view.showLoading()
        DispatchQueue.global().async { [weak self] in
            guard let self = self else{return}

            self.tableOfContentsCopyItems = self.tableOfContentsCopyAndMarkOutlineLevel()
            self.initCurrentReadPageOnToc()
            self.tocShowItems = self.resetTocItems()

            DispatchQueue.main.async {
//                if Paginator.current.isAllLoad{
                    self.tableView.reloadData {[weak self] in
                        guard let self = self else{return}
                        if let lastReadItem = self.tocShowItems.filter({$0.isLastReadRecord}).first,
                           let index = self.tocShowItems.firstIndex(of:lastReadItem) {

                            let indexPath = IndexPath(item: index, section: 0)
                            self.tableView.scrollToRow(at: indexPath, at: .bottom, animated: false)
                            self.tableView.isHidden = false
                        }
                        self.view.hideLoading()
                    }
                    // Jump to the current chapter
            //        DispatchQueue.main.async {
            //            if let reference = self.book.spine.spineReferences[safe: 0],
            //               let index = self.tocShowItems.firstIndex(where: { $0.resource == reference.resource }) {
            //
            //                  let indexPath = IndexPath(item: index, section: 0)
            //                  self.tableView.scrollToRow(at: indexPath, at: .middle, animated: true)
            //            }
            //        }
//                }else{
//                    self.tableView.reloadData()
//                    self.view.hideLoading()
//                }
            }
        }
    }

    // MARK: - Table view data source
    override func numberOfSections(in tableView: UITableView) -> Int {
        return 1
    }

    override func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return tocShowItems.count
    }

    override func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: kReuseCellIdentifier, for: indexPath) as! FolioReaderChapterListCell
        let tocReference = tocShowItems[indexPath.row]
        
        cell.updateData(resource: tocReference)
        
        cell.delegate = self
        return cell
    }

    // MARK: - Table view delegate
    override func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        let currentIndexPathRow = (indexPath as NSIndexPath).row
        let tocReference = tocShowItems[currentIndexPathRow]
        delegate?.chapterList(self, didSelectRowAtIndexPath: indexPath, withTocReference: tocReference)
        tableView.deselectRow(at: indexPath, animated: true)
    }
    
    // MARK: - FolioReaderChapterListCell delegate
    func folioReaderChapterListCellTapExpand(tocReference: FRTocReference) {
        if tocReference.children.count > 0 {
            if tocReference.isExpand {
                //收起来
                tocReference.isExpand = !tocReference.isExpand
                tocShowItems = resetTocItems()
                tableView.reloadData()
            }else{
                //展开
                tocReference.isExpand = !tocReference.isExpand
                tocShowItems = resetTocItems()
                tableView.reloadData()
            }
        }
    }
    
    private func tableOfContentsCopyAndMarkOutlineLevel() -> [FRTocReference] {
        
        func search(_ items: [FRTocReference],outlineLevel:Int = 0) -> [FRTocReference] {
            var results = [FRTocReference]()
            for item in items {
                var children = [FRTocReference]()
                if item.children.count > 0{
                    children = search(item.children,outlineLevel: outlineLevel + 1)
                }
                let itemCopy = FRTocReference(title: item.title, resource: item.resource, fragmentID: item.fragmentID ?? "",children: children)
                itemCopy.outlineLevel = outlineLevel
                results.append(itemCopy)
            }
            return results
        }
        
        return search(book.tableOfContents)
    }
    
    /// 1、当前页码，和resource。
    /// 2、遍历展开的toc, 在通过toc获取的页码相等的当中，选取最后的那个toc。
                   //如果没有相等的，就选取小于当前页且差值最小的那个toc，
    /// 3、在选择的这个toc的所有父类都展开。
    private func initCurrentReadPageOnToc(){
        //1、找到阅读页码属于的最子的目录
//        markIsLastReadRecord()
        markIsLastReadRecordForChapter()

        //2、该目录父级别设为isexpand。
        markIsExpand()
    }
    
    @available(*, deprecated, message: "")
    private func markIsLastReadRecord(){
        let currentPageNum = Paginator.current.currentPageNum
        var readItem:FRTocReference?
        
        func search(_ items:[FRTocReference]){
            for item in items{
                let pageNum = Paginator.current.resourcePageNumberDic[item.resourceHrefAndFragmentId()] ?? 1
                if pageNum <= currentPageNum{
                    readItem = item
                }
                if item.children.count > 0 {
                    search(item.children)
                }
            }
        }
        
        search(tableOfContentsCopyItems)
        
        readItem?.isLastReadRecord = true
    }
    
    //未加载完所有章的时候，算法。有问题
    private func markIsLastReadRecordForChapter(){
        let currentIndexPath = Paginator.current.currentIndexPath
        Log.d(currentIndexPath)
        let chapter = Paginator.current.currentChapter!
        var readItem:FRTocReference?

        func search(_ items:[FRTocReference]){
            for item in items{
                if item.resource == chapter.resource {
                    if let searchPageIndex = chapter.resourcePageIndexDic[item.resourceHrefAndFragmentId()]{
                        if searchPageIndex <= currentIndexPath.item{
                            readItem = item
                        }
                    }
//                    else if readItem == nil{
//                        readItem = item
//                    }
                }
                if item.children.count > 0 {
                    search(item.children)
                }
            }
        }

        search(tableOfContentsCopyItems)

        readItem?.isLastReadRecord = true
    }
    
    private func markIsExpand(){
        var queue = [FRTocReference]()
        
        var needBreak = false
        func search(_ items:[FRTocReference]){
            for item in items{
                if needBreak {
                    break
                }
                if queue.count == 0 {
                    queue.append(item)
                }
                //同级别
                if !queue.last!.equal(other: item) && queue.last?.outlineLevel == item.outlineLevel {
                    queue.removeLast()
                    queue.append(item)
                }
                //子级别
                if !queue.last!.equal(other: item) && queue.last?.outlineLevel < item.outlineLevel{
                    queue.append(item)
                }
                //父级别
                if !queue.last!.equal(other: item) && queue.last?.outlineLevel > item.outlineLevel{
                    while queue.count > 0 && queue.last!.outlineLevel >= item.outlineLevel{
                        queue.removeLast()
                    }
                    queue.append(item)
                }
                if item.children.count > 0 {
                    search(item.children)
                }
                if item.isLastReadRecord {
                    needBreak = true
                    break
                }
            }
        }
        
        search(tableOfContentsCopyItems)
        
        while queue.count > 0 {
            let item = queue.removeLast()
            if item.children.count > 0{
                item.isExpand = true
            }
        }
    }
    
    private func getFlatTocItems() -> [FRTocReference] {
        var results = [FRTocReference]()

        for item in tableOfContentsCopyItems {
            item.outlineLevel = 0
            results.append(item)
            if item.children.count > 0 {
                results.append(contentsOf: flatTocItems(item,outlineLevel: 1))
            }
        }
        
        return results
    }
    
    private func flatTocItems(_ reference: FRTocReference,outlineLevel:Int) -> [FRTocReference] {
        var tocItems = [FRTocReference]()

        for item in reference.children {
            item.outlineLevel = outlineLevel
            tocItems.append(item)
            if item.children.count > 0 {
                tocItems.append(contentsOf: flatTocItems(item,outlineLevel: outlineLevel + 1))
            }
        }
        
        return tocItems
    }
    
    /// 包含子级别且展开的目录，顺序排列目录。
    /// - Returns: description
    private func resetTocItems() -> [FRTocReference] {
        var tocItems = [FRTocReference]()

        for item in self.tableOfContentsCopyItems {
            tocItems.append(item)
            if item.isExpand {
                tocItems.append(contentsOf: WDReaderCenter.shared.getTocChildren(item,outlineLevel:item.outlineLevel + 1))
            }

        }
        return tocItems
    }
}


private var TABLE_OF_CONTENT_LEVEL_KEY: UInt8 = 0
private var TABLE_OF_CONTENT_EXPAND_KEY: UInt8 = 0
private var TABLE_OF_CONTENT_LAST_READ_RECORD_KEY: UInt8 = 0
extension FRTocReference{
    //书的层级，第一级0，第二级1，第三级2。
    var outlineLevel:Int{
        set{
            objc_setAssociatedObject(self, &TABLE_OF_CONTENT_LEVEL_KEY, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_ASSIGN)
        }
        get{
            return objc_getAssociatedObject(self, &TABLE_OF_CONTENT_LEVEL_KEY) as? Int ?? 0
        }
    }
    
    var isExpand:Bool{
        set{
            objc_setAssociatedObject(self, &TABLE_OF_CONTENT_EXPAND_KEY, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_ASSIGN)
        }
        get{
            return objc_getAssociatedObject(self, &TABLE_OF_CONTENT_EXPAND_KEY) as? Bool ?? false
        }
    }
    
    /// 上一次阅读的记录，同时需要isExpand
    var isLastReadRecord:Bool{
        set{
            objc_setAssociatedObject(self, &TABLE_OF_CONTENT_LAST_READ_RECORD_KEY, newValue, objc_AssociationPolicy.OBJC_ASSOCIATION_ASSIGN)
        }
        get{
            return objc_getAssociatedObject(self, &TABLE_OF_CONTENT_LAST_READ_RECORD_KEY) as? Bool ?? false
        }
    }
}

extension UITableView{
    func reloadData(completion:@escaping (()->())){
        UIView.animate(withDuration: 0) {
            self.reloadData()
        } completion: { (finished) in
            completion()
        }
    }
}
