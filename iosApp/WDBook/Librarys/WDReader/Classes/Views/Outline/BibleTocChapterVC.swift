//
//  BibleTocChapterVC.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/27.
//  Copyright © 2020 <PERSON>. All rights reserved.
// TODO：改名。本类支持章目录和节目录

import UIKit

class BibleTocChapterVC: UIViewController {
    weak var delegate:BibleTocStructureVCDelegate?
    
    fileprivate let NUM_ON_ROW = 5
    private var collectionView:UICollectionView!
    var structures:[BibleTocItem] = [BibleTocItem]()
    fileprivate weak var book: FRBook?
//    fileprivate weak var navigationVC:UINavigationController?
    
    init(book:FRBook?) {
        self.book = book
//        self.navigationVC = navigationVC
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = dynamicBackgroundColor1
        
        initNavigation(title:title, hasBack: true, hasClose: true, hasBottomLine: false)
        
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 3.0
        layout.minimumInteritemSpacing = 3.0
        
        //NSStringFromClass(BibleTocVerseCell.self).components(separatedBy: ".").last!
        collectionView = UICollectionView(frame: view.frame,collectionViewLayout: layout)
        collectionView.register(BibleTocStructureCell.self, forCellWithReuseIdentifier: NSStringFromClass(BibleTocStructureCell.self))
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.backgroundColor = dynamicBackgroundColor1

        view.addSubview(collectionView)
        collectionView.snp.makeConstraints { (make) in
            make.top.equalTo(wd_navigation!.snp.bottom)
            make.bottom.equalToSuperview()
            make.leading.equalTo(14)
            make.trailing.equalTo(-14)
        }
    }

    //转向后重新layout collection
    override func viewWillLayoutSubviews() {
        super.viewWillLayoutSubviews()
        collectionView.collectionViewLayout.invalidateLayout()
    }
}


extension BibleTocChapterVC: UICollectionViewDelegate,UICollectionViewDataSource,UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return structures.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let reuseableCell = collectionView.dequeueReusableCell(withReuseIdentifier: NSStringFromClass(BibleTocStructureCell.self), for: indexPath) as! BibleTocStructureCell
        let structure = structures[indexPath.row]
        reuseableCell.updateData(title: structure.abbreviation, details: structure.title)
        return reuseableCell
    }
    
    public func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let itemWidth = ((collectionView.frame.size.width - 14.0*2) - (CGFloat(NUM_ON_ROW) - 1)*3 ) / CGFloat(NUM_ON_ROW)
        return CGSize(width:itemWidth,height:itemWidth)
    }
    
//    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, minimumLineSpacingForSectionAt section: Int) -> CGFloat {
//        let itemWidth = ((collectionView.frame.size.width - 14.0*2) - (CGFloat(NUM_ON_ROW) - 1)*3 ) / CGFloat(NUM_ON_ROW)
//        let f = ((collectionView.frame.size.width - 14.0*2) - itemWidth * CGFloat(NUM_ON_ROW)) / (CGFloat(NUM_ON_ROW) - 1)
//        return f
//    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        collectionView.deselectItem(at: indexPath, animated: false)
        
        let structure = structures[indexPath.row]
        if structure.level == .chapter {
            let vc = BibleTocChapterVC(book: book)
            vc.delegate = delegate
            vc.title = "\(title != nil ? title! : "") \(structure.num)\(structure.title)"
            vc.structures = structure.children ?? [BibleTocItem]()
            navigationController?.pushViewController(vc, animated: true)
        }else if structure.level == .verse{
            
                  
            //新版本 <a class="anchor" id="v5z01lspg" href="wdbible://bible/gen.1.4-gen.1.5"></a>
                  //<a class="anchor" id="2nkwd0d5r" href="wdbible://bible/gen.1.20"></a>
            if let toc = WDReaderCenter.shared.getBibleFRTocReference(bibleToc: structure){
                delegate?.bibleTocStructureVCDidSelected(tocReference: toc)
            }else{
                
            }
            
            /*
             //老版本 <div id="partial_44844">.....</div>
            //老版本经文目录逻辑
            if let href = structure.resuorceHref{
                
               if let toc = WDReaderCenter.shared.getTocReference(href: href){
                    //查询目录文件。
                    //把经文目录的fragmentid补充上。
                    toc.fragmentID = structure.fragmentId
                    delegate?.bibleTocStructureVCDidSelected(tocReference: toc)
                }else{
                    let r = FRResource()
                    r.href = href
                    let tocR = FRTocReference(title: "", resource: r)
                    tocR.fragmentID = structure.fragmentId
                    delegate?.bibleTocStructureVCDidSelected(tocReference: tocR)
                }
            }
             */
        }
        
    }
}
