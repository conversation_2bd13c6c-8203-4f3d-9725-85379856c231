//
//  ReaderProgress.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/2.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import UIKit
import SnapKit

//进度操作：改tips页码，文字，方向按钮还原；翻页。（近路上次页，本次页）
//章操作：改进度，改tips页码，文字，方向按钮还原；翻页。（记录上次页，本次页）
//返回操作：如果上次页没有，忽略。
//        如果本次页没有，忽略。
//        如果上次页==本次页，忽略。
//        如果当前页==本次页，向前。
//            向前一记录， 改进度，改tips页码，文字，方向按钮调整；翻页。 （不做记录保存。）
//        如果当前页==上次页，向后。
//            向后一记录，改进度，改tips页码，文字，方向按钮调整；翻页。 （不做记录保存。）
protocol ReaderProgressDelegate:AnyObject {
    func readerProgressValueChanged(type:ReaderToolBar.ItemType,value:Int)
    func readerProgressDidSelected(type:ReaderToolBar.ItemType,isLeft:Bool)
    func readerProgressRecordJump(type:ReaderToolBar.ItemType,isPre:Bool,indexPath:IndexPath)
}

class ReaderProgress: UIView,OutlineProgressTipsDelegate {
    
    var type:ReaderToolBar.ItemType

    weak var delegate:ReaderProgressDelegate?
    
    var tips:OutlineProgressTips?

    private var slider:WDCustomSlider
    private var value:Float
    private var minValue:Float
    private var maxValue:Float
    
    private var isChanged:Bool = false
    
    required init(type:ReaderToolBar.ItemType,minValue minV:Float,maxValue maxV:Float,value v:Float,isSliderEnable:Bool = true) {
        self.type = type
        self.value = v
        self.minValue = minV
        self.maxValue = maxV
        
        if type == .light{
            value = Float(WDReaderConfig.getUserBrightness())// Float(UIScreen.main.brightness)
            minValue = 0
            maxValue = 1
        }
        
        self.slider = WDCustomSlider.generate()
        if type == .progress {
            if Paginator.current != nil && Paginator.current.isAllLoad {
                slider.enableState()
            }else{
                slider.disableState()
            }
        }
        
        super.init(frame: CGRect.zero)
        
        self.backgroundColor = dynamicBackgroundColor1
        
        let leftControl = controlBtn(isLeft: true)
        addSubview(leftControl)
        leftControl.snp.makeConstraints { (make) in
            make.leading.equalTo(16)
            make.centerY.equalTo(self)
        }
        
        let rightControl = controlBtn(isLeft: false)
        addSubview(rightControl)
        rightControl.snp.makeConstraints { (make) in
            make.trailing.equalTo(-16)
            make.centerY.equalTo(self)
        }
        
        slider.isUserInteractionEnabled = isSliderEnable
        slider.minimumValue = minValue
        slider.maximumValue = maxValue
        slider.value = value
        
        slider.addTarget(self, action: #selector(onSliderValChanged(slider:event:)), for: .valueChanged)
        addSubview(slider)
        slider.snp.makeConstraints { (make) in
            make.height.equalTo(16)
            make.leading.equalTo(leftControl.snp.trailing).offset(16)
            make.trailing.equalTo(rightControl.snp.leading).offset(-16)
            make.centerY.equalTo(self)
        }

        if type == .style {
            slider.minimumTrackTintColor = dynamicMaximumColor
            
            let scaleStack = UIStackView()
            scaleStack.axis = .horizontal
            scaleStack.distribution = .equalSpacing
            scaleStack.alignment = .center
            insertSubview(scaleStack, belowSubview: slider)
            scaleStack.snp.makeConstraints { (make) in
                make.height.equalTo(8)
                make.leading.equalTo(slider.snp.leading).offset(0)
                make.trailing.equalTo(slider.snp.trailing).offset(0)
                make.centerY.equalTo(slider)
            }
            
            for i in 0 ..< Int(((maxValue - minValue) + 1)){
                let scale = UIView()
                scale.backgroundColor = dynamicMaximumColor
                scale.snp.makeConstraints { (make) in
                    make.width.equalTo(1)
                    make.height.equalTo(8)
                }
                scaleStack.addArrangedSubview(scale)
            }
        }else if type == .light{
            //禁用（跟随系统禁用亮度调节）
//            slider.isUserInteractionEnabled = !UserDefaults.standard.isFollowSystemDarkMode
//            NotificationCenter.default.addObserver(self, selector: #selector(systemDarkModeDidChanged(noti:)), name: NOTI_SYSTEM_DARKMODE_DID_CHANGED, object: nil)
        }
        
        
        let line = UIView()
        line.backgroundColor = dynamicSpanLineColor
        addSubview(line)
        line.snp.makeConstraints { (make) in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(0.5)
        }
        
    }

    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
        
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    //只在.progress类型下可用
    func enableSlideState(minValue minV:Float,maxValue maxV:Float,value v:Float){
        if type == .progress {
            self.value = v
            self.minValue = minV
            self.maxValue = maxV
            slider.minimumValue = minValue
            slider.maximumValue = maxValue
            slider.value = value
            
            slider.enableState()
            slider.isUserInteractionEnabled = true
        }
    }
    
    func disableSlideState(){
        if type == .progress {
            self.value = 1
            self.minValue = 1
            self.maxValue = 1
            slider.minimumValue = minValue
            slider.maximumValue = maxValue
            slider.value = value
            
            slider.disableState()
            slider.isUserInteractionEnabled = false
        }
    }
    
    @objc func systemDarkModeDidChanged(noti:Notification){
        slider.isUserInteractionEnabled = !UserDefaults.standard.isFollowSystemDarkMode
    }
    
    /// 左右两边的控制按钮；小的在左，大的在右。
    func controlBtn(isLeft:Bool) -> UIView{
        let bg = UIView()
        
        //图片
        var imgName:String = ""
        var imgWidth:CGFloat = 0.0
        var imgHeight:CGFloat = 0.0
        switch type {
        case .progress:
            imgName = isLeft ? "reader_back_ui":"reader_next_ui"
            imgWidth = 11.3
            imgHeight = 20
            break
        case .style:
            imgName = "control_style"
            imgWidth = isLeft ? 13.8 : 15
            imgHeight = isLeft ? 13.8 : 15
            break
        case .light:
            imgName = "control_light"
            imgWidth = isLeft ? 12 : 14
            imgHeight = isLeft ? 12 : 14
        default:
            break
        }
        
        let iv = UIImageView(image: UIImage(named: imgName))
        iv.contentMode = .scaleToFill
        bg.addSubview(iv)
        iv.snp.makeConstraints { (make) in
            make.center.equalTo(bg)
            make.width.equalTo(imgWidth)
            make.height.equalTo(imgHeight)
        }
        
        //交互
        let btn = UIButton()
        btn.tag = isLeft ? 0:1
        btn.imageView?.contentMode = .scaleToFill
        btn.addTarget(self, action: #selector(tapItem(btn:)), for: .touchUpInside)
        bg.addSubview(btn)
        btn.snp.makeConstraints { (make) in
//            make.center.equalTo(bg)
//            make.width.height.equalTo(24)
            make.edges.equalToSuperview()
        }
        
        bg.snp.makeConstraints { (make) in
            make.width.height.equalTo(24)
        }
        return bg
    }
    
    @objc func tapItem(btn:UIButton){
        switch type {
        case .progress:
            if btn.tag == 0{
                print("点击左边")
                showProgressTips() //只显示，等待外界调用update数值
//                tips?.updateValue(value: Int(slider.value), maxValue: Int(maxValue))
                 delegate?.readerProgressDidSelected(type: type,isLeft: true)
            }else{
                print("点击右边")
                showProgressTips() //只显示，等待外界调用update数值
//                tips?.updateValue(value: Int(slider.value), maxValue: Int(maxValue))
                 delegate?.readerProgressDidSelected(type: type,isLeft: false)
            }
            break
        case .style:
            //点击事件改为发送ValueChanged
            if btn.tag == 0{
                var scaleValue = slider.value - 1
                if scaleValue < minValue {
                    scaleValue = minValue
                }
                if value != scaleValue {
                    slider.value = scaleValue
                    value = slider.value
                    dispatchStyleDelay(value: Int(slider.value))
                    print(slider.value)
                }
            }else{
               var scaleValue = slider.value + 1
                if scaleValue > maxValue {
                    scaleValue = maxValue
                }
                if value != scaleValue {
                    slider.value = scaleValue
                    value = slider.value
                    dispatchStyleDelay(value: Int(slider.value))
                    print(slider.value)
                }
            }
            break
        case .light: //点击不做操作
            break
        default:
            break
        }
        
       
    }
    
    var isSliderMoved:Bool = false
    @objc func onSliderValChanged(slider: UISlider, event: UIEvent) {
        switch type {
        case .progress:
            if let touchEvent = event.allTouches?.first {
//                Log.d("事件：\(touchEvent.phase.rawValue), 值：\(slider.value)")
                switch touchEvent.phase {
                case .began:
                    showProgressTips()
//                    value = slider.value
//                    tips?.updateValue(value: Int(slider.value), maxValue: Int(maxValue))
//                    preValue = Int(slider.value)
                    
                    //方式仅began的情况出现抖动
                    tips?.updateValue(value: Int(value), maxValue: Int(maxValue))
                    break
                case .moved:
                    isSliderMoved = true
                    value = slider.value
                    tips?.updateValue(value: Int(slider.value), maxValue: Int(maxValue))
                    break
                case .ended:
                    if isSliderMoved {
                        value = slider.value
                        tips?.updateValue(value: Int(slider.value), maxValue: Int(maxValue))
                        isChanged = true
                        delegate?.readerProgressValueChanged(type: type, value: Int(slider.value))
                    }
                    isSliderMoved = false
                    break
                default:
                    break
                }
            }
            
            break
        case .style:
//            let isTouchEnd = event.allTouches?.first?.phase == .ended
            
            let scaleValue = Float(lroundf(slider.value))
            self.slider.value = scaleValue
            if value != scaleValue {
                self.slider.value = scaleValue
                value = slider.value
                //做定时器延时。
                dispatchStyleDelay(value: Int(slider.value))
            }
            break
        case .light:
            value = slider.value
            
            WDReaderConfig.setUserBrightness(brightness: Double(value))
            UIScreen.main.brightness = CGFloat(value)
            //不发送代理
            break
        default:
            break
        }
    }
    
    var delayStyleWork:DispatchWorkItem?
    func dispatchStyleDelay(value:Int){
        delayStyleWork?.cancel()
        delayStyleWork = DispatchWorkItem {[weak self] in
            guard let self = self else{ return }
            self.delegate?.readerProgressValueChanged(type: self.type, value: value)
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: delayStyleWork!)
    }
    
    func changeFontName(){
        if type == .style{
            dispatchStyleDelay(value: Int(slider.value))
        }
    }
    
    func showProgressTips(){
        if let superV = superview, tips == nil{
            tips = OutlineProgressTips(frame: CGRect(x: (frame.width - 312)/2, y: frame.minY - 38, width: 312, height: 60))
            tips?.delegate = self
            superV.addSubview(tips!)
            tips?.snp.makeConstraints({ (make) in
                make.width.equalTo(312)
                make.height.equalTo(60)
                make.centerX.equalTo(superV)
                make.bottom.equalTo(self.snp.top).offset(-38)
            })
        }
    }
    
    func hideProgressTips(){
        tips?.removeFromSuperview()
        tips = nil
    }
    
    //滑动翻页走的时候onSliderValChanged，已经设置了状态。不需要再次更改。
    func update(value:Float){
        isChanged = true
        self.value = value
        slider.value = value
        
        //更新maxView
        maxValue = Float(Paginator.current.getPagesCount())
        slider.maximumValue = maxValue
        
        //更新tips
        tips?.updateValue(value: Int(value), maxValue: Int(maxValue))
    }
    
//    func update(chapterIndex:Int,hrefAndFragmentId:String,title:String,isSaveRecord:Bool = true){
//        if type == .progress,isSaveRecord{
//            preValue = Int(slider.value)
//        }
//
//        self.value = value
//        slider.value = value
//
//        if type == .progress,isSaveRecord{
//            nextValue = Int(slider.value)
//        }
//
//        //更新maxView
//        maxValue = Float(Paginator.current.getPagesCount())
//        slider.maximumValue = maxValue
//
//        tips?.updateValue(value: Int(value), maxValue: Int(maxValue))
//
//    }

    func update(title:String){
        isChanged = true
        tips?.updateValue(title: title)
    }
    
    //跳转记录。
    func outlineProgressTipsRecordJump() {
        if isChanged, let lastRecordIndexPath = Paginator.current.lastRecordIndexPath,Paginator.current.currentIndexPath != lastRecordIndexPath{
            
            if Paginator.current.isAllLoad {
                value = Float(Paginator.current.getPageNum(indexPath: lastRecordIndexPath))
                slider.value = value
                
                //更新maxView
                maxValue = Float(Paginator.current.getPagesCount())
                slider.maximumValue = maxValue
                
                //更新tips
                tips?.updateTitleAndValue(value: Int(value), maxValue: Int(maxValue))
                tips?.turnAroundIcon()
            }else{
                tips?.updateTitle(title: Paginator.current.getSubLevelChapterName(indexPath: lastRecordIndexPath) ?? "")
                tips?.turnAroundIcon()
            }
            //跳转
            delegate?.readerProgressRecordJump(type: type, isPre: true, indexPath: lastRecordIndexPath)
            
        }
    }
}


class WDCustomSlider: UISlider {
    
    static func generate() -> WDCustomSlider{
        let slider = WDCustomSlider()
    //        slider.thumbTintColor = UIColor(hex: 0x4C4C4C)
        slider.setThumbImage(dynamicThumbColor.makeCircleWith(size: CGSize(width: 16, height: 16))!, for: .normal)
        
    //        slider.tintColor = UIColor.red
         slider.minimumTrackTintColor = dynamicThumbColor
         slider.maximumTrackTintColor = dynamicMaximumColor
        return slider
    }
    
    override func trackRect(forBounds bounds: CGRect) -> CGRect {
       var newBounds = super.trackRect(forBounds: bounds)
       newBounds.size.height = 2
       return newBounds
    }
    
    func enableState(){
        setThumbImage(dynamicThumbColor.makeCircleWith(size: CGSize(width: 16, height: 16))!, for: .normal)
    }
    
    func disableState(){
        setThumbImage(dynamicDisableThumbColor.makeCircleWith(size: CGSize(width: 16, height: 16))!, for: .normal)
    }
}

let dynamicThumbColor = UIColor { (trainCollection) -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x444444)
    } else {
        return UIColor(hex:0x4C4C4C)
    }
}
let dynamicMaximumColor = UIColor { (trainCollection) -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x444444)
    } else {
        return UIColor(hex:0xD7DADE)
    }
}

let dynamicDisableThumbColor = UIColor { (trainCollection) -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x282828)
    } else {
        return UIColor.lightGray
    }
}

//class WDReaderSlider:UIView{
//    
//    var slider:WDCustomSlider
//    override init(frame: CGRect) {
//        slider = WDCustomSlider.generate()
//        super.init(frame:frame)
//    }
//    
//    required init?(coder: NSCoder) {
//        fatalError("init(coder:) has not been implemented")
//    }
//}
