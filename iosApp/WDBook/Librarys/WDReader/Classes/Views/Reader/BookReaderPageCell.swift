//  BookReaderPageCell.swift
//  EpubDemo
//
//  Created by <PERSON> on 2020/5/17.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import SnapKit
import <PERSON><PERSON><PERSON>serDefaults
import UIKit

protocol BookReaderPageCellDelegate: AnyObject {
    func superViewController() -> UIViewController

    func readerPageCell(_ pageCell: BookReaderPageCell, shouldInteractWith URL: URL, in characterRange: NSRange, location: CGPoint)
    func readerPageCellDidSelected(_ pageCell: BookReaderPageCell, position: TapPosition)
    func readerPageCellHideNavAndToolBar()

    func readerPageCellSelectdModeChanged(_ pageCell: BookReaderPageCell, isSelected: Bool)
    func readerPageCellSelectedTextChangePage(_ pageCell: BookReaderPageCell, isNext: Bool)
    func readerPageCellGetCurrentPageCell() -> BookReaderPageCell?
    func readerPageCellShowNoteEditer(_ pageCell: BookReaderPageCell, note: Note)
    func readerPageCellShowQuickNoteEntries(_ pageCell: BookReaderPageCell, notes: [Note])
    func readerPageCellShowNoteGuide(_ pageCell: BookReaderPageCell)
    func readerPageCellShowPhotoBrowser(_ pageCell: BookReaderPageCell, photo: UIImage, sourceView: UIView, sourceFrame: CGRect)
}

class BookReaderPageCell: ReuseViewCell, UIGestureRecognizerDelegate {
    weak var delegate: BookReaderPageCellDelegate?

    var page: Page?
    var ctCellView: CTView?
    var chapterNameLabel: UILabel!
    var pageNumLabel: UILabel!
    var quickEntryView: UIView!
    var quickEntryLabel: UILabel!
    var quickEntryBtn: UIButton!

    var highlightView: HighlightRangeView?
    var currentPageNotes: [Note] = .init()
    var noteEntryHighlightViews: [UIView] = .init()
    static let MaxNoteNum = 11

    var bookMarkImageView: UIImageView?
    var isBookmarkDragging: Bool = false

    override init(frame: CGRect) {
        print("CTCollectionCell init: \(frame)")
        super.init(frame: frame)
        backgroundColor = dynamicBackgroundColor1
//        backgroundColor = .purple

        chapterNameLabel = UILabel()
        chapterNameLabel.textAlignment = .center
        chapterNameLabel.textColor = dynamicTextColor3
        chapterNameLabel.font = UIFont.regular(size: 12)
        addSubview(chapterNameLabel)
        chapterNameLabel.snp.makeConstraints { make in
            make.top.equalTo(27)
            make.leading.equalTo(32)
            make.trailing.equalTo(-32)
            make.height.equalTo(17)
        }

        pageNumLabel = UILabel()
        pageNumLabel.textAlignment = .right
        pageNumLabel.textColor = dynamicTextColor3
        pageNumLabel.font = UIFont.regular(size: 12)
        addSubview(pageNumLabel)
        pageNumLabel.snp.makeConstraints { make in
            make.bottom.equalTo(-15)
            make.trailing.equalTo(-24)
            make.height.equalTo(17)
            make.width.equalTo(120)
        }

        // 快捷笔记入口
        quickEntryView = UIView()
        quickEntryView.backgroundColor = dynamicQuickEntryColor
        quickEntryView.layer.cornerRadius = 2
        quickEntryView.layer.masksToBounds = true
        addSubview(quickEntryView)
        quickEntryView.snp.makeConstraints { make in
            make.bottom.equalTo(-15)
            make.leading.equalTo(32)
            make.height.equalTo(17)
            make.width.equalTo(57)
        }
        quickEntryLabel = UILabel()
        quickEntryLabel.textAlignment = .left
        quickEntryLabel.textColor = UIColor.white
        quickEntryLabel.font = UIFont.regular(size: 10)
        quickEntryView.addSubview(quickEntryLabel)
        quickEntryLabel.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.bottom.equalToSuperview()
            make.leading.equalTo(5)
            make.width.equalTo(52)
        }

        let iconSize = CGSize(width: 15.0, height: 15.0)
        let narrowSize = CGSize(width: iconSize.width / 2, height: iconSize.height / 2)
        let narrow = UIView()
        let path = CGMutablePath()
        path.move(to: CGPoint(x: 0, y: 0))
        path.addLine(to: CGPoint(x: narrowSize.width / 2, y: narrowSize.height / 2))
        path.addLine(to: CGPoint(x: 0, y: narrowSize.height))
        path.addLine(to: CGPoint(x: 0, y: 0))

        let shape = CAShapeLayer()
        shape.path = path
        shape.fillColor = UIColor.white.cgColor
        narrow.layer.addSublayer(shape)

        quickEntryView.addSubview(narrow)
        narrow.snp.makeConstraints { make in
            make.trailing.equalTo(-(iconSize.width - narrowSize.width) / 2)
            make.centerY.equalToSuperview()
            make.width.equalTo(narrowSize.width)
            make.height.equalTo(narrowSize.height)
        }

        quickEntryBtn = UIButton()
        quickEntryBtn.addTarget(self, action: #selector(tapQuickNoteEntry(btn:)), for: .touchUpInside)
        quickEntryView.addSubview(quickEntryBtn)
        quickEntryBtn.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        //        contentView.backgroundColor = UIColor.purple

        // 书签相关
        let insets: UIEdgeInsets = UIApplication.shared.windows[0].safeAreaInsets

        bookMarkImageView = UIImageView(image: UIImage(named: "bookmark_light"))
        addSubview(bookMarkImageView!)
        bookMarkImageView!.snp.makeConstraints { make in
            make.top.equalTo(-insets.top)
            make.width.equalTo(16)
            make.height.equalTo(48)
            make.trailing.equalTo(-21)
        }
        bookMarkImageView?.isHidden = true

        NotificationCenter.default.addObserver(self, selector: #selector(showNavBar(noti:)), name: BookReaderVC.showNavBarNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(hideNavBar(noti:)), name: BookReaderVC.hideNavBarNotification, object: nil)

        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(tapAction(_:)))
        tapGesture.delegate = self
        addGestureRecognizer(tapGesture)

        NotificationCenter.default.addObserver(self, selector: #selector(loadAllPagesComplete(noti:)), name: Paginator.isAllLoadChangedNotification, object: nil)
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    //    override func draw(_ rect: CGRect) {
    //        guard let context = UIGraphicsGetCurrentContext() else { return }
    //
    //        context.textMatrix = .identity
    //        context.translateBy(x: 0, y: bounds.size.height)
    //        context.scaleBy(x: 1.0, y: -1.0)
    //
    //        //所有文字高亮
    ////        var allRects = page!.ctPage.ctFrame.getRangeRects(frame:rect,range: page!.ctPage.range)
    ////        allRects = allRects.map{CGRect(origin:CGPoint(x: $0.origin.x + Paginator.PAGE_INSERT.left, y: $0.origin.y + Paginator.PAGE_INSERT.bottom), size: $0.size)}
    //
    //        //所有文字右边沿显示 笔记入口。
    //        var allRects = page!.ctPage.ctFrame.getRangeBorderEntryRects(frame:rect.inset(by: Paginator.PAGE_INSERT),range: page!.ctPage.range)
    //        allRects = allRects.map{CGRect(origin:CGPoint(x: $0.origin.x, y: $0.origin.y + Paginator.PAGE_INSERT.bottom), size: $0.size)}
    //        let fillPath = CGMutablePath()
    //        UIColor.yellow.setFill()
    //        fillPath.addRects(allRects)
    //        context.addPath(fillPath)
    //        context.fillPath()
    //    }

    func gestureRecognizer(_: UIGestureRecognizer, shouldReceive touch: UITouch) -> Bool {
        if touch.view is UIButton {
            return false
        }
        return true
    }

    @objc func tapAction(_ g: UIGestureRecognizer) {
        if SelectMenu.isShowing(), let tips = SelectMenu.menu?.noteTips {
            let pointInTips = g.location(in: tips)
            if tips.bounds.contains(pointInTips) {
                SelectMenu.menu?.selectedNoteOnModifyMode()
                SelectMenu.hide()
                return
            }
        }

        if !SelectedRangeManager.isEmpty() {
            ctCellView?.clearSelectedState()
            return
        }

        let point = g.location(in: self)
        //        if let tappedInfo = self.ctFrame.isTapLink(point: point){
        //            delegate?.ctView(self, interactWith: tappedInfo.url, in: tappedInfo.range,location: tappedInfo.location)
        //        }else{
        var position = TapPosition.center
        if point.x <= bounds.width * Paginator.PAGE_TAP_AREA_RATE_LEFT {
            ctCellView?.clearSelectedState()
            position = .left
        } else if point.x >= bounds.width * Paginator.PAGE_TAP_AREA_RATE_RIGHT {
            ctCellView?.clearSelectedState()
            position = .right
        }
        delegate?.readerPageCellDidSelected(self, position: position)
        //        }
    }

    func resetTxt(page: Page, chapterName: String?, pageNum: String?) {
        exitLoadingState()

        self.page = page
        if ctCellView == nil {
            let f = CGRect(x: 0, y: 0, width: frame.width, height: frame.height).inset(by: Paginator.PAGE_INSETS)
            ctCellView = CTView(frame: f, page: page, ctFrame: page.ctPage.ctFrame, imageAttchaments: page.ctPage.imageAttachments)
            insertSubview(ctCellView!, at: 0)
//            ctCellView!.snp.makeConstraints { (make) in
//                make.edges.equalTo(Paginator.PAGE_INSERT)
//            }
        } else {
            ctCellView!.resetCTPage(page: page, ctFrame: page.ctPage.ctFrame, imageAttchaments: page.ctPage.imageAttachments)
            refreshBookMarkCheckNav()
        }

        ctCellView!.delegate = self

        let para = NSMutableParagraphStyle()
        para.lineBreakMode = .byTruncatingTail
        para.baseWritingDirection = .leftToRight
        let attributedString = NSMutableAttributedString(string: chapterName ?? "", attributes: [NSAttributedString.Key.font: chapterNameLabel.font ?? UIFont.regular(size: 12),
                                                                                                 NSAttributedString.Key.paragraphStyle: para,
                                                                                                 NSAttributedString.Key.foregroundColor: chapterNameLabel.textColor ?? UIColor(hex: 0x707A89)])
        chapterNameLabel.attributedText = attributedString
        pageNumLabel.text = pageNum
        updateHighlightArea()
    }

    func enterLoadingState() {
        ctCellView?.isHidden = true
        chapterNameLabel.isHidden = true
        pageNumLabel.isHidden = true
        showLoading()
    }

    func exitLoadingState() {
        ctCellView?.isHidden = false
        chapterNameLabel.isHidden = false
        pageNumLabel.isHidden = false
        hideLoading()
    }

    func layout(size _: CGSize) {
        let f = CGRect(x: 0, y: 0, width: frame.width, height: frame.height).inset(by: Paginator.PAGE_INSETS)
        ctCellView?.frame = f
    }

    func clearSelectedState(isHideMenu: Bool = true) {
        ctCellView?.clearSelectedState(isHideMenu: isHideMenu)
    }

    @objc func loadAllPagesComplete(noti: Notification) {
        if let isAllLoad = noti.object as? Bool,
           isAllLoad,
           let pageIndexPath = indexPath,
           Paginator.current != nil,
           pageIndexPath == Paginator.current.currentIndexPath
        {
            pageNumLabel.text = Paginator.current.getCurrentPageAndTotalNum(indexPath: pageIndexPath)
        }
    }
}

extension BookReaderPageCell: CTViewDelegate {
    // MARK: CTViewDelegate

    func ctView(_: CTView, interactWith URL: URL, in characterRange: NSRange, location: CGPoint) {
        delegate?.readerPageCell(self, shouldInteractWith: URL, in: characterRange, location: location)
    }

    func ctViewDidSelected(_: CTView, position: TapPosition) {
        delegate?.readerPageCellDidSelected(self, position: position)
    }

    func ctViewHideNavAndToolBar() {
        delegate?.readerPageCellHideNavAndToolBar()
    }

    func ctViewSelectdModeChanged(_: CTView, isSelected: Bool) {
        delegate?.readerPageCellSelectdModeChanged(self, isSelected: isSelected)
    }

    func ctViewSelectedTextChangePage(_: CTView, isNext: Bool) {
        delegate?.readerPageCellSelectedTextChangePage(self, isNext: isNext)
    }

    func ctViewGetCurrentPageCell() -> BookReaderPageCell? {
        delegate?.readerPageCellGetCurrentPageCell()
    }

    func ctViewShowPhotoBrowser(_: CTView, photo: UIImage, sourceView: UIView, sourceFrame: CGRect) {
        delegate?.readerPageCellShowPhotoBrowser(self, photo: photo, sourceView: sourceView, sourceFrame: sourceFrame)
    }

    func ctViewGetSelfPageCell() -> BookReaderPageCell {
        self
    }
}

extension BookReaderPageCell {
    // MARK: 高亮

    func updateHighlightArea() {
        if let href = page?.href {
            var allNotes: [Note] = []

            // Add user notes
            if let userNotes = HighlightRangeManager.chapterNotesDic?[href] {
                allNotes.append(contentsOf: userNotes)
            }

            // Add search highlights first (they take precedence)
            if let searchNotes = SearchState.shared.getSearchNotes(chapterPath: href) {
                allNotes.append(contentsOf: searchNotes)
            }

            if !allNotes.isEmpty {
                let sortedNotes = allNotes.sorted {
                    let showRange0 = $0.showRange
                    let showRange1 = $1.showRange
                    return (showRange0.location, showRange0.length) < (showRange1.location, showRange1.length)
                }
                showHighlightRangeView(notes: sortedNotes)
            } else {
                hideHighlightRangeView()
            }
        } else {
            hideHighlightRangeView()
        }
    }

    func clearHighlightState() {
        highlightView?.clear()
        hideHighlightRangeView()

        HighlightRangeManager.clear()
    }

    func hideHighlightRangeView() {
        if highlightView != nil {
            highlightView?.removeFromSuperview()
            highlightView = nil
        }
        clearNoteEntriesHighlight()
    }

    private func showHighlightRangeView(notes: [Note]) {
        let ctViewFrame = CGRect(x: 0, y: 0, width: frame.width, height: frame.height).inset(by: Paginator.PAGE_INSETS)
        if highlightView == nil {
            highlightView = HighlightRangeView(frame: ctViewFrame, page: page!)
            //            highlightView?.delegate = self
            //            addSubview(highlightView!)
            insertSubview(highlightView!, at: 0)
        }
        highlightView?.frame = ctViewFrame
        highlightView?.update(page: page!)
        highlightView?.update(by: notes)

        refreshNoteEntries(notes: notes)
    }

    // MARK: 笔记入口

    private func refreshNoteEntries(notes: [Note]) {
        //        setNeedsDisplay()

        // 测试显示所有行的矫正值
        //        var allRects = page!.ctPage.ctFrame.getRangeRects(range: page!.ctPage.range)
        //        转换rect到uiview坐标系； 增加insert
        //        allRects = allRects.map{CGRect(origin:CGPoint(x: $0.origin.x + Paginator.PAGE_INSERT.left, y: highlightView!.frame.height - $0.maxY + Paginator.PAGE_INSERT.top), size: $0.size)}

        clearNoteEntriesHighlight()

        ////所有行对应的右边的位置。
//        var allFixedRightEntryRects = page!.ctPage.ctFrame.getRangeBorderEntryRects(frame:highlightView!.frame,range: page!.ctPage.range)
        // 笔记入口区域计算
//        allFixedRightEntryRects = allFixedRightEntryRects.map{CGRect(origin:CGPoint(x: $0.origin.x, y: highlightView!.frame.height - $0.maxY + Paginator.PAGE_INSERT.top), size: $0.size)}
        // 笔记触发区域计算
//        allFixedRightEntryRects = allFixedRightEntryRects.map{CGRect(origin:CGPoint(x: $0.origin.x - 10, y: highlightView!.frame.height - $0.maxY + Paginator.PAGE_INSERT.top), size: CGSize(width: frame.maxX - highlightView!.frame.maxX, height: $0.size.height) )}
//        for r in allFixedRightEntryRects{
//            Log.d(r)
//            let btn = UIButton(frame: r)
        ////            btn.setTitle("哈哈", for: .normal)
//            btn.backgroundColor = UIColor.yellow.alpha(0.3)
//            btn.addTarget(self, action: #selector(tapNoteEntry(btn:)), for: .touchUpInside)
//            addSubview(btn)
//            noteEntryHighlightBtns.append(btn)
//        }

        // 1、计算所有笔记在当前页的第一行对应的右边位置
        for i in 0 ..< notes.count {
            let note = notes[i]
            let range = note.showRange
            // 笔记包含当前页,并且有内容。
            if let intersectionRange = range.intersection(page!.ctPage.range), intersectionRange.length > 0,!note.noteText.isEmpty {
                let noteEntries = page!.ctPage.getRangeBorderEntryRects(frame: highlightView!.frame, range: intersectionRange)
                let entryRects = noteEntries.map { CGRect(origin: CGPoint(x: $0.origin.x, y: highlightView!.frame.height - $0.maxY + Paginator.PAGE_INSETS.top), size: $0.size) }
                let entryInteractRects = noteEntries.map { CGRect(origin: CGPoint(x: $0.origin.x - 10, y: highlightView!.frame.height - $0.maxY + Paginator.PAGE_INSETS.top), size: CGSize(width: frame.maxX - highlightView!.frame.maxX, height: $0.size.height)) }
                Log.d(noteEntries)
                if let firstEntry = entryRects.first, let firstEntryInteract = entryInteractRects.first {
                    note.entryRect = firstEntry
                    note.entryInteractRect = firstEntryInteract
                    currentPageNotes.append(note)
                }
            }
        }

        Log.d(currentPageNotes.count)
        if currentPageNotes.count < BookReaderPageCell.MaxNoteNum {
            // 2、调整位置
            for i in 0 ..< currentPageNotes.count {
                if i > 0,
                   i < currentPageNotes.count,
                   currentPageNotes[i - 1].entryRect!.maxY + 8 > currentPageNotes[i].entryRect!.minY
                {
                    currentPageNotes[i].entryRect?.origin.y = currentPageNotes[i - 1].entryRect!.maxY + 8
                    currentPageNotes[i].entryInteractRect?.origin.y = currentPageNotes[i - 1].entryRect!.maxY + 8
                }
                currentPageNotes[i].entryInteractRect = CGRect(x: currentPageNotes[i].entryInteractRect!.origin.x, y: currentPageNotes[i].entryInteractRect!.origin.y - 4, width: currentPageNotes[i].entryInteractRect!.size.width, height: currentPageNotes[i].entryInteractRect!.size.height + 8)

                // 3、展示
                let note = currentPageNotes[i]
                let entryView = UIView(frame: note.entryInteractRect!)
//                entryView.backgroundColor = UIColor.red
                // 显示区域
                let icon = UIView(frame: CGRect(x: 10, y: 4, width: note.entryRect!.size.width, height: note.entryRect!.size.height))
                icon.backgroundColor = note.style.showColor
                entryView.addSubview(icon)
                // 触发区域
                let btn = UIButton(frame: CGRect(x: 0, y: 0, width: entryView.frame.width, height: entryView.frame.height))
                btn.tag = i
                btn.addTarget(self, action: #selector(tapNoteEntry(btn:)), for: .touchUpInside)
                entryView.addSubview(btn)
                addSubview(entryView)
                noteEntryHighlightViews.append(entryView)
            }
        }

        refreshQuickNoteEntryHighlight()
    }

    private func clearNoteEntriesHighlight() {
        noteEntryHighlightViews.forEach { subView in
            subView.removeFromSuperview()
        }
        noteEntryHighlightViews = [UIView]()
        currentPageNotes = [Note]()

        quickEntryView.isHidden = true
    }

    private func refreshQuickNoteEntryHighlight() {
        if currentPageNotes.count >= BookReaderPageCell.MaxNoteNum {
            quickEntryView.isHidden = false
            quickEntryLabel.text = "note_count".localizedFormat(currentPageNotes.count)
            if !Defaults[key: DefaultsKeys.IS_MARKED_NOTE_GUIDE] {
                DispatchQueue.main.async { [unowned self] in
                    SelectMenu.hide()
                    delegate?.readerPageCellShowNoteGuide(self)
                }
                Defaults[key: DefaultsKeys.IS_MARKED_NOTE_GUIDE] = true
            }
        } else {
            quickEntryView.isHidden = true
        }
    }

    @objc func tapNoteEntry(btn: UIButton) {
        let note = currentPageNotes[btn.tag]
        delegate?.readerPageCellShowNoteEditer(self, note: note)
    }

    @objc func tapQuickNoteEntry(btn _: UIButton) {
        delegate?.readerPageCellShowQuickNoteEntries(self, notes: currentPageNotes)
    }
}

// MARK: 书签

extension BookReaderPageCell {
    func refreshBookMarkCheckNav() {
        if let page = page {
            if let bookReaderVC = delegate?.superViewController() as? BookReaderVC,
               bookReaderVC.isHideNavAndTool
            {
                if let _ = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range) {
                    bookMarkImageView?.isHidden = false
                } else {
                    bookMarkImageView?.isHidden = true
                }
            } else {
                bookMarkImageView?.isHidden = true
            }
        }
    }

    func refreshBookMark() {
        if let page = page {
            if let _ = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range) {
                bookMarkImageView?.isHidden = false
            } else {
                bookMarkImageView?.isHidden = true
            }
        }
    }

    func hideBookmark() {
        isBookmarkDragging = true
        bookMarkImageView?.isHidden = true
    }

    func resetBookmark() {
        isBookmarkDragging = false
        refreshBookMark()
    }

//    @objc func updateBookMark(btn:UIButton) {
//        if let page = page{
//            if let bookMark = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range){
//                BookmarkManager.remove(bookmark: bookMark)
//                bookMarkImageView?.isHidden = true
//            }else{
//                BookmarkManager.add(chapterPath: page.href, range: page.ctPage.range)
//                bookMarkImageView?.isHidden = false
//            }
//        }
//    }

    @objc func showNavBar(noti _: Notification) {
        bookMarkImageView?.isHidden = true
    }

    @objc func hideNavBar(noti _: Notification) {
        if !isBookmarkDragging {
            refreshBookMark()
        }
    }
}
