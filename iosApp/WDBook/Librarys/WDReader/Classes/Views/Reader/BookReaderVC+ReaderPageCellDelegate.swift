//
//  BookReaderVC+ReaderPageCellDelegate.swift
//  WDReader
//
//  Created by <PERSON> on 2021/3/3.
//  Copyright © 2021 <PERSON>. All rights reserved.
//

import CoreGraphics
import Foundation
import SwiftUI
import UIKit

extension BookReaderVC: BookReaderPageCellDelegate {
    func superViewController() -> UIViewController {
        self
    }

    func readerPageCell(_ ctCollectionCell: BookReaderPageCell, shouldInteractWith URL: URL, in characterRange: NSRange, location _: CGPoint) {
        enterFullModeWorkItem?.cancel()
        hideNavAndTool()

        // 注脚
        if URL.absoluteString.hasPrefix("#") {
            if let attributedString = paginator.getChapter(href: ctCollectionCell.page?.href ?? "")?.originalAttributedString {
                if let footNote = attributedString.getEpubFootNote(id: URL.absoluteString) {
                    Log.d("脚注location:\(String(describing: footNote.location)),位置：\(String(describing: footNote.numInChapter)),内容:\(footNote.content)")
                    FootNoteV.show(footNote: footNote, on: navigationController?.view ?? view)
                }
            }
            // 经文注脚
        } else if URL.absoluteString.hasPrefix("wdbible://bible/") {
            let versesResult = BibleContent.findTitleContent(herf: URL.absoluteString.replacingOccurrences(of: "wdbible://bible/", with: ""))

            let footNote = FootNote(type: .bible, title: versesResult.0, content: versesResult.1, location: nil, numInChapter: nil)
            FootNoteV.show(footNote: footNote, on: navigationController?.view ?? view)
        } else if URL.isHttpProtocol {
            if UIApplication.shared.canOpenURL(URL) {
                UIApplication.shared.open(URL, options: [:]) { success in
                    if success {
                        print("10以后可以跳转url")
                    } else {
                        print("10以后不能完成跳转")
                    }
                }
            }
        } else if URL.scheme == nil, URL.absoluteString.contains("html#"), let split = URL.absoluteString.split(separator: "#").map({ String($0) }) as? [String], split.count == 2, let href = split.first, href.hasSuffix("html") {
            let toChapterHref = "EPUB/" + href
            let toChapterIndex = paginator.getChapterIndex(href: toChapterHref)
            let toHrefAndFragmentId = "EPUB/" + URL.absoluteString

            let returnChapterHref = paginator.currentChapter!.href
            let returnContentOffset = characterRange.location

            gotoPage(chapterIndex: toChapterIndex, hrefAndFragmentId: toHrefAndFragmentId)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self else { return }
                self.addAnchorReturnV(chapterHref: returnChapterHref, contentOffset: returnContentOffset) { [weak self] href, contentOffset in
                    Log.d("returnV:返回2\(href),\(contentOffset),\(String(describing: self))")
                    self?.gotoPage(href: href, contentOffset: contentOffset)
                }
            }
        } else if URL.scheme == nil, URL.absoluteString.hasSuffix("html") {
            let href = URL.absoluteString

            let toChapterHref = "EPUB/" + href
            let toChapterIndex = paginator.getChapterIndex(href: toChapterHref)
            let toHrefAndFragmentId = "EPUB/" + URL.absoluteString

            let returnChapterHref = paginator.currentChapter!.href
            let returnContentOffset = characterRange.location

            gotoPage(chapterIndex: toChapterIndex, hrefAndFragmentId: toHrefAndFragmentId)
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                guard let self = self else { return }
                self.addAnchorReturnV(chapterHref: returnChapterHref, contentOffset: returnContentOffset) { [weak self] href, contentOffset in
                    Log.d("returnV:返回2\(href),\(contentOffset),\(String(describing: self))")
                    self?.gotoPage(href: href, contentOffset: contentOffset)
                }
            }
        }
    }

    func readerPageCellDidSelected(_: BookReaderPageCell, position: TapPosition) {
        // 点击页面
        enterFullModeWorkItem?.cancel()
        if isHideNavAndTool {
            switch position {
            case .left:
                Log.d("点击左页面")
                gotoPreviousPage()
            case .right:
                Log.d("点击右页面")
                gotoNextPage()
            default:
                // 显示
                showNavAndTool()
            }

        } else {
            // 隐藏
            hideNavAndTool()
        }
    }

    func readerPageCellHideNavAndToolBar() {
        enterFullModeWorkItem?.cancel()
        hideNavAndTool()
    }

    func readerPageCellSelectdModeChanged(_: BookReaderPageCell, isSelected: Bool) {
        if isSelected {
            hideNavAndTool()
        }
    }

    func readerPageCellSelectedTextChangePage(_ pageCell: BookReaderPageCell, isNext: Bool) {
        Log.d("当前选中页:\(pageCell),索引:\(String(describing: pageCell.indexPath))")
        guard reuseView.isUserInteractionEnabled else {
            return
        }
        // 获取当前页，不从pageCell获取，暂时数据不对。
        if let chapter = paginator.currentChapter, chapter.pages.count > currentIndexPath.item {
            let page = chapter.pages[currentIndexPath.item]
            let href = page.href
            // 通过事件获取数据
//                 50-100,
//                 50-100,100-200,
//                 50-100,100-200,200-300,
//                 50-100,100-200,
//                 0 -50,
//         -100-0,0 - 50,
            // -200-100,-100-0,0 - 50,
//         -100-0,0 - 50,
//                 50-100,
//        if let page = pageCell.page,let chapter = page.chapter{
            let fixedPosition = SelectedRangeManager.fiexdPosition

            if isNext {
                if page.pageIndexInChapter < chapter.pages.count - 1 {
                    let nextPage = chapter.pages[page.pageIndexInChapter + 1]

                    var newRange: NSRange!
                    if fixedPosition < nextPage.ctPage.range.location {
                        newRange = NSRange(location: fixedPosition, length: nextPage.ctPage.range.location + nextPage.ctPage.range.length - fixedPosition)
                        let newLocation = newRange.location + newRange.length
                        if SelectedRangeManager.pageCount(chapterPath: href, newLocation: newLocation) <= SelectedRangeManager.MAX_SELECTED_PAGE_COUNT {
                            SelectedRangeManager.updateRange(chapterPath: href, newLocation: newLocation)
                            gotoNextPage()
                        }
                    } else {
                        newRange = NSRange(location: nextPage.ctPage.range.location + nextPage.ctPage.range.length, length: fixedPosition - (nextPage.ctPage.range.location + nextPage.ctPage.range.length))
                        let newLocation = newRange.location
                        if SelectedRangeManager.pageCount(chapterPath: href, newLocation: newLocation) <= SelectedRangeManager.MAX_SELECTED_PAGE_COUNT {
                            SelectedRangeManager.updateRange(chapterPath: href, newLocation: newLocation)
                            gotoNextPage()
                        }
                    }
                } else {
                    Log.d("选中时候不能翻页")
                }
            } else {
                if page.pageIndexInChapter > 0 {
                    let prePage = chapter.pages[page.pageIndexInChapter - 1]

                    var newRange: NSRange!
                    if fixedPosition < prePage.ctPage.range.location {
                        newRange = NSRange(location: fixedPosition, length: prePage.ctPage.range.location - fixedPosition)
                        let newLocation = prePage.ctPage.range.location
                        if SelectedRangeManager.pageCount(chapterPath: href, newLocation: newLocation) <= SelectedRangeManager.MAX_SELECTED_PAGE_COUNT {
                            SelectedRangeManager.updateRange(chapterPath: href, newLocation: newLocation)
                            gotoPreviousPage()
                        }

                    } else {
                        newRange = NSRange(location: prePage.ctPage.range.location, length: fixedPosition - prePage.ctPage.range.location)
                        let newLocation = newRange.location
                        if SelectedRangeManager.pageCount(chapterPath: href, newLocation: newLocation) <= SelectedRangeManager.MAX_SELECTED_PAGE_COUNT {
                            SelectedRangeManager.updateRange(chapterPath: href, newLocation: newLocation)
                            gotoPreviousPage()
                        }
                    }

                } else {
                    Log.d("选中时候不能翻页")
                }
            }
        }
    }

    func readerPageCellGetCurrentPageCell() -> BookReaderPageCell? {
        let cell = reuseView.getCurrentCell() as? BookReaderPageCell
        cell?.indexPath = currentIndexPath
        return cell
    }

    func readerPageCellShowNoteEditer(_ pageCell: BookReaderPageCell, note: Note) {
        if HighlightRangeManager.isNeedMerge(href: pageCell.page?.href ?? "", range: note.showRange) {
            let newNote = HighlightRangeManager.generateMergeNote(href: pageCell.page?.href ?? "", range: note.showRange)
            if newNote.noteText.count > NoteEditerVC.MAX_TEXT_LENGTH {
                let alert = UIAlertController(title: "操作错误", message: "笔记合并超出字数限制", preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "取消", style: .cancel, handler: nil))
                alert.addAction(UIAlertAction(title: "继续合并", style: .default, handler: { _ in
                    continueMerge()
                }))
                present(alert, animated: true, completion: nil)
                return
            }
            func continueMerge() {
                let noteEditorVC = NoteEditerVC()
                noteEditorVC.isTempNote = true
                noteEditorVC.isForMerge = true
                // noteEditorVC.delegate = self
                noteEditorVC.note = newNote
                present(noteEditorVC, animated: true, completion: nil)
            }
            continueMerge()
        } else {
            let noteEditorVC = NoteEditerVC()
            noteEditorVC.note = note
            present(noteEditorVC, animated: true, completion: nil)
        }
    }

    func readerPageCellShowQuickNoteEntries(_: BookReaderPageCell, notes: [Note]) {
        // 1、显示快捷入口笔记列表，
        // 2、从快捷入口笔记列表跳转笔记编辑页面，直接使用Note对象。
        // 3,编辑完笔记后，不需要通知。在笔记列表的.onAppear刷新数据
        Log.d("显示笔记列表")

//        if let page = pageCell.page{
//            let noteEntities = WDBookUserSDK.shared.getPageNoteEntityList(resourceId: WDReaderConfig.resourceId, pathIndex: page.chapterPath, firstWordPosition: page.ctPage.range.location, lastWordPosition: page.ctPage.range.location + page.ctPage.range.length)
//            //显示笔记列表。
//
//        }

        // 显示笔记捕获编辑完成通知。
//            .onReceive(NotificationCenter.default.publisher(for: NoteEditerVC.noteChangedNotification), perform: { (obj) in
//                if let entity = obj.object as? NoteEntity{
//
//                }
//            })
        let userData = NoteMarkUserData(WDReaderConfig.resourceId)
        userData.setQuickNotes(notes: notes)
        let vc = UIHostingController(rootView: QuickNoteView(dismissAction: { [weak self] in
            guard let self = self else { return }
            self.dismiss()
//            HighlightRangeManager.loadNotes(chapterPath: self.paginator.currentIndexPath.section)
//            self.reuseView.reload(indexPath: self.paginator.currentIndexPath)
        }, jumpAction: { [weak self] _, _ in
//            self?.dismiss()
//            self?.gotoPage(href: pagePath, contentOffset: contentOffset)
        }).environmentObject(userData))
        present(vc, animated: true, completion: nil)
    }

    func readerPageCellShowNoteGuide(_ pageCell: BookReaderPageCell) {
        enterFullModeWorkItem?.cancel()
        hideNavAndTool(animated: false)

        var targetFrame = pageCell.quickEntryView.frame
        let insets: UIEdgeInsets = UIApplication.shared.windows[0].safeAreaInsets
        targetFrame.origin.y += insets.bottom
        showNoteGuide(sourceRect: targetFrame) { [weak self] in
            self?.hideNoteGuide()
            pageCell.tapQuickNoteEntry(btn: pageCell.quickEntryBtn)
        }
    }

    func readerPageCellShowPhotoBrowser(_: BookReaderPageCell, photo: UIImage, sourceView: UIView, sourceFrame _: CGRect) {
        let photoBrowser = PhotoBrowserVC(photo: photo, sourceView: sourceView)
        photoBrowser.modalPresentationStyle = .fullScreen
        photoBrowser.modalTransitionStyle = .crossDissolve
        present(photoBrowser, animated: true, completion: nil)
    }

    func currentPage() -> Page? {
        readerPageCellGetCurrentPageCell()?.page
    }
}
