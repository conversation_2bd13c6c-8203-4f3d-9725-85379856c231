//
//  OutlineProgressTips.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/3.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation

import UIKit
import SnapKit

protocol OutlineProgressTipsDelegate:AnyObject {
    func outlineProgressTipsRecordJump() //前一步还是后一步
}

class OutlineProgressTips: UIView {

    weak var delegate:OutlineProgressTipsDelegate?
    var gotoIV:UIImageView!
    var gotoBtn:UIButton!
    var spanV:UIView!
    var titleLabel:UILabel!
    var pageNumLabel:UILabel!
    
    var value:Int?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        self.backgroundColor = dynamicBackgroundColor2
        self.layer.cornerRadius = 4
        self.layer.applySketchShadow()
        
        gotoIV = UIImageView(image: UIImage(named: "jump_back"), highlightedImage: UIImage(named: "jump_forward"))
        addSubview(gotoIV)
        gotoIV.snp.makeConstraints { (make) in
            make.width.equalTo(24)
            make.height.equalTo(24)
            make.trailing.equalTo(-30)
            make.centerY.equalToSuperview()
        }

        gotoBtn = UIButton()
        gotoBtn.addTarget(self, action: #selector(tapGoto(btn:)), for: .touchUpInside)
        addSubview(gotoBtn)
        gotoBtn.snp.makeConstraints { (make) in
            make.width.equalTo(46 + 18)
            make.top.bottom.equalToSuperview()
            make.trailing.equalTo(-10)
        }

        spanV = UIView()
        spanV.backgroundColor = UIColor(hex: 0x8E8E93)
        spanV.layer.cornerRadius = 0.5
        spanV.layer.masksToBounds = true
        addSubview(spanV)
        spanV.snp.makeConstraints { (make) in
            make.width.equalTo(1)
            make.top.equalTo(15)
            make.bottom.equalTo(-15)
            make.trailing.equalTo(gotoBtn.snp.leading)
        }

        titleLabel = UILabel()
        titleLabel.text = ""
        titleLabel.textColor = dynamicTextColor4
        titleLabel.font = UIFont.medium(size: 16)
        titleLabel.textAlignment = .center
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { (make) in
            make.leading.equalTo(10)
            make.trailing.equalTo(spanV.snp.leading).offset(-10)
            make.height.equalTo(22)
            make.top.equalTo(self.snp.centerY)
        }

        pageNumLabel = UILabel()
        pageNumLabel.text = ""
        pageNumLabel.textColor = dynamicTextColor4
        pageNumLabel.font = UIFont.regular(size: 12)
        pageNumLabel.textAlignment = .center
        addSubview(pageNumLabel)
        pageNumLabel.snp.makeConstraints { (make) in
            make.leading.equalTo(10)
            make.trailing.equalTo(spanV.snp.leading).offset(-10)
            make.height.equalTo(17)
            make.bottom.equalTo(self.snp.centerY)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func updateValue(value:Int,maxValue:Int){
        self.value = value
        setTitle(Paginator.current.getSubLevelChapterName(pageNum: value))
        pageNumLabel.text = "\(value)/\(maxValue)"
        gotoIV.isHighlighted = false
    }
    
    func updateValue(title:String){
//        self.value = value
        setTitle(title)
        pageNumLabel.text = ""
        gotoIV.isHighlighted = false
    }
    
    func canGotoPre(){
        gotoIV.isHighlighted = false
    }
    func canGotoNext(){
        gotoIV.isHighlighted = true
    }
    
    func updateTitle(title:String){
        setTitle(title)
        pageNumLabel.text = ""
    }
    func updateTitleAndValue(value:Int,maxValue:Int){
        self.value = value
        setTitle(Paginator.current.getSubLevelChapterName(pageNum: value))
        pageNumLabel.text = "\(value)/\(maxValue)"
    }
    
    func turnAroundIcon(){
        gotoIV.isHighlighted = !gotoIV.isHighlighted
    }
    
    @objc func tapGoto(btn:UIButton){
        delegate?.outlineProgressTipsRecordJump()
    }
    
    private func setTitle(_ title:String?){
        let para = NSMutableParagraphStyle()
        para.lineBreakMode = .byTruncatingTail
        para.baseWritingDirection = .leftToRight
        let attributedString = NSMutableAttributedString(string: title ?? "", attributes: [NSAttributedString.Key.font : titleLabel.font ?? UIFont.medium(size: 16),
                                                                                                 NSAttributedString.Key.paragraphStyle:para,
                                                                                                 NSAttributedString.Key.foregroundColor:titleLabel.textColor ?? dynamicTextColor4])
        titleLabel.attributedText = attributedString
    }
}
