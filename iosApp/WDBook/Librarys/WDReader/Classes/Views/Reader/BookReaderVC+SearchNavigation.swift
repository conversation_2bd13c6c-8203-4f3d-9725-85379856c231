//
//  BookReaderVC+SearchNavigation.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/27.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit

// MARK: - Search Navigation

extension BookReaderVC {
    // Setup search navigation components
    func setupSearchNavigation() {
        // Create search result header hosting controller
        searchResultHeaderVC = UIHostingController(rootView: ReaderViewSearchResultHeader(
            clearAction: { [weak self] in
                self?.clearSearch()
            },
            showSearchView: { [weak self] in
                self?.showSearchView()
            }
        ))
        searchResultHeaderVC?.view.backgroundColor = .clear
        searchResultHeaderVC?.view.isHidden = true
        if let headerView = searchResultHeaderVC?.view {
            // Insert below navigation bar to ensure proper z-ordering
            view.insertSubview(headerView, belowSubview: navigationBar)
            headerView.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview()
                make.top.equalTo(reuseViewFrame.minY)
                make.height.equalTo(ReaderNavigationBar.HEIGHT)
            }
        }

        // Create search result navigator hosting controller
        searchResultNavigatorVC = UIHostingController(rootView: ReaderViewSearchResultNavigator.createForReaderView(
            jumpAction: { [weak self] searchResult in
                guard let self = self else { return }
                self.navigateToSearchResult(searchResult)
            },
            showSearchAction: { [weak self] in
                self?.showSearchView()
            }
        ))
        searchResultNavigatorVC?.view.backgroundColor = .clear
        searchResultNavigatorVC?.view.isHidden = true
        if let navigatorView = searchResultNavigatorVC?.view {
            // Insert below toolbar to ensure proper z-ordering
            view.insertSubview(navigatorView, belowSubview: toolBar)
            navigatorView.snp.makeConstraints { make in
                make.leading.trailing.equalToSuperview()
                make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-10)
                make.height.equalTo(36)
            }
        }

        // Add observer for search state changes
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(searchStateDidChange),
            name: NSNotification.Name("SearchStateDidChange"),
            object: nil
        )
    }

    // Common navigation method for search results
    private func navigateToSearchResult(_ searchResult: SearchResult) {
        readerPageCellGetCurrentPageCell()?.clearHighlightState()

        let chapterPath = searchResult.href
        let chapterIndex = paginator.getChapterIndex(href: chapterPath)
        let chapter = paginator.getChapter(chapterIndex: chapterIndex)

        // Check if the chapter is paginated (meaning content is loaded and structured)
        if let loadedChapter = chapter, loadedChapter.pages.count > 0 {
            // Chapter is paginated. Ensure the offset is calculated before navigating.
            // It might already be calculated (adjustedStartOffset >= 0), or we calculate it now.
            if searchResult.adjustedStartOffset < 0 {
                Log.d("Chapter \(chapterPath) paginated, but offset not calculated yet. Calculating now.")
                SearchState.shared.calculateAndUpdateAdjustedOffset(for: searchResult, in: chapterPath)
            } else {
                Log.d("Chapter \(chapterPath) paginated, and offset already calculated.")
            }

            // Now get the result (which should have the offset) and navigate
            let updatedResult = SearchState.shared.getUpdatedSearchResult(for: searchResult)
            let finalOffset = updatedResult.navigationOffset

            Log.d("Navigating directly to search result: href=\(chapterPath), finalOffset=\(finalOffset)")
            gotoPage(href: chapterPath, contentOffset: finalOffset)

        } else {
            // Chapter not paginated yet, set as pending
            Log.d("Chapter \(chapterPath) not paginated, setting pending navigation and showing loading indicator.")
            pendingSearchResultNavigation = searchResult
            paginator.resetPriorityHigh(key: String(chapterIndex))
            preReloadChaptersDic[chapterIndex] = chapterIndex
            preReloadChapterIndex = chapterIndex
            // Show the central loading indicator instead of navigating immediately
            showSearchLoading()
        }
    }

    func clearSearch() {
        clearSearchResultNotes()
        SearchState.shared.clearSearch()
    }

    @objc func searchStateDidChange() {
        updateSearchNavigationVisibility()
    }

    func updateSearchNavigationVisibility() {
        let searchState = SearchState.shared

        // Update visibility based on search state
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }

            let shouldShow = searchState.showSearchNavigation

            // Update header and navigator visibility
            self.searchResultHeaderVC?.view.isHidden = !shouldShow
            self.searchResultNavigatorVC?.view.isHidden = !shouldShow

            // Update the UI if needed
            if shouldShow {
                hideNavAndTool()
                self.view.layoutIfNeeded()
            }
        }
    }

    private func configureSearchViewController() -> UIHostingController<SearchView> {
        let searchView = SearchView(
            dismissAction: { [weak self] in
                self?.dismiss(animated: true)
            },
            jumpAction: { [weak self] searchResult in
                Log.d("Jump to page: \(searchResult.href), offset: \(searchResult.startOffset)")
                self?.dismiss(animated: true) {
                    guard let self = self else { return }
                    self.navigateToSearchResult(searchResult)
                }
            }
        )

        let hostingController = UIHostingController(rootView: searchView)
        hostingController.modalPresentationStyle = .fullScreen
        hostingController.modalTransitionStyle = .coverVertical
        return hostingController
    }

    func showSearchView() {
        clearSearchResultNotes()
        let hostingController = configureSearchViewController()
        present(hostingController, animated: true)
    }

    private func clearSearchResultNotes() {
        if SearchState.shared.showSearchNavigation {
            SearchState.shared.showSearchNavigation = false
            reuseView.reload(indexPath: paginator.currentIndexPath)
        }
    }

    // Add new method for rotation handling
    func updateSearchNavigationLayout() {
        searchResultHeaderVC?.view?.snp.updateConstraints { make in
            make.top.equalTo(reuseViewFrame.minY)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(ReaderNavigationBar.HEIGHT)
        }

        searchResultNavigatorVC?.view?.snp.updateConstraints { make in
            make.leading.trailing.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide.snp.bottom).offset(-10)
            make.height.equalTo(36)
        }
    }
}

// MARK: - Property Extensions

extension BookReaderVC {
    // Add properties for search navigation components
    private enum AssociatedKeys {
        static var searchResultHeaderVC = UnsafeRawPointer(bitPattern: "searchResultHeaderVC".hashValue)!
        static var searchResultNavigatorVC = UnsafeRawPointer(bitPattern: "searchResultNavigatorVC".hashValue)!
    }

    var searchResultHeaderVC: UIHostingController<ReaderViewSearchResultHeader>? {
        get {
            return objc_getAssociatedObject(self, AssociatedKeys.searchResultHeaderVC) as? UIHostingController<ReaderViewSearchResultHeader>
        }
        set {
            objc_setAssociatedObject(self, AssociatedKeys.searchResultHeaderVC, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }

    var searchResultNavigatorVC: UIHostingController<ReaderViewSearchResultNavigator>? {
        get {
            return objc_getAssociatedObject(self, AssociatedKeys.searchResultNavigatorVC) as? UIHostingController<ReaderViewSearchResultNavigator>
        }
        set {
            objc_setAssociatedObject(self, AssociatedKeys.searchResultNavigatorVC, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
}
