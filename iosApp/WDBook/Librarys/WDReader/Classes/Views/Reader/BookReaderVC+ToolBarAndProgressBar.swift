//
//  ReaderVC+ToolBarAndProgressBar.swift
//  WDReader
//
//  Created by <PERSON> on 2020/10/2.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import shared
import SwiftUI
import UIKit

extension BookReaderVC {
    static let showNavBarNotification = Notification.Name("BookReaderVC_showNavBarNotification")
    static let hideNavBarNotification = Notification.Name("BookReaderVC_hideNavBarNotification")
}

// MARK: 工具栏相关

extension BookReaderVC: UIGestureRecognizerDelegate, ReaderToolBarDelegate, ReaderProgressDelegate {
    private enum AnimationConstants {
        static let duration: TimeInterval = 0.3
        static let delay: TimeInterval = 0.1
        static let options: UIView.AnimationOptions = .curveEaseInOut
    }

    func addNavAndTool() {
        navigationBar = ReaderNavigationBar()
        navigationBar.addBackTarget(self, action: #selector(tapBack))
        navigationBar.navigationBar.items![0].setRightBarButtonItems([
            UIBarButtonItem(image: UIImage(named: "ic_bookmark_unselected"), style: .plain, target: self, action: #selector(tapBookMark(id:))),
            UIBarButtonItem(image: UIImage(named: "ic_note"), style: .plain, target: self, action: #selector(tapOpenNoteList(id:))),
            UIBarButtonItem(image: UIImage(named: "ic_search"), style: .plain, target: self, action: #selector(tapSearch(_:))),
        ], animated: false)
        view.addSubview(navigationBar)
        navigationBar.snp.makeConstraints { make in
            make.leading.equalTo(0)
            make.trailing.equalTo(0)
            make.top.equalTo(isHideNavAndTool ? -ReaderNavigationBar.HEIGHT : reuseViewFrame.minY)
            make.height.equalTo(ReaderNavigationBar.HEIGHT)
        }

        let insets: UIEdgeInsets = UIApplication.shared.windows[0].safeAreaInsets // layout之前，view.safeAreaInsets是zero

        toolBar = ReaderToolBar()
//        toolBar.backgroundColor = UIColor.red
        toolBar.delegate = self
        view.addSubview(toolBar)
        toolBar.snp.makeConstraints { make in
            make.leading.equalTo(0)
            make.trailing.equalTo(0)
            make.bottom.equalTo(isHideNavAndTool ? 60 - insets.bottom : 0 - insets.bottom)
            make.height.equalTo(60)
        }

        NotificationCenter.default.addObserver(self, selector: #selector(progressStateChanged(noti:)), name: Paginator.isAllLoadChangedNotification, object: nil)
    }

    private func performNavToolAnimation(showing: Bool, animated: Bool = true, completion: @escaping () -> Void) {
        let updateConstraints = {
            self.navigationBar.snp.updateConstraints { make in
                make.top.equalTo(showing ? self.reuseViewFrame.minY : -ReaderNavigationBar.HEIGHT)
            }
            self.toolBar.snp.updateConstraints { make in
                make.bottom.equalTo(showing ? 0 - self.view.safeAreaInsets.bottom : 60)
            }
            self.view.layoutIfNeeded()
        }

        if animated {
            UIView.animate(withDuration: AnimationConstants.duration,
                           delay: AnimationConstants.delay,
                           options: AnimationConstants.options,
                           animations: updateConstraints,
                           completion: { _ in completion() })
        } else {
            updateConstraints()
            completion()
        }
    }

    func showNavAndTool() {
        NotificationCenter.default.post(name: BookReaderVC.showNavBarNotification, object: nil)
        performNavToolAnimation(showing: true) { [weak self] in
            self?.refreshBookMarkStateOnNav()
            self?.toolBarHideHandler?(false)
        }
        isHideNavAndTool = false
    }

    func hideNavAndTool(isNeedPost: Bool = true, animated: Bool = true) {
        hideProgressV()
        performNavToolAnimation(showing: false, animated: animated) { [weak self] in
            if isNeedPost {
                NotificationCenter.default.post(name: BookReaderVC.hideNavBarNotification, object: nil)
            }
            self?.toolBarHideHandler?(true)
        }
        isHideNavAndTool = true
    }

    func readerToolBarDidSelected(type: ReaderToolBar.ItemType) {
        enterFullModeWorkItem?.cancel()
        switch type {
        case .toc:
            hideProgressV()
            hideNavAndTool()
            clearSearch()

            //        folioReader.saveReaderState()
            let outlineVC = OutlinePageVC()
            outlineVC.delegate = self
            let nav = UINavigationController(rootViewController: outlineVC)
            present(nav, animated: true, completion: nil)

        case .progress:
            clearSearch()

            if let progress = progressV, progress.type == .progress {
                hideProgressV()
                return
            }
            showProgressV(type: type)
        case .style:
            if styleControlV != nil {
                hideProgressV()
                return
            }
            showProgressV(type: type)
        case .light:
            if lightControlV != nil {
                hideProgressV()
                return
            }
            showProgressV(type: type)
        }
    }

    func showProgressV(type: ReaderToolBar.ItemType) {
        hideProgressV()

        var minValue: Float = 0.0
        var value: Float = 0.0
        var maxValue: Float = 0.0
        switch type {
        case .progress:

            minValue = 1.0
            value = Float(currentPageNumber)
            maxValue = Float(paginator.getPagesCount())

            progressV = ReaderProgress(type: type, minValue: minValue, maxValue: maxValue, value: value)
            if paginator.isAllLoad {
                progressV?.enableSlideState(minValue: minValue, maxValue: maxValue, value: value)
            } else {
                progressV?.disableSlideState()
            }
            progressV?.delegate = self
            view.addSubview(progressV!)
            progressV?.snp.makeConstraints { make in
                make.leading.trailing.equalTo(0)
                make.height.equalTo(60)
                make.bottom.equalTo(toolBar.snp.top)
            }

        case .style:
            minValue = 0
            value = Float(WDReaderConfig.getCurrentFontSizeIndex())
            maxValue = Float(WDReaderConfig.StyleFontSizes.count - 1)

            styleControlV = StyleControlV(minValue: minValue, maxValue: maxValue, value: value, delegate: self)
            view.addSubview(styleControlV!)
            styleControlV?.snp.makeConstraints { make in
                make.leading.trailing.equalTo(0)
                make.height.equalTo(64 * 2)
                make.bottom.equalTo(toolBar.snp.top)
            }
        case .light: // 不设置值。在WDReaderProgress内部设置值

            lightControlV = LightControlV()
            view.addSubview(lightControlV!)
            lightControlV!.snp.makeConstraints { make in
                make.leading.trailing.equalTo(0)
                make.height.equalTo(16 + 60 + 16 + 86 + 42)
                make.bottom.equalTo(toolBar.snp.top)
            }
        default:
            break
        }
    }

    // 隐藏所有控制UI
    func hideProgressV() {
        progressV?.hideProgressTips()
        progressV?.removeFromSuperview()
        progressV = nil

        styleControlV?.removeFromSuperview()
        styleControlV = nil

        lightControlV?.removeFromSuperview()
        lightControlV = nil
    }

    func readerProgressValueChanged(type: ReaderToolBar.ItemType, value: Int) {
        switch type {
        case .progress:
            gotoPage(page: value)
        case .style:
            changeFontSize(value: value)
        case .light: // 更新值之后，不会通知外界。
            break
        default:
            break
        }
    }

    func readerProgressDidSelected(type: ReaderToolBar.ItemType, isLeft: Bool) {
        guard toolBar.isUserInteractionEnabled else {
            return
        }

        if type == .progress {
            if isLeft {
                Log.d("进度-左")
                if let (chapterIndex, hrefAndFragmentid, subLevelChapterTitle) = paginator.getTOCStartIndexPathForSubLevelChapter(indexPath: paginator.currentIndexPath).0 {
                    gotoPage(chapterIndex: chapterIndex, hrefAndFragmentId: hrefAndFragmentid)
                    if Paginator.current.isAllLoad {
                        // 如果全部加载完的话，那么gotoPage直接改变到下一页了。因此pageNum和currentPageNum相同。
                        // 如果没有加载完，虽然gotoPage异步，但是不调用progressV?.update(value:),只调用progressV?.update(title:)
                        let pageNum = paginator.getPageNum(hrefAndFragmentId: hrefAndFragmentid)
                        progressV?.update(value: Float(pageNum))
                    } else {
                        progressV?.update(title: subLevelChapterTitle)
                    }
                }

            } else {
                Log.d("进度-右")
                if let (chapterIndex, hrefAndFragmentid, subLevelChapterTitle) = paginator.getTOCStartIndexPathForSubLevelChapter(indexPath: paginator.currentIndexPath).1 {
                    gotoPage(chapterIndex: chapterIndex, hrefAndFragmentId: hrefAndFragmentid)
                    if Paginator.current.isAllLoad {
                        let pageNum = paginator.getPageNum(hrefAndFragmentId: hrefAndFragmentid)
                        progressV?.update(value: Float(pageNum))
                    } else {
                        progressV?.update(title: subLevelChapterTitle)
                    }
                }
            }
        }
    }

    func readerProgressRecordJump(type: ReaderToolBar.ItemType, isPre _: Bool, indexPath: IndexPath) {
        if type == .progress {
            gotoPage(indexPath: indexPath, needResetRecord: true)
        }
    }

    @objc func progressStateChanged(noti: Notification) {
        DispatchQueue.main.async { [weak self] in
            guard let self = self,
                  let isAllLoad = noti.object as? Bool,
                  self.progressV?.type == .progress
            else {
                Log.e("Invalid notification object type or progress view state")
                return
            }

            if isAllLoad {
                self.progressV?.enableSlideState(minValue: 1.0,
                                                 maxValue: Float(Paginator.current.getPagesCount()),
                                                 value: Float(Paginator.current.currentPageNum))
            } else {
                self.progressV?.disableSlideState()
            }
        }
    }

    @objc func tapOpenNoteList(id _: UIButton) {
        jumpNoteAndBookmark(animated: true)
    }

    func jumpNoteAndBookmark(animated: Bool = true, isShowBookmark: Bool = false) {
        enterFullModeWorkItem?.cancel()
//        NotificationCenter.default.addObserver(self, selector: #selector(noteChanged(noti:)), name: NoteEditerVC.noteChangedNotification, object: nil)
        let vc = UIHostingController(rootView: BookmarkAndNoteView(
            isShowBookmark: isShowBookmark,
//                                        dismissAction: {[weak self] in
//                                            guard let self = self else {return}
//                                            self.dismiss()
//                                //            NotificationCenter.default.removeObserver(self, name: NoteEditerVC.noteChangedNotification, object: nil)
//                                        },
            jumpAction: { [weak self] pagePath, contentOffset in
                self?.gotoPage(href: pagePath, contentOffset: contentOffset)
            }
        ).ignoresSafeArea().environmentObject(NoteMarkUserData(WDReaderConfig.resourceId)))
//        present(vc, animated: true, completion: nil)
        navigationController?.pushViewController(vc, animated: animated)
//        navigationController?.present(vc, animated: true)
    }

    @objc func noteBookmarkChanged(noti _: Notification) {
        HighlightRangeManager.loadNotes(chapterPath: paginator.currentChapter?.href ?? "")
        reuseView.reload(indexPath: paginator.currentIndexPath)
        refreshBookMarkStateOnNav()
    }

    @objc func tapBookMark(id _: UIButton) {
        if let page = currentPage() {
            if let bookMark = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range) {
                BookmarkManager.remove(bookmark: bookMark)
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].image = UIImage(named: "ic_bookmark_unselected")
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].tintColor = dynamicSegmentTitleColor
            } else {
                BookmarkManager.add(chapterPath: page.href, range: page.ctPage.range)
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].image = UIImage(named: "ic_bookmark_selected")
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].tintColor = dynamicBookMarkSelectedColor
            }
        }
    }

    @objc func appWillTerminate(noti _: Notification) {
        WDBookUserSDK.shared.updateBookLastVisitTime(resourceId: WDReaderConfig.resourceId)
    }

    // 刷新navigation的书签标记
    func refreshBookMarkStateOnNav() {
        if let page = currentPage() {
            if let _ = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range) {
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].image = UIImage(named: "ic_bookmark_selected")
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].tintColor = dynamicBookMarkSelectedColor
            } else {
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].image = UIImage(named: "ic_bookmark_unselected")
                navigationBar.navigationBar.items![0].rightBarButtonItems?[0].tintColor = dynamicSegmentTitleColor
            }
        }
    }

    @objc func tapSearch(_: Any) {
        enterFullModeWorkItem?.cancel()
        showSearchView()
    }
}
