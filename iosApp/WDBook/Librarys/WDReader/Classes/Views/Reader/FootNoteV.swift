//
//  FootNoteV.swift
//  WDReader
//
//  Created by <PERSON> on 2020/7/23.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import UIKit


class FootNoteV: UIView {
    let contentV = FootNoteContentV()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = UIColor.clear
        
        let btn = UIButton()
        btn.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
        addSubview(btn)
        btn.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        contentV.closeBtn.addTarget(self, action:  #selector(tapClose), for: .touchUpInside)
        addSubview(contentV)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func tapClose(){
        FootNoteV.hide()
    }
    
    //静态方法，管理器
    static var contentHeight:CGFloat = 0.0
    static var footNoteV:FootNoteV?
    static var isAnimation = false
    
    static func show(footNote:FootNote,on superV:UIView) {
        hide()
        
        if isAnimation{
            return
        }
        isAnimation = true
        
        footNoteV = FootNoteV()
        superV.addSubview(footNoteV!)
        footNoteV!.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        
        footNoteV!.contentV.setFootNote(footNote: footNote)
//        let maxHeight = UIScreen.main.bounds.height * 0.75
//        let textSize = footNoteV!.contentV.textView.sizeThatFits(CGSize(width: UIScreen.main.bounds.width - 36*2, height: CGFloat.infinity))
//        contentHeight = min(textSize.height + 74 + 18, maxHeight)
        //改为固定高度
        contentHeight = 335
        footNoteV!.contentV.snp.makeConstraints { (make) in
            make.bottom.equalTo(contentHeight)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(contentHeight)
        }
        footNoteV!.layoutIfNeeded()
        
        UIView.animate(withDuration: 0.3, delay: 0, options: .curveEaseInOut, animations: {
            footNoteV!.contentV.snp.updateConstraints { (make) in
                make.bottom.equalTo(0)
            }
            footNoteV!.layoutIfNeeded()
        }) { (success) in
            isAnimation = false
        }
    }
    
    static func hide(){
        if footNoteV == nil{
            return
        }
        if isAnimation {
            return
        }
        
        isAnimation = true
        UIView.animate(withDuration: 0.3, delay: 0.0, options: .curveEaseInOut, animations: {
            footNoteV?.contentV.snp.updateConstraints { (make) in
                make.bottom.equalTo(contentHeight)
            }
            footNoteV?.layoutIfNeeded()
        }) { (success) in
            footNoteV?.removeFromSuperview()
            footNoteV = nil
            isAnimation = false
        }
    }
}

class FootNoteContentV: UIView {
    lazy var titleLabel:UILabel = {
        let l = UILabel()
        l.textColor = dynamicTitleColor3
        l.font = WDReaderConfig.getCurrentFont.withSize(18).withWeight(.medium)
        return l
    }()
    
    var closeBtn:UIButton = {
        let closeBtn = UIButton()
        closeBtn.setImage(UIImage(named: "reader_btn_close"), for: .normal)
        closeBtn.snp.makeConstraints { (make) in
            make.width.height.equalTo(24)
        }
        return closeBtn
    }()
    
    var textView:UITextView = {
        let tv = UITextView()
        tv.backgroundColor = UIColor.clear
        tv.textColor = dynamicTitleColor3
        tv.font = WDReaderConfig.getCurrentFont.withSize(18)
        tv.isSelectable = false
        tv.showsVerticalScrollIndicator = false
        tv.showsHorizontalScrollIndicator = false
        return tv
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = dynamicBackgroundColor1
        
        addSubview(closeBtn)
        closeBtn.snp.makeConstraints { (make) in
            make.top.equalTo(26)
            make.trailing.equalTo(-24)
        }
        
        addSubview(titleLabel)
        titleLabel.snp.makeConstraints { (make) in
            make.top.equalTo(28)
            make.leading.equalTo(24)
            make.trailing.equalTo(closeBtn.snp.leading).offset(-12)
            make.height.equalTo(22)
        }
        
        addSubview(textView)
        textView.snp.makeConstraints { (make) in
            make.leading.equalTo(36)
            make.trailing.equalTo(-36)
            make.top.equalTo(70)
            make.bottom.equalTo(-20)
        }
                
        layer.cornerRadius = 8
        layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner]
        
        layer.applySketchShadow(y: -5)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func setFootNote(footNote:FootNote) {
        titleLabel.text = footNote.title
        
        let style = NSMutableParagraphStyle()
        style.lineSpacing = (28 - 18)/2.0
        style.alignment = .justified
        let attributes = [NSAttributedString.Key.paragraphStyle : style,
                          NSAttributedString.Key.font:WDReaderConfig.getCurrentFont.withSize(18),
                          NSAttributedString.Key.foregroundColor:dynamicTitleColor3]
        textView.attributedText = NSAttributedString(string: footNote.content, attributes: attributes)
    }
}
