//
//  AnchorReturnV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2022/1/17.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import UIKit
import SnapKit

private var BookReaderVC_ACCOCIATED_KEY: UInt8 = 0
extension BookReaderVC{
    var anchorReturnV:AnchorReturnV?{
        set{
            objc_setAssociatedObject(self, &BookReaderVC_ACCOCIATED_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &BookReaderVC_ACCOCIATED_KEY) as? AnchorReturnV
        }
    }
    
    func addAnchorReturnV(chapterHref:String,contentOffset:Int,action:@escaping (String,Int)->()){
        hideAnchorReturnV()
        anchorReturnV = AnchorReturnV(chapterHref: chapterHref, contentOffset: contentOffset,action: action)
        reuseView.insertSubview(anchorReturnV!, belowSubview: toolBar)
        anchorReturnV?.snp.makeConstraints({ make in
            make.bottom.equalTo(-16)
            make.centerX.equalToSuperview()
            make.width.equalTo(203)
            make.height.equalTo(32)
        })
    }
    
    func hideAnchorReturnV(){
        anchorReturnV?.removeFromSuperview()
        anchorReturnV = nil
    }
}


class AnchorReturnV: UIView {
    private var chapterHref:String
    private var contentOffset:Int
    private var pageNum:Int?
    private var action:(String,Int)->()
    
    private var returnLabel:UILabel!
    
    init(chapterHref:String,contentOffset:Int,action:@escaping (String,Int)->()) {
        self.chapterHref = chapterHref
        self.contentOffset = contentOffset
        self.action = action
        super.init(frame: CGRect.zero)
        
        backgroundColor = dynamicAnchorBGColor
        layer.cornerRadius = 32/2
        layer.masksToBounds = true
        
        let arrow = UIImageView(image: UIImage(named: "icon_arrow_Left_16"))
        addSubview(arrow)
        arrow.snp.makeConstraints { make in
            make.leading.equalTo(8)
            make.width.height.equalTo(16)
            make.centerY.equalToSuperview()
        }
        
        updatePageNum()
        returnLabel = UILabel()
        returnLabel.text = "返回".localized + (pageNum != nil ? "第%lld页".localizedFormat(pageNum!) : "")
        returnLabel.font = UIFont.regular(size: 12)
        returnLabel.textColor = UIColor.white
        addSubview(returnLabel)
        returnLabel.sizeToFit()
        let returnLabelSize = returnLabel.frame.size
        returnLabel.snp.makeConstraints { (make) in
            make.leading.equalTo(arrow.snp.trailing).offset(4)
            make.top.bottom.equalToSuperview()
            make.width.equalTo(returnLabelSize.width + 13)
        }
        
        let stayBtn = UIButton()
        stayBtn.setTitle("留在本页".localized, for: .normal)
        stayBtn.titleLabel?.font = UIFont.regular(size: 12)
        stayBtn.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
        addSubview(stayBtn)
        stayBtn.sizeToFit()
        let size = stayBtn.frame.size
        stayBtn.snp.makeConstraints { (make) in
            make.trailing.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalTo(size.width + 13*2)
        }
        
        let returnBtn = UIButton()
        returnBtn.addTarget(self, action: #selector(tapReturn), for: .touchUpInside)
        addSubview(returnBtn)
        returnBtn.snp.makeConstraints { (make) in
            make.leading.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.trailing.equalTo(returnLabel.snp.trailing)
        }
        
        NotificationCenter.default.addObserver(self, selector: #selector(loadAllPagesComplete(noti:)), name: Paginator.isAllLoadChangedNotification, object: nil)
        Log.d("returnV:创建\(chapterHref),\(contentOffset)")
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    @objc func tapReturn() {
        Log.d("returnV:返回\(chapterHref),\(contentOffset)")
        action(chapterHref, contentOffset)
        removeFromSuperview()
    }
    
    @objc func tapClose() {
        removeFromSuperview()
    }
    
    @objc func loadAllPagesComplete(noti:Notification) {
        if let isAllLoad = noti.object as? Bool{
            updatePageNum(isAllLoad: isAllLoad)
            DispatchQueue.main.async {[weak self] in
                guard let self = self else {return}
                self.relayoutReturnLabel()
            }
        }
    }
    
    private func updatePageNum(isAllLoad:Bool = Paginator.current.isAllLoad){
        if isAllLoad {
            let num = Paginator.current.getPageNum(chapterHref: chapterHref, contentOffset: contentOffset)
            pageNum = num
        }else{
            pageNum = nil
        }
    }
    private func relayoutReturnLabel(){
        returnLabel.text = "返回".localized + (pageNum != nil ? "第%lld页".localizedFormat(pageNum!) : "")
        returnLabel.sizeToFit()
        let returnLabelSize = returnLabel.frame.size
        returnLabel.snp.updateConstraints { (make) in
            make.width.equalTo(returnLabelSize.width + 13)
        }
    }
}

let dynamicAnchorBGColor = UIColor { (trainCollection) -> UIColor in
    if trainCollection.userInterfaceStyle == .dark {
        return UIColor(hex: 0x4E4E4E)
    } else {
        return UIColor(hex: 0x0E0E0E)
    }
}
