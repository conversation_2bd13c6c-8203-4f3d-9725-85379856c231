//
//  StyleControlV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/10/27.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import SnapKit
import UIKit

class StyleControlV: UIView,FontSelectVDelegate{
    var progressV:ReaderProgress
    var fontSelectV:FontSelectV
    
    required init(minValue minV:Float,maxValue maxV:Float,value v:Float,delegate:ReaderProgressDelegate? = nil) {
        
        progressV = ReaderProgress(type: .style, minValue: minV, maxValue: maxV, value: v)
        progressV.delegate = delegate
        fontSelectV = FontSelectV()
        super.init(frame: CGRect.zero)
        fontSelectV.delegate = self
        
        backgroundColor = dynamicBackgroundColor1
        
        addSubview(progressV)
        progressV.snp.makeConstraints({ (make) in
            make.top.leading.trailing.equalTo(0)
            make.height.equalTo(60)
            make.top.equalTo(0)
        })
        
        addSubview(fontSelectV)
        fontSelectV.snp.makeConstraints { (make) in
            make.leading.equalTo(24)
            make.trailing.equalTo(-24)
            make.top.equalTo(progressV.snp.bottom).offset(13.5)
            make.height.equalTo(36)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func fontSelectVChangeFontName() {
        progressV.changeFontName()
    }
    
}

protocol FontSelectVDelegate:AnyObject{
    func fontSelectVChangeFontName()
}

class FontSelectV:UIView{
    private var font1Btn:UIButton
    private var font2Btn:UIButton
    weak var delegate:FontSelectVDelegate?
    
    override init(frame: CGRect) {
        font1Btn = UIButton()
        font2Btn = UIButton()
        super.init(frame: CGRect.zero)
        
        let stackH = UIStackView()
        stackH.axis = .horizontal
        stackH.alignment = .center
        stackH.distribution = .fill
        
        let fontLabel = UILabel()
        fontLabel.text = "字体".localized
        fontLabel.font = UIFont.regular(size: 16)
        fontLabel.textColor = dynmicLabelColor
        
        font1Btn.tag = 0
        font1Btn.setTitle("系统字体".localized, for: .normal)
        font1Btn.titleLabel?.font = UIFont.regular(size: 16)
        font1Btn.layer.cornerRadius = 6
        font1Btn.addTarget(self, action: #selector(tapChangeFont(obj:)), for: .touchUpInside)
        
        font2Btn.tag = 1
        font2Btn.setTitle("思源宋体".localized, for: .normal)
        font2Btn.titleLabel?.font = UIFont.sourceHanSerif(size: 16)
        font2Btn.layer.cornerRadius = 6
        font2Btn.addTarget(self, action: #selector(tapChangeFont(obj:)), for: .touchUpInside)
        
        stackH.addArrangedSubview(fontLabel.addConstraint(width: 32))
        stackH.addArrangedSubview(viewWith(width: 14))
        stackH.addArrangedSubview(font1Btn)
        stackH.addArrangedSubview(viewWith(width: 21))
        stackH.addArrangedSubview(font2Btn)
        addSubview(stackH)
        stackH.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        font2Btn.snp.makeConstraints { make in
            make.width.equalTo(font1Btn.snp.width)
        }
        
        refreshFontState()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func viewWith(width:CGFloat) -> UIView{
        let newView = UIView()
        newView.snp.makeConstraints { make in
            make.width.equalTo(width)
        }
        return newView
    }
    
    @objc private func tapChangeFont(obj:UIButton){
        let fontId = WDReaderConfig.getCurrentFontId()
        guard fontId != obj.tag else {return}
        WDReaderConfig.setCurrentFontId(fontId: obj.tag)
        refreshFontState()
        delegate?.fontSelectVChangeFontName()
    }
    
    private func refreshFontState(){
        let fontId = WDReaderConfig.getCurrentFontId()
        if fontId == 0{
            setSelectedState(btn: font1Btn)
            setNormalState(btn: font2Btn)
        }else{
            setNormalState(btn: font1Btn)
            setSelectedState(btn: font2Btn)
        }
    }
    
    private func setNormalState(btn:UIButton){
        btn.setTitleColor(dynmicLabelColor, for: .normal)
        btn.backgroundColor = dynmicBgColor2
        btn.layer.borderWidth = 0
    }
    
    private func setSelectedState(btn:UIButton){
        btn.setTitleColor(dynmicLabelColor2, for: .normal)
        btn.backgroundColor = dynmicBgColor3
        btn.layer.borderWidth = 1
        btn.layer.borderColor = UIColor(hex:0xE9973E).cgColor
    }
}

extension UIView{
    func addConstraint(width:CGFloat) -> Self{
        snp.makeConstraints { make in
            make.width.equalTo(width)
        }
        return self
    }
}
