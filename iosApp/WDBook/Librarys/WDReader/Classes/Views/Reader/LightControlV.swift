//
//  LightControlV.swift
//  WDReader
//
//  Created by <PERSON> on 2020/8/17.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import SnapKit
import UIKit

class LightControlV: UIView{

    var progressV:ReaderProgress
    var themeSelectV:ThemeSelectV
    
    override init(frame: CGRect) {
        progressV = ReaderProgress(type: .light, minValue: 0, maxValue: 0, value: 0)
        themeSelectV = ThemeSelectV(frame: CGRect(x: 32, y: 16 + 60 + 16, width: UIScreen.main.bounds.width - 32*2, height: 86))
        
        super.init(frame: frame)
        
        backgroundColor = dynamicBackgroundColor1
        
        addSubview(progressV)
        progressV.snp.makeConstraints({ (make) in
            make.leading.trailing.equalTo(0)
            make.height.equalTo(60)
            make.top.equalTo(16)
        })
        
        addSubview(themeSelectV)
        themeSelectV.snp.makeConstraints { (make) in
            make.leading.equalTo(32)
            make.trailing.equalTo(-32)
            make.top.equalTo(progressV.snp.bottom).offset(16)
            make.height.equalTo(86)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
