//
//  ReaderNavigationBar.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/29.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import UIKit
import SnapKit

let Noti_Add_StatusBarBG = Notification.Name("Noti_Add_StatusBarBG")
let Noti_Remove_StatusBarBG = Notification.Name("Noti_Remove_StatusBarBG")

class ReaderNavigationBar: UIView {
    static let HEIGHT:CGFloat = 60
    weak var backTarget:AnyObject?
    var backAction:Selector!
    
    weak var closeTarget:AnyObject?
    var closeAction:Selector!
    
    var navigationBar:UINavigationBar
    

    required init(frame: CGRect, title:String? = nil,hasBack:Bool = true, hasClose:Bool = false, hasBottomLine:Bool = true) {
        navigationBar = UINavigationBar()
        super.init(frame: frame)
        
        navigationBar.shadowImage = UIImage()
        navigationBar.tintColor = dynamicSegmentTitleColor //按钮前色
        navigationBar.barTintColor = dynamicBackgroundColor1  //背景色
//        navigationBar.barStyle = UIBarStyle.black
        navigationBar.isTranslucent = false
        
        //状态栏背景色。
        addStatusBG()
        
        let item = UINavigationItem()
        item.title = title
        
        if hasBack {
            item.setLeftBarButton(UIBarButtonItem(image: UIImage(named: "reader_back_ui"), style: .plain, target: self, action: #selector(tapBack)), animated: false)
        }
        
        if hasClose {
            item.setRightBarButton(UIBarButtonItem(image: UIImage(named: "reader_btn_close"), style: .plain, target: self, action: #selector(tapClose)), animated: false)
        }
        navigationBar.setItems([item], animated: false)
        
        addSubview(navigationBar)
        navigationBar.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        
        if hasBottomLine {
            let line = UIView()
            line.backgroundColor = UIColor.black.withAlphaComponent(0.1)
            addSubview(line)
            line.snp.makeConstraints { (make) in
                make.bottom.leading.trailing.equalToSuperview()
                make.height.equalTo(0.5)
            }
        }
        
        NotificationCenter.default.addObserver(self, selector: #selector(addStatusBG), name: Noti_Add_StatusBarBG, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(removeStatusBG), name: Noti_Remove_StatusBarBG, object: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    convenience init(title:String? = nil,hasBack:Bool = true, hasClose:Bool = false) {
        self.init(frame:CGRect.zero,title:title,hasBack:hasBack,hasClose:hasClose)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    func addBackTarget(_ target: AnyObject?, action: Selector){
        self.backTarget = target
        self.backAction = action
    }
    
    func addCloseTarget(_ target: AnyObject?, action: Selector){
        self.closeTarget = target
        self.closeAction = action
    }
    
    @objc func tapBack(){
        if let target = self.backTarget, target.responds(to: self.backAction){
            let _ = target.perform(self.backAction, with: self)
        }
    }
    
    @objc func tapClose(){
        if let target = self.closeTarget, target.responds(to: self.closeAction){
            let _ = target.perform(self.closeAction, with: self)
        }
    }
    
    @objc func addStatusBG() {
        if !isCatalyst() {
            if let statusFrame = UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.windowScene?.statusBarManager?.statusBarFrame{
                let statusBar = UIView(frame: statusFrame)
                statusBar.tag = -999
//                statusBar.backgroundColor = dynamicBackgroundColor1
                statusBar.backgroundColor = UIColor.clear
                UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.addSubview(statusBar)
            }
        }
    }
    
    @objc func removeStatusBG() {
        if !isCatalyst() {
            UIApplication.shared.windows.first(where: { $0.isKeyWindow })?.subviews.filter{$0.tag == -999}.first?.removeFromSuperview()
        }
    }
}

func isCatalyst() -> Bool {
    #if targetEnvironment(macCatalyst)
    Log.d("是catalyst")
        return true
    #else
    Log.d("不是catalyst")
        return false
    #endif
}

//MARK: 相当于BaseVC
extension UIViewController{
    static private var wd_navigationKey: Void?
    
    var wd_navigation: ReaderNavigationBar? {
        get {
            objc_getAssociatedObject(self, &UIViewController.wd_navigationKey) as? ReaderNavigationBar
        }
        set {
            objc_setAssociatedObject(self, &UIViewController.wd_navigationKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    
    func initNavigation(title:String? = nil, hasBack:Bool = true,hasClose:Bool = false, hasBottomLine:Bool = true){
        wd_navigation = ReaderNavigationBar(frame: CGRect.zero, title:title, hasBack: hasBack, hasClose: hasClose, hasBottomLine: hasBottomLine)
        wd_navigation!.addBackTarget(self, action: #selector(wd_navigationTapBack))
        wd_navigation!.addCloseTarget(self, action: #selector(wd_navigationTapClose))
        view.addSubview(wd_navigation!)
        wd_navigation!.snp.makeConstraints { (make) in
            make.leading.equalTo(0)
            make.trailing.equalTo(0)
            make.top.equalTo(0)
            make.height.equalTo(60)
        }
    }
    
    @objc func wd_navigationTapBack(){
        navigationController?.popViewController(animated: true)
    }
    
    @objc func wd_navigationTapClose(){
        dismiss(animated: true, completion: nil)
//        navigationController?.dismiss(animated: true, completion: nil)
    }
}
