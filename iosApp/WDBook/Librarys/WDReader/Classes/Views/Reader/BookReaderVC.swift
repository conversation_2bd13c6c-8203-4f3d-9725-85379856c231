//  BookReaderVC.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/29.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import UIKit

public class BookReaderVC: UIViewController, ReuseViewDelegate {
    var activityIndicator: UIActivityIndicatorView?
    var reuseView: ReuseView!
    var reuseViewFrame: CGRect!
    var url: String?
    var needJumpBookmark = false

    // Threshold for determining significant size changes (in points)
    private let significantSizeChangeThreshold: CGFloat = 10.0

    var paginator: Paginator {
        set {
//            Paginator.patinators[String(WDReaderConfig.currentFontSize)] = newValue
            Paginator.current = newValue
        }
        get {
//            return Paginator.patinators[String(WDReaderConfig.currentFontSize)] ?? Paginator(pageSize: pageSize)
            return Paginator.current ?? Paginator(pageSize: pageSize)
        }
    }

    // 当前页码。
    var currentPageNumber: Int {
        let pageNum = paginator.currentPageNum
        let totalCount = paginator.getPagesCount()
        if pageNum > totalCount {
            return totalCount
        }
        return pageNum
    }

    var currentIndexPath: IndexPath {
        set {
            paginator.currentIndexPath = newValue
        }
        get {
            return paginator.currentIndexPath
        }
    }

    var pageSize: CGSize {
        return reuseViewFrame.size
        // 两种写法相同
        //        return reuseViewFrame.insetBy(dx: 0, dy: 20).size
        //        return CGSize(width: reuseViewFrame.size.width, height: reuseViewFrame.size.height - 20 * 2)
    }

    public var backHandler: ((_ userInfo: [String: Any]?) -> Void)?
    public var toolBarHideHandler: ((_ isHide: Bool) -> Void)?

    // 工具栏相关
    var navigationBar: ReaderNavigationBar!
    var toolBar: ReaderToolBar!
    var isHideNavAndTool = false
    var enterFullModeWorkItem: DispatchWorkItem?

    // 进度类型UI
    var progressV: ReaderProgress?
    // 字体控制UI
    var styleControlV: StyleControlV?
    // 亮度控制UI
    var lightControlV: LightControlV?

    // 分章加载相关
    var isFirstLoad = true
    var preReloadTimer: Timer?
    // (index,offset)成对出现。也相当于(herf,offset)。
    var preReloadContentOffset: Int?
    var preReloadChapterIndex: Int?

    // 只有滑动会触发prefetch。
    var preReloadChaptersDic: [Int: Int] = .init()
    var preReloadHrefAndFragmentId: String?
    var preReloadIndexPath: IndexPath?
    var preReloadChaptersDicOnPrefetch: [Int: Int] = .init()

    var preRecordReadProgressOnReloadChaptersDic: [Int: IndexPath] = .init() // 加载后，需要记录阅读记录

    var pendingSearchResultNavigation: SearchResult?
    var searchLoadingTimeoutWorkItem: DispatchWorkItem? // Added for timeout safety

    public required init(url: String? = nil) {
        Log.d("阅读器 加载 init：url")
        let bundle = Bundle.main.path(forResource: "bible", ofType: "wd")!
        DBConfigration.setDB(path: bundle, name: DBNAME_WDREADER)
        self.url = url
        super.init(nibName: nil, bundle: nil)
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    deinit {
        print("释放WDBookReaderVC")
        NotificationCenter.default.removeObserver(self)
    }

    override public func viewDidLoad() {
        super.viewDidLoad()
        Log.d("阅读器 加载 viewDidLoad")
        title = "CoreText + Epub Shower"
        view.backgroundColor = dynamicBackgroundColor1
//        self.view.backgroundColor = .yellow

        // 关于全屏时不显示statusBar和下拉添加删除书签的问题：
        // 刘海屏与非刘海屏逻辑相同，只不过statusbar和安全区域导致blackgroundView（top）高度不同而已
//                                          非全屏             全屏
        // statusBar                     显示，颜色是背景色     不显示，颜色是背景色
        //                      blackgroundView（top）占位，颜色与resueView相同，不必移动
        //                                  bookmarkView(从屏幕进出)
        //
        // resueView                     与statusBar挨着
        // bookmark
        // navigationView
        // toolbarView

        navigationController?.navigationBar.isHidden = true

        let insets: UIEdgeInsets = UIApplication.shared.windows[0].safeAreaInsets
        let bounds = UIApplication.shared.windows[0].bounds // 适配分屏
        reuseViewFrame = CGRect(
            x: 0,
            y: insets.top,
            width: bounds.width,
            height: bounds.height - insets.top - insets.bottom
        )

//        reuseViewFrame = CGRect(
//            x: 0,
//            y: insets.top,
//            width: view.bounds.width,
//            height: view.bounds.height - insets.top - insets.bottom)
//        reuseViewFrame = CGRect(
//            x: insets.left,
//            y: insets.top,
//            width: view.bounds.width - insets.left - insets.right,
//            height: view.bounds.height - insets.top - insets.bottom)

        var epubPath = url

        if epubPath == nil {
            let bookName = "丁道尔创世纪" // 1.3s
            let bundle = Bundle.main.path(forResource: bookName, ofType: "epub")!
            epubPath = bundle
        }

        paginator = Paginator(pageSize: pageSize)

        Log.d("阅读器 加载 viewDidLoad1")
        DispatchQueue.global().async { [weak self] in
            WDReaderCenter.shared.loadEpub(epubPath: epubPath!)
            Log.d("阅读器 加载 viewDidLoad2")
            self?.paginator.chainMutexThreadPoolSplitPage(oneTaskComplete: { key in
//                Log.d("阅读器 加载 viewDidLoad3")
                if let k = key, let section = Int(k) {
                    DispatchQueue.main.async { [weak self] in
                        self?.afterLoadOneChapter(section: section)
                    }
                }

            }, allTasksComplete: {
                //            DispatchQueue.main.async {[weak self] in
                //                Log.d("阅读器 加载 allTasksComplete 全部加载完毕。resourceId:\(WDReaderConfig.resourceId)")
                //            }
            })

            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }

                self.initPage()

                if self.needJumpBookmark {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                        self?.jumpNoteAndBookmark(animated: false, isShowBookmark: true)
                    }
                }
                self.needJumpBookmark = false
            }
        }

        let layout = ReuseViewFlowLayout(itemSize: reuseViewFrame.size)
        reuseView = ReuseView(frame: reuseViewFrame, layout: layout)
        reuseView.delegate = self
        reuseView.backgroundColor = dynamicBackgroundColor1
        view.addSubview(reuseView)
        //        reuseView.snp.makeConstraints { (make) in
        //            make.edges.equalToSuperview()
        //        }

        initNavAndTool()

        reuseView.addObserver(self, forKeyPath: "userInteractionEnabled", options: [.new, .old], context: nil)

        // 不仅仅是笔记编辑。笔记删除，笔记从回收站恢复，都会发送通知。笔记添加在添加时候已经单独处理了。
        NotificationCenter.default.addObserver(self, selector: #selector(noteBookmarkChanged(noti:)), name: NoteEditerVC.noteChangedNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(noteBookmarkChanged(noti:)), name: NoteBookmarkNotifications.noteDeletedNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(noteBookmarkChanged(noti:)), name: NoteBookmarkNotifications.noteResumeNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(noteBookmarkChanged(noti:)), name: NoteBookmarkNotifications.bookmarkDeletedNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(appWillTerminate(noti:)), name: UIApplication.willTerminateNotification, object: nil)

        // view和reuseView均可
        view.addGestureRecognizer(UIPanGestureRecognizer(target: self, action: #selector(panHandler(g:))))

        showFirstLoading()

        setupSearchNavigation()
    }

    private func afterLoadOneChapter(section: Int, enableLog: Bool = false) {
        Log.d("after load one chapter \(section)")
        if enableLog {
            Log.d("阅读器 加载 是否首次加载：\(isFirstLoad),章索引:\(section), offset: \(String(describing: preReloadContentOffset))")
        }
        if isFirstLoad && preReloadChapterIndex != nil && preReloadChapterIndex! == section && preReloadContentOffset != nil && preReloadChaptersDic[preReloadChapterIndex!] != nil {
            if enableLog {
                Log.d("阅读器 加载 首次加载，章索引:\(section), offset: \(String(describing: preReloadContentOffset)),resourceId:\(WDReaderConfig.resourceId)")
            }
            paginator.chapterFileDic[preReloadChapterIndex!]?.loadState = .loaded
            if let indexPath = paginator.getIndexPath(chapterIndex: preReloadChapterIndex!, contentOffset: preReloadContentOffset!) {
                currentIndexPath = indexPath
            }
            if reuseView.isInitData() {
                gotoPage(indexPath: currentIndexPath, needResetRecord: !isReseting)
//                        self.reuseView.reloadAndGoto(indexPath: self.currentIndexPath)
            } else {
                reuseView.initData()
            }
            refreshBookMarkStateOnNav()
            isFirstLoad = false
            preReloadContentOffset = nil
            preReloadChapterIndex = nil
            reuseView.isUserInteractionEnabled = true
            resetSemaphore.signal()
            isReseting = false
        } else if preReloadChaptersDic[section] != nil {
            if enableLog {
                Log.d("阅读器 加载 reloadSection: cell设置完优先级重加载,跳转: \(section),resourceId:\(WDReaderConfig.resourceId)")
            }
            paginator.chapterFileDic[section]?.loadState = .loaded

            if let hrefAndFragmentId = preReloadHrefAndFragmentId {
                gotoPage(chapterIndex: section, hrefAndFragmentId: hrefAndFragmentId)
                preReloadHrefAndFragmentId = nil
                // 目前只用于书签跳转
            } else if preReloadChapterIndex == section
                && preReloadContentOffset != nil,
                let indexPath = paginator.getIndexPath(chapterIndex: preReloadChapterIndex!, contentOffset: preReloadContentOffset!)
            {
                currentIndexPath = indexPath
                gotoPage(indexPath: indexPath, needResetRecord: true)
                preReloadChapterIndex = nil
                preReloadContentOffset = nil
            } else if let indexPath = preReloadIndexPath {
                gotoPage(indexPath: indexPath, needResetRecord: true)
                preReloadIndexPath = nil
            } else if reuseView.scrollDirection == .left {
                reuseView.reload(indexPath: IndexPath(item: 0, section: section))
            } else if reuseView.scrollDirection == .right {
                var i = 0
                if let chapterFile = paginator.chapterFileDic[section] {
                    i = max(chapterFile.pageCount - 1, 0)
                }
                reuseView.reload(indexPath: IndexPath(item: i, section: section), replaceIndexPath: IndexPath(item: 0, section: section))
            }

            preReloadChaptersDic[section] = nil

            if let indexPath = preRecordReadProgressOnReloadChaptersDic[section] {
                preRecordReadProgressOnReloadChaptersDic[section] = nil
                resetRecordAndPostBookReadProgressNoti(indexPath: indexPath)
            }

            reuseView.isUserInteractionEnabled = true

        } else if preReloadChaptersDicOnPrefetch[section] != nil {
            if enableLog {
                Log.d("阅读器 加载 reloadSection: prefetch设置完优先级重加载: \(section),resourceId:\(WDReaderConfig.resourceId)")
            }
            paginator.chapterFileDic[section]?.loadState = .loaded
            preReloadChaptersDicOnPrefetch[section] = nil
        } else {
            if enableLog {
                Log.d("阅读器 加载 正常加载: \(section),resourceId:\(WDReaderConfig.resourceId)")
            }
            paginator.chapterFileDic[section]?.loadState = .loaded
            if !isFirstLoad && WDReaderConfig.isDynamicLoadingMode {
                paginator.dynamicRelease(chapterIndex: section, currentChapterIndex: currentIndexPath.section)
            }
        }

        // Check for pending search result navigation
        if let pendingResult = pendingSearchResultNavigation,
           let chapter = paginator.getChapter(chapterIndex: section),
           chapter.resource.href == pendingResult.href
        {
            Log.d("Handling pending search navigation for loaded chapter: \(pendingResult.href)")
            // Chapter is now loaded, calculate the adjusted offset
            SearchState.shared.calculateAndUpdateAdjustedOffset(for: pendingResult, in: pendingResult.href)

            // Get the potentially updated result from SearchState
            let updatedResult = SearchState.shared.getUpdatedSearchResult(for: pendingResult)

            // Calculate final offset using the computed property on SearchResult
            let finalOffset = updatedResult.navigationOffset

            Log.d("Navigating to pending search result: href=\(updatedResult.href), finalOffset=\(finalOffset)")
            gotoPage(href: updatedResult.href, contentOffset: finalOffset)

            // Clear the pending request
            pendingSearchResultNavigation = nil
            // Re-enable interaction if it was disabled
            hideSearchLoading()
        }

        paginator.dynamicRelease(currentChapterIndex: currentIndexPath.section)
    }

    func showFirstLoading() {
        configureLoadingIndicator(show: true)
    }

    func hideFirstLoading() {
        configureLoadingIndicator(show: false)
    }

    @objc override public func observeValue(forKeyPath keyPath: String?, of _: Any?, change _: [NSKeyValueChangeKey: Any]?, context _: UnsafeMutableRawPointer?) {
        if keyPath == "userInteractionEnabled" {
            navigationBar.isUserInteractionEnabled = reuseView.isUserInteractionEnabled
            toolBar.isUserInteractionEnabled = reuseView.isUserInteractionEnabled
            reuseView.scrollView.isUserInteractionEnabled = reuseView.isUserInteractionEnabled
        }
    }

    func initNavAndTool() {
        addNavAndTool()
        if !isHideNavAndTool {
            enterFullModeWorkItem = DispatchWorkItem { [weak self] in
                self?.hideNavAndTool()
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 3, execute: enterFullModeWorkItem!)
        }
    }

    func initPage() {
        // 初始化页
        if let href = WDReaderConfig.chapterPath, !href.isEmpty {
            Log.d("阅读器 加载 initPage: 从阅读位置加载: \(href)，\(WDReaderConfig.contentOffset ?? 0)")
            gotoInitPage(href: href, contentOffset: WDReaderConfig.contentOffset ?? 0, needResetRecord: true)
        } else {
            Log.d("阅读器 加载 initPage: 没有阅读记录，从第一页打开")
            // 没有阅读记录，首次打开，如果第一页有空白需要跳过去。
            if preReloadTimer == nil {
                preReloadTimer = Timer(timeInterval: 0.3, repeats: true, block: { [weak self] _ in
                    guard let self = self else { return }
                    var reloadHref = ""
                    for i in 0 ..< self.paginator.chapterFileDic.count {
                        guard let chapter = self.paginator.chapterFileDic[i] else {
                            continue
                        }
                        // 跳过空页面
                        if WDReaderConfig.skipEmptyTextHTML, chapter.originalAttributedString.isEmpty() {
                            continue
                        }
                        reloadHref = chapter.resource.href
                        break
                    }
                    if !reloadHref.isEmpty {
                        self.gotoInitPage(href: reloadHref, contentOffset: 0, needResetRecord: true)
                        self.preReloadTimer?.invalidate()
                        self.preReloadTimer = nil
                    }
                })
                RunLoop.main.add(preReloadTimer!, forMode: .common)
            }
        }
    }

    func reuseViewCurrentIndexPath(_: ReuseView) -> IndexPath {
        currentIndexPath
    }

    func reuseView(_: ReuseView, previousIndexPathWithCurrentIndexPath currentIndexPath: IndexPath) -> IndexPath? {
        preIndexPath(currentIndexPath: currentIndexPath)
    }

    func reuseView(_: ReuseView, nextIndexPathWithCurrentIndexPath currentIndexPath: IndexPath) -> IndexPath? {
        nextIndexPath(currentIndexPath: currentIndexPath)
    }

    func reuseView(_: ReuseView, prefetchItemsAt indexPaths: [IndexPath]) {
//        Log.d("生命周期测试: 预加载 prefetchItemsAt \(indexPaths)")
        for indexPath in indexPaths {
            let chapterFile = paginator.chapterFileDic[indexPath.section]
            if !(chapterFile != nil
                && chapterFile!.pages.count > 0
//                 && chapterFile!.loadState == .loaded
            ) {
//                Log.d("reloadSection: 设置优先级: \(indexPath.section)")
                paginator.resetPriorityHigh(key: String(indexPath.section)) // 多次执行没事。
                preReloadChaptersDicOnPrefetch[indexPath.section] = indexPath.section
            }

            // 滑动中预加载
            if currentIndexPath.section != indexPath.section {
                HighlightRangeManager.loadNotes(chapterPath: paginator.getChapterHref(chapterIndex: indexPath.section))
            }
        }
    }

    // initData时候调用
    func reuseView(_: ReuseView, cellForItemAt indexPath: IndexPath) -> ReuseViewCell {
//        Log.d("生命周期测试: cellForItemAt \(indexPath)")
        let reuseableCell = BookReaderPageCell(frame: CGRect(origin: CGPoint.zero, size: pageSize))
        reuseableCell.delegate = self

        updateCellContent(pageCell: reuseableCell, indexPath: indexPath)
        // initData时候调用中加载
        if currentIndexPath.section != indexPath.section {
            HighlightRangeManager.loadNotes(chapterPath: paginator.getChapterHref(chapterIndex: indexPath.section))
        }
        return reuseableCell
    }

    func reuseView(_: ReuseView, updateCell cell: ReuseViewCell, forItemAt indexPath: IndexPath, needPrefetch: Bool) {
        updateCellContent(pageCell: cell, indexPath: indexPath, needPrefetch: needPrefetch)
    }

    func reuseView(_: ReuseView, willDisplay cell: ReuseViewCell, forItemAt indexPath: IndexPath) {
        updateCellContent(pageCell: cell, indexPath: indexPath)
    }

    func reuseView(_: ReuseView, didEndDisplaying cell: ReuseViewCell, forItemAt indexPath: IndexPath) {
        (cell as! BookReaderPageCell).indexPath = indexPath
        (cell as! BookReaderPageCell).enterLoadingState()
    }

    func updateCellContent(pageCell cell: ReuseViewCell, indexPath: IndexPath, needPrefetch: Bool = true) {
        Log.d("翻页测试：updateCellContent 0：")
        let reuseableCell = cell as! BookReaderPageCell
        if let page = getPage(indexPath: indexPath) {
            Log.d("翻页测试：updateCellContent 1：")
            reuseableCell.indexPath = indexPath
            reuseableCell.resetTxt(page: page,
                                   chapterName: page.pageIndexInChapter > 0 ? paginator.getSubLevelChapterName(indexPath: indexPath) : nil,
                                   pageNum: paginator.isAllLoad ? paginator.getCurrentPageAndTotalNum(indexPath: indexPath) : "") // "当前页码/总页码")
            Log.d("翻页测试：updateCellContent 2：")
        } else {
            reuseableCell.indexPath = indexPath
            reuseableCell.enterLoadingState()
            Log.d("翻页测试：updateCellContent 3：")
            if needPrefetch {
                Log.d("reloadSection: cellForItemAt中设置优先级: \(indexPath.section)")
                paginator.resetPriorityHigh(key: String(indexPath.section)) // 多次执行没事。
                preReloadChaptersDic[indexPath.section] = indexPath.section
                HighlightRangeManager.loadNotes(chapterPath: paginator.getChapterHref(chapterIndex: indexPath.section))
                Log.d("翻页测试：updateCellContent 4：")
            }
        }
    }

    func reuseViewLayout(_: ReuseView, cell: ReuseViewCell, indexPath: IndexPath, size: CGSize) {
        let reuseableCell = cell as! BookReaderPageCell
        reuseableCell.indexPath = indexPath
        reuseableCell.enterLoadingState()
        reuseableCell.layout(size: size)
    }

    // MARK: - ScrollView Delegate

    public func scrollViewWillBeginDragging(_ scrollView: UIScrollView) {
//        Log.d("scrollViewWillBeginDragging: \(scrollView.contentOffset),\(scrollView.contentSize)")
        WDReaderCenter.shared.isScrolling = true
        WDReaderCenter.shared.pointNow = scrollView.contentOffset

        if SelectMenu.isShowing() {
            SelectMenu.hide()
        }
        readerPageCellGetCurrentPageCell()?.clearSelectedState()

        // 隐藏工具栏
        enterFullModeWorkItem?.cancel()
        if !isHideNavAndTool {
            hideNavAndTool()
        }
    }

    public func scrollViewDidScroll(_ scrollView: UIScrollView) {
//        Log.d("scrollViewDidScroll: \(scrollView.contentOffset),\(scrollView.contentSize)")
        WDReaderCenter.shared.updatePageScrollDirection(inScrollView: scrollView)
    }

    public func scrollViewWillBeginDecelerating(_: UIScrollView) {
        // 上一次翻页未结束，不可以下次翻页。
//        Log.d("scrollViewWillBeginDecelerating: \(scrollView.contentOffset),\(scrollView.contentSize)")
        reuseView.isUserInteractionEnabled = false
    }

    // 只有滑动结束才会之行
    public func scrollViewDidEndDecelerating(_ scrollView: UIScrollView) {
        Log.d("翻页测试：BookReaderVC-scrollViewDidEndDecelerating1: contentOffset:\(scrollView.contentOffset), contentSize:\(scrollView.contentSize)")
        WDReaderCenter.shared.isScrolling = false
        currentIndexPath = reuseView.selectedIndexPath
        reuseView.isUserInteractionEnabled = true
        resetRecordAndPostBookReadProgressNotiWithAfterReload()
        Log.d("翻页测试：BookReaderVC-scrollViewDidEndDecelerating2: 滑动翻页结束")
    }

    @objc func tapBack() {
        let userInfo = bookReaderProgressUserInfo(currentIndexPath)

        paginator.stop()
        paginator.clear()
        backHandler?(userInfo)
        clearSearch()
    }

    // MARK: 渐变换页

    // 先判断边界，然后换页。
    func gotoPreviousPage(animated: Bool = true) {
        if let preIndexPath = preIndexPath(currentIndexPath: currentIndexPath) {
            guard reuseView.isUserInteractionEnabled else {
                return
            }
            reuseView.isUserInteractionEnabled = false
            reuseView.gotoPrevious(animated: animated) { [weak self] in
                guard let self = self else { return }
                self.currentIndexPath = preIndexPath
                Log.d("点击翻页结束。划线翻页结束。")
                self.resetRecordAndPostBookReadProgressNotiWithAfterReload()
                self.reuseView.isUserInteractionEnabled = true
            }
        }
    }

    func gotoNextPage(animated: Bool = true) {
        if let nextIndexPath = nextIndexPath(currentIndexPath: currentIndexPath) {
            guard reuseView.isUserInteractionEnabled else {
                return
            }
            reuseView.isUserInteractionEnabled = false
            reuseView.gotoNext(animated: animated) { [weak self] in
                guard let self = self else { return }
                self.currentIndexPath = nextIndexPath
                Log.d("点击翻页结束。划线翻页结束。")
                self.resetRecordAndPostBookReadProgressNotiWithAfterReload()
                self.reuseView.isUserInteractionEnabled = true
            }
        }
    }

    public func scrollViewDidEndScrollingAnimation(_: UIScrollView) {
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.03) { [weak self] in
            guard let self = self else { return }
            self.reuseView.isUserInteractionEnabled = true
        }
    }

    func gotoInitPage(href: String, contentOffset: Int, needResetRecord: Bool) {
        HighlightRangeManager.loadNotes(chapterPath: href)
        let chapterIndex = paginator.getChapterIndex(href: href)
        if let chapter = paginator.chapterFileDic[chapterIndex],
//           chapter.loadState == .loaded,
           chapter.pages.count > 0,
           let indexPath = paginator.getIndexPath(chapterIndex: chapterIndex, contentOffset: contentOffset)
        {
            currentIndexPath = indexPath
            if reuseView.isInitData() {
                gotoPage(indexPath: currentIndexPath, needResetRecord: needResetRecord)
            } else {
                reuseView.initData()
            }
            refreshBookMarkStateOnNav()
            reuseView.isHidden = false
            Log.d("阅读器 加载 gotoInitPage 初始化页结束。\(href), contentOffset:\(contentOffset),chapterIndex:\(chapterIndex)")
            resetRecordAndPostBookReadProgressNoti()
        } else {
            Log.d("阅读器 加载 gotoInitPage 未找到初始化页。\(href), contentOffset:\(contentOffset),chapterIndex:\(chapterIndex)")
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.preReloadChaptersDic[chapterIndex] = chapterIndex
                self.preReloadContentOffset = contentOffset
                self.preReloadChapterIndex = chapterIndex
                self.isFirstLoad = true
                self.paginator.resetPriorityHigh(key: String(chapterIndex))
                self.reuseView.isUserInteractionEnabled = false
            }
        }
        hideFirstLoading()
    }

    // 拖进度，预加载，目录跳转，书中锚点跳转。
    func gotoPage(chapterIndex: Int, hrefAndFragmentId: String) {
        HighlightRangeManager.loadNotes(chapterPath: paginator.getChapterHref(chapterIndex: chapterIndex))
        if let chapter = paginator.chapterFileDic[chapterIndex],
//           chapter.loadState == .loaded
           chapter.pages.count > 0
        {
            let pageIndex = chapter.resourcePageIndexDic[hrefAndFragmentId] ?? 0
            let indexPath = IndexPath(item: pageIndex, section: chapterIndex)
            reuseView.reloadAndGoto(indexPath: indexPath)
            currentIndexPath = indexPath
            Log.d("目录翻页结束。跳转（普通跳转，索引，纸书页码）结束。")
            resetRecordAndPostBookReadProgressNoti()
        } else {
            // 先fetch，后reload出loading，因为reload的方法会自动fetch，这个fetch没有preReloadHrefAndFragmentId
            preReloadChaptersDic[chapterIndex] = chapterIndex
            preReloadHrefAndFragmentId = hrefAndFragmentId
            reuseView.reloadAndGoto(indexPath: IndexPath(item: 0, section: chapterIndex))
            currentIndexPath = IndexPath(item: 0, section: chapterIndex)
            paginator.resetPriorityHigh(key: String(chapterIndex))
            reuseView.isUserInteractionEnabled = false
        }
    }

    // 书签跳转chapterIndex版本
    func gotoPage(chapterIndex: Int, contentOffset: Int) {
        HighlightRangeManager.loadNotes(chapterPath: paginator.getChapterHref(chapterIndex: chapterIndex))
        if let chapter = paginator.chapterFileDic[chapterIndex],
//           chapter.loadState == .loaded,
           chapter.pages.count > 0,
           let indexPath = paginator.getIndexPath(chapterIndex: chapterIndex, contentOffset: contentOffset)
        {
            reuseView.reloadAndGoto(indexPath: indexPath)
            currentIndexPath = indexPath
            Log.d("书签跳转结束。")
            resetRecordAndPostBookReadProgressNoti()
        } else {
            paginator.resetPriorityHigh(key: String(chapterIndex))
            preReloadChaptersDic[chapterIndex] = chapterIndex
            preReloadContentOffset = contentOffset
            preReloadChapterIndex = chapterIndex
            reuseView.reloadAndGoto(indexPath: IndexPath(item: 0, section: chapterIndex))
            currentIndexPath = IndexPath(item: 0, section: chapterIndex)
            reuseView.isUserInteractionEnabled = false
        }
    }

    // 书签跳转href版本
    func gotoPage(href: String, contentOffset: Int) {
        HighlightRangeManager.loadNotes(chapterPath: href)
        let chapterIndex = paginator.getChapterIndex(href: href)
        if let chapter = paginator.chapterFileDic[chapterIndex],
//           chapter.loadState == .loaded,
           chapter.pages.count > 0,
           let indexPath = paginator.getIndexPath(chapterIndex: chapterIndex, contentOffset: contentOffset)
        {
            reuseView.reloadAndGoto(indexPath: indexPath)
            currentIndexPath = indexPath
            Log.d("书签跳转结束。")
            resetRecordAndPostBookReadProgressNoti()
        } else {
            paginator.resetPriorityHigh(key: String(chapterIndex))
            preReloadChaptersDic[chapterIndex] = chapterIndex
            preReloadContentOffset = contentOffset
            preReloadChapterIndex = chapterIndex
            reuseView.reloadAndGoto(indexPath: IndexPath(item: 0, section: chapterIndex))
            currentIndexPath = IndexPath(item: 0, section: chapterIndex)
            reuseView.isUserInteractionEnabled = false
        }
    }

    /// 前后翻页
    /// - Parameters:
    ///   - indexPath: 位置
    ///   - needPostProgress: 需要发送(保存)阅读记录。
    ///   - needResetRecord: true，需要重新设置临时阅读进度变量，默认需要。 false，换字体或转屏时候，不更新阅读位置。
    func gotoPage(indexPath: IndexPath, needPostProgress: Bool = false, needResetRecord: Bool) {
        HighlightRangeManager.loadNotes(chapterPath: paginator.getChapterHref(chapterIndex: indexPath.section))
        let chapterIndex = indexPath.section

        if let chapter = paginator.chapterFileDic[chapterIndex],
           chapter.pages.count > 0
        {
            currentIndexPath = indexPath
            reuseView.reloadAndGoto(indexPath: indexPath)
            Log.d("前后章，滑动滚动条翻页结束。换页码，转向。跳转书签")
            refreshBookMarkStateOnNav()
            if needPostProgress {
                postBookReadProgressNoti(currentIndexPath)
            }
            if needResetRecord {
                resetRecord()
            }
        } else {
            // TODO: item要修改。可能不是当前章第1页。
            paginator.resetPriorityHigh(key: String(chapterIndex))
            preReloadChaptersDic[chapterIndex] = chapterIndex
            preReloadIndexPath = indexPath // IndexPath(item: 0, section: chapterIndex)
            reuseView.reloadAndGoto(indexPath: indexPath) // IndexPath(item: 0, section: chapterIndex))
            currentIndexPath = indexPath // IndexPath(item: 0, section: chapterIndex)
        }
    }

    // 目前滑动滚动条翻页
    func gotoPage(page: Int) {
        if let indexPath = paginator.getIndexPath(pageNum: page) {
            gotoPage(indexPath: indexPath, needResetRecord: true)
        }
    }

    func getPage(indexPath: IndexPath) -> Page? {
        if let chapter = paginator.chapterFileDic[indexPath.section], chapter.pages.count > indexPath.row {
            let page = chapter.pages[indexPath.item]
            return page
        }
        return nil
    }

    func preIndexPath(currentIndexPath: IndexPath) -> IndexPath? {
        if currentIndexPath.item > 0, let currentChapter = paginator.chapterFileDic[currentIndexPath.section], currentChapter.pages.count > currentIndexPath.item - 1 {
            return IndexPath(item: currentIndexPath.item - 1, section: currentIndexPath.section)
        } else if let preChapter = paginator.chapterFileDic[currentIndexPath.section - 1],
//                 preChapter.isReload,
                  preChapter.pages.count > 0,!preChapter.displayedAttributedString.isEmpty()
        {
            return IndexPath(item: preChapter.pages.count - 1, section: currentIndexPath.section - 1)
        } else if currentIndexPath.section - 1 >= 0 {
            // 第一个空页面。
            if currentIndexPath.section - 1 == 0, let preChapter = paginator.chapterFileDic[currentIndexPath.section - 1], preChapter.displayedAttributedString.isEmpty() {
                return nil
            }
            return IndexPath(item: 0, section: currentIndexPath.section - 1)
        } else {
            return nil
        }
    }

    func nextIndexPath(currentIndexPath: IndexPath) -> IndexPath? {
        print("\(CFAbsoluteTimeGetCurrent())翻页测试：情况0")
        if let chapter = paginator.chapterFileDic[currentIndexPath.section], chapter.pages.count > currentIndexPath.item + 1 {
            print("\(CFAbsoluteTimeGetCurrent())翻页测试：情况1")
            return IndexPath(item: currentIndexPath.item + 1, section: currentIndexPath.section)
        } else if let nextChapter = paginator.chapterFileDic[currentIndexPath.section + 1],
//                 nextChapter.loadState == .loaded,
                  nextChapter.pages.count > 0
        {
            print("\(CFAbsoluteTimeGetCurrent())翻页测试：情况2")
            return IndexPath(item: 0, section: currentIndexPath.section + 1)
        } else if currentIndexPath.section + 1 < WDReaderCenter.shared.chapterFilesMaxCount {
            print("\(CFAbsoluteTimeGetCurrent())翻页测试：情况3")
            return IndexPath(item: 0, section: currentIndexPath.section + 1)
        } else {
            print("\(CFAbsoluteTimeGetCurrent())翻页测试：情况4")
            return nil
        }
    }

    // MARK: 重设字体 or 转向重设size

    var isReseting: Bool = false {
        didSet {
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                self.reuseView.isUserInteractionEnabled = !self.isReseting
            }
        }
    }

    var resetSemaphore: DispatchSemaphore = .init(value: 1)
    var resetSerialQueue: DispatchQueue = .init(label: "resetSerialQueue", qos: .userInteractive)
    var changeFontHref: String?
    var changeFontContentOffset: Int?
    func resetRecord() {
        changeFontHref = nil
        changeFontContentOffset = nil
    }

    // MARK: 改变字体或改变字体大小

    /// <#Description#>
    /// - Parameters:
    ///   - value: <#value description#>
    ///   - isTouchEnd: true是1、slider的end事件; 或是2、按钮点击。 false是3、slider的move事件或其他事件。
    /// false没有阅读记录则保存，计算，结束后不清空阅读记录(3事件)。
    /// true没有阅读记录则保存，计算，结束后清空阅读记录(2事件)。 如果记录和字体都相同(1事件)则忽略。
    func changeFontSize(value: Int) {
        resetSerialQueue.async { [weak self] in
            guard let self = self else {
                return
            }

            self.resetSemaphore.wait()
            self.isReseting = true

            if self.changeFontHref == nil || self.changeFontContentOffset == nil {
                if let chapter = self.paginator.currentChapter {
                    let page = chapter.pages[self.paginator.currentIndexPath.item]
                    self.changeFontHref = chapter.resource.href
                    self.changeFontContentOffset = page.ctPage.range.location + page.ctPage.range.length / 2
                }
            }

            WDReaderConfig.setCurrentFontSizeIndex(bookid: WDReaderCenter.shared.fbBook.name!, footSizeIndex: value)

            self.paginator.reExecute {
                self.gotoInitPage(href: self.changeFontHref ?? "", contentOffset: self.changeFontContentOffset ?? 0, needResetRecord: false)
            }
        }
    }

    // MARK: 转向相关

    /// Determines whether layout should be updated for the given new frame
    private func shouldUpdateLayoutForNewFrame(_ newFrame: CGRect) -> Bool {
        #if targetEnvironment(macCatalyst)
            // For Mac Catalyst, always handle window resizing
            return true
        #else
            if UIDevice.current.userInterfaceIdiom == .pad {
                // iPad-specific size change logic
                let widthDiff = abs(reuseViewFrame.width - newFrame.width)
                let heightDiff = abs(reuseViewFrame.height - newFrame.height)
                return widthDiff > significantSizeChangeThreshold || heightDiff > significantSizeChangeThreshold
            } else {
                // iPhone-specific orientation change logic
                return reuseViewFrame.isOrientationPortrait != newFrame.isOrientationPortrait
            }
        #endif
    }

    override public func viewWillTransition(to size: CGSize, with coordinator: UIViewControllerTransitionCoordinator) {
        super.viewWillTransition(to: size, with: coordinator)
        hideNavAndTool(animated: false)

        // Define shouldUpdateLayout once at the beginning with a default value
        var shouldUpdateLayout = false

        coordinator.animate { [weak self] _ in
            guard let self = self else { return }

            let insets: UIEdgeInsets = UIApplication.shared.windows[0].safeAreaInsets

            // 如果有进度UI,则隐藏
            if self.progressV?.type == .progress {
                self.hideProgressV()
            }
            // 隐藏选中状态。
            SelectMenu.hideMenuAndClearSelectedState()

            let newFrame = CGRect(
                x: 0,
                y: insets.top,
                width: size.width,
                height: size.height - insets.top - insets.bottom
            )

            // Determine if layout update is needed
            shouldUpdateLayout = self.shouldUpdateLayoutForNewFrame(newFrame)

            // If no significant change, return early
            if !shouldUpdateLayout {
                return
            }

            readerPageCellGetCurrentPageCell()?.clearHighlightState()

            // 逻辑类似换字体大小
            self.reuseViewFrame = newFrame
            self.paginator.setPageSize(self.pageSize)

            // Update search navigation layout
            self.updateSearchNavigationLayout()

            if self.changeFontHref == nil || self.changeFontContentOffset == nil {
                if let chapter = self.paginator.currentChapter {
                    let page = chapter.pages[self.paginator.currentIndexPath.item]

                    self.changeFontHref = chapter.resource.href
                    self.changeFontContentOffset = page.ctPage.range.location + page.ctPage.range.length / 2
                }
            }

            self.reuseView.frame = self.reuseViewFrame
            self.reuseView.layout(size: self.pageSize)

            self.paginator.reExecute {
//                self.gotoInitPage(href: self.changeFontHref ?? "", contentOffset: self.changeFontContentOffset ?? 0, needResetRecord: false)
            }
        } completion: { [weak self] _ in
            guard let self = self else { return }
            if shouldUpdateLayout {
                self.gotoInitPage(href: self.changeFontHref ?? "", contentOffset: self.changeFontContentOffset ?? 0, needResetRecord: false)
            }
        }
    }

    // MARK: 重设置临时阅读记录，发送保存阅读记录。如果未加载，等加载后执行。

    func resetRecordAndPostBookReadProgressNotiWithAfterReload() {
        if let chapter = paginator.currentChapter, chapter.pages.count > 0 {
            resetRecordAndPostBookReadProgressNoti()
        } else {
            Log.d("还没加载前，试图查找新的阅读记录, chapter:\(String(describing: paginator.currentChapter?.resource.href)), currentIndexPath:\(currentIndexPath)")
            preRecordReadProgressOnReloadChaptersDic[currentIndexPath.section] = currentIndexPath
            reuseView.isUserInteractionEnabled = false
        }
    }

    // 重设置临时阅读记录，发送保存阅读记录。
    func resetRecordAndPostBookReadProgressNoti(indexPath: IndexPath) {
        resetRecord()
        postBookReadProgressNoti(indexPath)

        refreshBookMarkStateOnNav()
    }

    func resetRecordAndPostBookReadProgressNoti() {
        resetRecord()
        postBookReadProgressNoti(currentIndexPath)

        refreshBookMarkStateOnNav()
    }

    func postBookReadProgressNotiWithAfterReload() {
        if let chapter = paginator.currentChapter, chapter.pages.count > 0 {
            postBookReadProgressNoti(currentIndexPath)
        } else {
            Log.d("还没加载前，试图查找新的阅读记录, chapter:\(String(describing: paginator.currentChapter?.resource.href)), currentIndexPath:\(currentIndexPath)")
            preRecordReadProgressOnReloadChaptersDic[currentIndexPath.section] = currentIndexPath
            reuseView.isUserInteractionEnabled = false
        }
    }

    func postBookReadProgressNoti(_ indexPath: IndexPath) {
        let userInfo = bookReaderProgressUserInfo(indexPath)
        NotificationCenter.default.post(name: WDReaderView.readBookDidPageChangedNotification, object: nil, userInfo: userInfo)
    }

    func bookReaderProgressUserInfo(_ indexPath: IndexPath) -> [String: Any]? {
        Log.d("翻页测试：bookReaderProgressUserInfo")

        var userInfo: [String: Any]?
        if let chapter = paginator.getChapter(chapterIndex: indexPath.section),
           chapter.pages.count > 0
        {
            let page = chapter.pages[indexPath.item]
            let resourceHref = chapter.resource.href

            userInfo = [String: Any]()
            userInfo?["resourceId"] = WDReaderConfig.resourceId
            userInfo?["contentOffset"] = page.ctPage.range.location
            userInfo?["chapterPath"] = resourceHref
            if paginator.isAllLoad {
                Log.d("翻页测试：bookReaderProgressUserInfo2")
                let totalCount = paginator.getPagesCount()
                if totalCount > 0 {
                    userInfo?["progress"] = paginator.getPageNum(indexPath: indexPath) * 100 / totalCount
                } else {
                    userInfo?["progress"] = 0
                }
                Log.d("翻页测试：bookReaderProgressUserInfo3")
            } else {
//                let chapterStartProgress = Float(currentIndexPath.section) / Float(WDReaderCenter.shared.chapterFilesMaxCount)
//                let chapterInnerRate = Float(currentIndexPath.item + 1) / Float(chapter.pages.count)
//                let chapterInnerProgress = chapterInnerRate / Float(WDReaderCenter.shared.chapterFilesMaxCount)
//                userInfo?["progress"] = Int((chapterStartProgress + chapterInnerProgress) * 100)
                let pnum = indexPath.section == 0 && indexPath.item == 0 ? 0 : 1
                userInfo?["progress"] = (Float(indexPath.section * chapter.pages.count + indexPath.item + pnum) / Float(chapter.pages.count * WDReaderCenter.shared.chapterFilesMaxCount)).convertToPercentage
            }

            // 矫正边界
            var location = page.ctPage.range.location
            if page.ctPage.range.location > chapter.displayedAttributedString.string.count {
                location = chapter.displayedAttributedString.string.count - 1
            }
            var contentLength = (chapter.displayedAttributedString.string.count - location - 20)
            if contentLength > 20 {
                contentLength = 20
            }
            if contentLength < 0 {
                contentLength = 0
            }

            userInfo?["summary"] = chapter.displayedAttributedString.attributedSubstring(from: NSRange(location: location, length: contentLength)).string
            Log.d("翻页测试：bookReaderProgressUserInfo4- chapter:\(resourceHref), indexPath:\(indexPath)")
        }
        return userInfo
    }

    // MARK: - Search Specific Loading Indicator

    private func configureLoadingIndicator(show: Bool) {
        if show {
            if activityIndicator == nil {
                activityIndicator = UIActivityIndicatorView(style: .medium)
                activityIndicator?.center = view.center
            }
            activityIndicator?.startAnimating()
            if let indicator = activityIndicator, indicator.superview == nil {
                view.addSubview(indicator)
            }
            reuseView?.isHidden = true // Hide the view during loading
        } else {
            activityIndicator?.stopAnimating()
            activityIndicator?.removeFromSuperview()
            reuseView?.isHidden = false // Show the view again
        }
    }

    func showSearchLoading() {
        Log.d("Showing search loading indicator.")
        // Cancel any existing timeout first
        searchLoadingTimeoutWorkItem?.cancel()

        configureLoadingIndicator(show: true)
        reuseView?.isUserInteractionEnabled = false // Interaction disabled while hidden/loading

        // Safety timeout: Ensure loading indicator is hidden and view shown even if something goes wrong
        let workItem = DispatchWorkItem { [weak self] in
            Log.w("Search loading timeout triggered. Hiding indicator and showing view.")
            self?.hideSearchLoading()
        }
        searchLoadingTimeoutWorkItem = workItem
        DispatchQueue.main.asyncAfter(deadline: .now() + 10.0, execute: workItem) // 10 second timeout
    }

    func hideSearchLoading() {
        Log.d("Hiding search loading indicator.")
        // Cancel the timeout if it hasn't executed yet
        searchLoadingTimeoutWorkItem?.cancel()
        searchLoadingTimeoutWorkItem = nil

        configureLoadingIndicator(show: false)
        reuseView?.isUserInteractionEnabled = true // Re-enable interaction when shown
    }
}

extension Float {
    var convertToPercentage: Int {
        switch self {
        case ...0.0:
            return 0
        case 1.0...:
            return 100
        default:
            return max(1, min(99, Int((self * 100).rounded())))
        }
    }
}

extension CGRect {
    var isOrientationPortrait: Bool {
        height > width
    }
}
