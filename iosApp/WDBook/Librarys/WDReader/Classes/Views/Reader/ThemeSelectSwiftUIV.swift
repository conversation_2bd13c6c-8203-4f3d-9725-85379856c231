//
//  ThemeSelectSwiftUIV.swift
//  WDReader
//
//  Created by <PERSON> on 2020/8/24.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import UIKit
import SwiftUI
import UIKit

// 声明PlayerView代表PlayerUIView
public struct ThemeSelectSwiftUIV: UIViewRepresentable {
    
    public init() {
    }
    // update方法使我们能够将UIView和SwiftUI状态更新保持同步，目前我们暂且将其保留为空
    public func updateUIView(_ uiView: ThemeSelectV, context: Context) {
    
    }
    // make方法返回初始视图
    public func makeUIView(context: Context) -> ThemeSelectV {
        return ThemeSelectV()
    }
}
