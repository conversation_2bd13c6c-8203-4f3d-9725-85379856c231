//
//  BookReaderVC+OutlinePageVCDelegate.swift
//  WDReader
//
//  Created by <PERSON> on 2021/3/3.
//  Copyright © 2021 <PERSON>. All rights reserved.
//

import Foundation

// MARK: OutlinePageVCDelegate
extension BookReaderVC: OutlinePageVCDelegate {
    
    func outlinePageVCChapterList(tocReference reference: FRTocReference) {
        let chapterNum = findChapterNumber(reference)
        gotoPage(chapterIndex: chapterNum, hrefAndFragmentId: reference.resourceHrefAndFragmentId())
    }
    
    func chapterList(didDismissedChapterList chapterList: FolioReaderChapterList) {
        Log.d("chapterList:\(chapterList)")
    }
}
