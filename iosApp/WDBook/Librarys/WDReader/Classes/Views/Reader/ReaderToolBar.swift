//
//  ReaderToolBar.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/29.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import UIKit
import SnapKit

protocol ReaderToolBarDelegate:AnyObject {
    func readerToolBarDidSelected(type:ReaderToolBar.ItemType)
}

class ReaderToolBar: UIView {
    enum ItemType:Int {
        case toc = 0
        case progress
        case style
        case light
    }

    struct Item {
        let type:ItemType
        let imageName:String
    }
    
    let items = [Item(type: .toc, imageName: "tab_table_of_content"),
                 Item(type:.progress,imageName: "tab_progress"),
                 Item(type: .style, imageName:"tab_Font_Style"),
                 Item(type: .light, imageName:"tab_brightness_outlined")]
    let span:CGFloat = 55
    
    weak var delegate:ReaderToolBarDelegate?
    
    var imgStack:UIStackView
    var btnStack:UIStackView
    
    override init(frame: CGRect) {
        imgStack = UIStackView(frame: frame)
        btnStack = UIStackView(frame: frame)
        super.init(frame: frame)
        
        self.backgroundColor = dynamicBackgroundColor1
        
        imgStack.distribution = .equalSpacing
        imgStack.alignment = .center
        addSubview(imgStack)
        imgStack.snp.makeConstraints { (make) in
            make.top.bottom.equalToSuperview()
            make.leading.equalTo(span)
            make.trailing.equalTo(-span)
        }
        
        btnStack.distribution = .fillEqually
        btnStack.alignment = .fill
        btnStack.spacing = 10
        addSubview(btnStack)
        btnStack.snp.makeConstraints { (make) in
            make.top.bottom.equalToSuperview()
            make.leading.equalTo(span/2)
            make.trailing.equalTo(-span/2)
        }
        
        for i in 0 ..< items.count {
            let iv = UIImageView(image: UIImage(named: items[i].imageName))
//            iv.contentMode = .scaleAspectFit
            iv.contentMode = .center
            iv.snp.makeConstraints { (make) in
                make.width.height.equalTo(24)
            }
            imgStack.addArrangedSubview(iv)
            
            let btn = UIButton()
            btn.tag = items[i].type.rawValue
            btn.addTarget(self, action: #selector(tapItem(btn:)), for: .touchUpInside)
            btnStack.addArrangedSubview(btn)
        }
        
        let line = UIView()
        line.backgroundColor = dynamicSpanLineColor
        addSubview(line)
        line.snp.makeConstraints { (make) in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(0.5)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    @objc func tapItem(btn:UIButton){
        delegate?.readerToolBarDidSelected(type: ItemType(rawValue: btn.tag)!)
    }
}

