//
//  PullBookmarkView.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2021/8/9.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SnapKit

class PullBookmarkView: UIView {
    static let bookmarkAvaliableHeight:CGFloat = 80
    static let bookMarkViewHeight:CGFloat = 50
    
    var bookMarkContent = UIView()
    var bookmarkLabel = UILabel()
    var bookmarkImageViewEmpty = UIImageView()
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        bookMarkContent.backgroundColor = dynamicBookMarkBackgroundColor
        addSubview(bookMarkContent)
        bookMarkContent.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(PullBookmarkView.bookMarkViewHeight)
        }
        
        bookmarkLabel.textColor = .white
        bookmarkLabel.font = UIFont.regular(size: 14)
        bookmarkLabel.textAlignment = .right
        bookMarkContent.addSubview(bookmarkLabel)
        bookmarkLabel.snp.makeConstraints({ make in
            make.top.equalTo(PullBookmarkView.bookMarkViewHeight - 16)
            make.leading.equalTo(50)
            make.trailing.equalTo(-50)
            make.height.equalTo(16)
        })
        
        bookmarkImageViewEmpty.image = UIImage(named: "bookmark_outlined")
        bookMarkContent.addSubview(bookmarkImageViewEmpty)
        bookmarkImageViewEmpty.snp.makeConstraints({ make in
            make.top.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(48)
            make.trailing.equalTo(-21)
        })
        bookmarkImageViewEmpty.isHidden = true
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}

private var READERVC_BOOKMARK_BACKGROUND_KEY: UInt8 = 0
private var READERVC_BOOKMARK_PAGE_GAP_KEY: UInt8 = 0
private var READERVC_BOOKMARK_VIEW_KEY: UInt8 = 0
private var READERVC_BOOKMARK_PAN_START_POINT_KEY: UInt8 = 0
private var READERVC_BOOKMARK_PAN_END_POINT_KEY: UInt8 = 0
private var READERVC_BOOKMARK_GLOBAL_VIEW_KEY: UInt8 = 0
private var READERVC_BOOKMARK_CURRENT_PAGE_HAS_KEY: UInt8 = 0
extension BookReaderVC{
    var bookMarkBackgroundOnTop:UIView?{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_BACKGROUND_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_BACKGROUND_KEY) as? UIView
        }
    }
    
    var bookMarkPageCellGap:UIView?{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_PAGE_GAP_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_PAGE_GAP_KEY) as? UIView
        }
    }
    
    var pullBookMarkView:PullBookmarkView?{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_VIEW_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_VIEW_KEY) as? PullBookmarkView
        }
    }
    
    var panStartPoint:CGPoint?{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_PAN_START_POINT_KEY, newValue, .OBJC_ASSOCIATION_COPY_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_PAN_START_POINT_KEY) as? CGPoint
        }
    }
    
    var panEndPoint:CGPoint?{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_PAN_END_POINT_KEY, newValue, .OBJC_ASSOCIATION_COPY_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_PAN_END_POINT_KEY) as? CGPoint
        }
    }
    
    var hasBookmark:Bool{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_CURRENT_PAGE_HAS_KEY, newValue, .OBJC_ASSOCIATION_ASSIGN)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_CURRENT_PAGE_HAS_KEY) as? Bool ?? false
        }
    }
    
    func getBookmarkTitle(isAvaliable:Bool) -> String{
        if hasBookmark{
            if isAvaliable{
                return "松手删除书签".localized
            }else{
                return "下拉删除书签".localized
            }
        }else{
            if isAvaliable{
                return "松手添加书签".localized
            }else{
                return "下拉添加书签".localized
            }
        }
    }
    
    @objc func panHandler(g:UIPanGestureRecognizer){
//        Log.d("pangesture: BookReaderVC: \(g.state.rawValue)")
        switch g.state {
        case .began:
            
            if bookMarkBackgroundOnTop == nil {
                bookMarkBackgroundOnTop = UIView()
                bookMarkBackgroundOnTop?.frame = CGRect(x: 0, y: 0, width: reuseViewFrame.width, height: 0)
                bookMarkBackgroundOnTop?.backgroundColor = dynamicBookMarkBackgroundColor
                view.insertSubview(bookMarkBackgroundOnTop!, at: 0)
            }
            
            if let page = currentPage(),
               let _ = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range){
                hasBookmark = true
            }else{
                hasBookmark = false
            }
            
            readerPageCellGetCurrentPageCell()?.hideBookmark()
            
            hideNavAndTool()
            
            pullBookMarkView = PullBookmarkView()
            pullBookMarkView?.frame = CGRect(x: 0, y: -PullBookmarkView.bookMarkViewHeight, width: reuseViewFrame.width, height: PullBookmarkView.bookMarkViewHeight)
            view.insertSubview(pullBookMarkView!, aboveSubview: bookMarkBackgroundOnTop!)
            
            refreshPullBookmark()
            refreshGlobalBookmark()
            
            panStartPoint = g.location(in: g.view)
            panEndPoint = panStartPoint
//            Log.d("pangesture: BookReaderVC:.began, point: \(g.location(in: g.view))")
        case .changed:
//            Log.d("pangesture: BookReaderVC:.changed, point: \(g.location(in: g.view))")
            panEndPoint = g.location(in: g.view)
            let offsetY = (panEndPoint!.y - panStartPoint!.y)/2
            
            bookMarkBackgroundOnTop?.frame = CGRect(x: 0, y: 0, width: reuseViewFrame.width, height: offsetY)
            
            if offsetY > 0{
                reuseView.frame = reuseViewFrame.offsetBy(dx: 0, dy: offsetY)
            }else{
                reuseView.frame = reuseViewFrame
            }
            
            if offsetY - PullBookmarkView.bookMarkViewHeight <= 0{
                pullBookMarkView?.frame = CGRect(x: 0, y: offsetY - PullBookmarkView.bookMarkViewHeight, width: reuseViewFrame.width, height: PullBookmarkView.bookMarkViewHeight)
            }else{
                pullBookMarkView?.frame = CGRect(x: 0, y: 0, width: reuseViewFrame.width, height: offsetY)
            }
           
            pullBookMarkView?.bookmarkLabel.text = getBookmarkTitle(isAvaliable: offsetY > 80)
            
        case .ended:
//            Log.d("pangesture: BookReaderVC:.end, point: \(g.location(in: g.view))")
            panEndPoint = g.location(in: g.view)
            
            let offsetY = (panEndPoint!.y - panStartPoint!.y)/2
            hideBookmarkView(offsetY: offsetY)
            
        case .cancelled:
//            Log.d("pangesture: BookReaderVC:.cancel, point: \(g.location(in: g.view))")
            panEndPoint = g.location(in: g.view)
            let offsetY = (panEndPoint!.y - panStartPoint!.y)/2
            hideBookmarkView(offsetY: offsetY)
            
        default:
            break
        }
    }
    
    func refreshPullBookmark(){
        if let page = currentPage(){
            if let _ = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range){
                pullBookMarkView?.bookmarkImageViewEmpty.isHidden = true
            }else{
                pullBookMarkView?.bookmarkImageViewEmpty.isHidden = false
            }
        }
    }
    
    
    func hideBookmarkView(offsetY:CGFloat){
        if offsetY > 80 {
//            Log.d("满足条件")
            tapBookMark(id:UIButton())
            refreshPullBookmark()
            refreshGlobalBookmark()
        }else{
//            Log.d("不满足条件")
        }
        
        UIView.animate(withDuration: 0.3) { [self] in
            self.bookMarkBackgroundOnTop?.frame = CGRect(x: 0, y: 0, width: reuseViewFrame.width, height: 0)
            self.reuseView.frame = self.reuseViewFrame
            self.pullBookMarkView?.frame = CGRect(x: 0, y: -PullBookmarkView.bookMarkViewHeight, width: self.reuseViewFrame.width, height: PullBookmarkView.bookMarkViewHeight)
        } completion: { success in
            self.pullBookMarkView?.removeFromSuperview()
            self.pullBookMarkView = nil
            self.hideGlobalBookmark()
            self.readerPageCellGetCurrentPageCell()?.resetBookmark()
        }
    }
    
    //MARK: 屏幕上全局书签
    var bookmarkGlobalImageView:UIImageView?{
        set{
            objc_setAssociatedObject(self, &READERVC_BOOKMARK_GLOBAL_VIEW_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &READERVC_BOOKMARK_GLOBAL_VIEW_KEY) as? UIImageView
        }
    }
    
    func showGLobalBookmark(){
        hideGlobalBookmark()
        view.clipsToBounds = false
        bookmarkGlobalImageView = UIImageView(image: UIImage(named: "bookmark_light"))
        view.addSubview(bookmarkGlobalImageView!)
        bookmarkGlobalImageView?.snp.makeConstraints({ make in
            make.top.equalToSuperview()
            make.width.equalTo(16)
            make.height.equalTo(48)
            make.trailing.equalTo(-21)
        })
    }

    func hideGlobalBookmark(){
        bookmarkGlobalImageView?.removeFromSuperview()
        bookmarkGlobalImageView = nil
    }
    
    func refreshGlobalBookmark(){
        if let page = currentPage(){
            if let _ = BookmarkManager.get(chapterPath: page.href, range: page.ctPage.range){
                showGLobalBookmark()
            }else{
                hideGlobalBookmark()
            }
        }
    }
    
//    public override func viewWillAppear(_ animated: Bool) {
//        super.viewWillAppear(animated)
//        UIApplication.shared.setStatusBarHidden(true, with: .none)
////        self.performSelector(onMainThread: #selector(setNeedsStatusBarAppearanceUpdate), with: nil, waitUntilDone: true)
////        let statusBar = UIApplication.shared.value(forKey: "statusBar") as? UIView
////        statusBar?.isHidden = true
//    }
    //iPa的不能用此方法，iphone无影响。但都必须使用SwiftUI的.statusBar(hidden:)替代
//    public override var prefersStatusBarHidden: Bool{
//        return isHideNavAndTool
//    }
}
