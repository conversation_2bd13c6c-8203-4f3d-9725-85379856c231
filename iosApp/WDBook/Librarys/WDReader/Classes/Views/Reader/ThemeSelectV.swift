//
//  ThemeSelectV.swift
//  wedevotebible
//
//  Created by <PERSON> on 2019/11/6.
//  Copyright © 2019 WD Bible Team. All rights reserved.
//

import Foundation
import UIKit

public extension ThemeSelectV{
    static let languageChangedNotification = Notification.Name(rawValue: "ThemeSelectV.languageChangedNotification")
}

protocol ThemeSelectVDelegate:AnyObject {
    func themeSelectVDidSelected(index:Int)
}

public class ThemeSelectV: UIView {
    weak var delegate:ThemeSelectVDelegate?
    
    var titleWidth:CGFloat = 78
    var titleHeight:CGFloat = 50
    var imgTopGap:CGFloat = 16
    var imgHeight:CGFloat = 20
    var sectionHeight:CGFloat = 50 + 16 + 20
    var rate:CGFloat = 1.0
    
    var stack:UIStackView!
    
    var themeVs:[UIStackView]!
    var titles:[UILabel]!
    var iconImages:[UIImageView]!
    

    lazy var dayView:UIView = {
        let v = UIView()
        
        return v
    }()
    
    override init(frame:CGRect) {
        super.init(frame: frame)
        
        initTitles()
        
        stack = UIStackView(arrangedSubviews: themeVs)
        stack.axis = .horizontal
        stack.distribution = .equalSpacing
        stack.alignment = .center
        addSubview(stack)
        stack.snp.makeConstraints { (make) in
//            make.edges.equalToSuperview()
            make.top.equalToSuperview()
            make.leading.equalTo(0)
            make.trailing.equalTo(0)
            make.height.equalTo(sectionHeight)
        }
        
        refreshTheme()
        
        NotificationCenter.default.addObserver(self, selector: #selector(systemDarkModeChanged(noti:)), name: NOTI_SYSTEM_DARKMODE_CHANGED, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(languageChanged(noti:)), name: ThemeSelectV.languageChangedNotification, object: nil)
    }
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    func initTitles(){
        var themeVs = [UIStackView]()
        var titles = [UILabel]()
        var images = [UIImageView]()
        
        for i in 0 ..< 3 {
            let title = UILabel()
            if i == 0 {
                title.text = "跟随系统".localized
                title.textColor = dynamicDarkModeTextColor
                title.backgroundColor = dynamicDarkModeBGColor
                title.layer.borderColor = dynamicBorderColor1.cgColor
//                title.layerBorderColorDynimic = dynamicBorderColor1
                title.layer.borderWidth = 1.0
                
            }else if i == 1 {
                title.text = "浅色".localized
                title.textColor = dayTextColor
                title.backgroundColor = dynamicBackgroundColor
                title.layer.borderColor = UIColor(hex: 0xD8D8D8).cgColor
                title.layer.borderWidth = 1.0
                
            }else{
                title.text = "深色".localized
                title.textColor = darkTextColor
                title.backgroundColor = darkBGColor
            }
            
            title.font = UIFont.regular(size: 14)
            title.frame = CGRect(x: 0, y: 0, width: titleWidth, height: titleHeight)
            title.layer.cornerRadius = 4
            title.layer.masksToBounds = true
            title.textAlignment = .center
            titles.append(title)
            title.snp.makeConstraints { (make) in
                make.width.equalTo(titleWidth)
                make.height.equalTo(titleHeight)
            }
            
            let iv = UIImageView()
            iv.image = UIImage(named: "radio_off")
            iv.highlightedImage = UIImage(named: "radio_on")
            iv.contentMode = .center
            images.append(iv)
            iv.snp.makeConstraints { (make) in
                make.width.height.equalTo(imgHeight)
            }
            
            
            let s = UIStackView(arrangedSubviews: [title,iv])
            s.tag = i
            s.axis = .vertical
            s.distribution = .equalSpacing
            s.alignment = .center
            s.spacing = imgTopGap
            s.addGestureRecognizer(UITapGestureRecognizer(target: self, action:#selector(onTap(g:))))
            themeVs.append(s)
        }
        self.themeVs = themeVs
        self.titles = titles
        self.iconImages = images
    }
    
    func refreshTheme(){
        let index = getCurrentSelectedIndex()
        
        for (i, iv) in iconImages.enumerated() {
          iv.isHighlighted = i == index
        }
    }
    
    @objc func onTap(g:UIGestureRecognizer){
        if getCurrentSelectedIndex() != g.view!.tag {
            saveCurrentSelectedIndex(index: g.view!.tag)
            refreshTheme()
            delegate?.themeSelectVDidSelected(index: g.view!.tag)
        }

    }
    
    func getCurrentSelectedIndex() -> Int{
        if UserDefaults.standard.isFollowSystemDarkMode {
            return 0
        }else{
            return UserDefaults.standard.isDarkMode ? 2: 1
        }
    }
    
    func saveCurrentSelectedIndex(index:Int){
        setDarkMode(mode: DarkMode(rawValue: index)!)
    }
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func getImageWithColor(color: UIColor, size: CGSize) -> UIImage {
        let rect = CGRect(x: 0, y: 0, width: size.width, height: size.height)
        UIGraphicsBeginImageContextWithOptions(size, false, 0)
        color.setFill()
        UIRectFill(rect)
        let image: UIImage = UIGraphicsGetImageFromCurrentImageContext()!
        UIGraphicsEndImageContext()
        return image
    }
    
    public override func traitCollectionDidChange(_ previousTraitCollection: UITraitCollection?) {
        super.traitCollectionDidChange(previousTraitCollection)
        titles[0].layer.borderColor = dynamicBorderColor1.cgColor
    }
    
    @objc func systemDarkModeChanged(noti:Notification){
        titles[0].layer.borderColor = dynamicBorderColor1.cgColor
    }
    
    @objc func languageChanged(noti:Notification){
        titles[0].text = "跟随系统".localized
        titles[1].text = "浅色".localized
        titles[2].text = "深色".localized
    }
}


