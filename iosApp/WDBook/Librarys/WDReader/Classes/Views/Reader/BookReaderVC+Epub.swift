//
//  BookReaderVC+Epub.swift
//  WDReader
//
//  Created by <PERSON> on 2020/10/2.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import Foundation
import CoreGraphics

extension BookReaderVC{
    /**
     Find a page by FRTocReference.
     */
    public func findPageByResource(_ reference: FRTocReference) -> Int {
        var count = 0
        for item in WDReaderCenter.shared.fbBook.spine.spineReferences {
            if let resource = reference.resource, item.resource == resource {
                return count
            }
            count += 1
        }
        return count
    }

    /**
     Find a page by href.
     */
    public func findPageByHref(_ href: String) -> Int {
        var count = 0
        for item in WDReaderCenter.shared.fbBook.spine.spineReferences {
            if item.resource.href == href {
                return count
            }
            count += 1
        }
        return count
    }

    /**
     Find and return the current chapter resource.
     */
    public func getCurrentChapter() -> FRResource? {
        var foundResource: FRResource?

        func search(_ items: [FRTocReference]) {
            for item in items {
                guard foundResource == nil else { break }

                if let reference = WDReaderCenter.shared.fbBook.spine.spineReferences[safe: (currentPageNumber - 1)], let resource = item.resource, resource == reference.resource {
                    foundResource = resource
                    break
                } else if let children = item.children, children.isEmpty == false {
                    search(children)
                }
            }
        }
        search(WDReaderCenter.shared.fbBook.flatTableOfContents)

        return foundResource
    }

    /**
     Return the current chapter progress based on current chapter and total of chapters.
     */
    public func getCurrentChapterProgress() -> CGFloat {
        let total = paginator.getPagesCount()
        let current = currentPageNumber
        
        if total == 0 {
            return 0
        }
        
        return CGFloat((100 * current) / total)
    }

    /**
     Find and return the current chapter name.
     */
    public func getCurrentChapterName() -> String? {
        var foundChapterName: String?
        
        func search(_ items: [FRTocReference]) {
            for item in items {
                guard foundChapterName == nil else { break }
                
                if let reference = WDReaderCenter.shared.fbBook.spine.spineReferences[safe: (self.currentPageNumber - 1)],
                    let resource = item.resource,
                    resource == reference.resource,
                    let title = item.title {
                    foundChapterName = title
                } else if let children = item.children, children.isEmpty == false {
                    search(children)
                }
            }
        }
        search(WDReaderCenter.shared.fbBook.flatTableOfContents)
        
        return foundChapterName
    }
    
    
    /// 返回按照html文件顺序的索引
    /// - Parameter reference: <#reference description#>
    /// - Returns: <#description#>
    public func findChapterNumber(_ reference: FRTocReference) -> Int {
        var count = 0
        for item in WDReaderCenter.shared.fbBook.spine.spineReferences {
            if let resource = reference.resource, item.resource == resource {
                return count
            }
            count += 1
        }
        return count
    }
    
    @available(*, deprecated, message: "")
    public func findPageNumber(_ reference:FRTocReference) -> Int {
        for item in WDReaderCenter.shared.fbBook.spine.spineReferences {
            if let resource = reference.resource, item.resource == resource {
                return paginator.resourcePageNumberDic[reference.resourceHrefAndFragmentId()] ?? 1
            }
        }
        return 1
    }

}

