//
//  WDReaderView.swift
//  WDReader
//
//  Created by <PERSON> on 2020/6/9.
//  Copyright © 2020 <PERSON>. All rights reserved.
//

import DeviceKit
import Foundation
import SwiftUI
import UIKit

public extension WDReaderView {
    static let readBookWillShowNotification = Notification.Name(rawValue: "WDReaderView.readBookWillShowNotification")
    static let readBookWillHideNotification = Notification.Name(rawValue: "WDReaderView.readBookWillHideNotification")
    static let readBookDidPageChangedNotification = Notification.Name(rawValue: "WDReaderView.readBookDidPageChangedNotification")
}

struct WDReaderViewModel {
    let resourceId: String
    let fileId: String
    let url: String?
    let code: String?
    let chapterPath: String?
    let contentOffset: Int?
}

public struct WDReaderView: UIViewControllerRepresentable {
    @Environment(\.presentationMode) var presentationMode
    @Binding var isPresent: Bool // 无效
    @Binding var isHideStatusBar: Bool
    private var resourceId: String
    private var fileId: String
    private var url: String?
    private var code: String?
    private var chapterPath: String?
    private var contentOffset: Int?
    private var needJumpBookmark: Bool = false
    private var customPop: (() -> Void)?

    public init(isPresent: Binding<Bool> = .constant(false), isHideStatusBar: Binding<Bool> = .constant(false), resourceId: String, fileId: String, url: String? = nil, code: String? = nil, chapterPath: String?, contentOffset: Int?, needJumpBookmark: Bool = false, customPop: (() -> Void)? = nil) {
        _isPresent = isPresent
        _isHideStatusBar = isHideStatusBar
        self.resourceId = resourceId
        self.fileId = fileId
        self.url = url
        self.code = code
        self.chapterPath = chapterPath
        self.contentOffset = contentOffset
        self.needJumpBookmark = needJumpBookmark
        self.customPop = customPop
        Log.d("阅读器 WDReaderView 加载 init：resourceId:\(resourceId), chapterPath:\(String(describing: chapterPath)), contentOffset:\(String(describing: contentOffset))")
    }

    public func makeUIViewController(context _: Context) -> UINavigationController {
        Log.d("阅读器 WDReaderView 加载 makeUIViewController：resourceId:\(resourceId), chapterPath:\(String(describing: chapterPath)), contentOffset:\(String(describing: contentOffset))")
        if code != nil && !code!.isEmpty {
            WDReaderConfig.hasCode = true
            WDReaderConfig.code = code!
        } else {
            WDReaderConfig.hasCode = false
            WDReaderConfig.code = ""
        }

        enterReadBrightness()
        // TODO: 测试数据，和默认indexPath的测试数据同步修改。
        WDReaderConfig.resourceId = resourceId
        WDReaderConfig.fileId = fileId
        WDReaderConfig.chapterPath = chapterPath // "EPUB/71028.xhtml"
        WDReaderConfig.contentOffset = contentOffset

        NotificationCenter.default.post(name: WDReaderView.readBookWillShowNotification, object: nil)
        let epubVC = BookReaderVC(url: url)
        epubVC.needJumpBookmark = needJumpBookmark
        epubVC.backHandler = { userInfo in

            Log.d("[加载]阅读器 退出：resourceId:\(resourceId), userInfo:\(String(describing: userInfo))")
            exitReadeBrigtness()
            NotificationCenter.default.post(name: WDReaderView.readBookWillHideNotification, object: nil, userInfo: userInfo)

            if customPop != nil {
                customPop?()
            } else {
                self.isPresent = false
                self.presentationMode.wrappedValue.dismiss()
            }
        }
        epubVC.toolBarHideHandler = { isHideToolBar in
            isHideStatusBar = isHideToolBar
        }
        let navigationController = UINavigationController(rootViewController: epubVC)
        navigationController.navigationBar.isHidden = true
        Log.d("阅读器 WDReaderView 加载 makeUIViewController2：resourceId:\(resourceId), chapterPath:\(String(describing: chapterPath)), contentOffset:\(String(describing: contentOffset))")
        return navigationController
    }

    public func updateUIViewController(_: UINavigationController, context _: Context) {
        // 打印出
//        Trying to pop to a missing destination at /Library/Caches/com.apple.xbs/Sources/Monoceros_Sim/Monoceros-42.24.100/Shared/NavigationBridge_PhoneTV.swift:205

        Log.d("阅读器 WDReaderView 加载 updateUIViewController：resourceId:\(resourceId),  chapterPath:\(String(describing: chapterPath)), contentOffset:\(String(describing: contentOffset))")

        // 无效
//        if isPresent == false{
//            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
//                self.presentationMode.wrappedValue.dismiss()
//            }
//        }
    }
}
