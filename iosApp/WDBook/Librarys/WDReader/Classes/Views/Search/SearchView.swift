//
//  SearchView.swift
//  WDBook
//
//  Created by <PERSON> on 2025/2/12.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//
import SwiftUI
import UIKit

struct SearchView: View {
    @StateObject private var searchState = SearchState.shared
    var dismissAction: () -> Void
    var jumpAction: (SearchResult) -> Void

    init(dismissAction: @escaping () -> Void, jumpAction: @escaping (SearchResult) -> Void) {
        self.dismissAction = dismissAction
        self.jumpAction = jumpAction
    }

    var body: some View {
        let hasSearchResult = searchState.showingResults
        VStack(alignment: .leading, spacing: 0) {
            BookSearchBar(dismissAction: {
                dismissAction()
            }, shouldAutoFocus: !hasSearchResult)

            // Group the content below the search bar
            Group {
                if searchState.isSearching && searchState.results.isEmpty {
                    SearchLoading()
                } else if searchState.showingResults {
                    if searchState.results.isEmpty {
                        BookSearchEmptyStateView()
                    } else {
                        BookSearchResultsTableView(
                            onItemSelected: { result in
                                jumpAction(result)
                            }
                        )
                    }
                } else if !searchState.searchHistory.isEmpty {
                    BookSearchHistory()
                }

                Spacer()
            }
            .dismissKeyboardOnGesture()
        }
        .ignoresSafeArea(.all, edges: .bottom)
        .onAppear {
            // Load search history
            searchState.loadSearchHistory()
        }
        .background(
            // Add a background view that handles taps
            Color.clear
                .contentShape(Rectangle())
                .onTapGesture {
                    UIApplication.dismissKeyboard()
                }
        )
    }
}

#Preview {
    SearchView(
        dismissAction: {},
        jumpAction: { _ in }
    )
}
