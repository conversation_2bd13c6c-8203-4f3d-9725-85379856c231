//
//  NoteGuide.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/6/12.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import UIKit
import SnapKit
import SwiftyUserDefaults

extension DefaultsKeys {
    static let IS_MARKED_NOTE_GUIDE = DefaultsKey<Bool>("WD_BOOK_NOTE_GUIDE",defaultValue: false)
}

class NoteGuide: UIView {
    
    var sourceRect:CGRect!
    var descImageView:UIImageView!
    var alphaRect:CGRect!
    var action:(()->())!
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        backgroundColor = UIColor.clear
        isOpaque = false
        
        descImageView = UIImageView(image: UIImage(named: "guide_note"))
        addSubview(descImageView)
        descImageView.snp.makeConstraints { make in
            make.left.equalTo(16.5)
            make.bottom.equalTo(0)
            make.width.equalTo(242)
            make.height.equalTo(134)
        }
        
        let gesture = UITapGestureRecognizer(target: self, action: #selector(tapAction(g:)))
        addGestureRecognizer(gesture)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        //中间镂空的矩形框
        let radis:CGFloat = (sourceRect.width + 13*2 ) / 2
        alphaRect = CGRect(x:sourceRect.minX - 13,y:sourceRect.maxY - radis,width:radis*2, height:radis*2)
        let ctx = UIGraphicsGetCurrentContext()
        
        //背景色
        //[[UIColor colorWithPatternImage:[UIImage imageNamed:@"1.jpg"]] set];
        UIColor.black.alpha(0.8).set()
        ctx?.addRect(rect)
        ctx?.fillPath()
        
        //设置清空模式
        /**
         kCGBlendModeClear,
         kCGBlendModeCopy,
         kCGBlendModeSourceIn,
         kCGBlendModeSourceOut,
         kCGBlendModeSourceAtop,
         kCGBlendModeDestinationOver,
         kCGBlendModeDestinationIn,
         kCGBlendModeDestinationOut,
         kCGBlendModeDestinationAtop,
         kCGBlendModeXOR,
         kCGBlendModePlusDarker,
         kCGBlendModePlusLighter
         */
        ctx?.setBlendMode(.clear)
        //画圆
        ctx?.addEllipse(in: alphaRect)
        //填充
        ctx?.fillPath()
        
        descImageView.snp.updateConstraints { make in
            make.left.equalTo(sourceRect.minX - 13)
            make.bottom.equalTo(-(rect.size.height - alphaRect.minY) - 10)
        }
    }
    
    @objc func tapAction(g:UIGestureRecognizer){
        let point = g.location(in: self)
        if alphaRect.contains(point){
            Log.d("点击了区域")
            action()
        }else{
            removeFromSuperview()
        }
    }
}

private var BOOKREADERVC_NOTEGUIDE_KEY: UInt8 = 0
extension BookReaderVC{
    var noteGuide:NoteGuide? {
        set{
            objc_setAssociatedObject(self, &BOOKREADERVC_NOTEGUIDE_KEY, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
        get{
            return objc_getAssociatedObject(self, &BOOKREADERVC_NOTEGUIDE_KEY) as? NoteGuide
        }
    }
    
    func showNoteGuide(sourceRect:CGRect,action:@escaping ()->()) {
        hideNoteGuide()
        
        noteGuide = NoteGuide()
        noteGuide?.sourceRect = sourceRect//CGRect(x: sourceRect.origin.x, y: sourceRect.origin.y - view.safeAreaInsets.bottom, width: sourceRect.size.width, height: sourceRect.size.height)
        noteGuide?.action = action
        view.addSubview(noteGuide!)
        noteGuide?.snp.makeConstraints({ make in
            make.edges.equalToSuperview()
        })
    }
    
    func hideNoteGuide() {
        noteGuide?.removeFromSuperview()
        noteGuide = nil
    }
}
