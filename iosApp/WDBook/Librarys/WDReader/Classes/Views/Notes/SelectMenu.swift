//
//  SelectMenu.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/5/18.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared
import SnapKit
import SwiftUI
import UIKit

class SelectMenu: UIView {
    static let NARROW_SIZE: CGSize = .init(width: 18, height: 10)
    static var menu: SelectMenu?

    var SIZE: CGSize {
        if mode == .modify {
            return CGSize(width: 300, height: 38)
        } else {
            return CGSize(width: 268, height: 38)
        }
    }

    var ALL_SIZE: CGSize {
        CGSize(width: SIZE.width, height: SIZE.height + SelectMenu.NARROW_SIZE.height)
    }

    let collectionView: UICollectionView
    var narrow: UIView!

    var mode: MenuMode!
    var menus: [Menu]!
    var selectedStyle: HighlightStyle!
    var selectedNote: Note?

    var orignRect: CGRect!
    var noteTips: NoteTips?

    init(frame: CGRect, mode m: MenuMode, selectedNote: Note? = nil) {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 0.0
        layout.minimumInteritemSpacing = 0.0
        layout.scrollDirection = .horizontal

        collectionView = UICollectionView(frame: CGRect(x: 0, y: 0, width: frame.width, height: frame.height), collectionViewLayout: layout)
        collectionView.backgroundColor = dynmicBGColor
        collectionView.register(SelectMenuCell.self, forCellWithReuseIdentifier: NSStringFromClass(SelectMenuCell.self))
        collectionView.layer.cornerRadius = 8

        super.init(frame: frame)

        self.selectedNote = selectedNote
        selectedStyle = selectedNote?.style ?? HighlightRangeManager.defaultStyle
        changeMode(m)

        addSubview(collectionView)
        collectionView.snp.makeConstraints { make in
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.height.equalTo(SIZE.height)
            make.top.equalTo(0)
        }

        collectionView.dataSource = self
        collectionView.delegate = self

        // Triangle will be added in the show method based on position
    }

    @discardableResult
    func addTriangle(isDown: Bool = true) -> UIView {
        // Remove any existing triangle
        if narrow != nil {
            narrow.removeFromSuperview()
            narrow = nil
        }

        narrow = UIView()

        if isDown {
            // Triangle pointing down (menu above selection)
            let path = CGMutablePath()

            path.move(to: CGPoint(x: 0, y: 0))
            path.addLine(to: CGPoint(x: SelectMenu.NARROW_SIZE.width / 2, y: SelectMenu.NARROW_SIZE.height))
            path.addLine(to: CGPoint(x: SelectMenu.NARROW_SIZE.width, y: 0))
            path.addLine(to: CGPoint(x: 0, y: 0))

            let shape = CAShapeLayer()
            shape.path = path
            shape.fillColor = dynmicBGColor.cgColor
            shape.strokeColor = dynmicBGColor.cgColor
            shape.lineWidth = 1.0

            narrow.layer.insertSublayer(shape, at: 0)
            addSubview(narrow)
            narrow.snp.makeConstraints { make in
                make.bottom.equalTo(0)
                make.centerX.equalTo(self)
                make.width.equalTo(SelectMenu.NARROW_SIZE.width)
                make.height.equalTo(SelectMenu.NARROW_SIZE.height)
            }
        } else {
            // Triangle pointing up (menu below selection)
            let path = CGMutablePath()

            // Create a clear upward-pointing triangle
            path.move(to: CGPoint(x: 0, y: SelectMenu.NARROW_SIZE.height))
            path.addLine(to: CGPoint(x: SelectMenu.NARROW_SIZE.width / 2, y: 0))
            path.addLine(to: CGPoint(x: SelectMenu.NARROW_SIZE.width, y: SelectMenu.NARROW_SIZE.height))
            path.addLine(to: CGPoint(x: 0, y: SelectMenu.NARROW_SIZE.height))

            let shape = CAShapeLayer()
            shape.path = path
            shape.fillColor = dynmicBGColor.cgColor
            shape.strokeColor = dynmicBGColor.cgColor
            shape.lineWidth = 1.0

            narrow.layer.insertSublayer(shape, at: 0)
            addSubview(narrow)
            narrow.snp.makeConstraints { make in
                make.top.equalTo(0)
                make.centerX.equalTo(self)
                make.width.equalTo(SelectMenu.NARROW_SIZE.width)
                make.height.equalTo(SelectMenu.NARROW_SIZE.height)
            }
        }

        layoutIfNeeded()

        return narrow
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    static func show(point orignRect: CGRect, on view: UIView, mode: MenuMode, selectedNote: Note? = nil) {
        let orignPoint = orignRect.origin

        hide()

        // Create the menu first so that selectedNote is available for positioning decision
        menu = SelectMenu(frame: CGRect.zero, mode: mode, selectedNote: selectedNote)

        // Check if we're near the top of the screen or if search header is visible
        let isNearTop = shouldPositionMenuBelow(selectionOrigin: orignPoint)

        view.addSubview(menu!)

        if isNearTop {
            // Position below the selection
            positionMenuBelowSelection(menu: menu!, rect: orignRect, view: view)
        } else {
            // Position above the selection
            positionMenuAboveSelection(menu: menu!, point: orignPoint, view: view)
        }

        menu?.orignRect = orignRect

        // Show note tips regardless of position
        menu!.showNoteTips()
    }

    /// Determines if the menu should be positioned below the selection
    private static func shouldPositionMenuBelow(selectionOrigin: CGPoint) -> Bool {
        // Use a threshold that accounts for the search header height and some additional space
        let searchHeaderHeight: CGFloat = 50
        let menuHeight: CGFloat = menu?.ALL_SIZE.height ?? 60 // Default if menu not yet created
        let safetyMargin: CGFloat = 20

        // If a note is selected, add additional space for note tips
        var noteTipsHeight: CGFloat = 0
        if let selectedNote = menu?.selectedNote, !selectedNote.noteText.isEmpty {
            // Estimate note tips height (base height + some margin)
            noteTipsHeight = 80 // Base height for note tips

            // Add extra height based on text length (rough estimate)
            let textLength = selectedNote.noteText.count
            if textLength > 100 {
                noteTipsHeight += 40 // Add more space for longer notes
            } else if textLength > 50 {
                noteTipsHeight += 20 // Add some space for medium notes
            }

            // Add spacing between menu and note tips
            noteTipsHeight += 8 // Offset between menu and note tips
        }

        let threshold = searchHeaderHeight + menuHeight + noteTipsHeight + safetyMargin

        Log.d("Menu positioning threshold: \(threshold), with note tips height: \(noteTipsHeight)")

        return selectionOrigin.y < threshold
    }

    /// Positions the menu below the selection with triangle pointing up
    private static func positionMenuBelowSelection(menu: SelectMenu, rect: CGRect, view: UIView) {
        let point = CGPoint(x: rect.origin.x, y: rect.maxY + 10)

        // For menu below selection, we need to position the menu differently
        menu.snp.makeConstraints { make in
            make.width.equalTo(menu.SIZE.width)
            make.height.equalTo(menu.SIZE.height + SelectMenu.NARROW_SIZE.height)
            make.top.equalTo(point.y)
            make.centerX.equalTo(view)
        }

        // Force layout to ensure view dimensions are set
        view.layoutIfNeeded()

        // Add the triangle at the top of the menu
        menu.addTriangle(isDown: false)

        // Adjust the collection view position to make room for the triangle at the top
        menu.collectionView.snp.updateConstraints { make in
            make.top.equalTo(SelectMenu.NARROW_SIZE.height)
        }

        Log.d("Menu positioned BELOW selection with triangle pointing UP")
    }

    /// Positions the menu above the selection with triangle pointing down
    private static func positionMenuAboveSelection(menu: SelectMenu, point: CGPoint, view: UIView) {
        let adjustedPoint = CGPoint(x: point.x, y: point.y - 10)

        menu.snp.makeConstraints { make in
            make.width.equalTo(menu.SIZE.width)
            make.height.equalTo(menu.SIZE.height + SelectMenu.NARROW_SIZE.height)
            make.top.equalTo(adjustedPoint.y - (menu.SIZE.height + SelectMenu.NARROW_SIZE.height))
            make.centerX.equalTo(view)
        }

        // Force layout to ensure view dimensions are set
        view.layoutIfNeeded()

        // Add the triangle at the bottom of the menu
        menu.addTriangle(isDown: true)

        Log.d("Menu positioned ABOVE selection with triangle pointing DOWN")
    }

    static func isShowing() -> Bool {
        menu != nil
    }

    static func hide() {
        menu?.removeFromSuperview()
        menu = nil
    }

    static func hideMenuAndClearSelectedState() {
        if let pageCell = findParentPageCell(startingFrom: menu) {
            pageCell.clearSelectedState()
        }
        hide()
    }

    /// Finds the parent BookReaderPageCell by traversing up the view hierarchy
    private static func findParentPageCell(startingFrom view: UIView?) -> BookReaderPageCell? {
        var currentView = view
        while currentView != nil {
            if let cell = currentView as? BookReaderPageCell {
                return cell
            }
            currentView = currentView?.superview
        }
        return nil
    }

    func changeMode(_ m: MenuMode) {
        mode = m
        switch mode {
        case .add:
            let width = SIZE.width / 5
            menus = [Menu(.copy, width: width), Menu(.highlight, width: width), Menu(.note, width: width), Menu(.share, width: width), Menu(.feedback, width: width)]
        case .modify:
            let deleteHighlightWidth: CGFloat = 64
            let otherWidth: CGFloat = 32
            let span = (SIZE.width - deleteHighlightWidth - otherWidth * 4) / (5 + 1)
            menus = [Menu(.selectHighlightColor, width: otherWidth + span * 1.5), Menu(.deleteHighlight, width: deleteHighlightWidth + span), Menu(.note, width: otherWidth + span), Menu(.copy, width: otherWidth + span), Menu(.share, width: otherWidth + span * 1.5)]
        case .selectStyle:
            let width = SIZE.width / 5
            menus = [Menu(.highlightStyle(HighlightStyle.Styles[0]), width: width), Menu(.highlightStyle(HighlightStyle.Styles[1]), width: width), Menu(.highlightStyle(HighlightStyle.Styles[2]), width: width), Menu(.highlightStyle(HighlightStyle.Styles[3]), width: width), Menu(.highlightStyle(HighlightStyle.Styles[4]), width: width)]
        default:
            let width = SIZE.width / 4
            menus = [Menu(.copy, width: width), Menu(.highlight, width: width), Menu(.note, width: width), Menu(.share, width: width)]
        }
        if let superV = superview {
            snp.updateConstraints { make in
                make.width.equalTo(SIZE.width)
            }
            superV.layoutIfNeeded()
        }
    }

    func showNoteTips() {
        hideNoteTips()
        if let text = selectedNote?.noteText, text.count > 0 {
            var tipsHeight: CGFloat = 60 + 20
            noteTips = NoteTips(text: text)

            addSubview(noteTips!)

            // Check if the triangle is at the top (menu is below selection) or bottom (menu is above selection)
            let isMenuBelowSelection = narrow?.frame.minY == 0

            // Position note tips based on menu position
            positionNoteTips(isMenuBelowSelection: isMenuBelowSelection, tipsHeight: tipsHeight)

            // Calculate actual height based on content
            let actualNumberOfLines = noteTips!.label.maxNumberOfLines(width: 268 - 20)
            if actualNumberOfLines < noteTips!.label.numberOfLines {
                noteTips!.label.numberOfLines = actualNumberOfLines
                tipsHeight = CGFloat(20 * actualNumberOfLines + 20)
                noteTips!.snp.updateConstraints { make in
                    make.height.equalTo(tipsHeight)
                }
            }

            // Only check for space constraints if menu is above selection (original behavior)
            if !isMenuBelowSelection {
                checkAndAdjustForSpaceConstraints(tipsHeight: tipsHeight)
            }
        }
    }

    /// Positions the note tips based on whether the menu is below or above the selection
    private func positionNoteTips(isMenuBelowSelection: Bool, tipsHeight: CGFloat) {
        if isMenuBelowSelection {
            // Position note tips below the menu
            noteTips!.snp.makeConstraints { make in
                make.width.equalTo(268)
                make.height.equalTo(tipsHeight)
                make.centerX.equalToSuperview()
                make.top.equalTo(self.snp.bottom).offset(8)
            }
        } else {
            // Position note tips above the menu
            noteTips!.snp.makeConstraints { make in
                make.width.equalTo(268)
                make.height.equalTo(tipsHeight)
                make.centerX.equalToSuperview()
                make.bottom.equalTo(self.snp.top).offset(-8)
            }
        }
    }

    /// Checks if there's enough space above the menu for note tips and adjusts if needed
    private func checkAndAdjustForSpaceConstraints(tipsHeight: CGFloat) {
        // Check if there's enough space above the menu
        guard let window = UIApplication.shared.windows.first(where: { $0.isKeyWindow }),
              let superview = superview else { return }

        let menuFrameInWindow = superview.convert(frame, to: window)

        // Calculate minimum required space (tips height + spacing)
        let requiredSpace = tipsHeight + 8
        let availableSpace = menuFrameInWindow.minY

        Log.d("Note tips space check - Required: \(requiredSpace), Available: \(availableSpace)")

        // If there's not enough space above
        if requiredSpace > availableSpace {
            Log.d("Not enough space for note tips above menu - repositioning")

            // Switch to showing below
            if let orignRect = orignRect {
                // Reposition the entire menu below the selection
                let point = CGPoint(x: orignRect.origin.x, y: orignRect.maxY + 10)
                snp.updateConstraints { make in
                    make.top.equalTo(point.y)
                }

                // Adjust collection view position for the triangle
                collectionView.snp.updateConstraints { make in
                    make.top.equalTo(SelectMenu.NARROW_SIZE.height)
                }

                // Remove any existing triangle and add a new one pointing up
                if let narrow = narrow {
                    narrow.removeFromSuperview()
                    self.narrow = nil
                }
                addTriangle(isDown: false)

                // Reposition note tips below the menu
                noteTips?.snp.remakeConstraints { make in
                    make.width.equalTo(268)
                    make.height.equalTo(tipsHeight)
                    make.centerX.equalToSuperview()
                    make.top.equalTo(self.snp.bottom).offset(8)
                }

                layoutIfNeeded()
            }
        }
    }

    func hideNoteTips() {
        noteTips?.removeFromSuperview()
        noteTips = nil
    }

    func selectedNoteOnModifyMode() {
        // 跳转笔记编辑 .note
        tapNoteCell()
    }

    private func tapNoteCell() {
        if let currentPageCell = superview as? BookReaderPageCell {
            if mode == .add {
                if let selectedNote = HighlightRangeManager.containsNote(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range) {
                    currentPageCell.ctCellView?.clearSelectedState(isHideMenu: false)
                    self.selectedNote = selectedNote
                    changeMode(.modify)
                    collectionView.reloadData()
                    showNoteTips()
                } else if HighlightRangeManager.isNeedMerge(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range) {
                    let newNote = HighlightRangeManager.generateMergeNote(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range)
                    if newNote.noteText.count > NoteEditerVC.MAX_TEXT_LENGTH {
                        currentPageCell.ctCellView?.clearSelectedState()
                        let alert = UIAlertController(title: "操作错误".localized, message: "笔记合并超出字数限制".localized, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "取消".localized, style: .cancel, handler: nil))
                        alert.addAction(UIAlertAction(title: "继续合并".localized, style: .default, handler: { _ in
                            continueMerge()
                        }))
                        currentPageCell.delegate?.superViewController().present(alert, animated: true, completion: nil)
                        return
                    }

                    func continueMerge() {
                        SelectedRangeManager.clear()

                        let noteEditorVC = NoteEditerVC()
                        noteEditorVC.isTempNote = true
                        noteEditorVC.isForMerge = true
                        // noteEditorVC.delegate = self
                        noteEditorVC.note = newNote
                        currentPageCell.delegate?.superViewController().present(noteEditorVC, animated: true, completion: nil)

                        currentPageCell.clearSelectedState()
                        currentPageCell.updateHighlightArea()

                        SelectMenu.hide()
                    }
                    continueMerge()

                } else {
                    if let chapterPath = SelectedRangeManager.chapterPath,
                       let title = currentPageCell.page?.chapter?.displayedAttributedString.attributedSubstring(from: SelectedRangeManager.range).resetImageToBlank().string
                    {
                        // 添加笔记
                        let newNote = Note(chapterPath: chapterPath, range: SelectedRangeManager.range, summary: title)
                        SelectedRangeManager.clear()

                        let noteEditorVC = NoteEditerVC()
                        noteEditorVC.isTempNote = true
                        // noteEditorVC.delegate = self
                        noteEditorVC.note = newNote
                        currentPageCell.delegate?.superViewController().present(noteEditorVC, animated: true, completion: nil)

                        currentPageCell.clearSelectedState()
                        currentPageCell.updateHighlightArea()
                        SelectMenu.hide()
                    }
                }
            } else {
                if HighlightRangeManager.isNeedMerge(href: currentPageCell.page?.href ?? "", range: selectedNote!.showRange) {
                    let newNote = HighlightRangeManager.generateMergeNote(href: currentPageCell.page?.href ?? "", range: selectedNote!.showRange)
                    if newNote.noteText.count > NoteEditerVC.MAX_TEXT_LENGTH {
                        currentPageCell.clearSelectedState()
                        let alert = UIAlertController(title: "操作错误".localized, message: "笔记合并超出字数限制".localized, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "取消".localized, style: .cancel, handler: nil))
                        alert.addAction(UIAlertAction(title: "继续合并".localized, style: .default, handler: { _ in
                            continueMerge()
                        }))
                        currentPageCell.delegate?.superViewController().present(alert, animated: true, completion: nil)
                        return
                    }

                    func continueMerge() {
                        currentPageCell.clearSelectedState()

                        let noteEditorVC = NoteEditerVC()
                        noteEditorVC.isTempNote = true
                        noteEditorVC.isForMerge = true
                        // noteEditorVC.delegate = self
                        noteEditorVC.note = newNote
                        currentPageCell.delegate?.superViewController().present(noteEditorVC, animated: true, completion: nil)
                        SelectMenu.hide()
                    }
                    continueMerge()

                } else {
                    let noteEditorVC = NoteEditerVC()
                    // noteEditorVC.delegate = self
                    noteEditorVC.note = selectedNote
                    currentPageCell.delegate?.superViewController().present(noteEditorVC, animated: true, completion: nil)
                    currentPageCell.clearSelectedState()
                }
            }
        }
    }

    private func getProcessedTextForHebrew() -> String? {
        guard let chapterPath = SelectedRangeManager.chapterPath,
              let chapter = Paginator.current.getChapter(href: chapterPath)
        else {
            return nil
        }

        var range = SelectedRangeManager.range

        // 确保range有效
        if range.location == NSNotFound || range.length == 0 {
            return nil
        }

        // 对希伯来文，如果选择范围过小，进行扩展以获取完整的单词
        if range.length <= 1 {
            // 获取前后内容以进行扩展选择
            let fullLength = chapter.displayedAttributedString.length

            // 更安全的扩展范围：前后都最多扩展20个字符
            let contextBeforeStart = max(0, range.location - 20)
            let contextBeforeLength = range.location - contextBeforeStart

            let contextAfterStart = min(fullLength, range.location + range.length)
            let contextAfterLength = min(20, fullLength - contextAfterStart)

            // 获取前后文本
            let beforeRange = NSRange(location: contextBeforeStart, length: contextBeforeLength)
            let afterRange = NSRange(location: contextAfterStart, length: contextAfterLength)

            let beforeString = chapter.displayedAttributedString.safeAttributedSubstring(from: beforeRange).string
            let afterString = chapter.displayedAttributedString.safeAttributedSubstring(from: afterRange).string

            // 判断选中的字符是否是希伯来文
            let selectedCharRange = NSRange(location: range.location, length: range.length)
            let selectedChar = chapter.displayedAttributedString.safeAttributedSubstring(from: selectedCharRange).string

            // 初始化扩展范围变量
            var extendedStart = range.location
            var extendedEnd = range.location + range.length

            // 特殊处理：如果选中的是单个RLE标记，直接找到对应的PDF标记
            if range.length == 1 {
                if selectedChar == "\u{202B}" || selectedChar == "\u{202A}" {
                    // 从选中位置开始向后查找PDF标记
                    let searchStart = range.location
                    let searchLength = min(100, chapter.displayedAttributedString.length - searchStart)
                    let searchRange = NSRange(location: searchStart, length: searchLength)

                    let stringRange = Range(searchRange, in: chapter.displayedAttributedString.string)!

                    if let pdfRange = chapter.displayedAttributedString.string.range(of: "\u{202C}", options: [.literal], range: stringRange, locale: nil) {
                        // 计算PDF标记的位置
                        let pdfLocation = chapter.displayedAttributedString.string[..<pdfRange.lowerBound].utf16.count

                        // 确保找到的PDF标记在搜索范围内
                        guard pdfLocation >= searchStart, pdfLocation < searchStart + searchLength else {
                            return nil
                        }

                        // 设置范围从RLE标记开始到PDF标记结束
                        extendedEnd = pdfLocation + 1

                        // 获取RLE和PDF标记之间的内容
                        let contentRange = NSRange(location: range.location + 1, length: pdfLocation - (range.location + 1))

                        if contentRange.length > 0 {
                            let contentString = chapter.displayedAttributedString.safeAttributedSubstring(from: contentRange).string
                            if contentString.isHebrewText() {
                                // 如果内容是希伯来文，使用这个范围
                                range = contentRange
                            } else {
                                // 如果不是希伯来文，使用整个范围（包括标记）
                                range = NSRange(location: range.location, length: extendedEnd - range.location)
                            }
                        } else {
                            // 如果没有内容，使用整个范围（包括标记）
                            range = NSRange(location: range.location, length: extendedEnd - range.location)
                        }
                    }
                } else if selectedChar == "\u{202C}" {
                    // 检查前面的文本是否是希伯来文
                    let prevCharRange = NSRange(location: range.location - 1, length: 1)
                    if prevCharRange.location >= 0 {
                        let prevChar = chapter.displayedAttributedString.safeAttributedSubstring(from: prevCharRange).string
                        if prevChar.isHebrewText() {
                            // 如果前面的字符是希伯来文，向前查找RLE标记
                            let searchStart = max(0, range.location - 100)
                            let searchLength = range.location - searchStart
                            let searchRange = NSRange(location: searchStart, length: searchLength)

                            if let rleRange = chapter.displayedAttributedString.string.range(of: "\u{202B}", options: .backwards, range: Range(searchRange, in: chapter.displayedAttributedString.string)!, locale: nil) {
                                let rleLocation = chapter.displayedAttributedString.string.distance(from: chapter.displayedAttributedString.string.startIndex, to: rleRange.lowerBound)
                                // 设置范围从RLE标记开始到PDF标记结束
                                extendedStart = rleLocation
                                extendedEnd = range.location + 1

                                // 获取RLE和PDF标记之间的内容
                                let contentRange = NSRange(location: extendedStart + 1, length: extendedEnd - extendedStart - 2)
                                if contentRange.length > 0 {
                                    let contentString = chapter.displayedAttributedString.safeAttributedSubstring(from: contentRange).string
                                    if contentString.isHebrewText() {
                                        // 如果内容是希伯来文，使用这个范围
                                        range = contentRange
                                    } else {
                                        // 如果不是希伯来文，使用整个范围（包括标记）
                                        range = NSRange(location: extendedStart, length: extendedEnd - extendedStart)
                                    }
                                } else {
                                    // 如果没有内容，使用整个范围（包括标记）
                                    range = NSRange(location: extendedStart, length: extendedEnd - extendedStart)
                                }
                            }
                        }
                    }
                }
            } else {
                // 检查选中的字符或者前后文本是否包含希伯来文
                let isSelectedHebrewChar = selectedChar.isHebrewText()
                let isHebrewContext = beforeString.isHebrewText() || afterString.isHebrewText()

                if isSelectedHebrewChar || isHebrewContext {
                    // 原有的扩展方法 - 向前查找希伯来文边界
                    if beforeString.count > 0 {
                        // 判断是否在控制字符内部
                        if let startBiDiMatch = beforeString.range(of: "\u{202B}", options: .backwards) {
                            let startDistance = beforeString.distance(from: beforeString.startIndex, to: startBiDiMatch.lowerBound)
                            extendedStart = contextBeforeStart + startDistance
                        } else {
                            // 原逻辑：从右向左查找第一个非希伯来文字符
                            var i = beforeString.count - 1
                            var foundNonHebrew = false

                            while i >= 0 {
                                let index = beforeString.index(beforeString.startIndex, offsetBy: i)
                                let char = String(beforeString[index])
                                // 只跳过RLE和PDF控制字符
                                if char == "\u{202B}" || char == "\u{202C}" {
                                    i -= 1
                                    continue
                                }

                                if !char.isHebrewText() {
                                    foundNonHebrew = true
                                    extendedStart = contextBeforeStart + i + 1 // 非希伯来文字符之后一位
                                    break
                                }
                                i -= 1
                            }

                            // 如果没有找到非希伯来文字符，使用整个前文本
                            if !foundNonHebrew {
                                extendedStart = contextBeforeStart
                            }
                        }
                    }

                    // 向后查找希伯来文边界
                    if afterString.count > 0 {
                        // 判断是否在控制字符内部
                        if let endBiDiMatch = afterString.range(of: "\u{202C}") {
                            let endDistance = afterString.distance(from: afterString.startIndex, to: endBiDiMatch.upperBound)
                            extendedEnd = contextAfterStart + endDistance
                        } else {
                            // 原逻辑：从左向右查找第一个非希伯来文字符
                            var i = 0
                            var foundNonHebrew = false

                            while i < afterString.count {
                                let index = afterString.index(afterString.startIndex, offsetBy: i)
                                let char = String(afterString[index])
                                // 只跳过RLE和PDF控制字符
                                if char == "\u{202B}" || char == "\u{202C}" {
                                    i += 1
                                    continue
                                }

                                if !char.isHebrewText() {
                                    foundNonHebrew = true
                                    extendedEnd = contextAfterStart + i // 非希伯来文字符之前
                                    break
                                }
                                i += 1
                            }

                            // 如果没有找到非希伯来文字符，使用整个后文本
                            if !foundNonHebrew {
                                extendedEnd = contextAfterStart + afterString.count
                            }
                        }
                    }

                    // 确保扩展后的范围是有效的
                    if extendedStart < extendedEnd {
                        range = NSRange(location: extendedStart, length: extendedEnd - extendedStart)
                    }
                }
            }
        }

        // Ensure range is valid for the attributed string to prevent crashes
        let safeRange = range.constrainedTo(length: chapter.displayedAttributedString.length)

        // 使用安全方法获取子字符串
        let attributedSubstring = chapter.displayedAttributedString.safeAttributedSubstring(from: safeRange)

        // 检查获取的子字符串是否有效
        if attributedSubstring.length > 0 {
            // 先重置图片为空白
            let processedSubstring = attributedSubstring.resetImageToBlank()
            var text = processedSubstring.string

            // 特殊处理希伯来文等RTL文本
            if text.isHebrewText() {
                // 移除所有BiDi控制字符
                text = text
                    .replacingOccurrences(of: "\u{202A}", with: "") // LRM mark
                    .replacingOccurrences(of: "\u{202B}", with: "") // RLM mark
                    .replacingOccurrences(of: "\u{202C}", with: "") // Pop directional formatting
                    .replacingOccurrences(of: "\u{202D}", with: "") // LRO mark
                    .replacingOccurrences(of: "\u{202E}", with: "") // RLO mark
                    .replacingOccurrences(of: "\u{200E}", with: "") // LRM mark
                    .replacingOccurrences(of: "\u{200F}", with: "") // RLM mark
            }

            return text
        }

        return nil
    }
}

extension SelectMenu: UICollectionViewDataSource {
    func collectionView(_: UICollectionView, numberOfItemsInSection _: Int) -> Int {
        return menus.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: NSStringFromClass(SelectMenuCell.self), for: indexPath) as! SelectMenuCell
        cell.delegate = self
        cell.indexPath = indexPath
        let menu = menus[indexPath.row]
        cell.updateData(menu: menu, selectedStyle: selectedStyle)
        return cell
    }
}

extension SelectMenu: UICollectionViewDelegateFlowLayout {
    func collectionView(_: UICollectionView, layout _: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let width = menus[indexPath.item].width
        return CGSize(width: width, height: SIZE.height)
    }
}

extension SelectMenu: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        Log.d("选择索引:\(indexPath.item), menu title: \(String(describing: menus[indexPath.item].title))")
        let menu = menus[indexPath.item]
        switch menu.type {
        case .copy:
            var summary = ""
            if let text = selectedNote?.summary {
                summary = text
            } else if let processedText = getProcessedTextForHebrew() {
                summary = processedText
            }

            // Add suffix only if we have actual text content
            if !summary.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                UIPasteboard.general.string = summary + (summary.count > 10 ? WDBookUserSDK.shared.getCopySuffix(resourceId: WDReaderConfig.resourceId) : "")

                // Show a toast to confirm copy action
                (superview as? BookReaderPageCell)?.delegate?.superViewController().view.makeToast("复制成功".localized)
            } else {
                // Show a toast for empty text
                (superview as? BookReaderPageCell)?.delegate?.superViewController().view.makeToast("无法复制，文本为空".localized)
            }

            (superview as? BookReaderPageCell)?.clearSelectedState()

        case .highlight:
            if let currentPageCell = superview as? BookReaderPageCell {
                // 1、区域内只有一条
                // 2、区域内全部是待合并划线
                // 3、区域内有待合并笔记
                // 4、新划线
                if let selectedNote = HighlightRangeManager.containsNote(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range) {
                    currentPageCell.ctCellView?.clearSelectedState(isHideMenu: false)
                    self.selectedNote = selectedNote
                    changeMode(.modify)
                    collectionView.reloadData()
                    showNoteTips()
                } else if HighlightRangeManager.isNeedMergeForHighlight(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range) {
                    let newNote = HighlightRangeManager.generateMergeHighlight(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range)
                    HighlightRangeManager.add(note: newNote, chapterPath: currentPageCell.page?.href ?? "", isForMerge: true)
                    SelectedRangeManager.clear()

                    currentPageCell.clearSelectedState(isHideMenu: false)
                    currentPageCell.updateHighlightArea()

                    selectedNote = newNote
                    changeMode(.modify)
                    collectionView.reloadData()

                    NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: newNote)
                } else if HighlightRangeManager.isNeedMerge(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range) {
                    let newNote = HighlightRangeManager.generateMergeNote(href: currentPageCell.page?.href ?? "", range: SelectedRangeManager.range)
                    if newNote.noteText.count > NoteEditerVC.MAX_TEXT_LENGTH {
                        currentPageCell.ctCellView?.clearSelectedState()
                        let alert = UIAlertController(title: "操作错误".localized, message: "笔记合并超出字数限制".localized, preferredStyle: .alert)
                        alert.addAction(UIAlertAction(title: "取消".localized, style: .cancel, handler: nil))
                        alert.addAction(UIAlertAction(title: "继续合并".localized, style: .default, handler: { _ in
                            continueMerge()
                        }))
                        currentPageCell.delegate?.superViewController().present(alert, animated: true, completion: nil)
                        return
                    }

                    func continueMerge() {
                        SelectedRangeManager.clear()

                        let noteEditorVC = NoteEditerVC()
                        noteEditorVC.isTempNote = true
                        noteEditorVC.isForMerge = true
                        // noteEditorVC.delegate = self
                        noteEditorVC.note = newNote
                        currentPageCell.delegate?.superViewController().present(noteEditorVC, animated: true, completion: nil)

                        currentPageCell.ctCellView?.clearSelectedState()
                        currentPageCell.updateHighlightArea()

                        SelectMenu.hide()
                    }
                    continueMerge()
                } else {
                    // 隐藏选中，
                    // 显示高亮，
                    if let chapterPath = SelectedRangeManager.chapterPath,
                       let title = currentPageCell.page?.chapter?.displayedAttributedString.attributedSubstring(from: SelectedRangeManager.range).resetImageToBlank().string
                    {
                        let newNote = Note(chapterPath: chapterPath, range: SelectedRangeManager.range, summary: title)
                        HighlightRangeManager.add(note: newNote, chapterPath: chapterPath)
                        SelectedRangeManager.clear()

                        currentPageCell.clearSelectedState(isHideMenu: false)
                        currentPageCell.updateHighlightArea()

                        selectedNote = newNote
                        changeMode(.modify)
                        collectionView.reloadData()

                        NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: newNote)
                    }
                }
            }

        case .deleteHighlight:
            if let note = selectedNote, note.noteText.isEmpty {
                HighlightRangeManager.remove(note: note, chapterPath: note.chapterPath)
                NotificationCenter.default.post(name: NoteBookmarkNotifications.noteDeletedNotification, object: note)
                if let superV = superview as? BookReaderPageCell {
                    superV.updateHighlightArea()
                }
                SelectMenu.hide()
            } else {
                let alert = UIAlertController(title: "删除划线".localized, message: "\n" + "相关联的笔记也将被删除".localized + "\n", preferredStyle: .alert)
                alert.addAction(UIAlertAction(title: "取消".localized, style: .cancel, handler: nil))
                alert.addAction(UIAlertAction(title: "删除".localized, style: .destructive, handler: { [weak self] _ in
                    guard let self = self else { return }
                    if let note = self.selectedNote {
                        HighlightRangeManager.remove(note: note, chapterPath: note.chapterPath)
                        NotificationCenter.default.post(name: NoteBookmarkNotifications.noteDeletedNotification, object: note)
                    }
                    if let superV = self.superview as? BookReaderPageCell {
                        superV.updateHighlightArea()
                    }
                    SelectMenu.hide()
                }))

                if let superV = superview as? BookReaderPageCell {
                    superV.delegate?.superViewController().present(alert, animated: true, completion: nil)
                }
            }

        case .note:
            tapNoteCell()

        case .selectHighlightColor:
            // 进入选择颜色模式
            changeMode(.selectStyle)
            collectionView.reloadData()

        case let .highlightStyle(style):
            // 选中一个样式。
            selectedStyle = style
            HighlightStyle.defaultStyle = style
            collectionView.reloadData()

            if let superV = superview as? BookReaderPageCell,
               let href = superV.page?.href
            {
                if let note = HighlightRangeManager.chapterNotesDic?[href]?.filter({ $0 == selectedNote }).first {
                    note.style = selectedStyle
                    superV.updateHighlightArea()
                    HighlightRangeManager.update(note: note)
                    NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: note)
                }
            }

        case .share:
            var shareText = ""
            if let text = selectedNote?.summary {
                shareText = text
            } else if let processedText = getProcessedTextForHebrew() {
                shareText = processedText
            }

            // Add suffix only if we have actual text content
            if !shareText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
                shareText += (shareText.count > 10 ? WDBookUserSDK.shared.getCopySuffix(resourceId: WDReaderConfig.resourceId) : "")

                if let readerVC = (superview as? BookReaderPageCell)?.delegate?.superViewController() {
                    let activityViewController = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)
                    if UIDevice.current.userInterfaceIdiom == .pad {
                        activityViewController.popoverPresentationController?.sourceView = self
                        activityViewController.popoverPresentationController?.sourceRect = CGRect(x: 0, y: 0, width: frame.width, height: frame.height)
                    }
                    readerVC.present(activityViewController, animated: true, completion: nil)
                }
            } else {
                // Show a toast for empty text
                (superview as? BookReaderPageCell)?.delegate?.superViewController().view.makeToast("无法分享，文本为空".localized)
            }

        case .feedback:
            Log.d("纠错")
            if let currentPageCell = superview as? BookReaderPageCell,
               let readerVC = currentPageCell.delegate?.superViewController(),
               let chapterPath = SelectedRangeManager.chapterPath,
               let summary = currentPageCell.page?.chapter?.displayedAttributedString.attributedSubstring(from: SelectedRangeManager.range).resetImageToBlank().string
            {
                let range = SelectedRangeManager.range

                let dic: [String: Any] = ["resourceId": WDReaderConfig.resourceId,
                                          "fileId": WDReaderConfig.fileId,
                                          "pagePath": chapterPath,
                                          "wordStartOffset": range.location,
                                          "wordEndOffset": range.location + range.length,
                                          "summary": summary,
                                          "bookName": WDBookUserSDK.shared.getShelfBookItemEntityByResourceId(resourceId: WDReaderConfig.resourceId)?.resourceName ?? "",
                                          "chapterName": Paginator.current.getSubLevelChapterName(indexPath: Paginator.current.currentIndexPath) ?? "",
//                                ,"tagIds":""
//                                readType:["",""]
                ]

                ReaderFeedbackV.show(jsonDic: dic, on: readerVC.view, onCommit: {
                    currentPageCell.clearSelectedState()
                }, onCancel: {
                    currentPageCell.clearSelectedState()
                })
            }
        }
    }
}

extension SelectMenu: SelectMenuCellDelegate {
    func selectMenuCell(_: SelectMenuCell, didSelectItemAt indexPath: IndexPath) {
        collectionView(collectionView, didSelectItemAt: indexPath)
    }
}

// extension SelectMenu:NoteEditerDelegate{
//    func noteEditerDidChangedNote() {
//        HighlightRangeManager.update(note: self.selectedNote!)
//
//
//        //关闭编辑器不会自动显示菜单。
////        let orignRect = self.orignRect!
////        let superV = self.superview!
////        let mode = self.mode!
//
////        SelectMenu.hide() 开启编辑器就隐藏。
//
////        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1, execute: {
////            SelectMenu.show(point: orignRect, on: superV, mode: mode)
////        })
//    }
// }

// 其他工具类
enum MenuType {
    case copy
    case highlight
    case deleteHighlight
    case note
    case selectHighlightColor
    case highlightStyle(HighlightStyle)
    case share
    case feedback
}

struct Menu {
    let type: MenuType
    private(set) var width: CGFloat
    private(set) var style: HighlightStyle?
    private(set) var title: String?

    init(_ type: MenuType, width: CGFloat, color _: String? = nil) {
        //    init(_ type:MenuType) {
        self.type = type
        self.width = width
        //        self.icon = icon

        switch type {
        case .copy:
            title = "复制".localized
        case .highlight:
            title = "划线".localized
        case .deleteHighlight:
            title = "删除划线".localized
        case .note:
            title = "笔记".localized
        case .selectHighlightColor: // 选择笔记颜色
            title = ""
        case let .highlightStyle(s):
            title = ""
            style = s
        case .share:
            title = "分享".localized
        case .feedback:
            title = "纠错".localized
        }
    }
}

enum MenuMode {
    case add
    case modify
    case selectStyle
}
