//
//  SelectMenuCell.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2021/5/25.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit

protocol SelectMenuCellDelegate: AnyObject {
    func selectMenuCell(_ cell: SelectMenuCell, didSelectItemAt indexPath: IndexPath)
}

class SelectMenuCell: UICollectionViewCell {
    weak var delegate: SelectMenuCellDelegate?
    var indexPath: IndexPath!

    var titleLabel: UILabel = {
        let l = UILabel()
        l.textAlignment = .center
        l.font = UIFont.regular(size: 16)
        l.textColor = UIColor(hex: 0xF0F0F2)
        return l
    }()

    var imageStyleV: UIView?

    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = dynmicBGColor
        contentView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

//        contentView.layer.borderColor = UIColor.red.cgColor
//        contentView.layer.borderWidth = 1
        addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapMenuItem)))
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    func updateData(menu: Menu, selectedStyle: HighlightStyle) {
        titleLabel.text = menu.title

        if imageStyleV != nil {
            imageStyleV?.removeFromSuperview()
            imageStyleV = nil
        }

        switch menu.type {
        case let .highlightStyle(s):
            if s.isUnderline {
                imageStyleV = UIView(frame: CGRect.zero)
                contentView.addSubview(imageStyleV!)
                imageStyleV!.snp.makeConstraints { make in
                    make.center.equalTo(contentView)
                    make.width.height.equalTo(CricleView.WIDTH)
                }

                let imageView = UIImageView(image: UIImage(named: "ic_colour_line"))
                imageStyleV!.addSubview(imageView)
                imageView.snp.makeConstraints { make in
                    make.center.equalTo(imageStyleV!)
                    make.width.height.equalTo(CricleView.WIDTH - 2)
                }
                let isSelected = menu.style == selectedStyle
                if isSelected {
                    imageStyleV!.layer.borderColor = UIColor.systemFill.cgColor
                    imageStyleV!.layer.borderWidth = 2
                    imageStyleV!.layer.cornerRadius = CricleView.WIDTH / 2
                    imageStyleV!.layer.masksToBounds = true
                }
            } else {
                let isSelected = menu.style == selectedStyle
                imageStyleV = CricleView(frame: CGRect(x: 0, y: 0, width: CricleView.WIDTH, height: CricleView.WIDTH), color: s.showColor, borderColor: isSelected ? UIColor.white : dynmicBGColor)
                contentView.addSubview(imageStyleV!)
                imageStyleV!.snp.makeConstraints { make in
                    make.center.equalTo(contentView)
                    make.width.height.equalTo(CricleView.WIDTH)
                }
            }
        case .selectHighlightColor:
            imageStyleV = UIView(frame: CGRect.zero)
            contentView.addSubview(imageStyleV!)
            imageStyleV!.snp.makeConstraints { make in
                make.center.equalTo(contentView)
                make.width.equalTo(38)
                make.height.equalTo(36)
            }
            let imageView = UIImageView(image: UIImage(named: "ic_colour"))
            imageStyleV!.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }

        default:
            break
        }
    }

    @objc func tapMenuItem() {
        delegate?.selectMenuCell(self, didSelectItemAt: indexPath)
    }
}

class CricleView: UIView {
    static let WIDTH: CGFloat = 26
    var color: UIColor {
        didSet {
            backgroundColor = color
        }
    }

    init(frame: CGRect, color: UIColor, borderColor: UIColor = dynmicBGColor) {
        self.color = color
        super.init(frame: frame)
        backgroundColor = color
        layer.borderColor = borderColor.cgColor
        layer.borderWidth = 2
        layer.cornerRadius = frame.width / 2
        layer.masksToBounds = true
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}
