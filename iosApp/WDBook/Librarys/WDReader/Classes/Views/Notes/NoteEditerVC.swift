//
//  NoteEditerVC.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2021/5/26.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI
import UIKit

struct NoteEditerVCUIKit: UIViewControllerRepresentable {
    typealias UIViewControllerType = NoteEditerVC
    var note: Note
    var needPostNoti: Bool

    init(note: Note, needPostNoti: Bool) {
        self.note = note
        self.needPostNoti = needPostNoti
    }

    func makeUIViewController(context _: Context) -> NoteEditerVC {
        let editerVC = NoteEditerVC()
        editerVC.note = note
        editerVC.needPostNoti = needPostNoti
        editerVC.isFromSwiftUI = true
        return editerVC
    }

    func updateUIViewController(_: NoteEditerVC, context _: Context) {}
}

protocol NoteEditerDelegate: AnyObject {
    func noteEditerDidChangedNote()
}

enum NoteBookmarkNotifications {
    static let noteDeletedNotification = Notification.Name("NoteBookmarkNotifications_noteDeletedNotification")
    static let noteResumeNotification = Notification.Name("NoteBookmarkNotifications_noteResumeNotification")
    static let bookmarkDeletedNotification = Notification.Name("NoteBookmarkNotifications_bookmarkDeletedNotification")
}

extension NoteEditerVC {
    static let noteChangedNotification = Notification.Name("NoteEditerVC_noteChangedNotification")
}

class NoteEditerVC: UIViewController {
    weak var delegate: NoteEditerDelegate?

    private var navigationBar: UIView!
    private var noteView: UIView!
    private var textView: UITextView!
    private var KeyboardRect: CGRect = .init(x: 0, y: 0, width: UIScreen.main.bounds.width, height: 200)
    private var numberOfWordsView: UIView!

    var note: Note!
    var needPostNoti: Bool = true
    var isTempNote: Bool = false // 是否首次添加笔记。 isTempNote = true情况下，如果编辑过，即字符>0 ，那么就添加，并且isTempNote改为false
    var isForMerge: Bool = false
    var isFromSwiftUI: Bool = false
    var isDoneSaved = false
    var autoSaveTimer: Timer?
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = dynamicBackgroundColor1

        if note.isConflict {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
                guard let self = self else { return }
                HighlightRangeManager.update(note: self.note)
                NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: self.note)
            }
        }

        addNavigationBar()
        addTitleView()
        addNumberOfWordsView()
        addTextView()
        if !isTempNote {
            startAutoSaveTimer()
        }
        view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(tapBackground)))
    }

    deinit {
        autoSaveTimer?.invalidate()
        autoSaveTimer = nil
        NotificationCenter.default.removeObserver(self)
    }

    func addNavigationBar() {
        navigationBar = UIView()
        navigationBar.backgroundColor = dynamicBackgroundColor1

        let btn = UIButton(type: .custom)
        btn.setTitle("完成".localized, for: .normal)
        btn.setTitleColor(dynamicTitleColor, for: .normal)
        btn.titleLabel?.font = UIFont.regular(size: 16)
        btn.addTarget(self, action: #selector(tapDoneWhenClose(item:)), for: .touchUpInside)
        navigationBar.addSubview(btn)
        btn.snp.makeConstraints { make in
            make.top.bottom.trailing.equalToSuperview()
            make.width.equalTo(32 + 16 * 2)
        }

        let line = UIView()
        line.backgroundColor = dynamicNavBarBottomLineColor
        navigationBar.addSubview(line)
        line.snp.makeConstraints { make in
            make.bottom.leading.trailing.equalToSuperview()
            make.height.equalTo(0.5)
        }

        view.addSubview(navigationBar)
        navigationBar.snp.makeConstraints { make in
            make.top.leading.trailing.equalTo(0)
            make.height.equalTo(60)
        }
    }

    func addTitleView() {
        noteView = UIView()
        noteView.backgroundColor = dynamicBackgroundColor1
        view.addSubview(noteView)
        noteView.snp.makeConstraints { make in
            make.top.equalTo(navigationBar.snp.bottom).offset(5)
            make.leading.equalTo(16)
            make.trailing.equalTo(-20)
            make.height.equalTo(66)
        }

        let noteColorView = UIView()
        noteColorView.backgroundColor = note.style.showColor
        noteView.addSubview(noteColorView)
        noteColorView.snp.makeConstraints { make in
            make.top.bottom.leading.equalToSuperview()
            make.width.equalTo(4)
        }

        let titleLabel = UILabel()
        //        noteTextLabel.textAlignment = .left
        titleLabel.backgroundColor = dynamicBackgroundColor1
        titleLabel.numberOfLines = 3
        let para = NSMutableParagraphStyle()
        para.paragraphSpacing = 0
        para.lineSpacing = 5
        para.lineBreakMode = .byTruncatingTail
//        let sourceStr = "\u{200E}" + note.summary
        let attributedString = NSMutableAttributedString(string: note.summary, attributes: [NSAttributedString.Key.font: UIFont.regular(size: 12),
//                                                                                     NSAttributedString.Key.paragraphStyle:para,
                                                                                            NSAttributedString.Key.foregroundColor: UIColor(hex: 0x707A89)])
//        if let writingDirection = attributedString.attribute(.writingDirection, at: 0, effectiveRange: nil) as? Int {
//            if writingDirection & NSWritingDirection.rightToLeft.rawValue != 0 {
//                para.baseWritingDirection = .rightToLeft
//            } else {
        para.baseWritingDirection = .leftToRight
//            }
//        }
        attributedString.addAttribute(.paragraphStyle, value: para, range: NSRange(location: 0, length: attributedString.length))

        titleLabel.attributedText = attributedString
        noteView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.bottom.trailing.equalToSuperview()
            make.leading.equalTo(noteColorView.snp.trailing).offset(8)
        }

        let actualNumberOfLines = titleLabel.maxNumberOfLines(width: UIScreen.main.bounds.width - 28 - 20)
        if actualNumberOfLines < titleLabel.numberOfLines {
            titleLabel.numberOfLines = actualNumberOfLines
            noteView.snp.updateConstraints { make in
                make.height.equalTo(22 * actualNumberOfLines)
            }
        }
    }

    func addTextView() {
        textView = UITextView()
        //        textView.returnKeyType = .done
        //        textView.textAlignment = .left
        textView.backgroundColor = dynamicBackgroundColor1
        textView.font = UIFont.regular(size: 16)
        textView.textColor = dynamicTitleColor2
        textView.text = note.noteText
        textView.delegate = self
        if textView.text.count > NoteEditerVC.MAX_TEXT_LENGTH {
            showMaxCharacterPrompt()
        }
        //        let para = NSMutableParagraphStyle()
        //        para.paragraphSpacing = 0
        //        para.lineSpacing = 5
        //        let attributedString = NSAttributedString(string: note.content, attributes: [NSAttributedString.Key.font : UIFont.regular(),
        //                                                                                   NSAttributedString.Key.paragraphStyle:para,
        //                                                                                   NSAttributedString.Key.foregroundColor:dynamicTitleColor2])
        //        textView.attributedText = attributedString

        view.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.top.equalTo(noteView.snp.bottom).offset(24)
            make.leading.equalTo(24)
            make.trailing.bottom.equalTo(-24)
        }

        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShowHandler(noti:)), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillChangeFrame(noti:)), name: UIResponder.keyboardWillChangeFrameNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHideHandler(noti:)), name: UIResponder.keyboardWillHideNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(didEnterBackgroundHandler(noti:)), name: UIApplication.didEnterBackgroundNotification, object: nil)

        if isTempNote {
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) { [weak self] in
                self?.textView.becomeFirstResponder()
            }
        }
    }

    func addNumberOfWordsView() {
        let numberOfWordsView = UIView()
        numberOfWordsView.backgroundColor = UIColor.clear
        let numberOfWordsLabel = UILabel()
        numberOfWordsLabel.text = "最大字数限制 20000，超出部分将无法保存".localized
        numberOfWordsLabel.font = UIFont.regular(size: 12)
        numberOfWordsLabel.textColor = UIColor(hex: 0xDD5B56)
        numberOfWordsLabel.frame = CGRect(x: 15, y: 0, width: UIScreen.main.bounds.width, height: 17)
        numberOfWordsView.addSubview(numberOfWordsLabel)
        self.numberOfWordsView = numberOfWordsView
        updateNumberOfWordsViewFrame()
    }

    func updateNumberOfWordsViewFrame() {
        let keyboardRectOnSelf = view.convert(KeyboardRect, from: nil)
        numberOfWordsView.frame = CGRect(x: 0, y: keyboardRectOnSelf.minY - 17 - 4, width: view.frame.size.width, height: 17 + 4)
    }

    static let MAX_TEXT_LENGTH = 20000
    var showPromptDelayWork: DispatchWorkItem?
    func showMaxCharacterPrompt() {
//        textView.inputAccessoryView = numberOfWordsView
        updateNumberOfWordsViewFrame()
        view.addSubview(numberOfWordsView)

        showPromptDelayWork?.cancel()
        showPromptDelayWork = DispatchWorkItem { [weak self] in
            guard let self = self else { return }
//            self.textView.inputAccessoryView = nil
            self.numberOfWordsView.removeFromSuperview()
        }
        DispatchQueue.main.asyncAfter(deadline: .now() + 5, execute: showPromptDelayWork!)
    }

    func updateTextViewFrame(isExpand: Bool, noti: Notification) {
        if let duration = noti.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? TimeInterval,
           let curveInt = noti.userInfo?[UIResponder.keyboardAnimationCurveUserInfoKey] as? Int
        {
            let options = UIView.AnimationOptions(rawValue: UInt(curveInt << 16))
            updateTextViewFrame(isExpand: isExpand, duration: duration, options: options)
        } else {
            updateTextViewFrame(isExpand: isExpand, duration: 0.25, options: .curveEaseInOut)
        }
    }

    func updateTextViewFrame(isExpand: Bool, duration: TimeInterval, options: UIView.AnimationOptions) {
        UIView.animate(withDuration: duration, delay: 0, options: options) {
            self.textView.snp.updateConstraints { make in
                make.bottom.equalTo(-24 - (isExpand ? 0.0 : self.KeyboardRect.size.height))
            }
            self.view.layoutIfNeeded()
        } completion: { _ in
        }
    }

    @objc func keyboardWillShowHandler(noti: Notification) {
        if let rect = (noti.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            KeyboardRect = rect
            updateNumberOfWordsViewFrame()
            updateTextViewFrame(isExpand: isFromSwiftUI, noti: noti)
        }
    }

    @objc func keyboardWillChangeFrame(noti: Notification) {
        if let rect = (noti.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue)?.cgRectValue {
            KeyboardRect = rect
            updateNumberOfWordsViewFrame()
            updateTextViewFrame(isExpand: isFromSwiftUI, noti: noti)
        }
    }

    @objc func keyboardWillHideHandler(noti: Notification) {
        updateTextViewFrame(isExpand: true, noti: noti)
    }

    @objc func tapBackground() {
        textView.selectedTextRange = nil
        textView.resignFirstResponder()
    }

    @objc func tapDoneWhenClose(item _: UIBarButtonItem) {
        isDoneSaved = true
        save()
        dismiss()
    }

    @objc func didEnterBackgroundHandler(noti _: Notification) {
        save()
    }

    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        if isFromSwiftUI && !isDoneSaved {
            save()
        }
    }

    // SwiftUI不会调用
    override func endAppearanceTransition() {
        if !isFromSwiftUI && isBeingDismissed && !isDoneSaved {
            save()
        }
    }

    fileprivate func save() {
        let content = self.content()
        if isTempNote {
            let text = content.trimmingCharacters(in: .whitespacesAndNewlines)
            if !text.isEmpty {
                note.noteText = text
                HighlightRangeManager.add(note: note, chapterPath: note.chapterPath, isForMerge: isForMerge)
                isForMerge = false
                delegate?.noteEditerDidChangedNote()
                if needPostNoti {
                    NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: note)
                }
            }
        } else {
            if note.noteText != content {
                note.noteText = content
                HighlightRangeManager.update(note: note!)
                delegate?.noteEditerDidChangedNote()
                if needPostNoti {
                    NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: note)
                }
            }
        }
    }

    func startAutoSaveTimer() {
        autoSaveTimer = Timer(timeInterval: 10, repeats: true, block: { [weak self] _ in
            guard let self = self else { return }
            self.save()
        })
        RunLoop.main.add(autoSaveTimer!, forMode: .common)
    }

    func content() -> String {
        if textView.text.count > NoteEditerVC.MAX_TEXT_LENGTH {
            let index = textView.text.index(textView.text.startIndex, offsetBy: NoteEditerVC.MAX_TEXT_LENGTH)
            return String(textView.text[..<index])
        } else {
            return textView.text
        }
    }
}

extension NoteEditerVC: UITextViewDelegate {
    func textView(_ textView: UITextView, shouldChangeTextIn range: NSRange, replacementText text: String) -> Bool {
        var shouldText = (textView.text ?? "") as NSString
        shouldText = shouldText.replacingCharacters(in: range, with: text) as NSString
        let shouldString = shouldText as String
        if shouldString.count > NoteEditerVC.MAX_TEXT_LENGTH {
            showMaxCharacterPrompt()
            return shouldString.count <= textView.text.count
        }
        return true
    }

    func textViewDidChange(_ textView: UITextView) {
        if isTempNote && textView.text.count > 0 && textView.text.trimmingCharacters(in: .whitespacesAndNewlines).count > 0 {
            save()
            isTempNote = false
            startAutoSaveTimer()
        }

//        if textView.text.count > NoteEditerVC.MAX_TEXT_LENGTH {
//            //获得已输出字数与正输入字母数
//            let selectRange = textView.markedTextRange
//
//            //获取高亮部分 － 如果有联想词则解包成功
//            if let selectRange = selectRange {
//                if let position =  textView.position(from: (selectRange.start), offset: 0){
//                    Log.d("textView 高亮情况，字符超过限制")
//                    showMaxCharacterPrompt()
//                    return
//                }
//            }
//
//            //截取200个字
//            if let textContent = textView.text,
//               textContent.count > NoteEditerVC.MAX_TEXT_LENGTH {
//                let index = textContent.index((textContent.startIndex), offsetBy: NoteEditerVC.MAX_TEXT_LENGTH)
//                textView.text = String(textContent[..<index])
//                Log.d("textView 字符超过限制")
//                showMaxCharacterPrompt()
//            }
//        }
    }
}
