//
//  RecycleGuide.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/6/15.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import UIKit
import SnapKit
import SwiftyUserDefaults
import SwiftUI

struct RecycleGuideUIKit: UIViewRepresentable {
    typealias UIViewType = RecycleGuide
    
    var action:()->()
    var dismiss:()->()
    
    func makeUIView(context: Context) -> RecycleGuide {
        let guide = RecycleGuide()
        guide.action = action
        guide.dismiss = dismiss
        return guide
    }
    
    func updateUIView(_ uiView: RecycleGuide, context: Context) {
        
    }
    
}

extension DefaultsKeys {
    static let IS_MARKED_RECYLE_GUIDE = DefaultsKey<Bool>("WD_BOOK_RECYLE_GUIDE",defaultValue: false)
}

class RecycleGuide: UIView {
    
    var sourceRect:CGRect = CGRect(x: 0, y: -66, width: 156, height: 54)
    var descImageView:UIImageView!
    var alphaPath:UIBezierPath!
    
    var action:(()->())!
    var dismiss:(()->())!
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        backgroundColor = UIColor.clear
        isOpaque = false

        descImageView = UIImageView(image: UIImage(named: "guide_delete".localized))
        addSubview(descImageView)
        descImageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(-91)
            make.width.equalTo(242)
            make.height.equalTo(127)
        }
        
        let gesture = UITapGestureRecognizer(target: self, action: #selector(tapAction(g:)))
        addGestureRecognizer(gesture)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        let alphaRect = CGRect(x:(rect.width - sourceRect.width) / 2,y:rect.height + sourceRect.minY,width:sourceRect.width, height:sourceRect.height)
        alphaPath = UIBezierPath(roundedRect: alphaRect, cornerRadius: 10)
        let ctx = UIGraphicsGetCurrentContext()
        
        UIColor.black.alpha(0.8).set()
        ctx?.addRect(rect)
        ctx?.fillPath()
        ctx?.setBlendMode(.clear)
        ctx?.addPath(alphaPath.cgPath)
//        ctx?.setFillColor(UIColor.clear.cgColor)
        ctx?.fillPath()
    }
    
    @objc func tapAction(g:UIGestureRecognizer){
        let point = g.location(in: self)
        if alphaPath.contains(point){
            Log.d("点击了区域")
            action()
        }else{
            dismiss()
        }
    }
}
