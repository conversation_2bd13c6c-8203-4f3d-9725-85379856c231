//
//  NoteTips.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/5/28.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import UIKit
import SnapKit

class NoteTips: UIView {
    let label:UILabel!
    
    init(text:String) {
        label = UILabel()
        
        super.init(frame: CGRect.zero)
        backgroundColor = dynmicBGColor
        layer.cornerRadius = 8
        layer.masksToBounds = true
            
        label.text = text
        label.font = UIFont.regular()
        label.textColor = UIColor(hex: 0xF0F0F2)
        label.numberOfLines = 3
        
        let para = NSMutableParagraphStyle()
//        para.paragraphSpacing = 0
//        para.lineSpacing = 0
        para.lineBreakMode = .byTruncatingTail
        para.baseWritingDirection = .leftToRight
        let attributedString = NSAttributedString(string: text, attributes: [NSAttributedString.Key.font : label.font ?? UIFont.regular(),
                                                                                   NSAttributedString.Key.paragraphStyle:para,
                                                                                   NSAttributedString.Key.foregroundColor:label.textColor ?? UIColor(hex: 0xF0F0F2)])
        label.attributedText = attributedString
        
        addSubview(label)
        label.snp.makeConstraints { make in
            make.edges.equalTo(UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10))
        }
        
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
}
