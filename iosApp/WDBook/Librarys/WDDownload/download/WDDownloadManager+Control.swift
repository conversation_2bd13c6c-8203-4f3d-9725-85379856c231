//
//  DownloadManager+Control.swift
//  WDDownload
//
//  Created by <PERSON><PERSON><PERSON> on 2018/4/26.
//  Copyright © 2018 kai zhou. All rights reserved.
//

import Foundation
import shared

extension WDDownloadManager{
    
    public func start(key:String, url:String, destinationFilePath:String, downloadListKey:String? = nil,md5:String? = nil,isReplace:Bool = false){
        cacheLock.lock()
        downloadCache[key] = DownloadCacheInfo(key: key, url: url, destinationFilePath: destinationFilePath,md5:md5)
        cacheLock.unlock()
        
        var info = WDBookDownloadSDK.shared.getDownloadFileEntity(fileId: key)
        if info == nil{
            info = WDBookDownloadSDK.shared.fetchDownloadFileEntity(fileId: key)//如果查到返回;如果没查到创建新的没保存。
            WDBookDownloadSDK.shared.saveDownloadData(entity: info) // replace操作
        }
        guard let downloadDataInfo = info else{
            DownloadLog.e("没有数据，key：\(key)")
            return
        }
        

        let cacheFilePath = FilePath.getCacheFilePath(key: key)
        let destFilePath = FilePath.homeDictionary() + "/" + destinationFilePath

        //2、判断是否满足下载条件  Satisfy download conditions
        guard !DownloadManager.shared.isRunning(taskID: downloadDataInfo.fileId!),
              downloadDataInfo.downloadStatus != .complete else{
            return
        }
        
        //或判断downlaodstatus
//        if AppState.shared.shelfItems.filter{ $0.downloadState == .downloading}.count >= WDDownloadManager.shared.MAX_CONCURRENCE_COUNT {
        if DownloadManager.shared.runningCount() >= WDDownloadManager.shared.MAX_CONCURRENCE_COUNT {
            if downloadDataInfo.downloadStatus != .downloading {
                waitingStatusAndPost(downloadInfo: downloadDataInfo)
                return
            }
        }
        
        
        //3、开始下载 start downloading
        downloadDataInfo.downloadStatus = .begin
        DispatchQueue.main.async {
             NotificationCenter.default.post(name: Noti_Download_Start, object: downloadDataInfo, userInfo: nil)
        }
        
        if downloadDataInfo.downloadStatus == DownloadStatus.error || downloadDataInfo.downloadStatus == DownloadStatus.cancel || isReplace{
            downloadDataInfo.downloadSize = 0
            try? FileManager.default.removeItem(atPath: cacheFilePath)
            try? FileManager.default.removeItem(atPath: destFilePath)
        }
        
        //4,设置下载状态（应该是preDownloading,此处省略预下载状态，直接设置为下载状态）
        //Set download status to Downloading. ingore preDownloading.
        downloadingStatusAndPost(downloadInfo: downloadDataInfo)

        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_START_DOWNLOAD, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_FILE_ID, downloadDataInfo.fileId!])
        
        //5，下载 Download
        DownloadManager.shared.start(taskID: downloadDataInfo.fileId!, url: url, cacheFilePath: cacheFilePath, destinationFilePath: destFilePath, group: nil,start: { [weak self] (task) in
                DownloadLog.i("即将下载end-- preDownloading .begin status end")
                downloadDataInfo.downloadStatus = .begin
            },progress: {[weak self, downloadDataInfo] (task, progress, writedBytes, totalBytes) in
                downloadDataInfo.downloadStatus = .downloading
                downloadDataInfo.downloadSize = Int64(writedBytes)
                downloadDataInfo.actualFileSize = Int64(totalBytes)

//                //TODO: 下载进度没有存到db里
//                if writedBytes == totalBytes {
//                    WDDownloadManager.shared.lastPostTimeIntervalDic.removeValue(forKey: key)
//                    self?.downloadingStatusAndPost(downloadInfo: downloadDataInfo)
//                }
                self?.downloadingStatus(downloadInfo: downloadDataInfo)
                
                let currentTimeInterval = Date().timeIntervalSince1970
                if let lastUpdateTimeInterval = WDDownloadManager.shared.lastPostTimeIntervalDic[key] {
                    if lastUpdateTimeInterval + WDDownloadManager.shared.progressPostTimeInterval <= currentTimeInterval {
                        WDDownloadManager.shared.lastPostTimeIntervalDic[key] = currentTimeInterval
                        DispatchQueue.main.async {
                            NotificationCenter.default.post(name: Noti_Download_Progress, object: downloadDataInfo, userInfo: nil)
                        }
                    }
                } else {
                    WDDownloadManager.shared.lastPostTimeIntervalDic[key] = currentTimeInterval
                    DispatchQueue.main.async {
                        NotificationCenter.default.post(name: Noti_Download_Progress, object: downloadDataInfo, userInfo: nil)
                    }
                }

            }, success: {[weak self, downloadDataInfo] (task) in
                if downloadDataInfo.md5 == "" {
                    self?.completeStatusAndPost(downloadInfo: downloadDataInfo)
                } else {
                    let fileMd5 = self?.md5FromFile(url: getCacheDestFilePath(downloadData: downloadDataInfo))
                    if fileMd5 == downloadDataInfo.md5 {
                        //校验成功
                        DownloadLog.i("MD5校验成功 md5 validate correct")
                        self?.completeStatusAndPost(downloadInfo: downloadDataInfo)
                        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_FINISH_DOWNLOAD, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_FILE_ID, downloadDataInfo.fileId!])
                    } else {
                        DownloadLog.e("MD5校验失败 md5 validate incorrect")
                        self?.retryDic[downloadDataInfo.resourceId] = self?.MAX_RETRY_COUNT
                        SentryUtils.captureMD5(resourceId: downloadDataInfo.resourceId)
                        
                        self?.failedStatusAndPost(downloadInfo: downloadDataInfo, reason: DownloadFailReason.md5Verification)
                    }
                }
        }) {[weak self, downloadDataInfo] (task, error) in
            guard let wSelf = self else{
                return
            }
            
            //重试流程
            if wSelf.retryDic[downloadDataInfo.resourceId] == nil{ //bst_tmo_lev_s_2021
                wSelf.retryDic[downloadDataInfo.resourceId] = 0
            }
            
            if wSelf.retryDic[downloadDataInfo.resourceId]! < wSelf.MAX_RETRY_COUNT{
                
                let downloadCacheInfo:DownloadCacheInfo?
                wSelf.cacheLock.lock()
                downloadCacheInfo = wSelf.downloadCache[key] //1a2a32157e72447861436287e6483443
                wSelf.cacheLock.unlock()
                
                if let cacheInfo = downloadCacheInfo{
                    weak var weakSelf = self
                    DispatchQueue.global().asyncAfter(deadline: .now() + wSelf.RETRY_DURATION_DTIMEINTERVAL){
                        guard let strongSelf = weakSelf else {
                            return
                        }
                        
                        var retryCount = 1
                        if var count = strongSelf.retryDic[downloadDataInfo.resourceId] {
                            count += 1
                           retryCount = count
                        }
                        strongSelf.retryDic[downloadDataInfo.resourceId] = retryCount
                        DownloadLog.e("重试resourceId：\(downloadDataInfo.resourceId), 次数:\(retryCount)")
                        strongSelf.start(key: cacheInfo.key, url: cacheInfo.url, destinationFilePath: cacheInfo.destinationFilePath, md5: cacheInfo.md5, isReplace: true)
                        
                    }
                }
                
            }else{
                //错误流程
                downloadDataInfo.downloadStatus = .error
                let e = error as NSError
                if  e.domain == NSURLErrorDomain && e.code == -999 {
                    DownloadLog.e(e.localizedDescription) //取消（暂停）  cancel(pause)
                    self?.failedStatusAndPost(downloadInfo: downloadDataInfo, reason: .cancel)
                }else if e.domain == NSURLErrorDomain && e.code == -1005{
                    //网络连接已中断。（关闭wifi）-> 不做任何操作
                    //offline. (close wifi)
                    DownloadLog.e(e.localizedDescription)
                    self?.failedStatusAndPost(downloadInfo: downloadDataInfo, reason: .noNetwork)
                }else if e.domain == NSURLErrorDomain && e.code == -1009 {
                    // 无网络情况点击下载
                    // click to start download when offline
                    DownloadLog.e(e.localizedDescription) //  "The Internet connection appears to be offline."
                    self?.failedStatusAndPost(downloadInfo: downloadDataInfo, reason: .noNetwork)
                }else{
                    if e.domain == NSURLErrorDomain && e.code == -1001 {
                        DownloadLog.e(e.localizedDescription) // 超时  "The request timed out."
                        self?.failedStatusAndPost(downloadInfo: downloadDataInfo, reason: .timeout)
                    }else{
                         DownloadLog.e(e.localizedDescription)
                        self?.failedStatusAndPost(downloadInfo: downloadDataInfo, reason: .unknown)
                    }
                }
            }
        }
    }

       
    //终止所有下载任务
    //用户退出； 断网；进入后台。执行
    /// terminate all download tasks
    /// Call this function when : user logout; no network; go to background;
    internal func shutDown(){
        
        DownloadManager.shared.stopAll()
    }
}

import UIKit
extension UIDevice {
    var systemSize: Int64? {
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory()),
            let totalSize = (systemAttributes[.systemSize] as? NSNumber)?.int64Value else {
                return nil
        }
        return totalSize
    }
    
    var systemFreeSize: Int64? {
        guard let systemAttributes = try? FileManager.default.attributesOfFileSystem(forPath: NSHomeDirectory()),
            let freeSize = (systemAttributes[.systemFreeSize] as? NSNumber)?.int64Value else {
                return nil
        }
        return freeSize
    }
}
