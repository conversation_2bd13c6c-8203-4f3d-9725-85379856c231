//
//  DBTableP.swift
//  GRDB
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2018/5/28.
//  Copyright © 2018 <EMAIL>. All rights reserved.
//

import Foundation
import GRDB

//TODO:换名字
public protocol DBTableP:AnyObject {
    
}

//extension DBTableP:TableRecord{
//    static var databaseTableName: String {
//        return ""
//    }
//}
public extension DBTableP where Self:DBModel{
    internal var primaryKeyPropertyName:String{
        return "id"
    }
    
    internal func noSavedProperties() -> [String]{
        if type(of: self).isSaveDefaulttimestamp{
            return transientTypes()
        }else{
            return transientTypes() + [type(of: self).CREATE_AT_KEY,type(of: self).UPDATE_AT_KEY]
        }
    }

    func uniqueProperties() -> [String]{
        return [String]()
    }
    
    
    //MARK: Utils
    static func read<T>(_ block: (Database) throws-> T) throws  -> T{
        return try getDBQueue().read{ db in
            try block(db)
        }
    }
    static func write<T>(_ updates: (Database) throws -> T) throws -> T {
        return try getDBQueue().write({ db in
            try updates(db)
        })
    }
    
}





