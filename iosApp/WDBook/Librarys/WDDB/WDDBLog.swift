//
//  WDDBLogger.swift
//  GRDB
//
//  Created by <PERSON> on 04/07/2017.
//  Copyright © 2017 <EMAIL>. All rights reserved.
//

import Foundation

public enum WDDBLogLevel:Int {
    case debug = 1,info,warn,error
}

public class WDDBLog{
    static func d(_ message: Any = "",
                  file: String = #file,
                  line: Int = #line,
                  function: String = #function) {
        //    debugPrint( "***[debug]***-\(Thread.current)-\(name(of: file))[\(line)]:\(function) --> \(message)")
        if DBConfigration.logLevel == .debug {
            print( "***[debug]***-\(name(of: file))[\(line)]:\(function) --> \(message)")
        }
    }

    static func i(_ message: Any = "",
                 file: String = #file,
                 line: Int = #line,
                 function: String = #function) {
        if DBConfigration.logLevel.rawValue >= LogLevel.info.rawValue {
            print( "***[info]***-\(name(of: file))[\(line)]:\(function) --> \(message)")
        }
        
    }

    static func w(_ message: Any = "",
                 file: String = #file,
                 line: Int = #line,
                 function: String = #function) {
        if DBConfigration.logLevel.rawValue >= LogLevel.warn.rawValue {
            print( "***[warn]***-\(name(of: file))[\(line)]:\(function) --> \(message)")
        }
        
        
    }

    static func e(_ message: Any = "",
                  file: String = #file,
                  line: Int = #line,
                  function: String = #function) {
        print( "***[error]***-\(name(of: file))[\(line)]:\(function) --> \(message)")
        
    }
    
    static func name(of file:String) -> String {
        return URL(fileURLWithPath: file).lastPathComponent
    }
}
