//
//  WDSimplePingHelper.h
//  wedevotebible
//
//  Created by 杜文泽 on 2022/9/19.
//  Copyright © 2022 WD Bible Team. All rights reserved.
//

#import <Foundation/Foundation.h>

NS_ASSUME_NONNULL_BEGIN

typedef void(^PingFinishBlock)(double loss, NSString *server);

@interface WDSimplePingHelper : NSObject

- (instancetype)initWithHostName:(NSString*)hostName;

- (instancetype)initWithHostName:(NSString*)hostName pingTimeout: (double)pingTimeout pingCount: (int)pingCount;

@property(nonatomic, readonly) NSString *hostName;

@property (nonatomic, copy) PingFinishBlock pingFinishBlock;

- (void)startPing;
- (void)stopPing;

@end

NS_ASSUME_NONNULL_END
