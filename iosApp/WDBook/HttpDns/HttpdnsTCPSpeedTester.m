/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#import "HttpdnsTCPSpeedTester.h"
#import <sys/socket.h>
#import <netinet/in.h>
#import <fcntl.h>
#import <arpa/inet.h>
#import <netdb.h>
#include <sys/time.h>
#import "WDSimplePingHelper.h"

static NSString *const testSpeedKey = @"testSpeed";
static NSString *const ipKey = @"ip";
static NSString *const lossRateKey = @"lossRate";


static dispatch_queue_t pingQueue;
static dispatch_semaphore_t pingSemaphore;

@interface HttpdnsTCPSpeedTester()


@property (nonatomic, strong) NSMutableArray<WDSimplePingHelper*> *pingHelpers;
@property (nonatomic, strong) NSLock *pingHelpersLock;
@property (nonatomic, strong) NSTimer *timer;
@property (nonatomic, assign) int count;

- (int)testSpeedOf:(NSString *)ip;
- (int)testSpeedOf:(NSString *)ip port:(int16_t)port;
- (void)ipRankingWithIPs:(NSArray<NSString *> *)IPs host:(NSString *)host complete:(void (^)(NSArray<NSString *> *))complete;

@end


@implementation HttpdnsTCPSpeedTester

-(void)test{
    _count = 0;
    self.timer = [NSTimer scheduledTimerWithTimeInterval:1 target:self selector:@selector(testRefresh) userInfo:nil repeats:YES];
}

//-(void)testRefresh{
//    if (_count == 30){
//        NSLog(@"ip测速：终止。");
//        if (self.timer) {
//            [self.timer invalidate];
//        }
//        self.timer = nil;
//        return;
//    }
//    _count += 1;
//    [[WDSystemContext sharedInstance] checkServer];
//}

-(void)testOne{
    NSArray<NSString *> *IPStrings = @[@"**************"
                                       ,@"**************"
                                       ,@"************"
                                       ,@"************"
                                       ,@"**************"
                                       ,@"**************"
                                       ,@"**************"
                                       ,@"**************"
                                       ,@"**************"
                                       ,@"**************"
                                       ,@"**************"
                                       ,@"**************"
    ];
//    NSString *host = @"adb.xiaoen.app";

    dispatch_async(dispatch_get_global_queue(0, 0), ^{
        NSDictionary *fastIps = [[HttpdnsTCPSpeedTester new] checkIPList:IPStrings];
        NSLog(@"ip测试：最快ips:%@",fastIps);
    });
}


-(NSDictionary<NSString *,NSNumber *> *)checkIPList:(NSArray<NSString *> *)ips{
    if (pingQueue == nil){
        pingQueue = dispatch_queue_create("pingqueue", DISPATCH_QUEUE_SERIAL);
    }
    if(pingSemaphore == nil){
        pingSemaphore = dispatch_semaphore_create(2);
    }
    _pingHelpers = [NSMutableArray<WDSimplePingHelper*> new];
    _pingHelpersLock = [[NSLock alloc] init];
    
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    __block NSDictionary<NSString *,NSNumber *> *result = @{};
    [self ipRankingWithIPs:ips complete:^(NSArray<NSDictionary *> *sortedIps) {
        #ifdef DEBUG
            NSLog(@"ip测试：可用ip: %@\n ",sortedIps);
        #endif
        
        NSMutableDictionary<NSString *,NSNumber *> *availableIPs = [[NSMutableDictionary<NSString*,NSNumber *> alloc] init];
        for (NSDictionary *dict in sortedIps) {
            NSString *ip = [dict objectForKey:ipKey];
            long speed = [[dict valueForKey:testSpeedKey] integerValue];
            availableIPs[ip] = [NSNumber numberWithInteger:speed];
        }
        
        result = [[NSDictionary<NSString *,NSNumber *> alloc] initWithDictionary:availableIPs];
        
        dispatch_semaphore_signal(semaphore);
    }];
    
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    return result;
}

/**
 *  本测速函数，使用linux socket connect 和select函数实现的。 基于以下原理
 *  1. 即使套接口是非阻塞的。如果连接的服务器在同一台主机上，那么在调用connect 建立连接时，连接通常会立即建立成功，我们必须处理这种情况。
 *  2. 源自Berkeley的实现(和Posix.1g)有两条与select 和非阻塞IO相关的规则：
 *     A. 当连接建立成功时，套接口描述符变成可写；
 *     B. 当连接出错时，套接口描述符变成既可读又可写。
 *  @param ip 用于测速对Ip，应该是IPv4格式。
 *
 *  @return 测速结果，单位时毫秒，HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT 代表超时。
 */
- (int)testSpeedOf:(NSString *)ip {
    return [self testSpeedOf:ip port:80];
}

/*!
 * 如果用户对域名提供多个端口，取任意一个端口。
 假设：同一个域名，不同端口到达速度一致。
 让优选逻辑，尽量少de
 15s 100s
 
 - IP池在2个到5个范围内，才进行测速逻辑。
 - 只在ttl未过期内测试。
 - ~~只取内存缓存，与持久化缓存逻辑不产生交集。持久化优先级更高。~~ 无法区分持久化，持久化缓存也可能参与排序。
 - 测速逻辑公开，作为最佳实践。
 - 只在 IPv4 逻辑下测试，IPv6 环境不测。
 - 开启IPv6解析结果时，不测试。
 - 测速逻辑不能增加用户计费请求次数。
 - 预加载也参与IP优选，网络请求成功就异步排序。
 -
 */
- (void)ipRankingWithIPs:(NSArray<NSString *> *)IPs complete:(void (^)(NSArray<NSDictionary*> *))complete{
#ifdef DEBUG
    NSLog(@"ip测试：开始测速，ip个数: %lu",(unsigned long)IPs.count);
    speedtest_added_count += IPs.count;
    NSLog(@"ip测试：开始测速，总added ip个数: %lu",(unsigned long)speedtest_added_count);
#endif
    
    dispatch_queue_t globalQueue = dispatch_queue_create("swiftlee.concurrent.queue", DISPATCH_QUEUE_CONCURRENT);
    dispatch_group_t group = dispatch_group_create();
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(2);
      
    NSMutableArray<NSDictionary *> *ipSpeeds = [[NSMutableArray<NSDictionary *> alloc] init];
    NSLock *lock = [[NSLock alloc] init];
    for (NSString *ip in IPs) {
        dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
        dispatch_group_async(group, globalQueue, ^{
            NSMutableDictionary *ipSpeed = [self checkIpSpeedAndPing:ip];
            if(ipSpeeds != nil && [ipSpeeds isKindOfClass:[NSMutableArray class]]){
                if(ipSpeed != nil && ipSpeed.count == 3){
                    [lock lock];
                    [ipSpeeds addObject:ipSpeed];
                    [lock unlock];
                }
            }
            dispatch_semaphore_signal(semaphore);
        });
    }
    
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
    NSArray *sortedIPSpeedsArray = [ipSpeeds sortedArrayUsingComparator:^NSComparisonResult(id obj1, id obj2) {
        long speed1 = [[obj1 valueForKey:testSpeedKey] integerValue];
        long speed2 = [[obj2 valueForKey:testSpeedKey] integerValue];
        double rate1 = [[obj1 valueForKey:lossRateKey] doubleValue];
        double rate2 = [[obj2 valueForKey:lossRateKey] doubleValue];
        if(speed1 == speed2){
            return (rate1 < rate2) ? NSOrderedAscending : NSOrderedDescending;
        }else{
            return (speed1 < speed2) ? NSOrderedAscending : NSOrderedDescending;
        }
    }];
    
#ifdef DEBUG
    NSLog(@"ip测试：所有ip 个数: %lu, 速度: %@\n ",(unsigned long)sortedIPSpeedsArray.count,sortedIPSpeedsArray);
#endif
    
    //可用ip
    NSMutableArray<NSDictionary *> *availableIPSpeeds = [[NSMutableArray<NSDictionary *> alloc] init];
    for (NSDictionary *dict in sortedIPSpeedsArray) {
        long speed = [[dict valueForKey:testSpeedKey] integerValue];
        double lossRate = [[dict valueForKey:lossRateKey] doubleValue];
        if(availableIPSpeeds != nil && [availableIPSpeeds isKindOfClass:[NSMutableArray class]]){
            if(speed != HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT && lossRate == 0.0){
                [availableIPSpeeds addObject:dict];
            }
        }
    }
    
    complete(availableIPSpeeds);
}

-(NSMutableDictionary*)checkIpSpeedGroupAndPingSerial:(NSString*)ip{
#ifdef DEBUG
    speedtest_started_count += 1;
    NSLog(@"ip测试：总start测ip个数: %lu",(unsigned long)speedtest_started_count);
#endif
    
    NSMutableDictionary *ipSpeed = [[NSMutableDictionary alloc] init];
    [ipSpeed setObject:@(HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT) forKey:testSpeedKey];
    [ipSpeed setObject:ip forKey:ipKey];
    [ipSpeed setObject:@(100) forKey:lossRateKey];
    
    NSLock *lock = [[NSLock alloc] init];
    dispatch_queue_t globalQueue = dispatch_get_global_queue(0, 0);
    dispatch_group_t group = dispatch_group_create();
    
    dispatch_group_enter(group);
    dispatch_async(globalQueue, ^{
        int16_t port = 80;
        int testSpeed =  [self testSpeedOf:ip port:port];
        if (testSpeed == 0) {
            testSpeed = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
        }
        
        [lock lock];
#ifdef DEBUG
        speedtest_ended_speed_count += 1;
        NSLog(@"ip测试：host:%@, 速度：%d。总end速度个数：%d",ip, testSpeed,speedtest_ended_speed_count);
#endif
        [ipSpeed setObject:@(testSpeed) forKey:testSpeedKey];
        [ipSpeed setObject:ip forKey:ipKey];
        [lock unlock];
        dispatch_group_leave(group);
    });
    
    
    dispatch_group_enter(group);
    dispatch_async(pingQueue, ^{
        dispatch_semaphore_wait(pingSemaphore, DISPATCH_TIME_FOREVER);
        WDSimplePingHelper *pingHelper = [[WDSimplePingHelper alloc] initWithHostName:ip];
        [self.pingHelpersLock lock];
        [self.pingHelpers addObject:pingHelper];
        [self.pingHelpersLock unlock];
        pingHelper.pingFinishBlock = ^(double loss, NSString *serverStr) {
            
            [lock lock];
#ifdef DEBUG
            speedtest_ended_loss_count += 1;
            NSLog(@"ip测试：host:%@, 丢包率：%f-。总end丢包率个数: %d",serverStr, loss,speedtest_ended_loss_count);
#endif
            [ipSpeed setObject:@(loss) forKey:lossRateKey];
            [lock unlock];
            dispatch_semaphore_signal(pingSemaphore);
            dispatch_group_leave(group);
        };
        [pingHelper startPing];
    });
    
    //可以10秒结束，避免外层中断。但是不能发起新的ping了。
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
#ifdef DEBUG
    speedtest_ended_count += 1;
    NSLog(@"ip测试：总end测ip个数: %lu",(unsigned long)speedtest_ended_count);
#endif
    return ipSpeed;
}

-(NSMutableDictionary*)checkIpSpeedAndPing:(NSString*)ip{
#ifdef DEBUG
    speedtest_started_count += 1;
    NSLog(@"ip测试：总start测ip个数: %lu",(unsigned long)speedtest_started_count);
#endif
    
    NSMutableDictionary *ipSpeed = [[NSMutableDictionary alloc] init];
    [ipSpeed setObject:@(HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT) forKey:testSpeedKey];
    [ipSpeed setObject:ip forKey:ipKey];
    [ipSpeed setObject:@(100) forKey:lossRateKey];
    
    NSLock *lock = [[NSLock alloc] init];
    dispatch_queue_t globalQueue = dispatch_get_global_queue(0, 0);
    dispatch_group_t group = dispatch_group_create();
    
    dispatch_group_enter(group);
    dispatch_async(globalQueue, ^{
        int16_t port = 80;
        int testSpeed =  [self testSpeedOf:ip port:port];
        if (testSpeed == 0) {
            testSpeed = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
        }

        [lock lock];
#ifdef DEBUG
        speedtest_ended_speed_count += 1;
        NSLog(@"ip测试：host:%@, 速度：%d。总end速度个数：%d",ip, testSpeed,speedtest_ended_speed_count);
#endif
        [ipSpeed setObject:@(testSpeed) forKey:testSpeedKey];
        [ipSpeed setObject:ip forKey:ipKey];
        [lock unlock];
        dispatch_group_leave(group);
    });
    
    dispatch_group_enter(group);
    dispatch_async(globalQueue, ^{
#ifdef DEBUG
            NSLog(@"ip测试：开始测丢包率:host:%@",ip);
#endif
        WDSimplePingHelper *pingHelper = [[WDSimplePingHelper alloc] initWithHostName:ip];
        [self.pingHelpersLock lock];
        [self.pingHelpers addObject:pingHelper];
        [self.pingHelpersLock unlock];
        pingHelper.pingFinishBlock = ^(double loss, NSString *serverStr) {
            [lock lock];
#ifdef DEBUG
            speedtest_ended_loss_count += 1;
            NSLog(@"ip测试：host:%@, 丢包率：%f-。总end丢包率个数: %d",serverStr, loss,speedtest_ended_loss_count);
#endif
            [ipSpeed setObject:@(loss) forKey:lossRateKey];
            [lock unlock];
            dispatch_group_leave(group);
        };
        [pingHelper startPing];
    });
    
    //可以10秒结束，避免外层中断。但是不能发起新的ping了。
    dispatch_group_wait(group, DISPATCH_TIME_FOREVER);
#ifdef DEBUG
    speedtest_ended_count += 1;
    NSLog(@"ip测试：总end测ip个数: %lu",(unsigned long)speedtest_ended_count);
#endif
    return ipSpeed;
}

-(NSMutableDictionary*)checkIpSpeed:(NSString*)ip{
    NSMutableDictionary *ipSpeed = [[NSMutableDictionary alloc] init];
    
    int16_t port = 80;
    int testSpeed =  [self testSpeedOf:ip port:port];
    if (testSpeed == 0) {
        testSpeed = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
    }
    [ipSpeed setObject:@(testSpeed) forKey:testSpeedKey];
    [ipSpeed setObject:ip forKey:ipKey];
    
    //临时丢包率
    [ipSpeed setObject:@(0.0) forKey:lossRateKey];
    return ipSpeed;
}

-(NSMutableDictionary*)checkIpSpeedAndPingSemaphore:(NSString*)ip{
    NSMutableDictionary *ipSpeed = [[NSMutableDictionary alloc] init];
    
    dispatch_group_t group = dispatch_group_create();
    dispatch_queue_t queue = dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0);
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(2);
    
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    dispatch_group_async(group, queue, ^{
        int16_t port = 80;
        int testSpeed =  [self testSpeedOf:ip port:port];
        if (testSpeed == 0) {
            testSpeed = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
        }
        [ipSpeed setObject:@(testSpeed) forKey:testSpeedKey];
        [ipSpeed setObject:ip forKey:ipKey];
        dispatch_semaphore_signal(semaphore);
    });
        
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    dispatch_group_async(group, queue, ^{
        WDSimplePingHelper *pingHelper = [[WDSimplePingHelper alloc] initWithHostName:ip];
        [self.pingHelpersLock lock];
        [self.pingHelpers addObject:pingHelper];
        [self.pingHelpersLock unlock];
        pingHelper.pingFinishBlock = ^(double loss, NSString *serverStr) {
#ifdef DEBUG
            NSLog(@"ip测试：host:%@, 丢包率：%f-",serverStr, loss);
#endif
            [ipSpeed setObject:@(loss) forKey:lossRateKey];
            dispatch_semaphore_signal(semaphore);
        };
        [pingHelper startPing];
    });
    
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    return ipSpeed;
}

-(NSMutableDictionary*)checkIpSpeedAndPingSerial:(NSString*)ip{
    NSMutableDictionary *ipSpeed = [[NSMutableDictionary alloc] init];
    
    dispatch_semaphore_t semaphore = dispatch_semaphore_create(0);
    
    int16_t port = 80;
    int testSpeed =  [self testSpeedOf:ip port:port];
    if (testSpeed == 0) {
        testSpeed = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
    }
    [ipSpeed setObject:@(testSpeed) forKey:testSpeedKey];
    [ipSpeed setObject:ip forKey:ipKey];
        
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        WDSimplePingHelper *pingHelper = [[WDSimplePingHelper alloc] initWithHostName:ip];
        [self.pingHelpersLock lock];
        [self.pingHelpers addObject:pingHelper];
        [self.pingHelpersLock unlock];
        pingHelper.pingFinishBlock = ^(double loss, NSString *serverStr) {
            NSLog(@"ip测试：host:%@, 丢包率：%f-",serverStr, loss);
            [ipSpeed setObject:@(loss) forKey:lossRateKey];
            dispatch_semaphore_signal(semaphore);
        };
        [pingHelper startPing];
    });
    
    dispatch_semaphore_wait(semaphore, DISPATCH_TIME_FOREVER);
    return ipSpeed;
}

-(NSMutableDictionary*)checkIpSpeedAndPingSerial2:(NSString*)ip{
    NSMutableDictionary *ipSpeed = [[NSMutableDictionary alloc] init];
    
    int16_t port = 80;
    int testSpeed =  [self testSpeedOf:ip port:port];
    if (testSpeed == 0) {
        testSpeed = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
    }
    [ipSpeed setObject:@(testSpeed) forKey:testSpeedKey];
    [ipSpeed setObject:ip forKey:ipKey];
        
    dispatch_async(dispatch_get_global_queue(DISPATCH_QUEUE_PRIORITY_DEFAULT, 0), ^{
        WDSimplePingHelper *pingHelper = [[WDSimplePingHelper alloc] initWithHostName:ip];
        [self.pingHelpersLock lock];
        [self.pingHelpers addObject:pingHelper];
        [self.pingHelpersLock unlock];
        pingHelper.pingFinishBlock = ^(double loss, NSString *serverStr) {
#ifdef DEBUG
            NSLog(@"ip测试：host:%@, 丢包率：%f-",serverStr, loss);
#endif
            [ipSpeed setObject:@(loss) forKey:lossRateKey];
            dispatch_semaphore_signal(pingSemaphore);
        };
        [pingHelper startPing];
    });
    
    dispatch_semaphore_wait(pingSemaphore, dispatch_time(DISPATCH_TIME_NOW, 10*NSEC_PER_SEC));
    return ipSpeed;
}

- (int)testSpeedOf:(NSString *)ip port:(int16_t)port {
    NSString *oldIp = ip;
    //request time out
    float rtt = 0.0;
    //sock：将要被设置或者获取选项的套接字。
    int s = 0;
    struct sockaddr_in saddr;
    saddr.sin_family = AF_INET;
    // MARK: - 设置端口，这里需要根据需要自定义，默认是80端口。
    saddr.sin_port = htons(port);
    saddr.sin_addr.s_addr = inet_addr([ip UTF8String]);
    //saddr.sin_addr.s_addr = inet_addr("*********");
    if( (s=socket(AF_INET, SOCK_STREAM, 0)) < 0) {
        NSLog(@"ERROR:%s:%d, create socket failed.",__FUNCTION__,__LINE__);
        return 0;
    }
    NSDate *startTime = [NSDate date];
    NSDate *endTime;
    //为了设置connect超时 把socket设置称为非阻塞
    int flags = fcntl(s, F_GETFL,0);
    fcntl(s,F_SETFL, flags | O_NONBLOCK);
    //对于阻塞式套接字，调用connect函数将激发TCP的三次握手过程，而且仅在连接建立成功或者出错时才返回；
    //对于非阻塞式套接字，如果调用connect函数会之间返回-1（表示出错），且错误为EINPROGRESS，表示连接建立，建立启动但是尚未完成；
    //如果返回0，则表示连接已经建立，这通常是在服务器和客户在同一台主机上时发生。
    int i = connect(s,(struct sockaddr*)&saddr, sizeof(saddr));
    if(i == 0) {
        //建立连接成功，返回rtt时间。 因为connect是非阻塞，所以这个时间就是一个函数执行的时间，毫秒级，没必要再测速了。
        close(s);
        return 1;
    }
    struct timeval tv;
    int valopt;
    socklen_t lon;
    tv.tv_sec = HTTPDNS_SOCKET_CONNECT_TIMEOUT;
    tv.tv_usec = 0;
    
    fd_set myset;
    FD_ZERO(&myset);
    FD_SET(s, &myset);
    
    // MARK: - 使用select函数，对套接字的IO操作设置超时。
    /**
     select函数
     select是一种IO多路复用机制，它允许进程指示内核等待多个事件的任何一个发生，并且在有一个或者多个事件发生或者经历一段指定的时间后才唤醒它。
     connect本身并不具有设置超时功能，如果想对套接字的IO操作设置超时，可使用select函数。
     **/
    int maxfdp = s+1;
    int j = select(maxfdp, NULL, &myset, NULL, &tv);
    
    if (j == 0) {
//        NSLog(@"ip测试：INFO:%s:%d, test rtt of (%@) timeout.",__FUNCTION__,__LINE__, oldIp);
        rtt = HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT;
        close(s);
        return rtt;
    }
    
    if (j < 0) {
//        NSLog(@"ip测试：ERROR:%s:%d, select function error.",__FUNCTION__,__LINE__);
        rtt = 0;
        close(s);
        return rtt;
    }
    
    /**
     对于select和非阻塞connect，注意两点：
     [1] 当连接成功建立时，描述符变成可写； [2] 当连接建立遇到错误时，描述符变为即可读，也可写，遇到这种情况，可调用getsockopt函数。
     **/
    lon = sizeof(int);
    //valopt 表示错误信息。
    // MARK: - 测试核心逻辑，连接后，获取错误信息，如果没有错误信息就是访问成功
    /*!
     * //getsockopt函数可获取影响套接字的选项，比如SOCKET的出错信息
     * (get socket option)
     */
    getsockopt(s, SOL_SOCKET, SO_ERROR, (void*)(&valopt), &lon);
    //如果有错误信息：
    if (valopt) {
#ifdef DEBUG
        NSLog(@"ip测试：ERROR:%s:%d, select function error.",__FUNCTION__,__LINE__);
#endif
        rtt = 0;
    } else {
        endTime = [NSDate date];
        rtt = [endTime timeIntervalSinceDate:startTime] * 1000;
    }
    close(s);
    return rtt;
}

@end
