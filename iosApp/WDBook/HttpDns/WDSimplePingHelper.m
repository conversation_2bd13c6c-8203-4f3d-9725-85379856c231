//
//  WDSimplePingHelper.m
//  wedevotebible
//
//  Created by 杜文泽 on 2022/9/19.
//  Copyright © 2022 WD Bible Team. All rights reserved.
//

#import "WDSimplePingHelper.h"
#import "SimplePing.h"
#include <netdb.h>

//超时时间
#define PING_TIMEOUT 1.5
//发送的ping的总次数
#define kSequenceNumber  10

@interface WDSimplePingHelper ()<SimplePingDelegate>

@property (nonatomic, strong) SimplePing *simplePing;
/**目标域名的IP地址*/
@property (nonatomic, copy) NSString *iPAddress;
/**开始发送数据的时间*/
@property (nonatomic) NSTimeInterval startTimeInterval;
/**消耗的时间*/
@property (nonatomic) NSTimeInterval delayTime;
/**接收到数据或者丢失的数据的次数*/
@property (nonatomic, assign) NSInteger receivedOrDelayCount;
/**发出的数据包*/
@property (nonatomic) NSUInteger sendPackets;
/**收到的数据包*/
@property (nonatomic) NSUInteger receivePackets;
/**丢包率*/
@property (nonatomic, assign) double packetLoss;

@property(nonatomic, readwrite, copy) NSString *hostName;
@property (nonatomic, readonly) double pingTimeout;
@property (nonatomic, readonly) int pingCount;

@end

@implementation WDSimplePingHelper

- (instancetype)initWithHostName:(NSString*)hostName
{
    return [self initWithHostName:hostName pingTimeout:PING_TIMEOUT pingCount:kSequenceNumber];
}

- (instancetype)initWithHostName:(NSString *)hostName pingTimeout:(double)pingTimeout pingCount:(int)pingCount {
    if (self = [super init]) {
        self.simplePing = [[SimplePing alloc] initWithHostName:hostName];
        _pingTimeout = pingTimeout;
        _pingCount = pingCount;
        self.simplePing.addressStyle = SimplePingAddressStyleAny;
        self.simplePing.delegate = self;
        self.sendPackets = 0;
        self.receivePackets = 0;
        self.packetLoss = 100.0;
        self.hostName = hostName;
    }
    return self;
}

- (void)startPing{
    dispatch_async(dispatch_get_main_queue(), ^{
        [self.simplePing start];  //必须在主线程中运行
    });
    
}

#pragma mark - SimplePingDelegate
#pragma mark - start方法回调
// [self.simplePing start] 成功
- (void)simplePing:(SimplePing *)pinger didStartWithAddress:(NSData *)address{
#ifdef DEBUG
    NSLog(@"ip测试：ping: ip: %@,didStartWithAddress",_hostName);
#endif
    self.iPAddress = [self displayIPFormAddress:address];
    [self sendPingData];
}

// [self.simplePing start] 失败
- (void)simplePing:(SimplePing *)pinger didFailWithError:(NSError *)error{
#ifdef DEBUG
    NSString *failCreateLog = [NSString stringWithFormat:@"#%ld try create failed: %@", self.receivedOrDelayCount,[self shortErrorFromError:error]];
    NSLog(@"ip测试：ip: %@,didFailWithError:%@",_hostName,failCreateLog);
#endif
    
    //启动发送data失败
    if (_pingFinishBlock) {
        _pingFinishBlock(100.0, self.hostName);
        _pingFinishBlock = nil;
    }
    [self stopPing];
}


#pragma mark - sendPingWithData方法回调
//sendPingWithData 发送数据成功
- (void)simplePing:(SimplePing *)pinger didSendPacket:(NSData *)packet sequenceNumber:(uint16_t)sequenceNumber{
    self.sendPackets++;
}

// sendPingWithData 发送数据失败，并返回错误信息
- (void)simplePing:(SimplePing *)pinger didFailToSendPacket:(NSData *)packet sequenceNumber:(uint16_t)sequenceNumber error:(NSError *)error{
#ifdef DEBUG
    NSString *sendFailLog = [NSString stringWithFormat:@"#%u send failed: %@",sequenceNumber,[self shortErrorFromError:error]];
    NSLog(@"ip测试：ping: %@, didFailToSendPacket:%@",_hostName,sendFailLog);
#endif
    [self receiveFails];
}

//发送数据后接收到主机返回应答数据的回调
- (void)simplePing:(SimplePing *)pinger didReceivePingResponsePacket:(NSData *)packet sequenceNumber:(uint16_t)sequenceNumber{
    //    DLog(@"ip:%@  #%u received, size=%zu time=%f loss=%f", self.iPAddress,sequenceNumber, packet.length,self.delayTime,self.packetLoss);
    [self receiveSuccess];
}

// 收到的未知的数据包
- (void)simplePing:(SimplePing *)pinger didReceiveUnexpectedPacket:(NSData *)packet{
#ifdef DEBUG
    NSLog(@"ip测试：ping: %@, 收到未知数据包, 判定为丢失。",_hostName);
#endif
    [self receiveSuccess];
}

-(void)receiveSuccess{
    [self cancelTimeOut];
    
    self.receivedOrDelayCount++;
    self.receivePackets++;
    [self calculateLossRate];
    
    [self sendPingData];
}

-(void)receiveFails{
    [self cancelTimeOut];
    
    self.receivedOrDelayCount++;
    [self calculateLossRate];
    
    [self sendPingData];
}

-(void)calculateLossRate{
    self.packetLoss = (double)((self.sendPackets - self.receivePackets) * 1.f / self.sendPackets * 100);
    self.delayTime = ([NSDate timeIntervalSinceReferenceDate] - self.startTimeInterval) * 1000;
}

//发送ping
- (void)sendPingData{
    //执行到指定次数后停止时间搓
    if (self.receivedOrDelayCount >= self.pingCount) {
        if (_pingFinishBlock) {
            _pingFinishBlock(self.packetLoss, self.hostName);
            _pingFinishBlock = nil;
        }
        [self stopPing];
        return;
    }
    
    self.startTimeInterval = [NSDate timeIntervalSinceReferenceDate];
    [self.simplePing sendPingWithData:nil];
    //超时问题处理
    [self performSelector:@selector(pingTimeoutHandler) withObject:nil afterDelay:self.pingTimeout];
}

//取消超时
- (void)cancelTimeOut{
    [NSObject cancelPreviousPerformRequestsWithTarget:self selector:@selector(pingTimeoutHandler) object:nil];
}


-(void)pingTimeoutHandler{
#ifdef DEBUG
    NSLog(@"ip测试：ping: %@, ++++超时了+++",_hostName);
#endif
    
    self.receivedOrDelayCount++;
    [self calculateLossRate];
    
    [self sendPingData];
}

-(void)stopPing {
    [self.simplePing stop];
    self.simplePing = nil;
    self.delayTime = 0;
}

/**
 * 将ping接收的数据转换成ip地址
 * @param address 接受的ping数据
 */
-(NSString *)displayIPFormAddress:(NSData *)address
{
    int err;
    NSString *result;
    char hostStr[NI_MAXHOST];
    
    result = nil;
    
    if (address != nil) {
        err = getnameinfo([address bytes], (socklen_t)[address length], hostStr, sizeof(hostStr),
                          NULL, 0, NI_NUMERICHOST);
        if (err == 0) {
            result = [NSString stringWithCString:hostStr encoding:NSASCIIStringEncoding];
            assert(result != nil);
        }
    }
    
    return result;
}


/*
 * 解析错误数据并翻译
 */
- (NSString *)shortErrorFromError:(NSError *)error
{
    NSString *result;
    NSNumber *failureNum;
    int failure;
    const char *failureStr;
    
    assert(error != nil);
    
    result = nil;
    
    // Handle DNS errors as a special case.
    
    if ([[error domain] isEqual:(NSString *)kCFErrorDomainCFNetwork] &&
        ([error code] == kCFHostErrorUnknown)) {
        failureNum = [[error userInfo] objectForKey:(id)kCFGetAddrInfoFailureKey];
        if ([failureNum isKindOfClass:[NSNumber class]]) {
            failure = [failureNum intValue];
            if (failure != 0) {
                failureStr = gai_strerror(failure);
                if (failureStr != NULL) {
                    result = [NSString stringWithUTF8String:failureStr];
                    assert(result != nil);
                }
            }
        }
    }
    
    if (result == nil) {
        result = [error localizedFailureReason];
    }
    if (result == nil) {
        result = [error localizedDescription];
    }
    if (result == nil) {
        result = [error description];
    }
    assert(result != nil);
    return result;
}


@end
