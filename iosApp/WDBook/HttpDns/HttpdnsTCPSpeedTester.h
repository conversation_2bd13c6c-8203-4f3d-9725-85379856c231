/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#import <Foundation/Foundation.h>

#define HTTPDNS_SOCKET_CONNECT_TIMEOUT 10 //单位秒
#define HTTPDNS_SOCKET_CONNECT_TIMEOUT_RTT 600000//10分钟 单位毫秒

static int speedtest_added_count = 0;
static int speedtest_started_count = 0;
static int speedtest_ended_count = 0;
static int speedtest_ended_speed_count = 0;
static int speedtest_ended_loss_count = 0;

@interface HttpdnsTCPSpeedTester : NSObject

- (NSDictionary<NSString *,NSNumber *> *)checkIPList:(NSArray<NSString *> *)ips;

-(void)test;
@end
