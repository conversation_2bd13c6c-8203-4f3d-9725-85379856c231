//
//  UserDefaults+Extension.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/13.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftyUserDefaults

extension DefaultsKeys {
    static let DOWNLOAD_ONLY_ON_WIFI = DefaultsKey<Bool>("WDBOOK_DOWNLOAD_ONLY_ON_WIFI",defaultValue: true)
    static let NOT_AUTO_LOCK_SCREEN_ON_READ = DefaultsKey<Bool>("WDBOOK_NOT_AUTO_LOCK_SCREEN_ON_READ",defaultValue: false)
    static let SHOW_PAPER_BOOK_PAGE_NUM = DefaultsKey<Bool>("WDBOOK_SHOW_PAPER_BOOK_PAGE_NUM",defaultValue: false)
    static let ACCEPT_DISCOUNT = DefaultsKey<Bool>("WDBOOK_ACCEPT_DISCOUNT",defaultValue: true)
    static let SELECTED_LANGUAGE = DefaultsKey<String?>("WD<PERSON><PERSON>_SELECTED_LANGUAGE",defaultValue: nil)
    static let LAST_LOGIN_COUNTRY_CODE = DefaultsKey<String?>("WDBOOK_LAST_LOGIN_COUNTRY_CODE",defaultValue: nil)
    static let LAST_LOGIN_PHONE = DefaultsKey<String?>("WDBOOK_LAST_LOGIN_PHONE",defaultValue: nil)
    static let LAST_LOGIN_EMAIL = DefaultsKey<String?>("WDBOOK_LAST_LOGIN_EMAIL",defaultValue: nil)
}

extension UserDefaults {
    static func getLastDialogTime(userId:String,method:NotificationShowMethod) -> Int64{
        let key = DefaultsKey<Int64>("NOTIFICATION_DIALOG_TIME_\(userId)_method_\(method.rawValue)",defaultValue: 0)
        return Defaults[key:key]
    }
    
    static func setLastDialogTime(userId:String,method:NotificationShowMethod){
        let key = DefaultsKey<Int64>("NOTIFICATION_DIALOG_TIME_\(userId)_method_\(method.rawValue)",defaultValue: 0)
        Defaults[key:key] = Int64(Date().timeIntervalSince1970) * 1000
    }
}

extension UserDefaults {
    func set<Element: Codable>(value: Element?, forKey key: String) {
        guard let v = value else{
            setValue(nil, forKey: key)
            return
        }
        
        let data = try? JSONEncoder().encode(v)
        setValue(data, forKey: key)
    }
    func codable<Element: Codable>(forKey key: String) -> Element? {
        guard let data = data(forKey: key) else {
            return nil
        }
        let element = try? JSONDecoder().decode(Element.self, from: data)
        return element
    }
}


//MARK: SwiftyUserDefaults的Int64扩展
public struct DefaultsInt64Bridge: DefaultsBridge {

    public init() {}

    public func save(key: String, value: Int64?, userDefaults: UserDefaults) {
        userDefaults.set(value, forKey: key)
    }

    public func get(key: String, userDefaults: UserDefaults) -> Int64? {
        if let int = (userDefaults.object(forKey: key) as? NSNumber)?.int64Value {
            return int
        }

        // Fallback for launch arguments
        if let string = userDefaults.object(forKey: key) as? String,
            let int = Int64(string) {
            return int
        }

        return nil
    }

    public func deserialize(_ object: Any) -> Int64? {
        return nil
    }
}

extension Int64: DefaultsSerializable {
    public static var _defaults: DefaultsInt64Bridge { return DefaultsInt64Bridge() }
    public static var _defaultsArray: DefaultsInt64Bridge { return DefaultsInt64Bridge() }
}
