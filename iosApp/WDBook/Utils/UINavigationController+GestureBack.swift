//
//  UINavigationController+GestureBack.swift
//  WDBook
//
//  Created by <PERSON> on 2020/12/8.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//  

import Foundation
import UIKit

extension UINavigationController: UIGestureRecognizerDelegate {
    override open func viewDidLoad() {
        super.viewDidLoad()
        interactivePopGestureRecognizer?.delegate = self
    }

    //每次滑动
    public func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        debugPrint("返回测试：gestureRecognizerShouldBegin:\(gestureRecognizer)")
        return viewControllers.count > 1
    }
    
    //没有调用
    open override func didMove(toParent parent: UIViewController?) {
        super.didMove(toParent: parent)
        debugPrint("返回测试：didMove:\(parent)")
        if parent != nil{
            Log.d("返回测试：didMove: pop成功,\(parent)")
        }
    }
}

