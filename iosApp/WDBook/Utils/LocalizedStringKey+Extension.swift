//
//  LocalizedStringKey+Extension.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/1/11.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import SwiftyUserDefaults
import shared

struct LocaleManager{
    static var currentLanguage = ""
    static var currentBundle:Bundle!
    static func checkCurrentLanguage(){
        if let savedLanguage = Defaults[key:DefaultsKeys.SELECTED_LANGUAGE]{
            currentLanguage = savedLanguage
            if let path = Bundle.main.path(forResource: currentLanguage, ofType: "lproj"),
                let bundle = Bundle(path: path) {
                currentBundle = bundle
            }
        }else{
            let language = Locale.preferredLanguages.first ?? Bundle.main.preferredLocalizations.first ?? "zh-Hans"
            if language.contains("zh-Hans"){
                currentLanguage = "zh-Hans"
            }else if language.contains("zh-Hant"){
                currentLanguage = "zh-Hant"
            }else if ["CN","MY","SG"].contains(Locale.current.regionCode){
                currentLanguage = "zh-Hans"
            }else{
                currentLanguage = "zh-Hant"
            }
            
            if let path = Bundle.main.path(forResource: currentLanguage, ofType: "lproj"),
                let bundle = Bundle(path: path) {
                currentBundle = bundle
            }
        }
        
        AppState.shared.selectedLanguage = currentLanguage
        
        //更新中间层marker语言
        let languageMode:LanguageMode
        if currentLanguage == "zh-Hans"{
            languageMode = .simplifiedchinese
        }else{
            languageMode = .traditionalchinese
        }
        WDBookSessionSDK.shared.setMarket(market: languageMode)
        
        NotificationCenter.default.post(name: ThemeSelectV.languageChangedNotification, object: nil)
//        print("地区\(Locale.current.regionCode)")
//        //马来西亚 "MY"
//        //新加坡 "SG"
//        //中国大陆 "CN"
//        //香港 "HK"
//        //台湾 "TW"
//        //澳门 "MO"
//        print("语言\(Bundle.main.preferredLocalizations)")
//        //简体中文 "zh-Hans"
//        //繁体中文 "zh-Hant"
    }
    
    static var locale:Locale{
        if currentLanguage == "zh-Hans" {
            return Locale(identifier: "zh-CN")
        }else{
            return Locale(identifier: "zh-TW")
        }
    }
}

extension String{
    var localized:String{
//        NSLocalizedString(self, comment: "")
//        LocalizedStringKey(self).toString()
        
        if let bundle = LocaleManager.currentBundle {
            return bundle.localizedString(forKey: self, value: nil, table: nil)
        }else{
            return LocalizedStringKey(self).toString()
        }
    }
    
    var nslocalized:String{
        NSLocalizedString(self, comment: "")
    }
    
    func localizedFormat(_ arguments: CVarArg...) -> String {
//        return String(format: nslocalized, arguments: arguments)
        return String(format: localized, arguments: arguments)
    }
}

extension LocalizedStringKey {
    public func toString() -> String {
        let mirror = Mirror(reflecting: self)
        
        let attributeLabelAndValue = mirror.children.first { (arg0) -> Bool in
            let (label, _) = arg0
            if(label == "key"){
                return true;
            }
            return false;
        }
        
        if(attributeLabelAndValue != nil) {
            return String.localizedStringWithFormat(NSLocalizedString(attributeLabelAndValue!.value as! String, comment: ""));
        }
        else {
            return "Swift LocalizedStringKey signature must have changed. @see Apple documentation."
        }
    }
}
