//
//  AppUtils.swift
//  WDBook
//
//  Created by <PERSON> on 2023/7/7.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftyUserDefaults
import shared

enum InstallationSource {
    case appStore
    case testFlight
    case xcode
    case unknown
}

extension DefaultsKeys {
    static let APP_DEPRECATED_VERSION = DefaultsKey<String?>("APP_DEPRECATED_VERSION", defaultValue: nil)
    static let APP_FORCE_UPGRADE = DefaultsKey<Bool>("APP_FORCE_UPGRADE", defaultValue: false)
    static let UPGRADE_ALERT_PRESENTED = DefaultsKey<Bool>("UPGRADE_ALERT_PRESENTED", defaultValue: false)
}

class AppUtils: NSObject {
    static func getInstallationSource() -> InstallationSource {
#if DEBUG
        return .xcode
#else
        guard let appStoreReceiptURL = Bundle.main.appStoreReceiptURL else {
            return .unknown
        }

        let isSandboxReceipt = appStoreReceiptURL.lastPathComponent.lowercased().contains("sandboxreceipt")
        let isSimulator = TARGET_OS_SIMULATOR != 0

        if isSandboxReceipt || isSimulator {
            return .testFlight
        } else {
            return .appStore
        }
#endif
    }
    
    static func getAppVersion() -> String {
        let versionKey = "CFBundleShortVersionString"
        let version = Bundle.main.object(forInfoDictionaryKey: versionKey) as? String
        return version ?? ""
    }

    static func getDeeplinkScheme() -> String {
        Bundle.main.object(forInfoDictionaryKey: "DeeplinkScheme") as? String ?? ""
    }
    
    static func getDeeplinkSchemeDomain() -> String {
        if let schemeStr = Bundle.main.object(forInfoDictionaryKey: "DeeplinkScheme") as? String {
            return schemeStr + "://"
        }
        return "://"
    }
    
    //MARK: - APP升级弹框相关
    static func resetAppUpgradeAlertInfo(){
        Defaults[key: DefaultsKeys.UPGRADE_ALERT_PRESENTED] = false
    }

    static func handleUpgrade(isForce: Bool, upgradeUrl: String) {
        if !isForce {
            let alertHasPresented = Defaults[key: DefaultsKeys.UPGRADE_ALERT_PRESENTED]
            if alertHasPresented {
                return
            }
        }
        Defaults[key: DefaultsKeys.APP_FORCE_UPGRADE] = isForce
        Defaults[key: DefaultsKeys.APP_DEPRECATED_VERSION] = self.getAppVersion()
        Defaults[key: DefaultsKeys.UPGRADE_ALERT_PRESENTED] = true
        showAppUpgradeAlert()
    }
    
    static func showForceUpgradeAlertIfNeed() {
        let isForceUpgrade = Defaults[key: DefaultsKeys.APP_FORCE_UPGRADE]
        guard let deprecatedVersion = Defaults[key: DefaultsKeys.APP_DEPRECATED_VERSION] else {
            return
        }
        if isForceUpgrade && deprecatedVersion == getAppVersion() {
            // 强制更新&&用户还未升级
            showAppUpgradeAlert()
        }
    }
    
    static func showAppUpgradeAlert(_ upgradeEntity: UpgradeEntity? = nil) {
        var message = "您的版本过低，请升级至最新版本继续使用。".localized
        if upgradeEntity != nil {
            message = upgradeEntity!.versionDescription
        }

        AppState.shared.alert.appUpgradeAlert(title: "版本更新提醒".localized, msg: message) {
            if isTestFlight1() {
                // 跳转TestFlight
                let urlStr = "itms-beta://testflight.apple.com/join/AlO1xmeG"
                UIApplication.shared.open(URL(string:urlStr)!)
            } else {
                // 跳转AppStore
                UIApplication.shared.open(URL(string:"https://apps.apple.com/cn/app/wei-du-shu-cheng/id1528441683")!)
            }
        } cancelHandler: {
            Log.d("取消更新，跳过该版本")
        }
    }
}
