//
//  AttributedTextView.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/4/29.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import SwiftUI

struct AttributedTextView: UIViewRepresentable {
    typealias UIViewType = UITextView
    
    @Binding var attributedText: NSAttributedString
    @Binding var height: CGFloat
    
    var linkTextAttributes: [NSAttributedString.Key: Any] = [:]
    var onLinkTapped: ((URL) -> Void)? = nil
    
    func makeUIView(context: Context) -> UITextView {
        let textView = UITextView()
        textView.attributedText = attributedText
        textView.textAlignment = .center
        textView.isEditable = false
        textView.isSelectable = true
        textView.dataDetectorTypes = .link
        textView.linkTextAttributes = linkTextAttributes
        textView.delegate = context.coordinator
        
        // Enable text wrapping
        textView.textContainer.lineFragmentPadding = 0
        textView.textContainerInset = .zero
        textView.backgroundColor = .clear
        return textView
    }
    
    func updateUIView(_ uiView: UITextView, context: Context) {
        uiView.attributedText = attributedText
        
        // Calculate the height of the text view and update the binding variable
        let size = uiView.sizeThatFits(CGSize(width: uiView.bounds.width, height: CGFloat.greatestFiniteMagnitude))
        DispatchQueue.main.async {
            height = size.height
        }
        
        // Center the text view horizontally
        uiView.frame = CGRect(x: (uiView.bounds.width - size.width) / 2, y: 0, width: size.width, height: size.height)
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(onLinkTapped: onLinkTapped)
    }
    
    class Coordinator: NSObject, UITextViewDelegate {
        let onLinkTapped: ((URL) -> Void)?
        
        init(onLinkTapped: ((URL) -> Void)? = nil) {
            self.onLinkTapped = onLinkTapped
        }
        
        func textView(_ textView: UITextView, shouldInteractWith URL: URL, in characterRange: NSRange) -> Bool {
            onLinkTapped?(URL)
            return false
        }
    }
}
