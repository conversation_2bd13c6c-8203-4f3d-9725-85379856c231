//
//  SearchBar.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/12/21.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import SnapKit
import SwiftUI

struct SearchBar: View {
    @Binding var searchText: String
    @Binding var searching: Bool

    var body: some View {
        ZStack {
            Rectangle()
                .foregroundColor(Color("LightGray"))
            HStack {
                Image(systemName: "magnifyingglass")
                TextField("Search ..", text: $searchText) { startedEditing in
                    if startedEditing {
                        withAnimation {
                            searching = true
                        }
                    }
                } onCommit: {
                    withAnimation {
                        searching = false
                    }
                }
            }
            .foregroundColor(.gray)
            .padding(.leading, 13)
        }
        .frame(height: 30)
        .cornerRadius(13)
    }
}

struct SearchBarStatic: View {
    @Binding var searchText: String

    var body: some View {
        ZStack {
            Rectangle()
                .foregroundColor(Color("LightGray"))
            HStack {
                Image(systemName: "magnifyingglass")
                TextField("搜索书名或作者".localized, text: $searchText)
                    .multilineTextAlignment(.leading)
                    .allowsHitTesting(false)
            }
            .foregroundColor(.gray)
            .padding(.leading, 13)
        }
        .frame(height: 30)
        .cornerRadius(13)
    }
}

struct SearchBarUIKit: UIViewRepresentable {
    @Binding var text: String
    @Binding var isBecomeFirstResponderWhenInit: Bool
    var placeholder: String = ""
    var isUserInteractionEnabled: Bool = true
    var searchAction: ((String) -> Void)?
    var textChangeAction: ((String) -> Void)?
    var maxLength = 0

    init(text: Binding<String> = .constant(""), placeholder: String = "", isBecomeFirstResponderWhenInit: Binding<Bool> = .constant(false), isUserInteractionEnabled: Bool = true, maxLength: Int, searchAction: ((String) -> Void)? = nil, textChangeAction: ((String) -> Void)? = nil) {
        _text = text
        _isBecomeFirstResponderWhenInit = isBecomeFirstResponderWhenInit
        self.placeholder = placeholder
        self.isUserInteractionEnabled = isUserInteractionEnabled
        self.searchAction = searchAction
        self.textChangeAction = textChangeAction
        self.maxLength = maxLength
    }

    class Coordinator: NSObject, UISearchBarDelegate {
        var contentView: SearchBarUIKit
        var maxLength: Int

        init(contentView: SearchBarUIKit, text _: Binding<String>, maxLength: Int) {
            self.contentView = contentView
            self.maxLength = maxLength
        }

        func searchBar(_: UISearchBar, textDidChange searchText: String) {
            if searchText.isEmpty {
                contentView.text = ""
            }
            contentView.textChangeAction?(searchText)
        }

        func searchBarSearchButtonClicked(_ searchBar: UISearchBar) {
            let searchText = searchBar.text ?? ""
            contentView.text = searchText
            contentView.searchAction?(searchText)
            UIApplication.dismissKeyboard()
        }

        func searchBar(_ searchBar: UISearchBar, shouldChangeTextIn range: NSRange, replacementText: String) -> Bool {
            var shouldText = (searchBar.text ?? "") as NSString
            shouldText = shouldText.replacingCharacters(in: range, with: replacementText) as NSString
            let shouldString = shouldText as String
            if shouldString.count > maxLength {
                searchBar.text = shouldString.truncate(maxLength)
                return false
            }
            return true
        }
    }

    func makeCoordinator() -> SearchBarUIKit.Coordinator {
        return Coordinator(contentView: self, text: $text, maxLength: maxLength)
    }

    func getMaxLength() -> Int {
        return maxLength
    }

    func makeUIView(context: UIViewRepresentableContext<SearchBarUIKit>) -> UISearchBar {
        let searchBar = UISearchBar(frame: .zero)
        searchBar.delegate = context.coordinator
        searchBar.placeholder = placeholder
        searchBar.searchBarStyle = .minimal
        searchBar.autocapitalizationType = .none
        searchBar.isUserInteractionEnabled = isUserInteractionEnabled
        searchBar.tintColor = UIColor(named: "wd_orange")
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.6) {
            if isBecomeFirstResponderWhenInit {
                searchBar.becomeFirstResponder()
            }
        }
        return searchBar
    }

    func updateUIView(_ uiView: UISearchBar, context _: UIViewRepresentableContext<SearchBarUIKit>) {
        if uiView.text != text {
            uiView.text = text
        }
    }
}
