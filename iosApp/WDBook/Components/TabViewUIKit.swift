//
//  TabViewUIKit.swift
//  TabViewUIKit
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2021/7/30.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import UIKit

struct TabBarUIKit: UIViewRepresentable {
    @Binding var selection:TabName
    @Binding var hasDot2:Bool //2代表索引
    
    var tabbarItems:[UITabBarItem]
    var shouldChange: ((Int)->(Bool))?

    init(selection:Binding<TabName>,hasDot2:Binding<Bool>,tabbarItems:[UITabBarItem],shouldChange:((Int)->(Bool))?) {
        _selection = selection
        _hasDot2 = hasDot2
        self.tabbarItems = tabbarItems
        self.shouldChange = shouldChange
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(pepresentable: self)
    }
    
    class Coordinator: NSObject, UITabBarDelegate {
        var pepresentable: TabBarUIKit
        
        init(pepresentable:TabBarUIKit) {
            self.pepresentable = pepresentable
        }
        
        func tabBar(_ tabBar: UITabBar, didSelect item: UITabBarItem) {
            if pepresentable.shouldChange?(item.tag) ?? true{
                AppState.shared.changeTab(tabName: TabName(rawValue: item.tag)!)
            }else{
                tabBar.selectedItem = pepresentable.tabbarItems.filter({$0.tag == pepresentable.selection.rawValue}).first
            }
        }
        
        deinit {
            NotificationCenter.default.removeObserver(self)
        }
        
        @objc func languageChanged(noti:Notification){
            debugPrint("切换语言")
            pepresentable.tabbarItems.forEach { item in
                item.title = TabName(rawValue: item.tag)?.title ?? ""
            }
        }
    }
    
    func makeUIView(context: Context) -> UIView{
        let tabbar = UITabBar()
        tabbar.setItems(tabbarItems, animated: false)
        tabbar.delegate = context.coordinator
        tabbar.selectedItem = tabbarItems.filter({$0.tag == selection.rawValue}).first
        tabbar.backgroundColor = dynamicBackgroundColor4
        tabbar.backgroundImage = UIImage()
        UITabBar.appearance().unselectedItemTintColor = dynamicTitleColor2
        UITabBar.appearance().tintColor = UIColor.primaryColor1
//        UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.foregroundColor: UIColor.primaryColor1], for: .selected)
//        UITabBarItem.appearance().setTitleTextAttributes([NSAttributedString.Key.foregroundColor: dynamicTitleColor2], for: .normal)
          
//        tabBarItem.setTitleTextAttributes([NSForegroundColorAttributeName : UIColor.red], for: .highlighted)
//        tabBarItem.setTitleTextAttributes([NSForegroundColorAttributeName : UIColor.red], for: .highlighted)
        NotificationCenter.default.addObserver(context.coordinator, selector: #selector(Coordinator.languageChanged(noti:)), name: ThemeSelectV.languageChangedNotification, object: nil)
        return tabbar
    }

    func updateUIView(_ view: UIView, context: Context) {
        let tabbar = view as! UITabBar
        tabbar.selectedItem = tabbar.items?.filter({$0.tag == selection.rawValue}).first
        //TODO: 3.1放开注释。
//        if hasDot2{
//            tabbar.addRedDot(2)
//        }else{
//            tabbar.removeRedDot(2)
//        }
    }

}
