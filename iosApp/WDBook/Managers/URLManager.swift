//
//  URLManager.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/31.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import shared
import UIKit

class URLManager {
    static let shared: URLManager = .init()

    private init() {}

    func allowScheme(_ schema: String) -> <PERSON><PERSON> {
        ["wdbook", "wdbooktest", "wdbookbeta"].contains(schema.lowercased())
    }

    func allowHost(_ host: String) -> <PERSON><PERSON> {
//        ["wdbook.com","wdbooktest.com"].contains(host.lowercased())
        ["wdbooktest.com"].contains(host.lowercased())
    }

    func allowURL(_ url: URL) -> Bool {
        allowScheme(url.scheme ?? "") || allowHost(url.host ?? "")
    }

    // MARK: 解析 widget,

    func parseWidgetTypeUrlView(widget: NewWidgetDetailEntity) {
//        let paramsDic = widget.widgetViewParam.components(separatedBy: "\n")
//            .reduce([String:String]()) { (result, paramValueStr) -> [String:String] in
//            let paramValueArr = paramValueStr.components(separatedBy: "=")
//            if paramValueArr.count >= 2 {
//                var r = result
//                r[paramValueArr[0]] = paramValueArr[1]
//                return r
//            }else{
//                return result
//            }
//        }

        if let data = widget.widgetViewParam.data(using: .utf8) {
            let decoder = JSONDecoder()
            if let paramsDic = try? decoder.decode(BinnerUrlModel.self, from: data) {
                guard let urlStr = paramsDic.dataSource, !urlStr.isEmpty, let url = URL(stringRobustness: urlStr) else {
                    return
                }

                if urlStr.lowercased().contains("https://") || urlStr.lowercased().contains("http://") {
                    if WDBookSessionSDK.shared.isLogin {
                        var urlDic = url.params()
                        WDBookAppSDK.shared.getTokenNonce { result in
                            switch result {
                            case let .success(tokenNonce):
                                if let nonce = tokenNonce {
                                    urlDic["nonce"] = nonce
                                }
                            case let .failure(error):
                                if let sdkException = error as? SDKException {
                                    print(sdkException)
                                } else {
                                    print(error)
                                }
                            }
                            if let newURL = url.withQueries(urlDic) {
                                self.openURL(urlString: newURL.absoluteString, title: "", openWithBrowser: paramsDic.openWithBrowser == 1)
                            }
                        }
                    } else {
                        openURL(urlString: urlStr, title: "", openWithBrowser: paramsDic.openWithBrowser == 1)
                    }

                } else if url.scheme! == WDBookAppSDK.shared.wdBookSchema || url.host == WDBookAppSDK.shared.wdBookSchemaHost {
                    parseWDBookUrl(url: url)
                    Log.i(paramsDic)
                }
            }
        }
    }

    func openURL(urlString: String, title: String, openWithBrowser: Bool) {
        guard let url = URL(string: urlString) else {
            return
        }
        debugPrint(urlString)
        if openWithBrowser {
            if UIApplication.shared.canOpenURL(url) {
                UIApplication.shared.open(url, options: [:], completionHandler: nil)
            }
        } else {
            // 打开webview。
            RoutableManager.push(LightWebview(url: url.absoluteString, title: title))
        }
    }

    func parseWDBookUrl(url: URL, duration: Int = 0) {
        guard allowURL(url) else {
            return
        }

        // 授权登录
        if url.host == "authlogin" {
            let params = url.params()
            if let success = params["success"] as? String, success == "true" {
                // FIXME: 是否要删掉Token
                let token = Token(accessToken: params["accessToken"]! as! String,
                                  refreshToken: params["refreshToken"]! as! String,
                                  userId: params["userid"]! as! String,
                                  expiredIn: Int64(params["expiredIn"] as! String)!,
                                  refreshTokenExpiredIn: Int64(params["refreshTokenExpiredIn"] as! String)!)
                do {
                    let data = try JSONEncoder().encode(token)
                    if let jsonString = String(data: data, encoding: .utf8) {
                        WDBookSessionSDK.shared.setUserTokenString(tokenString: jsonString)
                        AppState.shared.tokenString = jsonString
                        AppState.shared.refreshUserInfo(forceRefresh: false)
                        // 登录成功以后
                        HUDManager.showLoadingBlockHUD(text: "")
                        WDBookUserSDK.shared.checkAndAddDevice(deviceId: WDBookSessionSDK.shared.deviceId) { result in
                            HUDManager.hideLoadingHUD()
                            switch result {
                            case let .success(result):
                                if result == 1312 {
                                    RoutableManager.showDeviceManagerV()
                                    DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                                        AppState.shared.hideLoginRegisterV(isClearCache: false)
                                    }
                                } else {
                                    WDBookDataSyncManager.shared.loginComplete()
                                    Toaster.showToast(message: "授权成功".localized, duration: 1.5) { _ in
                                        AppState.shared.hideLoginRegisterV(isSuccess: true)
                                    }
                                }
                            case let .failure(error):
                                print("添加设备信息出错：\(error)。仍然弹出登录成功")

                                WDBookDataSyncManager.shared.loginComplete()
                                Toaster.showToast(message: "授权成功".localized, duration: 1.5) { _ in
                                    AppState.shared.hideLoginRegisterV(isSuccess: true)
                                }
                            }
                        }
                    }
                } catch {
                    Toaster.showToast(message: "授权失败".localized) { _ in
                        AppState.shared.hideLoginRegisterV()
                    }
                }
            } else {
                Toaster.showToast(message: "授权失败".localized) { _ in
                    AppState.shared.hideLoginRegisterV()
                }
            }

        } else {
            var nativePath = ""
            if allowHost(url.host ?? "") {
                nativePath = url.path
            } else if allowScheme(url.scheme ?? "") {
                nativePath = url.absoluteString.replacingOccurrences(of: url.scheme! + ":/", with: "")
            }
            if duration > 0 {
                DispatchQueue.main.asyncAfter(deadline: .now().advanced(by: .seconds(duration))) {
                    RoutableManager.navigate(toPath: nativePath)
                }
            } else {
                RoutableManager.navigate(toPath: nativePath)
            }
        }
    }
}

struct BinnerUrlModel {
    var dataSource: String?
    var openWithBrowser: Int = 0
}

extension BinnerUrlModel: Decodable {
    enum CodingKeys: String, CodingKey {
        case dataSource
        case openWithBrowser
    }

    init(from decoder: Decoder) throws {
        // 处理name、age和hobby的解码
        let rootContainer = try? decoder.container(keyedBy: CodingKeys.self)
        dataSource = try? rootContainer?.decode(String?.self, forKey: .dataSource)
        openWithBrowser = (try? rootContainer?.decode(Int.self, forKey: .openWithBrowser)) ?? 0
    }
}

extension URL {
    func withQueries(_ queries: [String: Any]) -> URL? {
        var components = URLComponents(url: self, resolvingAgainstBaseURL: false)
        components?.queryItems = queries.map {
//            URLQueryItem(name: $0.key, value: $0.value as? String)
            URLQueryItem(name: $0.key, value: String(describing: $0.value))
        }
        return components?.url
    }
}

extension URL {
    init?(stringRobustness: String) {
        if let urlwithPercentEscapes = stringRobustness.trim().addingPercentEncoding(withAllowedCharacters: .urlQueryAllowed) {
            self.init(string: urlwithPercentEscapes)
        } else {
            return nil
        }
    }
}
