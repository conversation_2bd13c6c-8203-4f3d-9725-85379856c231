//
//  File.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/5/13.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation
import shared
import SwiftUI

let Store_UpdateTime = "store_data_updateTime"
let Account_UpdateTime = "account_data_updateTime"
let Data_Update_Interval = 60*5 // 点击tab，数据更新时间区间，此处修改时长

enum SyncDataType {
    case dataDefault
    case dataRechargeSuccess
    case dataLoginSuccess
    case dataAppSetup
}

class WDBookDataSyncManager {
    // 单例
    static let shared = WDBookDataSyncManager()
    var storeUpdateTime: Int64
    var accountUpdateTime: Int64
    
    // 私有化构造方法，不允许外部创建
    private init() {
        storeUpdateTime = 0
        accountUpdateTime = 0
    }
    
    func syncDataFromServer() {
        syncDataWithHomeType(tabType: .store)
        if WDBookSessionSDK.shared.isLogin {
            syncAccountData(syncType: .dataAppSetup)
        }
    }

    func syncDataWithHomeType(tabType: TabName) {
        switch tabType {
        case .bookshelf:
            syncAccountData(syncType: .dataDefault)
        case .account:
            syncAccountData(syncType: .dataDefault)
            
            //不需要等待5分钟，立刻同步
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.refreshFeedbackStatus()
                AppState.shared.refreshNotificationStatus()
                AppState.shared.refreshCouponCount()
            }
        case .store:
            syncStoreData()
        default:
            break
        }
    }
    
    func syncStoreData() {
        Log.d("-----storeData发起同步")
        if isDataNeedUpdate(tabType: TabName.store) {
            Log.d("-----storeData终止同步")
            return
        }
        Log.d("-----storeData开始同步")
        updateSyncTime(tabType: TabName.store)

        //syncAppData中内容，换成iOS单独调用
        AppState.shared.syncAndRefreshWidgets()
        WDBookDataSyncManager.shared.syncCategoryListData()
//        WDBookSyncSDK.shared.syncAppData { [self] result in
//            switch result {
//            case .success(let list):
//                handleSyncResultList(list: list)
//            case .failure(let error):
//                handleError(error: error)
//            }
//        }
    }
    
    func syncAccountData(syncType: SyncDataType) {
        // default , recharge , pay
        Log.d("-----accountData发起同步")
        WDBookSyncSDK.shared.syncWalletBalance { [unowned self] result in
            handleResponseResult(result: result)
        }
        if syncType == .dataRechargeSuccess {
            WDBookSyncSDK.shared.syncWalletTransactions { [unowned self] result in
                handleResponseResult(result: result)
            }
        } else {
            if syncType == .dataDefault && isDataNeedUpdate(tabType: TabName.account) {
                return
            }
            updateSyncTime(tabType: TabName.account)
            

            AppState.shared.refreshUserInfo(forceRefresh: false)
            DispatchQueue.global().async {
                let forceRefresh = WDReachability().isWDAPIReachability(hostURL: WDBookSessionSDK.shared.wdBookEndpoint())
                AppState.shared.refreshUserInfo(forceRefresh: forceRefresh)
            }
            
            WDBookSyncSDK.shared.syncWalletBalance { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncWalletTransactions { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncPurchasedData { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncShelfList { [unowned self] result in
                switch result {
                case .success(let list):
                    handleSyncResultList(list: list)
                case .failure(let error):
                    handleError(error: error)
                }
            }
            
            WDBookSyncSDK.shared.syncUpdatedResourceData { [unowned self] result in
                handleResponseResult(result: result)
            }

            WDBookSyncSDK.shared.syncReadProgressData { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncBookmarkData { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncNoteData { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            var typeList:[KotlinInt]? = nil
            if AppState.shared.acceptDiscout {
                typeList = nil
            }else{
                typeList = [KotlinInt.init(value: Int32(NotificationMsgType.platform.rawValue))]
            }
            
            WDBookSyncSDK.shared.syncAllNotificationData(typeList: typeList) {[unowned self] result in
                handleResponseResult(result: result)
            }
            
        }
    }
    
    func updateShelfBookResourceToServe(){
        WDBookSyncSDK.shared.updateShelfBookResourceToServe { [unowned self] result in
            switch result {
            case .success: break
            case .failure(let error):
                handleError(error: error)
            }
        }
    }

    func syncUserActionData() {
        if WDBookSessionSDK.shared.isLogin {
            WDBookSyncSDK.shared.syncNoteData { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncBookmarkData { [unowned self] result in
                handleResponseResult(result: result)
            }
            
            WDBookSyncSDK.shared.syncReadProgressData { [unowned self] result in
                handleResponseResult(result: result)
            }
        }
    }
    
    func isLogin() -> Bool {
        return WDBookSessionSDK.shared.isLogin
    }
    
    func signOut(msg: String?, complete: (() -> ())? = nil) {
        logout()
        complete?()
    }
    
    // 登录成功以后的方法
    func loginComplete() {
        if WDBookSessionSDK.shared.isLogin {
            WDBookDataSyncManager.shared.syncAccountData(syncType: .dataLoginSuccess)
            loginRefresh()
            NotificationCenter.default.post(name: AuthLoginV.loginSuccessNotification, object: nil)
        }
    }
    
    private func loginRefresh() {
        AppState.shared.refreshShelfList()
        AppState.shared.refreshFeedbackStatus()
        AppState.shared.refreshNotificationStatus()
        
        AppState.shared.refreshNoteCountStr()
        AppState.shared.refreshPurchasedList()
        AppState.shared.refreshFavoriteListCount()
        AppState.shared.refreshWalletBalance()
        AppState.shared.refreshCouponCount()
    }
    
    private func logout() {
        deleteUserDevice(deviceId: WDBookSessionSDK.shared.deviceId)
        WDBookSessionSDK.shared.logout { result in
            switch result {
            case .failure(let error):
                print(error.localizedDescription)
                AppState.shared.cleanup()
            case .success:
                AppState.shared.cleanup()
            }
        }
    }
    
    func deleteUserDevice(deviceId: String) {
        WDBookUserSDK.shared.deleteUserDevice(deviceId: deviceId) { result in
            switch result {
            case .success:
                break
            case .failure(let error):
                print(error)
            }
        }
    }

    func updateSyncTime(tabType: TabName) {
        let defaults = UserDefaults.standard
        let timeStamp = getCurrentTimeStamp()
        switch tabType {
        case .store:
            defaults.setValue(timeStamp, forKey: Store_UpdateTime)
        case .bookshelf, .account:
            defaults.setValue(timeStamp, forKey: Account_UpdateTime)
        default:
            break
        }
    }
    
    func syncShelfList(_ onSuccess: (()->())?){
        WDBookSyncSDK.shared.syncShelfList { [unowned self] result in
            switch result {
            case .success:
                AppDownloadManager.shared.checkToStart()
                onSuccess?()
                break
            case .failure(let error):
                handleError(error: error)
            }
        }
    }
    
    // 避免网络请求太频繁，五分钟内最多同步数据一次
    func isDataNeedUpdate(tabType: TabName) -> Bool {
        var result = true
        let currentTime = getCurrentTimeStamp()
        var lastUpdateTime: Int64
        switch tabType {
        case .store:
            lastUpdateTime = Int64(UserDefaults.standard.integer(forKey: Store_UpdateTime))
        case .bookshelf, .account:
            lastUpdateTime = Int64(UserDefaults.standard.integer(forKey: Account_UpdateTime))
        default:
            lastUpdateTime = 0
        }
        if Data_Update_Interval < currentTime - lastUpdateTime {
            result = false
        }
        return result
    }
    
    func getCurrentTimeStamp() -> Int64 {
        let timeInterval: TimeInterval = Date().timeIntervalSince1970
        let timeStamp = Int64(timeInterval)
        return timeStamp
    }
    
    private func handleSyncResultList(list: [SyncResult]?) {
        guard let list = list else {
            return
        }
        for syncResult in list {
            if syncResult.count > 0 {
                handleResponseResult(result: syncResult)
            }
        }
    }
    
    private func handleResponseResult(result: Result<SyncResult?, SDKError>) {
        switch result {
        case .success(let result):
            handleResponseResult(result: result!)
        case .failure(let error):
            handleError(error: error)
        }
    }
    
    private func handleResponseResult(result: SyncResult) {
        switch result.syncType {
        case .bookmark:
            Log.d("Update bookmark")
        case .note:
            Log.d("Update note")
            AppState.shared.refreshNoteCountStr()
        case .widgetcontainer, .widgetdetail:
            Log.d("性能测试：Update widget container。\(result.syncType)")
        case .bookcategory, .bookcategoryproductcount:
            Log.d("性能测试：Update book category。\(result.syncType)")
        case .purchaseddata:
            Log.d("Update purchased data")
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.purchasedListResources.removeAll()
                AppState.shared.refreshPurchasedList()
                AppDownloadManager.shared.checkToStart()
            }
            
        case .shelfbook,.shelfarchive:
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.reloadShelfData()
                AppDownloadManager.shared.checkToStart()
            }
            
        case .readprogress:
            Log.d("Update shelfBook list")
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.reloadShelfData()
            }

        case .inapppurchaseproduct:
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.refreshIapPurchaseProducts()
            }
            
        case .walletbalance:
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.refreshWalletBalance()
            }
            
        case .notificationmessages:
            Log.d("Update notificationmessages list")
            if WDBookSessionSDK.shared.isLogin{
                AppState.shared.refreshNotificationStatus()
                
                //测试
    //            WDBookUserSDK.shared.makeNotificationReadStatusAll(readStatus: 0)
                
                AppState.shared.checkNotificationRemindWhenReload(method: .banner)
                AppState.shared.checkNotificationRemindWhenReload(method: .dialog)
            }
        case .updatedresource:
            if WDBookSessionSDK.shared.isLogin{
                AppDownloadManager.shared.checkToStart()
            }
        default:
            Log.d("Other")
        }
    }
    
    private func handleError(error: SDKError) {
        if let sdkException = error as? SDKException {
            Log.d(sdkException)
        } else {
            Log.d(error)
        }
    }
    
    //kmm接口按照顺序执行。只针对最后一次及时间最大的一次进行刷新。
    @Atomic var syncReloadTimes:[Double] = []
    func syncShelfRelateAndDownload(resourceId:String = "",progress:Int = 0,complete:(()->())? = nil){
        let t = CFAbsoluteTimeGetCurrent()
        syncReloadTimes.append(t)
        
        let group = DispatchGroup()
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            Log.d("start同步书架,resourceId:\(resourceId),progress:\(progress)")
            WDBookSyncSDK.shared.syncShelfList { result in
                Log.d("end同步书架,resourceId:\(resourceId),progress:\(progress)")
                group.leave()
            }
        }))
                   
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            Log.d("start同步阅读记录,resourceId:\(resourceId),progress:\(progress)")
            WDBookSyncSDK.shared.syncReadProgressData { result in
                Log.d("end同步阅读记录,resourceId:\(resourceId),progress:\(progress)")
                group.leave()
            }
        }))
        
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            WDBookSyncSDK.shared.syncUpdatedResourceData { result in
                group.leave()
            }
        }))
        
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            WDBookSyncSDK.shared.syncPurchasedData { result in
                group.leave()
            }
        }))
        
        
        group.notify(queue: DispatchQueue.main) {[weak self] in
            guard let self = self else {return}
            if let max = self.syncReloadTimes.max(),max == t{
                Log.d("end同步阅读记录后,重新加载书架,resourceId:\(resourceId),progress:\(progress)")
                AppState.shared.reloadShelfData()
                AppState.shared.purchasedListResources.removeAll()
                AppState.shared.refreshPurchasedList()
                
                AppDownloadManager.shared.checkToStart()
            }
            
            self.syncReloadTimes.removeAll{$0 == t}
            complete?()
        }
    }
    
    func syncCategoryListData(complete:(()->())? = nil){
        WDBookSyncSDK.shared.syncBookCategoryData{ result in
            WDBookSyncSDK.shared.syncBookCategoryProductCount{ result in
                AppState.shared.refreshCategoryList()
                complete?()
            }
        }
    }
    
    func syncWhenBuyNewBook(complete:(()->())? = nil){
        let group = DispatchGroup()
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            WDBookSyncSDK.shared.syncShelfList { result in
                group.leave()
            }
        }))
                   
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            WDBookSyncSDK.shared.syncReadProgressData { result in
                group.leave()
            }
        }))
        
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            WDBookSyncSDK.shared.syncUpdatedResourceData { result in
                group.leave()
            }
        }))
        
        group.enter()
        DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
            WDBookSyncSDK.shared.syncPurchasedData { result in
                group.leave()
            }
        }))
        
        WDBookSyncSDK.shared.syncWalletBalance { result in
            AppState.shared.refreshWalletBalance()
        }
        
        group.notify(queue: DispatchQueue.main) {
            AppState.shared.reloadShelfData()
            AppState.shared.purchasedListResources.removeAll()
            AppState.shared.refreshPurchasedList()
            
            AppDownloadManager.shared.checkToStart()
            complete?()
        }
    }
}
