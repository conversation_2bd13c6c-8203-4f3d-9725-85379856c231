//
//  WDRoutableModule.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2022/4/19.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import NavigationRouter
import shared
import SwiftUI
import ZIPFoundation

enum RouterName: String {
    case root = "/"
    case tabStore = "/store"
    case tabShelf = "/shelf"
    case tabAccount = "/mine"

    case browser = "/browser"
    case productDetail = "/product/detail/:productid" // **************
    case dp = "/dp/:productid"
    case aboutus = "/aboutus"
    case agreement = "/agreement"
    case privacyPolicy = "/privacy-policy"
    case labPage = "/enableLabPage"

    case columnWithId = "/store/column/:detailid" // **************
    case widgetWithContainerId = "/store/featured/:containerid" // **************
    case search = "/store/search"
    case shelfSearch = "/shelf/search"
    case category = "/store/category" // 分类列表
    case categoryWithId = "/store/category/:categoryid" // 全部书籍的某个分类
    case editorWithId = "/editor/:editorid"
    case publisherWithId = "/publisher/:publisherid"

    // MARK: 需要登录

    case login = "/mine/login"
    case favorites = "/mine/favorites"
    case purchasedBooks = "/mine/purchased-books"
    case balance = "/account/balance"
    case recharge = "/account/recharge"
    case rechargeRecords = "/account/recharge-records"
    case notesBooks = "/mine/notes/books"
    case notesBooksWithId = "/mine/notes/books/:resourceid" // wthfcr_jd_2021
    case notesRecycleWithResourceId = "/mine/notes/recycling/books/:resourceid" // wthfcr_jd_2021

    // 帮助与反馈
    case help = "/mine/help"
    case helpArticleWithId = "/mine/help/:articleid"
    case feedbacks = "/mine/feedbacks"
    case feedbackCreate = "/mine/feedbacks/create" // 参数entrance 1:个人中心 2:支付流程 3:阅读纠错
    case feedbackWithId = "/mine/feedbacks/:feedbackid"

    // 通知
    case messages = "/mine/messages"
    case messageWithId = "/mine/messages/:messageid" // mine/messages/{messageId}?type=1
    case messagesTypeWithId = "/mine/messages/types/:typeid"

    case purchaseRecords = "/account/purchase-records" // 购买记录

    // 活动
    case activityWithId = "/activity/:activityid"
    case couponsCenter = "/native/coupons" // 优惠券中心

    // 阅读书籍内部
    case readerWithId = "/reader/:resourceid" // wthfcr_jd_2021
    case readerBookmarksWithId = "/mine/bookmarks/books/:resourceid" // 第一次没有出现笔记列表。

    // 内部router
    case setting = "/native/setting"
    case deleteAccount = "/native/deleteAccount"
//    case loginPhone = "/native/loginphone"
//    case countryCode = "/native/countrycode"
    case register = "/native/register"
    case editUserInfo = "/native/userinfo/edit"
    case editNickname = "/native/userinfo/editnickname"
    case accountAndSecurity = "/native/accountandsecurity"
    case checkpwdWithPageActionType = "/native/accountandsecurity/checkpwd/:pageactiontype" // SecutiryVerifyV.PageActionType
    case bindingMobile = "/native/accountandsecurity/bindingmobile"
    case resetpwdWithOldPwd = "/native/accountandsecurity/resetpwd:oldpwd"

    func withParam(_ pathParam: Any, queryParams: [String: Any]? = nil) -> String {
        let first = rawValue.split(separator: ":").first ?? ""
        let path = first + "\(pathParam)"

        if let params = queryParams, params.count > 0,
           let url = URL(string: String(path)),
           let urlWithParams = url.withQueries(params)
        {
            return urlWithParams.absoluteString
        }

        return String(path)
    }
}

public final class WDRoutableModule: RoutableModule {
    public init() {}

    public func setup() {}

    public func registerRoutes() {
        // MARK: Tab

        NavigationRouter.bind(route: NavigationRoute(route: .root) { _ in
            RoutableManager.popToRoot()
            AppState.shared.changeTab(tabName: .store)
        })

        NavigationRouter.bind(route: NavigationRoute(route: .tabStore) { _ in
            RoutableManager.popToRoot()
            AppState.shared.changeTab(tabName: .store)
        })

        NavigationRouter.bind(route: NavigationRoute(route: .tabShelf) { _ in
            guard WDBookSessionSDK.shared.isLogin else {
                RoutableManager.popToRoot()
                AppState.shared.showLoginRegisterV(returnTabAfterLogin: .bookshelf)
                return
            }

            RoutableManager.popToRoot()
            AppState.shared.changeTab(tabName: .bookshelf)
        })

        NavigationRouter.bind(route: NavigationRoute(route: .tabAccount) { _ in
            guard WDBookSessionSDK.shared.isLogin else {
                RoutableManager.popToRoot()
                AppState.shared.showLoginRegisterV(returnTabAfterLogin: .account)
                return
            }

            RoutableManager.popToRoot()
            AppState.shared.changeTab(tabName: .account)
        })

        // MARK: 内置url

        NavigationRouter.bind(route: NavigationRoute(route: .browser) { params in
            if let url = params?["url"] {
                RoutableManager.push(LightWebview(url: url, title: ""))
            }
        })

        // MARK: store

        NavigationRouter.bind(route: NavigationRoute(route: .columnWithId) { params in
            if let str = params?["detailid"], let detailid = Int64(str) {
                if let entity = WDBookAppSDK.shared.getWidgetDetailDataEntity(detailId: detailid) {
                    RoutableManager.showWidgetSectionList(title: entity.widgetTitle, detailEntity: entity)
                } else {
                    Toaster.showToast(message: "该板块已下线")
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .widgetWithContainerId) { params in
            if let str = params?["containerid"], let containerid = Int64(str) {
                if let container = WDBookAppSDK.shared.getWidgetCombineEntity(containerId: containerid),
                   let entity = container.detailEntityList?.first
                {
                    RoutableManager.showWidgetSectionList(title: container.containerTitle ?? "", detailEntity: entity)
                } else {
                    Toaster.showToast(message: "该板块已下线")
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .productDetail) { params in
            if let productIdStr = params?["productid"], let productId = Int64(productIdStr) {
                RoutableManager.push(ProductDetailsV(productId: productId))
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .dp) { params in
            if let productIdStr = params?["productid"], let productId = Int64(productIdStr) {
                RoutableManager.push(ProductDetailsV(productId: productId))
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .search) { params in
            RoutableManager.push(SearchListV(searchText: params?["query"] ?? ""))
        })

        NavigationRouter.bind(route: NavigationRoute(route: .shelfSearch) { _ in
            BookShelfSearchState.shared.clearSearch()
            let searchView = BookShelfSearchView(dismissAction: {
                Log.d("Dismiss action called within route handler - should be handled by presented view?")
            })

            RoutableManager.push(searchView)
        })

        NavigationRouter.bind(route: NavigationRoute(route: .category) { _ in
            RoutableManager.push(CategoryListV())
        })

        NavigationRouter.bind(route: NavigationRoute(route: .categoryWithId) { params in
            let categoryId = Int64(params?["categoryid"] ?? "") ?? 0
            RoutableManager.push(CategoryPageV(categoryId: categoryId))
        })

        // MARK: 个人中心

        NavigationRouter.bind(route: NavigationRoute(route: .aboutus) { _ in
            RoutableManager.push(AboutUsV())
        })

//        NavigationRouter.bind(route: NavigationRoute(route: .labPage) { _ in
//            RoutableManager.push(WDEnableLabView())
//        })
        
        NavigationRouter.bind(route: NavigationRoute(route: .labPage) { _ in
        })

        NavigationRouter.bind(route: NavigationRoute(route: .agreement) { _ in
            HUDManager.showLoadingBlockHUD(text: "")
            WDBookAppSDK.shared.getBookAgreementURL { result in
                HUDManager.hideLoadingHUD()
                switch result {
                case let .success(url):
                    if let u = url,!u.isEmpty {
                        RoutableManager.push(LightWebviewWithNav(url: u, title: "微读书城用户协议".localized))
                    }
                case .failure:
                    break
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .privacyPolicy) { _ in
            HUDManager.showLoadingBlockHUD(text: "")
            WDBookAppSDK.shared.getBookPrivacyURL { result in
                HUDManager.hideLoadingHUD()
                switch result {
                case let .success(url):
                    if let u = url,!u.isEmpty {
                        RoutableManager.push(LightWebviewWithNav(url: u, title: "微读书城隐私政策".localized))
                    }
                case .failure:
                    break
                }
            }
        })

        // MARK: 需要登录

        NavigationRouter.bind(route: NavigationRoute(route: .login) { _ in
            guard WDBookSessionSDK.shared.isLogin else {
                AppState.shared.isShowLoginV = true
                return
            }
            Toaster.showToast(message: "你已登录")
        })

        NavigationRouter.bind(route: NavigationRoute(route: .register) { _ in
            AppState.shared.isShowRegisterV = true
        })

        NavigationRouter.bind(route: NavigationRoute(route: .editUserInfo) { _ in
            Self.needLogin {
                RoutableManager.push(UserInfoEditV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .editNickname) { _ in
            Self.needLogin {
                RoutableManager.push(NicknameEditV(nickname: AppState.shared.userInfo.nickname ?? ""))
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .favorites) { _ in
            Self.needLogin {
                RoutableManager.push(FavoriteListV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .purchasedBooks) { _ in
            Self.needLogin {
                RoutableManager.push(PurchasedListV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .balance) { _ in
            Self.needLogin {
                RoutableManager.push(WalletBalanceV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .recharge) { _ in
            Self.needLogin {
                RoutableManager.push(WalletBalanceV(needShowRecharge: true))
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .rechargeRecords) { _ in
            Self.needLogin {
                RoutableManager.push(WalletTranscationsV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .purchaseRecords) { _ in
            Self.needLogin {
                RoutableManager.push(OrderListV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .notesBooks) { _ in
            Self.needLogin {
                RoutableManager.push(NoteCollectionV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .notesBooksWithId) { params in
            Self.needLogin {
                if let resourceid = params?["resourceid"] {
                    Self.needBuyBook(resourceid) {
                        RoutableManager.push(BookNoteListV(resourceId: resourceid))
                    }
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .notesRecycleWithResourceId) { params in
            Self.needLogin {
                if let resourceid = params?["resourceid"] {
                    Self.needBuyBook(resourceid) {
                        RoutableManager.push(NotesRecycleBinListView(resourceId: resourceid).ignoresSafeArea())
                    }
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .help) { _ in
            RoutableManager.push(QuestionListV())
        })

        NavigationRouter.bind(route: NavigationRoute(route: .helpArticleWithId) { params in
            if let param = params?["articleid"], let articleid = Int64(param) {
                RoutableManager.push(HelpArticleV(articleId: articleid))
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .feedbacks) { _ in
            Self.needLogin {
                RoutableManager.push(FeedbackListV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .feedbackCreate) { params in
            Self.needLogin {
                if let param = params?["entrance"], let entrance = Int(param), [1, 2, 3].contains(entrance) {
                    RoutableManager.push(FeedbackCreateV(entrance: entrance))
                } else {
                    RoutableManager.push(FeedbackCreateV())
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .feedbackWithId) { params in
            Self.needLogin {
                if let param = params?["feedbackid"], let feedbackid = Int64(param) {
                    RoutableManager.push(FeedbackDetailV(feedbackId: feedbackid))
                }
            }
        })

        // 通知
        NavigationRouter.bind(route: NavigationRoute(route: .messages) { _ in
            Self.needLogin {
                RoutableManager.push(NotificationCenterV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .messageWithId) { params in
            Self.needLogin {
                if let param = params?["messageid"], let messageid = Int64(param),
                   let paramTypeId = params?["type"], let typeid = Int(paramTypeId), let type = NotificationMsgType(rawValue: typeid)
                {
                    RoutableManager.push(NotificationDetailsV(messageId: messageid, type: type))
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .messagesTypeWithId) { params in
            Self.needLogin {
                if let param = params?["typeid"], let typeid = Int(param), let type = NotificationMsgType(rawValue: typeid) {
                    RoutableManager.push(NotificationListV(type: type))
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .activityWithId) { params in
            if let param = params?["activityid"], let activityid = Int64(param) {
                if !isTestFlight1() {
                    RoutableManager.push(PromotionDetailsV(activityId: activityid))
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .couponsCenter) { _ in
            Self.needLogin {
                if !isTestFlight1() {
                    RoutableManager.push(CouponCenterV())
                }
            }
        })

        // MARK: 阅读器

        NavigationRouter.bind(route: NavigationRoute(route: .readerWithId) { params in
            Self.needLogin {
                if let resourceid = params?["resourceid"] {
                    RoutableManager.pushBookReader(resourceId: resourceid)
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .readerBookmarksWithId) { params in
            Self.needLogin {
                if let resourceid = params?["resourceid"] {
                    RoutableManager.pushBookReader(resourceId: resourceid, needJumpBookmark: true)
                }
            }
        })

        // MARK: 内部路由

        NavigationRouter.bind(route: NavigationRoute(route: .setting) { _ in
            RoutableManager.push(SettingV())
        })

        NavigationRouter.bind(route: NavigationRoute(route: .deleteAccount) { _ in
            Self.needLogin {
                RoutableManager.push(DeleteAccountV())
            }
        })

//        NavigationRouter.bind(route: NavigationRoute(route: .loginPhone, { params in
//            RoutableManager.push(PhoneLoginV())
//        }))

        NavigationRouter.bind(route: NavigationRoute(route: .accountAndSecurity) { _ in
            Self.needLogin {
                RoutableManager.push(AccountAndSecurityV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .checkpwdWithPageActionType) { params in
            Self.needLogin {
                if let param = params?["pageactiontype"], let type = Int(param), let enumType = SecutiryVerifyV.PageActionType(rawValue: type) {
                    RoutableManager.push(SecutiryVerifyV(pageActionType: enumType))
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .bindingMobile) { _ in
            Self.needLogin {
                RoutableManager.push(BindingPhoneV())
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .resetpwdWithOldPwd) { params in
            Self.needLogin {
                if let oldpwd = params?["oldpwd"] {
                    RoutableManager.push(ResetPwdV(oldPwd: oldpwd))
                }
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .editorWithId) { params in
            if let param = params?["editorid"], let editorid = Int64(param) {
                RoutableManager.push(EditorDetailsPageV(editorId: editorid))
            }
        })

        NavigationRouter.bind(route: NavigationRoute(route: .publisherWithId) { params in
            if let param = params?["publisherid"], let publisherid = Int64(param) {
                RoutableManager.push(PublisherDetailsPageV(publisherId: publisherid))
            }
        })
    }

    // loginCompleteHandler的管理交给AppState.shared.hideLogin()函数
    static func needLogin(_ complete: @escaping () -> Void) {
        guard WDBookSessionSDK.shared.isLogin else {
            AppState.shared.showLoginRegisterV(loginComplete: complete)
            return
        }
        complete()
    }

    static func needBuyBook(_ resourceId: String, _ complete: @escaping () -> Void) {
        guard AppState.shared.hasBuyBook(resourceId: resourceId) else {
            Toaster.showToast(message: "你还没购买本书，请去商城购买。".localized)
            return
        }

        complete()
    }
}

extension NavigationRoute {
    init(route: RouterName,
         _ completeHandler: RouteCompleteHandler? = nil,
         type: Routable.Type? = nil,
         requiresAuthentication: Bool = true,
         allowedExternally: Bool = false)
    {
        self.init(path: route.rawValue, completeHandler, type: type, requiresAuthentication: requiresAuthentication, allowedExternally: allowedExternally)
    }
}

extension RoutedLink {
    init(toRoute route: RouterName,
         replace: Bool = false,
         embedInNavigationView: Bool = true,
         modal: Bool = false,
         shouldPreventDismissal: Bool = false,
         interceptionExecutionFlow: NavigationInterceptionFlow? = nil,
         animation: NRNavigationTransition? = nil,
         router: Router = NavigationRouter.main,
         @ViewBuilder label: () -> Label)
    {
        self.init(to: route.rawValue, replace: replace, embedInNavigationView: embedInNavigationView, modal: modal, shouldPreventDismissal: shouldPreventDismissal, interceptionExecutionFlow: interceptionExecutionFlow, animation: animation, router: router, label: label)
    }
}
