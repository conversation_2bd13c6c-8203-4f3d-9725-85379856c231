//
//  Toaster.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/13.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import Toast_Swift

class Toaster {

    static var style:ToastStyle = {
        var style = ToastStyle()
        style.backgroundColor = UIColor.clear
        return style
        
    }()
    
    static var keyWindow:UIWindow?{
        return UIApplication.shared.windows.first { $0.isKeyWindow }
    }
    static func showToast(message:String){
//        ToastManager.shared.isQueueEnabled = false
        keyWindow?.makeToast(message, duration: ToastManager.shared.duration, position: .center)
    }
    
    static func showToast(message:String,duration: TimeInterval = ToastManager.shared.duration,completion: ((Bool) -> Void)? = nil){
        keyWindow?.makeToast(message, duration: duration, position: .center, completion: completion)
    }
    
    static func showToast(message:String, position:ToastPosition){
        keyWindow?.makeToast(message, duration: ToastManager.shared.duration, position: position)
    }
    
    static func hideToast(){
        keyWindow?.hideToast()
    }
}
