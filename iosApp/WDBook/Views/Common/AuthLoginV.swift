//
//  AuthLoginV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/12.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

extension AuthLoginV{
    static let loginSuccessNotification = Notification.Name(rawValue: "AuthLoginV_loginSuccessNotification")
    static let logoutSuccessNotification = Notification.Name(rawValue: "AuthLoginV_logoutSuccessNotification")
}

struct AuthLoginV: View {
    @EnvironmentObject var appState: AppState
    
    @State var isChecked = false
    @State var isShowAlert = false
    @State var isShowWebView = false
    
    @State var isPresentAgreement = false
    @State var urlAgreement:String = ""
    @State var isPresentPrivacy = false
    @State var urlPrivacy:String = ""
    
    // FIXME: 是否需要通过kmm层来获取url
    func tapAuthLogin() {
        let authLoginCheckUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginCheckUrl())
        let authLoginUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginUrl())
        // 有新版授权登录，有老板提示升级，否则web登录。
        if UIApplication.shared.canOpenURL(authLoginCheckUrl!) {
            UIApplication.shared.open(authLoginUrl!, options: [:]) { success in
                if success {
                    Log.d("10以后可以跳转url")
                } else {
                    Log.d("10以后不能完成跳转")
                }
            }
        } else if UIApplication.shared.canOpenURL(authLoginUrl!) {
            isShowAlert = true
        } else {
            isShowWebView = true
        }
    }
    
    var body: some View {
        NavigationView {
            BackNavigation(title: "", isHideBottomLine: true, ignoreSafeArea: true,isEnableSlideBack:false) {
                ZStack {
                    VStack(alignment: .center, spacing: 0) {
                        Image("logo").frame(width: 94, height: 94)
                        
                        Spacer().frame(height: 21)
                        Text("微读书城".localized)
                            .foregroundColor(Color(dynamicTitleColor2))
                            .font(Font.medium(size: 24))
                        
                        Spacer().frame(minHeight:50,idealHeight: 226, maxHeight: 226)
                        Button(action: {
                            if self.isChecked {
                                self.tapAuthLogin()
                            } else {
                                Toaster.showToast(message: "请勾选「已阅读并同意用户协议」".localized)
                            }
                        }) {
                            Text("微读圣经帐号登录".localized)
                                .font(Font.medium(size: 16))
                                .foregroundColor(Color.white)
                                .frame(minWidth: 0, maxWidth: .infinity)
                        }
                        //                        .disabled(!isChecked)
                        .padding(12).background(Color(UIColor.primaryColor1))
                        //            .frame(height: 44)
                        .cornerRadius(22)
                        .padding(EdgeInsets(top: 0, leading: 32, bottom: 0, trailing: 32))
                        
                        Spacer().frame(minHeight:10,idealHeight: 38, maxHeight: 38)
                        HStack(spacing: 8) {
                            CheckBox(isChecked: self.$isChecked)
                            Text("已阅读并同意".localized)
                            
                            Text("用户协议".localized).foregroundColor(Color(HighlightUnderlineColor))
                                .contentShape(Rectangle()).onTapGesture {
                                    HUDManager.showLoadingBlockHUD(text: "")
                                    WDBookAppSDK.shared.getBookAgreementURL { result in
                                        HUDManager.hideLoadingHUD()
                                        switch result{
                                        case .success(let url):
                                            if let u = url,!u.isEmpty{
                                                self.urlAgreement = u
                                                self.isPresentAgreement = true
                                            }
                                            break
                                        case .failure:
                                            break
                                        }
                                    }
                                }
                            NavigationLink(destination: LightWebview(url: urlAgreement, title: "微读书城用户协议".localized),isActive: $isPresentAgreement) {
                                EmptyView()
                            }
                            
                            Text("&")
                            
                            Text("隐私条款".localized).foregroundColor(Color(HighlightUnderlineColor))
                                .contentShape(Rectangle()).onTapGesture {
                                    HUDManager.showLoadingBlockHUD(text: "")
                                    WDBookAppSDK.shared.getBookPrivacyURL { result in
                                        HUDManager.hideLoadingHUD()
                                        switch result{
                                        case .success(let url):
                                            if let u = url,!u.isEmpty{
                                                self.urlPrivacy = u
                                                self.isPresentPrivacy = true
                                            }
                                            break
                                        case .failure:
                                            break
                                        }
                                    }
                                }
                            NavigationLink(destination: LightWebview(url: urlPrivacy, title: "微读书城隐私政策".localized),isActive: $isPresentPrivacy) {
                                EmptyView()
                            }
                        }.font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                        
                        NavigationLink(
                            destination: LightWebViewVC(isPresent: $isShowWebView, url: WDBookSessionSDK.shared.getAuthorizeUrl(),title: "微读圣经".localized).navigationBarHidden(true).ignoresSafeArea(),
                            isActive: $isShowWebView,
                            label: {
                                EmptyView()
                            })
                    }.frame(maxWidth: .infinity, maxHeight: .infinity)
                        .alert(isPresented: $isShowAlert) {
                            Alert(title: Text("请使用最新版[微读圣经]APP进行授权登录".localized),
                                  //                        message: Text(message),
                                  primaryButton: .default(Text("去升级".localized)) {
                                UIApplication.shared.open(URL(string: "https://apps.apple.com/cn/app/wei-du-sheng-jing/id654898456")!, options: [:]) { _ in
                                }
                            }, secondaryButton: .cancel(Text("取消授权".localized)) {
                                isShowAlert = false
                            })
                        }
                }
            }.onAppear{
                
            }
        }
    }
}

#if DEBUG
struct AuthLoginV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            AuthLoginV().environmentObject(AppState.shared)
            
            AuthLoginV().environmentObject(AppState.shared).environment(\.colorScheme, .dark)
        }
    }
}
#endif
