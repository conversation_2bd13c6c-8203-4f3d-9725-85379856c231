//
//  SimpleBookDownloadingView.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/4/23.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation

import Foundation
import SwiftUI
import shared
import SDWebImageSwiftUI

struct SimpleBookDownloadingView: View {
    @Environment(\.viewController) private var holder
    @ObservedObject var bookItem: DownloadResourceObservable
    @State private var present: Bool = false
    @State private var downloadError: Bool = false
    var jumpToBookReadingView:(()->())?
    
    func startDownload(){
        if self.bookItem.needUpgradeApp {
            AppState.shared.alert.showFormatVersionAlert()
            return
        }
        if self.bookItem.fileId == "" {
            Toaster.showToast(message: "没有获取到文件信息！".localized)
        } else {
            download(fileId: self.bookItem.fileId)
        }
    }
    
    func download(fileId:String) {
        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in
            switch result {
            case .success(let fileDownloadEntity):
                if let downloadUrl = fileDownloadEntity?.downloadUrl{
                    Log.d("获取downloadurl 成功了")
                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case .success(_):
                            Log.d("开始下载了")
                            AppDownloadManager.shared.start(key: fileId, url: downloadUrl, destinationFilePath:PathManager.zipPathRelative(fileId), md5:self.bookItem.md5)
                        case .failure(_):
                            Log.d("获取加密key失败")
                        }
                    }
                }else{
                    Toaster.showToast(message: "没有获取到下载链接！".localized)
                }

            case .failure(_):
                self.downloadError = true
            }
        }
    }
    
    var body: some View {
        ZStack {
            Color.black.opacity(0.65)
                .edgesIgnoringSafeArea(.all)
            
            VStack(spacing: 16.0) {
                Text(downloadError ? "下载失败".localized : "请耐心等待，努力下载中...").font(Font.regular(size: 16)).foregroundColor(Color.white)
                ProgressViewSwiftUI(progress: self.$bookItem.downloadProgress, trackTintColor: .constant(UIColor(hex: 0xF5F5F5)) ,progressTintColor:.constant(UIColor(hex: 0xFF8A00)) ).frame(width: 232, height: 6)
            }.frame(width:232)
            
        }.overlay(Button {
            self.present = false
            self.holder?.dismiss(animated: true, completion: .none)
        } label: {
            Image("icon_close_40").frame(width: 40, height: 40)
        }.padding(EdgeInsets(top: 40, leading: 0, bottom: 0, trailing: 24)
                 ), alignment: .topTrailing)
        .onAppear {
            self.present = true
            startDownload()
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Complete), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == bookItem.fileId {
                if present{
                    DispatchQueue.main.asyncAfter(deadline: .now()+0.3) {
                        self.holder?.dismiss(animated: true, completion: .none)
                        self.jumpToBookReadingView?()
                    }
                }
            }
        })
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Fails), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == bookItem.fileId {
                self.downloadError = true
            }
        })
    }
}
