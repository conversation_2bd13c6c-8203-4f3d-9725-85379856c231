//
//  BookDownloadingView.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/4/22.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import shared
import SDWebImageSwiftUI

struct BookDownloadingView: View {
    @Environment(\.viewController) private var holder
    @ObservedObject var bookItem: DownloadResourceObservable
    @State private var present: Bool = false
    @State private var downloadError: Bool = false
    var jumpToBookReadingView:(()->())?
    
    func startDownload(){
        if self.bookItem.needUpgradeApp {
            AppState.shared.alert.showFormatVersionAlert()
            return
        }
        if self.bookItem.fileId == "" {
            Toaster.showToast(message: "没有获取到文件信息！".localized)
        } else {
            download(fileId: self.bookItem.fileId)
        }
    }
    
    func download(fileId:String) {
        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in
            switch result {
            case .success(let fileDownloadEntity):
                if let downloadUrl = fileDownloadEntity?.downloadUrl{
                    Log.d("获取downloadurl 成功了")
                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case .success(_):
                            Log.d("开始下载了")
                            AppDownloadManager.shared.start(key: fileId, url: downloadUrl, destinationFilePath:PathManager.zipPathRelative(fileId), md5:self.bookItem.md5)
                        case .failure(_):
                            Log.d("获取加密key失败")
                        }
                    }
                }else{
                    Toaster.showToast(message: "没有获取到下载链接！".localized)
                }
            case .failure:
                self.downloadError = true
            }
        }
    }
    
    var body: some View {
        ZStack {
            Color.black.opacity(present ? 0.3 : 0)
                .edgesIgnoringSafeArea(.all)
                .onTapGesture {
                    withAnimation(.linear(duration: 0.5)) {
                        self.present = false
                    }
                    DispatchQueue.main.asyncAfter(deadline: .now()) {
                        self.holder?.dismiss(animated: true, completion: .none)
                    }
                }
            
            VStack(spacing: 24) {
                HStack {
                    Spacer()
                    Button("✕") {
                        //关闭
                        withAnimation(.linear(duration: 0.5)) {
                            self.present = false
                        }
                        DispatchQueue.main.asyncAfter(deadline: .now()) {
                            self.holder?.dismiss(animated: true, completion: .none)
                        }
                    }.padding(.top, 16)
                        .padding(.trailing, 16)
                }
                Spacer()
                
                VStack(spacing: 24.0) {
                    ZStack {
                        ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(self.bookItem.imageCover)))
                            .resizable()
                            .placeholder{
                                Image("cover_92*132").frame(width: TabShelfV.itemMiniWidth, height: TabShelfV.itemMinHeight, alignment: .center)
                            }
                            .transition(.fade(duration: 0.5)) // Fade Transition with duration
                            .cornerRadius(4)
                            .frame(width: TabShelfV.itemMiniWidth, height: TabShelfV.itemMinHeight, alignment: .center)
                        //下载中显示半透明黑色背景和进度条
                            .overlay(RoundedRectangle(cornerRadius: 4, style: .continuous)
                                        .fill(Color(UIColor(hex: 0x373636,alpha: 0.4))))
                        
                        VStack {
                            ProgressViewUIKit(self.$bookItem.downloadProgress).padding(.horizontal, 4)
                            Text("\(lroundf(self.bookItem.downloadProgress * 100))%")
                                .font(Font.medium(size: 10))
                                .foregroundColor(Color.white)
                        }
                    }
                    
                    Text(self.bookItem.title)
                        .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                        .font(Font.semibold(size: 16))
                        .foregroundColor(Color.black)
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                }.frame(width:TabShelfV.itemMiniWidth,height: TabShelfV.itemMinHeight + 64)
                
                Spacer()
                
                Text(downloadError ? "下载失败".localized : "下载完成后，自动跳转至划线位置".localized)
                    .foregroundColor(Color.gray)
                
                Spacer().frame(height:50)
            }.frame(width: 304, height: 422, alignment: .center)
                .background(Color.white)
                .cornerRadius(8)
        }.onAppear {
            withAnimation(.linear(duration: 0.5)) {
                self.present = true
            }
            startDownload()
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Complete), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == bookItem.fileId {
                if present{
                    DispatchQueue.main.asyncAfter(deadline: .now()+0.3) {
                        self.holder?.dismiss(animated: true, completion: .none)
                        self.jumpToBookReadingView?()
                    }
                }
            }
        })
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Fails), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == bookItem.fileId {
                self.downloadError = true
            }
        })
    }
}
