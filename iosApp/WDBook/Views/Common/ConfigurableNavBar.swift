//
//  ConfigurableNavBar.swift
//  WDBook
//
//  Created by Gemini on 2024/7/29.
//  Copyright © 2024 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ConfigurableNavBar<RightContent: View>: View {
    @Environment(\.safeAreaInsets2) private var safeAreaInsets

    let title: String
    let rightContent: () -> RightContent

    init(title: String, @ViewBuilder rightContent: @escaping () -> RightContent) {
        self.title = title
        self.rightContent = rightContent
    }

    var body: some View {
        HStack {
            Text(title)
                .font(Font.semibold(size: 22)) // Example styling, adjust as needed
                .foregroundColor(Color(dynamicTitleColor2))

            Spacer()

            rightContent()
        }
        .frame(height: 45) // Standard height for nav bar
        .padding(.top, safeAreaInsets.top)
        .padding(.horizontal, 20)
        .background(Color(dynamicBackgroundColor1))
        .modifier(BottomLineViewModifier(isShowBottomLine: false)) // Reuse existing modifier if available
        .edgesIgnoringSafeArea(.top) // Extend background to top edge
    }
}

// Example Preview
struct ConfigurableNavBar_Previews: PreviewProvider {
    static var previews: some View {
        VStack {
            ConfigurableNavBar(title: "书架") {
                HStack(spacing: 16) {
                    Image(systemName: "magnifyingglass")
                    Button("编辑") {
                        print("Edit tapped")
                    }
                    .foregroundColor(Color(dynamicTitleColor2)) // Match example styling
                }
                .foregroundColor(Color(dynamicTitleColor2)) // Match example styling
            }
            Spacer()
        }
        .environmentObject(AppState.shared) // Assuming AppState is needed for dynamic colors/preview
    }
}

// Assuming BottomLineViewModifier and dynamic colors (like dynamicTitleColor2, dynamicBackgroundColor1)
// are defined elsewhere and accessible. If not, they need to be implemented or replaced.

// Placeholder for BottomLineViewModifier if it doesn't exist globally
// struct BottomLineViewModifier: ViewModifier {
//     var isShowBottomLine: Bool = true
//     func body(content: Content) -> some View {
//         content
//             .overlay(
//                 VStack {
//                     Spacer()
//                     if isShowBottomLine {
//                         Rectangle()
//                             .frame(height: 0.5)
//                             .foregroundColor(Color.gray.opacity(0.3)) // Example color
//                     }
//                 }
//             )
//     }
// }

// Placeholder for dynamic colors if not globally defined
// let dynamicTitleColor2 = UIColor.label // Example
// let dynamicBackgroundColor1 = UIColor.systemBackground // Example
