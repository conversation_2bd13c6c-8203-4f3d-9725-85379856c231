//
//  AppleReceiptValidatorX.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/9/22.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SwiftyStoreKit

let chargeRecordUnDone:String = "chargeRecordUnDone"

class AppleReceiptValidatorX: ReceiptValidator {
    private let transactionId: String
    public init(transactionId: String) {
        self.transactionId = transactionId
    }

    func validate(receiptData: Data, completion: @escaping (VerifyReceiptResult) -> Void) {
        let receiptStr = receiptData.base64EncodedString(options: [])
        Log.d("\(receiptStr)")
        Log.d("ios transactionId : \(transactionId)")
        if let iapOrder = WDBookUserSDK.shared.getInAppPurchaseOrder(), iapOrder.appleTransactionId.utf16.count > 0 {
            WDBookUserSDK.shared.updateInAppPurchaseOrder(receipt: receiptStr) { (result) in
                switch result {
                case .success(let paymentResult):
                    guard let receipt = paymentResult?.receipt else{
                        return
                    }
                    
                    if let walletBallance = paymentResult?.walletBalance,
                        let balance = Double(walletBallance){
                        AppState.shared.walletBalance = balance
                    }
                    
                    guard let receiptData = receipt.data(using: .utf8) else {
                        return
                    }
                    // cannot decode data
                    guard let receiptInfo = try? JSONSerialization.jsonObject(with: receiptData, options: .mutableLeaves) as? ReceiptInfo else {
                        let jsonStr = String(data: receiptData, encoding: String.Encoding.utf8)
                        InAppPurchaseManager.shared.clearRechargeOrder()
                        completion(.error(error: .jsonDecodeError(string: jsonStr)))
                        return
                    }
                    // 清空支付订单
                    InAppPurchaseManager.shared.clearRechargeOrder()
                    if let status = receiptInfo["status"] as? Int, status == 0 {
                        // 成功回调
                        completion(.success(receipt: receiptInfo))
                    } else {
                        // 不为0，失败处理，也需要关掉任务，不影响用户下一次操作
                        completion(.error(error: .jsonDecodeError(string: "error")))
                    }
                case .failure(let error):
                    Log.i("Error Message:\(error)")
                    HUDManager.hideLoadingHUD()
                }
            }
        } else {
            // 用户本地数据不存在
            WDBookUserSDK.shared.updateInAppPurchaseReceipt(receipt: receiptStr, transactionId: self.transactionId) { (result) in
                switch result {
                case .success(let dataStr):
                    guard let receiptData = dataStr!.data(using: .utf8) else {
                        return
                    }
                    // cannot decode data
                    guard let receiptInfo = try? JSONSerialization.jsonObject(with: receiptData, options: .mutableLeaves) as? ReceiptInfo else {
                        let jsonStr = String(data: receiptData, encoding: String.Encoding.utf8)
                        completion(.error(error: .jsonDecodeError(string: jsonStr)))
                        return
                    }
                    AppState.shared.refreshWalletBalance()
                    // 清空支付订单
                    InAppPurchaseManager.shared.clearRechargeOrder()
                    WDBookDataSyncManager.shared.syncAccountData(syncType: .dataRechargeSuccess)
                    completion(.success(receipt: receiptInfo))
                case .failure(let error):
                    Log.d("Error Message:\(error)")
                }
            }
        }
    }
}
