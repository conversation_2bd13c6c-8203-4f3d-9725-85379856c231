//
//  FileContentView.swift
//  WDBook
//
//  Created by <PERSON> on 2023/7/7.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UniformTypeIdentifiers

struct FileContentView: View {
    @State private var fileContent: String = ""
    @State private var isLoading: Bool = false
    @State private var errorMessage: String = ""
    @State private var showDocumentPicker: Bool = false

    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>

    var body: some View {
        NavigationView {
            Group {
                if isLoading {
                    ProgressView("Loading...")
                } else if !errorMessage.isEmpty {
                    Text(errorMessage)
                        .foregroundColor(.red)
                } else {
                    TextEditor(text: $fileContent)
                        .padding()
                        .font(.system(size: 14)) // Adjust the font size as needed
                }
            }
            .navigationBarItems(
                leading: Button(action: {
                    self.presentationMode.wrappedValue.dismiss()
                }) {
                    HStack {
                        Image(systemName: "chevron.left") // Use your custom back icon here
                        Text("Back")
                    }
                },
                trailing:
                HStack {
                    Button(action: {
                        showDocumentPicker = true
                    }) {
                        Image(systemName: "folder")
                    }

                    Button(action: {
                        copyFileContent()
                    }) {
                        Image(systemName: "doc.on.doc")
                    }

                    Button(action: {
                        exportFileContent()
                    }) {
                        Image(systemName: "square.and.arrow.up")
                    }
                }
            )
            .sheet(isPresented: $showDocumentPicker) {
                DocumentPicker(isPresented: $showDocumentPicker, text: $fileContent)
            }
            .onAppear {
                loadFileContent()
            }
            .navigationBarTitle("File Content")
        }
    }

    private func loadFileContent() {

        isLoading = true

        DispatchQueue.global().async {
            do {
                fileContent = try String(contentsOf: URL(fileURLWithPath: LogUtils.shared.logPath), encoding: .utf8)
                isLoading = false
            } catch {
                print("Error loading file content: \(error)")
                isLoading = false
                errorMessage = "Failed to load file content: \(error.localizedDescription)"
            }
        }
    }

    private func copyFileContent() {
        UIPasteboard.general.string = fileContent
    }

    private func exportFileContent() {
        guard let data = fileContent.data(using: .utf8) else {
            print("Error converting file content to data.")
            return
        }

        let temporaryDirectoryURL = FileManager.default.temporaryDirectory
        let fileURL = temporaryDirectoryURL.appendingPathComponent("console.log")

        do {
            try data.write(to: fileURL)
            let activityViewController = UIActivityViewController(activityItems: [fileURL], applicationActivities: nil)
            UIApplication.shared.windows.first?.rootViewController?.present(activityViewController, animated: true, completion: nil)
        } catch {
            print("Error exporting file content: \(error)")
        }
    }
}

struct DocumentPicker: View {
    @Binding var isPresented: Bool
    @Binding var text: String
    @State private var files: [URL] = []

    var body: some View {
        VStack {
            Text("Documents")
                .font(.largeTitle)
                .padding()

            List(files, id: \.self) { url in
                Button(action: { selectFile(url: url) }) {
                    Text(url.lastPathComponent)
                }
            }
        }
        .onAppear(perform: loadFiles)
    }

    func loadFiles() {
        let fileManager = FileManager.default
        let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask)[0]

        do {
            files = try fileManager.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: nil)
        } catch {
            print("Error loading files: \(error)")
        }
    }

    func selectFile(url: URL) {
        do {
            text = try String(contentsOf: url)
        } catch {
            print("Unable to load file: \(error)")
        }
        isPresented = false
    }
}
