//
//  SpeedupVCWrapper.swift
//  WDBook
//
//  Created by 杜文泽 on 2024/3/7.
//  Copyright © 2024 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit

struct SpeedupVCWrapper: UIViewControllerRepresentable {
    @Environment(\.presentationMode) var presentationMode
        
    func makeUIViewController(context: Context) -> UIViewController {
        let speedupVC = WDInternetSpeedupVC()
        // 可以在这里传递闭包或者使用其他方式来处理关闭操作
        speedupVC.closeAction = {
            self.presentationMode.wrappedValue.dismiss()
        }
        return UINavigationController(rootViewController: speedupVC)
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
    }
}

