//
//  ShelfItem.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/4.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWebImageSwiftUI
import shared

struct ShelfItemV: View {
    @ObservedObject var shelfItem: DownloadResourceObservable
    @Binding var isOnEdit:Bool
    var openBookAction:(()->())?
    var shelfEditAction:(()->())?
    
    @State var canTouch:Bool = true
    @State var bookUpdating:Bool = false
    
    func startDownload(){
        if self.shelfItem.needUpgradeApp {
            AppState.shared.alert.showFormatVersionAlert()
            return
        }
        self.canTouch = false
        if self.shelfItem.fileId == "" {
            WDBookStoreSDK.shared.getResourceFileEntity(resourceId: self.shelfItem.resourceId) { result in
                switch result {
                case .success(let fileEntity):
                    if let fileId = fileEntity?.fileId {
                        self.shelfItem.md5 = fileEntity?.md5 ?? ""
                        download(fileId: fileId)
                    } else {
                        // showTips
                        self.canTouch = true
                        Toaster.showToast(message: "没有获取到文件信息！".localized)
                    }
                case .failure:
                    // showTips
                    self.canTouch = true
                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                }
            }
        } else {
            download(fileId: self.shelfItem.fileId)
        }
    }

    func download(fileId:String) {
        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in
            self.canTouch = true
            switch result {
            case .success(let fileDownloadEntity):
                if fileDownloadEntity == nil {
                    // showTips
                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                } else {
                    Log.d("获取downloadurl 成功了")
                    
                    //3.0.4加上的修补逻辑
                    //修补FileDownload表
                    if let downloadDataInfo = WDBookDownloadSDK.shared.fetchDownloadFileEntity(fileId: fileId),
                       downloadDataInfo.downloadStatus == .complete,
                       downloadDataInfo.resourceId.isEmpty{
                        downloadDataInfo.resourceId = shelfItem.resourceId
                        downloadDataInfo.resourceTypeId = "eBook"
                        WDBookDownloadSDK.shared.saveDownloadData(entity: downloadDataInfo)

                        AppState.shared.reloadShelfData()
                        return
                    }
                    
                    //修补UserFileInfo表
                    if let fileinfo = WDBookUserSDK.shared.getUserFileInfo(fileId: fileId){
                       if fileinfo.resourceId.isEmpty{
                          WDBookUserSDK.shared.updatUserFileInfo(fileId: fileId, setResourceId: shelfItem.resourceId)
                          AppState.shared.reloadShelfData()
                          return
                       }
                    }
                    
                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case .success(_):
                            Log.d("开始下载了")
                            AppDownloadManager.shared.start(key: fileId, url: fileDownloadEntity!.downloadUrl, destinationFilePath:PathManager.zipPathRelative(fileId), md5:self.shelfItem.md5)
                        case .failure(_):
                            Log.d("获取加密key失败")
                        }
                    }
                }
            case .failure:
                Log.d("获取 downloadurl 失败")
                Toaster.showToast(message: "没有获取到下载链接！".localized)
            }
        }
    }

    var body: some View {
        VStack(spacing: 8) {
            ZStack {
                ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: shelfItem.imageCover))))
                    .resizable()
                    .placeholder{
                        Image("cover_92*132").aspectRatio(contentMode: .fit).frame(width: TabShelfV.itemMiniWidth, height: TabShelfV.itemMinHeight, alignment: .center)
                    }
                    .transition(.fade(duration: 0.5)) // Fade Transition with duration
                    .cornerRadius(4)
                    .overlay(
                        RoundedRectangle(cornerRadius: 4)
                            .stroke(Color("ShelfItemBorderColor"), lineWidth: 0.5)
                    )
                    .frame(width: TabShelfV.itemMiniWidth, height: TabShelfV.itemMinHeight, alignment: .center)
                    //下载中显示半透明黑色背景和进度条
                    .overlay(AppState.shared.enableDownloadProgress ? AnyView(RoundedRectangle(cornerRadius: 4, style: .continuous)
                        .fill(Color((shelfItem.downloadState == .downloading || shelfItem.downloadState == .waiting) || !canTouch || isOnEdit ? UIColor(hex: 0x373636,alpha: 0.4) : UIColor.clear))) : AnyView(EmptyView()))
                
                if AppState.shared.enableDownloadProgress{
                    if (shelfItem.downloadState == .downloading || shelfItem.downloadState == .waiting) && canTouch{
                        VStack {
                            ProgressViewUIKit(self.$shelfItem.downloadProgress).padding(.horizontal, TabShelfV.span/2)
                            Text("\(lroundf(self.shelfItem.downloadProgress * 100))%")
                            .font(Font.medium(size: 10))
                            .foregroundColor(Color.white)
                        }
                    }
                    if !self.canTouch {
                        VStack(spacing:2) {
                            ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: Color.white))
                                .scaleEffect(0.618, anchor: .center)
                            Text("正在加载...".localized).font(Font.medium(size: 10)).foregroundColor(Color.white)
                        }
                    }
                }
                
            }
            //未下载，暂停，错误时候，可以下载。
            .overlay(Image(AppState.shared.enableDownloadProgress && shelfItem.downloadState == .unDownload ? "item_download" : "" ).frame(width: 22, height: 22, alignment: .center), alignment: .bottomTrailing)
            .overlay(
                (isOnEdit && WDBookSessionSDK.shared.isLogin) ?
                    AnyView(Image(shelfItem.isSeleted ? "item_shelf_selected" : "item_shelf_unselected" ).frame(width: 22, height: 22, alignment: .center))
                : AnyView(EmptyView())
                 , alignment: .center)
//            .overlay(Image(self.shelfItem.downloadState == .completed ? "item_selected" : "" ).frame(width: 22, height: 22, alignment: .center), alignment: .bottomTrailing)
            .overlay(AppState.shared.enableDownloadProgress && shelfItem.downloadState == .completed && shelfItem.readProgress >= 0 ?
                        AnyView(ZStack(alignment:.top) {
                                    Image("item_book_Progress").padding(.horizontal, 0.5)
                                    Text("\(shelfItem.readProgress)%").minimumScaleFactor(0.5)
                                        .font(Font.medium(size: 6)).frame(maxWidth:.infinity,alignment: .center).foregroundColor(Color(UIColor(hex: 0xE3E3E3))).padding(.top,2)
                                }.frame(width: 18, height: 14, alignment: .center).padding(.trailing,4))
                        : AnyView(EmptyView())
                     , alignment: .topTrailing)
            
            Text(shelfItem.title)
                .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                .font(Font.regular(size: 12))
                .foregroundColor(Color(dynamicTextColor18))
                .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .topLeading)
        }
        .frame(width:TabShelfV.itemMiniWidth,height: TabShelfV.itemMinHeight + 42)
        // .background(Color(dynamicBackgroundColor4))
        .onTapGesture {
            guard WDBookSessionSDK.shared.isLogin else {
                AppState.shared.showLoginRegisterVAndPopToStore()
                return
            }
            guard self.canTouch || isOnEdit else{
                return
            }
            if (isOnEdit){
                shelfItem.isSeleted.toggle()
                shelfEditAction?()
            } else{
                if self.shelfItem.downloadState == .completed {
                    Log.d("已下载")
                    openBookAction?()
                } else if shelfItem.downloadState == .downloading || shelfItem.downloadState == .waiting{
                    Log.d("正在下载")
                    guard NetReachability.isReachability() else{
                        AppState.shared.alert.showNetworkErrorAlert()
                        return
                    }
                    Toaster.showToast(message: "书籍资源正在下载中".localized)
                } else {
                    Log.d("未下载")
//                    if AppState.shared.enableDownloadProgress{
                        guard NetReachability.isReachability() else{
                            AppState.shared.alert.showNetworkErrorAlert()
                            return
                        }
                        if !AppState.shared.isHide4GDownloadControl && NetReachability.isCellular() && AppState.shared.downloadOnlyOnWifi{
                            AppState.shared.alert.showCanCellularDownloadAlert {
                                startDownload()
                            }
                        } else {
                            startDownload()
                        }
//                    }else{
//                        Toaster.showToast(message: "书籍资源正在下载中".localized)
//                    }
                }
            }
        }
    }
}

//#if DEBUG
//struct ShelfItem_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            ShelfItemV(item: test_ShelfList().items.first!).environmentObject(AppState.shared)
//
//            ShelfItemV(item: test_ShelfList().items.first!)
//                .previewDevice("iPhone SE (2nd generation)").environmentObject(AppState.shared).environment(\.colorScheme, .dark)
//        }
//    }
//}
//#endif
