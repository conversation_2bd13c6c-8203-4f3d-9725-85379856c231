//
//  TabShelfV.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/5/19.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import DeviceKit
import NavigationRouter
import SDWebImageSwiftUI
import shared
import SwiftUI

struct TabShelfV: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.safeAreaInsets2) private var safeAreaInsets
    @State var isHideStatusBar = false
    @State var isFirstLoad = true
    @State private var headerRefreshing: Bool = false

    static let itemDesignWidth: CGFloat = 92.0
    static let itemDesignHeight: CGFloat = 132.0

    static let itemMiniWidth = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                               Device.iPodTouch7,
                                                               Device.simulator(.iPodTouch7),
                                                               Device.simulator(.iPhoneSE)]) ? 82 : itemDesignWidth)
    static let itemMinHeight = itemDesignHeight * itemMiniWidth / itemDesignWidth
    static let span = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                      Device.iPodTouch7,
                                                      Device.simulator(.iPodTouch7),
                                                      Device.simulator(.iPhoneSE)]) ? 12 : 24)
    private var columns: [GridItem] {
        return [GridItem(.adaptive(minimum: TabShelfV.itemMiniWidth))]
    }

    @Namespace var topID

    @State var isOnEdit = false
    @State var isBookSelcted = false
    @State var isSelectAll: Bool = false
    // 刷新是否选择了书状态
    func refreshSelectedState() {
        isBookSelcted = false
        for i in 0 ..< appState.shelfCombineItems.count {
            if appState.shelfCombineItems[i].isSeleted == true {
                isBookSelcted = true
            }
        }
    }

    var mainContent: some View {
        ZStack {
            if self.appState.isShelfFirstLoad {
                ActivityIndicator()
            } else if self.appState.shelfCombineItems.count > 0 {
                ScrollViewReader { scrollView in
                    RefreshableScrollView(
                        onRefresh: { done in
                            DispatchQueue.main.async {
                                WDBookDataSyncManager.shared.syncShelfRelateAndDownload {
                                    headerRefreshing = false
                                    done()
                                }
                            }
                        },
                        progress: { state in
                            switch state {
                            case .waiting, .primed:
                                Text("下拉刷新".localized).font(Font.regular())
                                    .foregroundColor(Color(dynamicTextColor9)).frame(height: 14)
                                    .onAppear {
                                        headerRefreshing = true
                                    }
                            case .loading:
                                if headerRefreshing {
                                    SimpleRefreshingView().padding()
                                }
                            }

//                            RefreshActivityIndicator(isAnimating: state == .loading) {
//                                     $0.hidesWhenStopped = true
//                                 }
                        }
                    ) {
                        Rectangle()
                            .fill(Color.clear)
                            .frame(height: 0.5)
                            .id(topID)

                        LazyVGrid(columns: columns, alignment: .center, spacing: TabShelfV.span) {
                            ForEach(self.appState.shelfCombineItems, id: \.itemId) { item in
                                if item.dataType == ShelfDataType.resource {
                                    ShelfItemV(shelfItem: item, isOnEdit: $isOnEdit, openBookAction: {
                                        RoutableManager.navigate(toPath: RouterName.readerWithId.withParam(item.resourceId))
                                    }, shelfEditAction: {
                                        refreshSelectedState()
                                        NotificationCenter.default.post(name: TabShelfV.Noti_IsShelfBookSelcted, object: isBookSelcted)
                                    }).onLongPressGesture(minimumDuration: 0.5) {
                                        if !isOnEdit {
                                            isOnEdit = true

                                            if let index = appState.shelfCombineItems.firstIndex(where: { $0.itemId == item.itemId }) {
                                                appState.shelfCombineItems[index].isSeleted = true
                                            }

                                            refreshSelectedState()
                                            NotificationCenter.default.post(name: TabShelfV.Noti_IsShelfBookSelcted, object: isBookSelcted)
                                        }
                                        NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
                                    }
                                } else if item.dataType == ShelfDataType.archive {
                                    GroupShelfItemV(item: item, isOnEdit: $isOnEdit, action: {
                                        if !isOnEdit {
                                            if WDBookSessionSDK.shared.isLogin {
                                                if let index = appState.shelfCombineItems.firstIndex(where: { $0.itemId == item.itemId }) {
                                                    RoutableManager.showShelfGroupListView(archiveItemIndex: index)
                                                }
                                            } else {
                                                AppState.shared.showLoginRegisterVAndPopToStore()
                                            }
                                        }
                                    })
                                }
                            }
                        }
                        .padding(.horizontal, TabShelfV.span)
                        .padding(.vertical, 27)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color(dynamicBackgroundColor1))
                    .onAppear {
                        //                        withAnimation {
                        scrollView.scrollTo(topID)
                        //                        }
                    }
                }
            } else {
                VStack(alignment: .center, spacing: 0, content: {
                    Image("error_empty").frame(width: 167, height: 110)
                    Spacer().frame(height: 54)
                    Text("您的书架上没有书籍".localized).font(Font.medium(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                    Button(action: {
                        self.appState.changeTab(tabName: .store)
                    }) {
                        Text("去书城逛逛".localized)
                            .font(Font.medium(size: 16))
                            .foregroundColor(Color.white)
                            .frame(maxWidth: .infinity)
                    }
                    .frame(height: 44)
                    .background(Color(UIColor.primaryColor1))
                    .cornerRadius(22)
                    .padding(EdgeInsets(top: 46, leading: 32, bottom: 0, trailing: 32))
                })
            }

        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .onReceive(NotificationCenter.default.publisher(for: ContentView.Noti_IsLogOut, object: nil)) { _ in
                isOnEdit = false
                isBookSelcted = false
                for i in 0 ..< self.appState.shelfCombineItems.count {
                    self.appState.shelfCombineItems[i].isSeleted = false
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: TabShelfV.Noti_IsOnShelfEdit, object: nil)) { noti in
                if let isOnEdit = noti.object as? Bool {
                    self.isOnEdit = isOnEdit
                    if !isOnEdit {
                        isBookSelcted = false
                        isSelectAll = false
                        for i in 0 ..< appState.shelfCombineItems.count {
                            appState.shelfCombineItems[i].isSeleted = false
                        }
                    }
                }
            }
            .onAppear {
                if self.isFirstLoad {
                    self.isFirstLoad = false
                    self.appState.refreshShelfList()
                }
            }
    }

    var body: some View {
        ZStack(alignment: .top, content: {
            mainContent
                .navigationBarHidden(true)
                .padding(.top, 45 + safeAreaInsets.top)

            navigationBar()

        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }

    // Extracted Navigation Bar Content
    @ViewBuilder
    private func navigationBar() -> some View {
        // Conditionally display Nav Bar
        if isOnEdit {
            // Existing Nav Bar for Editing State
            HStack(alignment: .center, spacing: 0, content: {
                Button(action: {
                    guard WDBookSessionSDK.shared.isLogin else {
                        AppState.shared.showLoginRegisterVAndPopToStore()
                        return
                    }
                    isSelectAll.toggle()
                    for i in 0 ..< self.appState.shelfCombineItems.count {
                        self.appState.shelfCombineItems[i].isSeleted = isSelectAll
                    }
                    isBookSelcted = isSelectAll
                    NotificationCenter.default.post(name: TabShelfV.Noti_IsShelfBookSelcted, object: isBookSelcted)
                }) {
                    Text(isSelectAll ? "取消全选".localized : "全选".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(.system(size: 16))
                        .isHidden(self.appState.shelfCombineItems.count == 0 || !isOnEdit || !WDBookSessionSDK.shared.isLogin)
                        .frame(maxWidth: 100, maxHeight: .infinity, alignment: .leading)
                }.padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
                Text("选择书籍".localized) // Title when editing
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18))
                Spacer()
                // Cancel Button (was Edit/Cancel toggle)
                Button {
                    guard WDBookSessionSDK.shared.isLogin else {
                        AppState.shared.showLoginRegisterVAndPopToStore()
                        return
                    }
                    self.isOnEdit = false // Always set to false to exit edit mode
                    isSelectAll = false
                    isBookSelcted = false
                    for i in 0 ..< self.appState.shelfCombineItems.count {
                        self.appState.shelfCombineItems[i].isSeleted = false
                    }
                    NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
                    NotificationCenter.default.post(name: TabShelfV.Noti_IsShelfBookSelcted, object: isBookSelcted)
                } label: {
                    Text("取消".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(.system(size: 16))
                        .frame(maxWidth: 100, maxHeight: .infinity, alignment: .trailing)
                }.padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0))
            })
            .frame(height: 45)
            .padding(.top, safeAreaInsets.top)
            .frame(maxWidth: .infinity)
            .padding(.horizontal, 20)
            .background(Color(dynamicBackgroundColor1))
            .modifier(BottomLineViewModifier(isShowBottomLine: true))
        } else {
            // Use ConfigurableNavBar for Non-Editing State
            ConfigurableNavBar(title: "书架".localized) {
                HStack(spacing: 16) {
                    // Replace Button with RoutedLink
                    RoutedLink(toRoute: .shelfSearch) { // <-- Use RoutedLink
                        Image(systemName: "magnifyingglass")
                            .resizable() // Allow resizing
                            .frame(width: 18, height: 18) // Set specific size
                            // Apply frame modifiers to the Image itself
                            .padding(8) // Add some padding around the image
                            .contentShape(Rectangle()) // Make padded area tappable
                    }
                    // Edit Button
                    Button {
                        guard WDBookSessionSDK.shared.isLogin else {
                            AppState.shared.showLoginRegisterVAndPopToStore()
                            return
                        }
                        // Only show edit button if there are items and user is logged in
                        if self.appState.shelfCombineItems.count > 0 && WDBookSessionSDK.shared.isLogin {
                            self.isOnEdit = true // Enter edit mode
                            NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
                        } else {
                            // Optionally handle the case where edit shouldn't be possible
                            // Maybe show an alert or do nothing
                            print("Cannot edit - no items or not logged in")
                        }
                    } label: {
                        Text("编辑".localized)
                    }
                    // Hide button if no items or not logged in (matching original logic)
                    .isHidden(self.appState.shelfCombineItems.count == 0 || !WDBookSessionSDK.shared.isLogin)
                }
                .font(.system(size: 16)) // Apply font to HStack content
                .foregroundColor(Color(dynamicTitleColor2)) // Apply color to HStack content
            }
        }
    }
}

extension TabShelfV {
    static let Noti_IsOnShelfEdit = Notification.Name("Noti_IsOnShelfEdit")
    static let Noti_IsShelfBookSelcted = Notification.Name("Noti_IsShelfBookSelcted")
}
