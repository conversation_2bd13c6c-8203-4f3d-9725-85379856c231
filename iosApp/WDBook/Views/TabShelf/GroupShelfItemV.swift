//
//  ShelfGroupItemV.swift
//  WDBook
//
//  Created by QK on 2021/9/17.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import shared

struct GroupShelfItemV: View {
    var archiveItem: DownloadResourceObservable
    @Binding var isOnEdit:Bool
    @State var isShowGroupList = false
    var groupClickAction:(()->())?
    var picPath: [String] = []
    
    init(item:DownloadResourceObservable, isOnEdit: Binding<Bool>, action:(()->())? = nil) {
        archiveItem = item
        groupClickAction = action
        self._isOnEdit = isOnEdit
        if WDBookSessionSDK.shared.isLogin{
            let imageList: [String] = WDBookUserSDK.shared.getShelfArchiveUrlList(archiveId: archiveItem.clientArchiveId, maxItem: 4)
            for i in 0..<imageList.count {
                picPath.append(WDBookAppSDK.shared.getFullImageUrl(url: imageList[i]))
            }
        }
    }
    
    var body: some View {
        ZStack {
            VStack(spacing: 8) {
                ZStack {
                    // Image("cover_archive")
                    //     .resizable().scaledToFill()
                    VStack{
                        if (picPath.count>0) {
                            HStack{
                                ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(picPath.count>0 ? picPath[0] : "")))
                                    .placeholder{
                                        Image("cover_92*132").resizable().frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                                    }
                                    .resizable()
                                    .frame(width: TabShelfV.itemMiniWidth/2-14, height: TabShelfV.itemMinHeight/2-18, alignment: .center)
                                
                                ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(picPath.count>1 ? picPath[1] : "")))
                                        .placeholder{
                                            Image("cover_92*132").resizable().frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                                        }
                                        .resizable()
                                        .isHidden(picPath.count<2)
                                        .frame(width: TabShelfV.itemMiniWidth/2-14, height: TabShelfV.itemMinHeight/2-18, alignment: .center)
                            }
                            HStack{
                                ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(picPath.count>2 ? picPath[2] : "")))
                                        .placeholder{
                                            Image("cover_92*132").resizable().frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                                        }
                                        .resizable()
                                        .isHidden(picPath.count<3)
                                        .frame(width: TabShelfV.itemMiniWidth/2-14, height: TabShelfV.itemMinHeight/2-18, alignment: .center)
                                
                                
                                ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(picPath.count>3 ? picPath[3] : "")))
                                        .placeholder{
                                            Image("cover_92*132").resizable().frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .center)
                                        }
                                        .resizable()
                                        .isHidden(picPath.count<4)
                                        .frame(width: TabShelfV.itemMiniWidth/2-14, height: TabShelfV.itemMinHeight/2-18, alignment: .center)
                            }
                        } else {
                            Image("folder_outlined").resizable().frame(width: 27, height: 27, alignment: .center)
                        }
                    }
                    .padding(2)
                    .frame(width: TabShelfV.itemMiniWidth, height: TabShelfV.itemMinHeight, alignment: .center)
                   .background(Color(UIColor(named: "SearchResultGroupItemBackground") ?? .systemGray6))
                   .overlay(
                       RoundedRectangle(cornerRadius: 4, style: .continuous)
                           .stroke(Color(UIColor(named: "SearchResultGroupItemBorder") ?? .systemGray3), lineWidth: 0.5)
                   )
                    .overlay(
                        RoundedRectangle(cornerRadius: 4, style: .continuous)
                            .fill(Color(isOnEdit ? UIColor(hex: 0xf5f5f5,alpha: 0.4) : UIColor.clear))
                    )
                    
                }
                
                Text(self.archiveItem.archiveName)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTextColor18))
                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .topLeading)
            }
            .frame(width:TabShelfV.itemMiniWidth, height: TabShelfV.itemMinHeight + 42)
            //            .background(Color(dynamicBackgroundColor4))
            
        }.contentShape(Rectangle())
            .onTapGesture {
            groupClickAction?()
        }
    }
}
