//
//  BookShelfSearchResult.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import Combine // For @StateObject and BookShelfSearchState
import SDWebImage // For SDWebImagePrefetcher if still used by remaining parts, or if any cell directly used it.
import SwiftUI
import UIKit // For UIViewControllerRepresentable

// MARK: - UIKit UITableView Implementation (Bridge)

struct SearchResultTableViewRepresentable: UIViewControllerRepresentable {
    @ObservedObject var searchState = BookShelfSearchState.shared

    func makeUIViewController(context _: Context) -> SearchResultListViewController {
        // SearchResultListViewController is now defined in its own file
        let viewController = SearchResultListViewController()
        // Pass network status to view controller
        viewController.storeNetworkUnavailable = searchState.storeNetworkUnavailable
        return viewController
    }

    func updateUIViewController(_ uiViewController: SearchResultListViewController, context _: Context) {
        // Update network availability status when it changes
        uiViewController.storeNetworkUnavailable = searchState.storeNetworkUnavailable
    }
}

// MARK: - Main SwiftUI View for Search Results

struct BookShelfSearchResult: View {
    // Access the shared state for resigning focus
    @StateObject private var searchState = BookShelfSearchState.shared

    var body: some View {
        // Use the UITableViewRepresentable
        SearchResultTableViewRepresentable()
            .background(Color(.systemGroupedBackground))
            .edgesIgnoringSafeArea(.bottom)
        // .onTapGesture { // Example: if we wanted to dismiss keyboard on tap of the whole area
        //     searchState.resignFocus()
        // }
    }
}

// MARK: - Section Header Class (Converted to UIKit)

class SearchResultSectionHeader: UIView {
    private let titleLabel = UILabel()
    private let countLabel = UILabel()

    init(title: String, countText: String) {
        super.init(frame: .zero)
        setupView()
        configure(title: title, countText: countText)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }

    private func setupView() {
        backgroundColor = UIColor(named: "SearchResultListBackground")

        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .center
        stackView.distribution = .fill
        stackView.spacing = 8
        stackView.translatesAutoresizingMaskIntoConstraints = false

        titleLabel.numberOfLines = 1

        countLabel.font = UIFont.secondaryTextRegular
        countLabel.textColor = UIColor.secondaryLabel
        countLabel.setContentHuggingPriority(.required, for: .horizontal)

        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(UIView()) // Spacer
        stackView.addArrangedSubview(countLabel)

        addSubview(stackView)

        NSLayoutConstraint.activate([
            stackView.topAnchor.constraint(equalTo: topAnchor, constant: 8),
            stackView.bottomAnchor.constraint(equalTo: bottomAnchor, constant: -8),
            stackView.leadingAnchor.constraint(equalTo: leadingAnchor, constant: 24),
            stackView.trailingAnchor.constraint(equalTo: trailingAnchor, constant: -24),
        ])
    }

    func configure(title: String, countText: String) {
        titleLabel.attributedText = createHighlightedAttributedString(
            text: title,
            font: UIFont.sectionHeaderSemibold,
            textColor: UIColor.label
        )

        countLabel.text = countText
    }
}

// MARK: - AttributedText UIViewRepresentable for SwiftUI

// Simple UIViewRepresentable to display NSAttributedString in SwiftUI
struct AttributedText: UIViewRepresentable {
    let text: String
    let font: UIFont // UIFont.appSectionHeader, UIFont.appBody will be passed here
    let textColor: UIColor? // Make optional for default
    let highlightColor: UIColor

    init(text: String, font: UIFont, textColor: UIColor? = nil, highlightColor: UIColor = .orange) {
        self.text = text
        self.font = font
        self.textColor = textColor
        self.highlightColor = highlightColor
    }

    func makeUIView(context _: Context) -> UILabel {
        let label = UILabel()
        label.numberOfLines = 1
        label.setContentCompressionResistancePriority(.defaultLow, for: .horizontal) // Allow truncation if needed
        label.lineBreakMode = .byTruncatingTail
        return label
    }

    func updateUIView(_ uiView: UILabel, context _: Context) {
        // createHighlightedAttributedString needs to be accessible here.
        // It was moved to BookShelfSearchCells.swift. This means either:
        // 1. AttributedText also moves to BookShelfSearchCells.swift (if tightly coupled).
        // 2. createHighlightedAttributedString becomes a more global helper.
        // 3. AttributedText is changed to not depend on it directly or uses another mechanism.
        // For now, assuming createHighlightedAttributedString is globally accessible or will be refactored.
        uiView.attributedText = createHighlightedAttributedString(
            text: text,
            font: font,
            textColor: textColor ?? UIColor.label,
            highlightColor: highlightColor
        )
    }
}
