//
//  BookShelfSearchCells.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22. // Note: Date might need adjustment
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SDWebImage // For UIImageView extension and potentially cell image loading
import UIKit

// MARK: - Loading Cell Implementation

class LoadingTableViewCell: UITableViewCell {
    static let reuseIdentifier = "LoadingCell"

    let activityIndicator = UIActivityIndicatorView(style: .medium)
    let loadingLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupView()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupView() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear // Ensure table background shows through
        selectionStyle = .none

        contentView.backgroundColor = UIColor(named: "SearchResultListBackground")
        contentView.layer.cornerRadius = 8

        // Stack for spinner and label
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.alignment = .center
        stackView.spacing = 8
        stackView.translatesAutoresizingMaskIntoConstraints = false

        // Configure the spinner
        activityIndicator.hidesWhenStopped = true
        // activityIndicator.color = .systemOrange // Removed
        // activityIndicator.transform = CGAffineTransform(scaleX: 1.5, y: 1.5) // Removed

        // Configure the label
        loadingLabel.text = "加载中...".localized // Changed text
        loadingLabel.font = UIFont.systemFont(ofSize: 14) // Changed font
        loadingLabel.textColor = .secondaryLabel // Changed text color

        // Add to stack
        stackView.addArrangedSubview(activityIndicator)
        stackView.addArrangedSubview(loadingLabel)

        // Add stack to content view
        contentView.addSubview(stackView)

        // Constraints
        NSLayoutConstraint.activate([
            stackView.centerXAnchor.constraint(equalTo: contentView.centerXAnchor),
            stackView.centerYAnchor.constraint(equalTo: contentView.centerYAnchor),
            contentView.heightAnchor.constraint(equalToConstant: 60), // Changed height from 80
        ])

        // Add a debug print in the constructor to confirm cell creation
        Log.d("DEBUG: LoadingTableViewCell initialized")
    }

    func startLoading() {
        activityIndicator.startAnimating()
        // Make sure the cell is visible in logs
        Log.d("DEBUG: LoadingTableViewCell started animating")
    }

    override func prepareForReuse() {
        super.prepareForReuse()
        activityIndicator.stopAnimating()
    }
}

// MARK: - Empty Result Cell Implementation

class EmptyResultTableViewCell: UITableViewCell {
    static let reuseIdentifier = "EmptyResultCell"

    private let containerView = UIView()
    private let emptyLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupView()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupView() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear // Ensure table background shows through
        selectionStyle = .none

        // Container View (The Card)
        containerView.backgroundColor = UIColor(named: "SearchResultListBackground")
        containerView.layer.cornerRadius = 8
        // Optional Shadow (Match other cells if needed)
        // containerView.layer.shadowColor = UIColor.black.cgColor
        // containerView.layer.shadowOpacity = 0.05
        // containerView.layer.shadowOffset = CGSize(width: 0, height: 1)
        // containerView.layer.shadowRadius = 1
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)

        // Label configuration
        emptyLabel.text = "search.results.notFound".localized
        emptyLabel.font = UIFont.primaryTextRegular // Use standard primary text font
        emptyLabel.textColor = UIColor.secondaryLabel // Use secondary text color
        emptyLabel.textAlignment = .center
        emptyLabel.numberOfLines = 0
        emptyLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(emptyLabel)

        // Constraints
        NSLayoutConstraint.activate([
            // Container constraints (padded)
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),

            // Label constraints (centered within container)
            emptyLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            emptyLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            emptyLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 16),
            emptyLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -16),

            // Give the cell a reasonable height
            containerView.heightAnchor.constraint(equalToConstant: 60), // Adjust height as needed
        ])
    }
}

// MARK: - Network Unavailable Cell Implementation

class NetworkUnavailableTableViewCell: UITableViewCell {
    static let reuseIdentifier = "NetworkUnavailableCell"

    private let containerView = UIView()
    private let messageLabel = UILabel()

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupView()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupView() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        selectionStyle = .none

        // Configure container view
        containerView.backgroundColor = UIColor(named: "SearchResultListBackground")
        containerView.layer.cornerRadius = 8
        containerView.clipsToBounds = true
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)

        // Configure label for unavailable message
        messageLabel.text = "search.results.networkUnavailable".localized
        messageLabel.textColor = UIColor.secondaryLabel
        messageLabel.font = UIFont.primaryTextRegular
        messageLabel.textAlignment = .center
        messageLabel.numberOfLines = 0
        messageLabel.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(messageLabel)

        // Set up constraints
        NSLayoutConstraint.activate([
            // Container view constraints
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            containerView.heightAnchor.constraint(equalToConstant: 60),

            // Message label constraints
            messageLabel.centerXAnchor.constraint(equalTo: containerView.centerXAnchor),
            messageLabel.centerYAnchor.constraint(equalTo: containerView.centerYAnchor),
            messageLabel.leadingAnchor.constraint(greaterThanOrEqualTo: containerView.leadingAnchor, constant: 16),
            messageLabel.trailingAnchor.constraint(lessThanOrEqualTo: containerView.trailingAnchor, constant: -16),
        ])
    }

    func configure(cornerMask: CACornerMask) {
        containerView.layer.maskedCorners = cornerMask
        containerView.layer.cornerRadius = 8
        containerView.clipsToBounds = true
    }
}

// MARK: - UIImageView Extension for Image Loading

extension UIImageView {
    // Helper to load image using the same logic as ShelfItemV + SDWebImage
    func loadImageFromUrlString(_ urlString: String?, placeholderName: String = "cover_92*132") {
        // Use the same URL processing logic
        let fullUrl = WDBookAppSDK.shared.getFullImageUrl(url: urlString ?? "")
        let finalUrlString = ImageManager.getImageUrl(fullUrl)
        let finalUrl = URL(string: finalUrlString)

        // Define placeholder image
        let placeholder = UIImage(named: placeholderName)

        // Use SDWebImage to load the image
        sd_setImage(with: finalUrl, placeholderImage: placeholder, options: [.retryFailed, .progressiveLoad]) { [weak self] _, error, _, url in
            if error != nil {
                // Handle error if needed, maybe show a specific error placeholder
                Log.d("Error loading image: \(error?.localizedDescription ?? "Unknown error") for URL: \(String(describing: url))")
            }
            // Ensure contentMode is correct after loading
            self?.contentMode = .scaleAspectFill
            self?.clipsToBounds = true
        }
    }
}

// MARK: - Custom Cells

class BookTableViewCell: UITableViewCell {
    static let reuseIdentifier = "BookCell"

    private let containerView = UIView() // Add container
    private let coverImageView = UIImageView()
    private let titleLabel = UILabel()
    private let authorLabel = UILabel()
    private let textStackView = UIStackView()
    private let mainStackView = UIStackView()
    private let purchasedMarkImageView = UIImageView() // For purchased mark

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
        setupLayout()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupViews() {
        // Cell background should be clear/default to show table background
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        selectionStyle = .none

        // Container View (The Card)
        containerView.backgroundColor = UIColor(named: "SearchResultCardBackground") // Use color from Assets
        containerView.layer.cornerRadius = 8 // Set corner radius
        // Optional Shadow:
        // containerView.layer.shadowColor = UIColor.black.cgColor
        // containerView.layer.shadowOpacity = 0.05
        // containerView.layer.shadowOffset = CGSize(width: 0, height: 1)
        // containerView.layer.shadowRadius = 1
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)

        // Cover Image View
        coverImageView.contentMode = .scaleAspectFill // Set default
        coverImageView.clipsToBounds = true
        coverImageView.layer.cornerRadius = 4
        // Placeholder will be set by SDWebImage
        coverImageView.backgroundColor = .systemGray5 // Background while loading/error
        // Always show border like ShelfItemV
        coverImageView.layer.borderColor = UIColor(named: "ShelfItemBorderColor")?.cgColor
        coverImageView.layer.borderWidth = 0.5

        // Purchased Mark Image View
        purchasedMarkImageView.image = UIImage(named: "purchased_small")
        purchasedMarkImageView.contentMode = .scaleAspectFit
        purchasedMarkImageView.isHidden = true // Initially hidden
        purchasedMarkImageView.translatesAutoresizingMaskIntoConstraints = false
        coverImageView.addSubview(purchasedMarkImageView) // Add to coverImageView

        // Title Label
        titleLabel.numberOfLines = 2
        titleLabel.font = UIFont.primaryTextRegular
        titleLabel.textColor = UIColor.label

        // Author Label
        authorLabel.numberOfLines = 1
        authorLabel.font = UIFont.secondaryTextRegular
        authorLabel.textColor = UIColor.gray

        // Text StackView (Vertical: Title, Spacer, Author)
        textStackView.axis = .vertical
        textStackView.alignment = .fill // Changed from .leading to .fill for labels to take full width
        textStackView.spacing = 6.0 // Define the gap between title and author
        textStackView.distribution = .fill // Distribute space to push content up

        // Main StackView (Horizontal: Indent, Cover, Text)
        mainStackView.axis = .horizontal
        mainStackView.alignment = .top // Align tops of cover and text stack
        mainStackView.spacing = 12
        mainStackView.translatesAutoresizingMaskIntoConstraints = false

        containerView.addSubview(mainStackView) // Add to container view
    }

    private func setupLayout() {
        // Add title and author as arranged subviews to the textStackView
        textStackView.addArrangedSubview(titleLabel)
        textStackView.addArrangedSubview(authorLabel)

        // Add a spacer view to push title and author to the top
        let spacerView = UIView()
        // spacerView.backgroundColor = .red // Uncomment for debugging layout
        textStackView.addArrangedSubview(spacerView)

        // Add arranged subviews to main stack (cover + text Vstack)
        mainStackView.addArrangedSubview(coverImageView)
        mainStackView.addArrangedSubview(textStackView)

        NSLayoutConstraint.activate([
            // Container constraints (as before)
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),

            // Main Stack constraints (as before)
            mainStackView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            mainStackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12),
            mainStackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            mainStackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            // Cover Image constraints (as before)
            coverImageView.widthAnchor.constraint(equalToConstant: 48),
            coverImageView.heightAnchor.constraint(equalToConstant: 64),

            // Text Stack View Height (as before)
            // Ensure textStackView itself has the correct height for .equalSpacing to work within.
            textStackView.heightAnchor.constraint(equalToConstant: 64),

            // --- Manual constraints for Title and Author within textStackView are REMOVED ---
            // The UIStackView's (textStackView) properties (axis, alignment, spacing, distribution)
            // will now handle the layout of titleLabel and authorLabel.

            // Purchased Mark constraints (top-trailing of coverImageView)
            purchasedMarkImageView.topAnchor.constraint(equalTo: coverImageView.topAnchor, constant: 0), // Adjust constant if padding is needed
            purchasedMarkImageView.trailingAnchor.constraint(equalTo: coverImageView.trailingAnchor, constant: 0), // Adjust constant if padding is needed
            // Optional: Set width/height constraints if image has intrinsic size but needs explicit sizing
            // purchasedMarkImageView.widthAnchor.constraint(equalToConstant: 20), // Example size
            // purchasedMarkImageView.heightAnchor.constraint(equalToConstant: 20), // Example size
        ])
        // Vertical priorities to ensure labels behave as expected within the stack view
        titleLabel.setContentCompressionResistancePriority(.required, for: .vertical)
        titleLabel.setContentHuggingPriority(.defaultLow, for: .vertical)
        authorLabel.setContentCompressionResistancePriority(.required, for: .vertical)
        authorLabel.setContentHuggingPriority(.required, for: .vertical)
    }

    func configure(with book: any BookRepresentable, isInGroup: Bool, cornerMask: CACornerMask, isStoreCell: Bool = false) {
        // Apply corner mask to container view
        containerView.layer.maskedCorners = cornerMask

        // Ensure corner radius is applied regardless of mask
        containerView.layer.cornerRadius = 8
        containerView.clipsToBounds = true

        // Log the corner mask for debugging
        if cornerMask.contains(.layerMinXMaxYCorner) || cornerMask.contains(.layerMaxXMaxYCorner) {
            Log.d("DEBUG: Applying bottom corner radius to cell for book: \(book.title)")
        }

        // Call helper - ensure fonts/colors passed are qualified if needed
        // (Helper function call itself is okay if helper is global)
        Log.d("DEBUG [BookTVC Configure]: Book Title before highlight: \(book.title)")
        let titleAttributed = createHighlightedAttributedString(
            text: book.title,
            font: UIFont.primaryTextRegular,
            textColor: UIColor.label
        )
        Log.d("DEBUG [BookTVC Configure]: Book Title AFTER highlight: \(titleAttributed)")
        titleLabel.attributedText = titleAttributed

        Log.d("DEBUG [BookTVC Configure]: Book Author before highlight: \(book.author)")
        let authorAttributed = createHighlightedAttributedString(
            text: book.author,
            font: UIFont.secondaryTextRegular,
            textColor: UIColor.gray
        )
        Log.d("DEBUG [BookTVC Configure]: Book Author AFTER highlight: \(authorAttributed)")
        authorLabel.attributedText = authorAttributed

        // Load image using the helper
        coverImageView.loadImageFromUrlString(book.coverImageUrl)

        // Show/hide purchased mark for store cells
        if isStoreCell, let storeBook = book as? StoreBook {
            purchasedMarkImageView.isHidden = !(storeBook.purchased ?? false)
        } else {
            purchasedMarkImageView.isHidden = true // Always hidden for non-store cells or if not a StoreBook
        }

        // Handle Indentation
        let indent: CGFloat = isInGroup ? 32 : 16
        mainStackView.layoutMargins = UIEdgeInsets(top: 0, left: indent - 16, bottom: 0, right: 0)
        mainStackView.isLayoutMarginsRelativeArrangement = true
        setNeedsLayout()
    }
}

// MARK: - Group TableView Cell (Restored)

class GroupTableViewCell: UITableViewCell {
    static let reuseIdentifier = "GroupCell"

    private let containerView = UIView() // Main card container
    private let coverContainerView = UIView() // Container for the composite cover
    private let backgroundImageView = UIImageView() // For cover_archive
    private let folderIconImageView = UIImageView() // For folder_outlined
    // ImageViews for 2x2 grid
    private let bookCoverTL = UIImageView()
    private let bookCoverTR = UIImageView()
    private let bookCoverBL = UIImageView()
    private let bookCoverBR = UIImageView()

    private let titleLabel = UILabel()
    private let mainStackView = UIStackView() // Horizontal stack: Cover Container + Title

    // Constants for layout based on design specs
    private let coverWidth: CGFloat = 48
    private let coverHeight: CGFloat = 64
    private let bookCoverWidth: CGFloat = 20.266666412353516
    private let bookCoverHeight: CGFloat = 27
    private let bookCoverRadius: CGFloat = 2
    private let verticalPadding: CGFloat = 4
    private let horizontalPadding: CGFloat = 2.6666666 // Approx 2.67
    private let verticalSpacing: CGFloat = 2
    private let horizontalSpacing: CGFloat = 2.1333333 // Approx 2.13

    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupViews()
        setupLayout()
    }

    @available(*, unavailable)
    required init?(coder _: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    private func setupViews() {
        backgroundColor = .clear
        contentView.backgroundColor = .clear
        selectionStyle = .none

        // Container View (Card)
        containerView.backgroundColor = UIColor(named: "SearchResultCardBackground") // Use color from Assets
        containerView.layer.cornerRadius = 8
        containerView.translatesAutoresizingMaskIntoConstraints = false
        contentView.addSubview(containerView)

        // --- Cover Setup ---
        coverContainerView.clipsToBounds = true
        coverContainerView.layer.cornerRadius = 4
        coverContainerView.translatesAutoresizingMaskIntoConstraints = false
        // Give container a background for when grid is visible
        coverContainerView.backgroundColor = UIColor(named: "SearchResultGroupItemBackground")
        // Add border with SearchResultGroupItemBorder color
        coverContainerView.layer.borderWidth = 0.5
        coverContainerView.layer.borderColor = UIColor(named: "SearchResultGroupItemBorder")?.cgColor

        backgroundImageView.image = UIImage(named: "cover_archive")
        backgroundImageView.contentMode = .scaleAspectFill
        backgroundImageView.translatesAutoresizingMaskIntoConstraints = false
        coverContainerView.addSubview(backgroundImageView)

        folderIconImageView.image = UIImage(named: "folder_outlined")
        folderIconImageView.contentMode = .scaleAspectFit
        folderIconImageView.translatesAutoresizingMaskIntoConstraints = false
        coverContainerView.addSubview(folderIconImageView)

        // Setup individual book covers
        for iv in [bookCoverTL, bookCoverTR, bookCoverBL, bookCoverBR] {
            iv.backgroundColor = .systemGray6
            iv.contentMode = .scaleAspectFill
            iv.clipsToBounds = true
            iv.layer.cornerRadius = bookCoverRadius
            iv.layer.masksToBounds = true
            iv.translatesAutoresizingMaskIntoConstraints = false
            // Add directly to cover container
            coverContainerView.addSubview(iv)
        }

        // Title Label
        titleLabel.numberOfLines = 2
        titleLabel.font = UIFont.primaryTextRegular
        titleLabel.textColor = UIColor.label

        // Main StackView (Horizontal)
        mainStackView.axis = .horizontal
        mainStackView.alignment = .top
        mainStackView.spacing = 12
        mainStackView.translatesAutoresizingMaskIntoConstraints = false
        containerView.addSubview(mainStackView)
    }

    private func setupLayout() {
        // Add cover container and title to main horizontal stack
        mainStackView.addArrangedSubview(coverContainerView)
        mainStackView.addArrangedSubview(titleLabel)

        NSLayoutConstraint.activate([
            // Container (Card) constraints (as before)
            containerView.topAnchor.constraint(equalTo: contentView.topAnchor),
            containerView.bottomAnchor.constraint(equalTo: contentView.bottomAnchor),
            containerView.leadingAnchor.constraint(equalTo: contentView.leadingAnchor, constant: 24),
            containerView.trailingAnchor.constraint(equalTo: contentView.trailingAnchor, constant: -24),

            // Main Stack constraints (inside container, as before)
            mainStackView.topAnchor.constraint(equalTo: containerView.topAnchor, constant: 12),
            mainStackView.bottomAnchor.constraint(equalTo: containerView.bottomAnchor, constant: -12),
            mainStackView.leadingAnchor.constraint(equalTo: containerView.leadingAnchor, constant: 16),
            mainStackView.trailingAnchor.constraint(equalTo: containerView.trailingAnchor, constant: -16),

            // Cover Container Size
            coverContainerView.widthAnchor.constraint(equalToConstant: coverWidth),
            coverContainerView.heightAnchor.constraint(equalToConstant: coverHeight),

            // Background Image constraints
            backgroundImageView.topAnchor.constraint(equalTo: coverContainerView.topAnchor),
            backgroundImageView.bottomAnchor.constraint(equalTo: coverContainerView.bottomAnchor),
            backgroundImageView.leadingAnchor.constraint(equalTo: coverContainerView.leadingAnchor),
            backgroundImageView.trailingAnchor.constraint(equalTo: coverContainerView.trailingAnchor),

            // Folder Icon constraints (centered)
            folderIconImageView.centerXAnchor.constraint(equalTo: coverContainerView.centerXAnchor),
            folderIconImageView.centerYAnchor.constraint(equalTo: coverContainerView.centerYAnchor),
            folderIconImageView.widthAnchor.constraint(equalToConstant: 27), // Match GroupShelfItemV
            folderIconImageView.heightAnchor.constraint(equalToConstant: 27),

            // --- Explicit Constraints for Book Covers ---
            // Top Left
            bookCoverTL.topAnchor.constraint(equalTo: coverContainerView.topAnchor, constant: verticalPadding),
            bookCoverTL.leadingAnchor.constraint(equalTo: coverContainerView.leadingAnchor, constant: horizontalPadding),
            bookCoverTL.widthAnchor.constraint(equalToConstant: bookCoverWidth),
            bookCoverTL.heightAnchor.constraint(equalToConstant: bookCoverHeight),

            // Top Right
            bookCoverTR.topAnchor.constraint(equalTo: bookCoverTL.topAnchor),
            bookCoverTR.leadingAnchor.constraint(equalTo: bookCoverTL.trailingAnchor, constant: horizontalSpacing),
            bookCoverTR.widthAnchor.constraint(equalToConstant: bookCoverWidth),
            bookCoverTR.heightAnchor.constraint(equalToConstant: bookCoverHeight),
            // Optional: bookCoverTR.trailingAnchor.constraint(equalTo: coverContainerView.trailingAnchor, constant: -horizontalPadding),

            // Bottom Left
            bookCoverBL.topAnchor.constraint(equalTo: bookCoverTL.bottomAnchor, constant: verticalSpacing),
            bookCoverBL.leadingAnchor.constraint(equalTo: bookCoverTL.leadingAnchor),
            bookCoverBL.widthAnchor.constraint(equalToConstant: bookCoverWidth),
            bookCoverBL.heightAnchor.constraint(equalToConstant: bookCoverHeight),
            // Optional: bookCoverBL.bottomAnchor.constraint(equalTo: coverContainerView.bottomAnchor, constant: -verticalPadding),

            // Bottom Right
            bookCoverBR.topAnchor.constraint(equalTo: bookCoverBL.topAnchor),
            bookCoverBR.leadingAnchor.constraint(equalTo: bookCoverBL.trailingAnchor, constant: horizontalSpacing),
            bookCoverBR.widthAnchor.constraint(equalToConstant: bookCoverWidth),
            bookCoverBR.heightAnchor.constraint(equalToConstant: bookCoverHeight),
            // Optional: bookCoverBR.trailingAnchor.constraint(equalTo: coverContainerView.trailingAnchor, constant: -horizontalPadding),
            // Optional: bookCoverBR.bottomAnchor.constraint(equalTo: coverContainerView.bottomAnchor, constant: -verticalPadding),

            // Title Label height (as before)
            titleLabel.heightAnchor.constraint(greaterThanOrEqualToConstant: 20),
        ])
        // ... (Hugging/Compression for titleLabel as before) ...
    }

    func configure(with group: ShelfGroup, cornerMask: CACornerMask) {
        // Apply corner mask to container view
        containerView.layer.maskedCorners = cornerMask

        // Ensure corner radius is applied regardless of mask
        containerView.layer.cornerRadius = 8
        containerView.clipsToBounds = true

        // Log the corner mask for debugging
        if cornerMask.contains(.layerMinXMaxYCorner) || cornerMask.contains(.layerMaxXMaxYCorner) {
            Log.d("DEBUG: Applying bottom corner radius to cell for group: \(group.title)")
        }

        titleLabel.attributedText = createHighlightedAttributedString(
            text: group.title,
            font: UIFont.primaryTextRegular,
            textColor: UIColor.label
        )

        let books = group.books ?? []
        let bookCovers = books.compactMap { $0.coverImageUrl }
        Log.d("Group \(group.title) - configuring cover. Book count: \(books.count)") // Debug Log.d

        if bookCovers.isEmpty {
            // Show folder icon & background, hide grid
            folderIconImageView.isHidden = false
            backgroundImageView.isHidden = false // Show archive background
            // Hide all book covers
            [bookCoverTL, bookCoverTR, bookCoverBL, bookCoverBR].forEach { $0.isHidden = true }
        } else {
            // Show grid, hide folder icon & background
            folderIconImageView.isHidden = true
            backgroundImageView.isHidden = true // Hide archive background

            // Show/hide and load images (loop is simpler now)
            let imageViews = [bookCoverTL, bookCoverTR, bookCoverBL, bookCoverBR]
            for (index, imageView) in imageViews.enumerated() {
                if index < bookCovers.count {
                    imageView.isHidden = false
                    Log.d("    Configuring imageView at index \(index) - Visible") // Debug Log.d
                    imageView.loadImageFromUrlString(bookCovers[index])
                } else {
                    imageView.isHidden = true
                    Log.d("    Configuring imageView at index \(index) - Hidden") // Debug Log.d
                    imageView.image = nil
                }
            }
        }
    }
}

// MARK: - Highlighting Helper (Restored)

// This function depends on HighlightProcessor. Ensure HighlightProcessor is globally accessible
// or defined/imported appropriately.
/// Creates an NSAttributedString with highlighted ranges based on {{{ }}} markers.
func createHighlightedAttributedString(text: String,
                                       font: UIFont,
                                       textColor: UIColor,
                                       highlightColor: UIColor = .orange) -> NSAttributedString
{
    // Simple safety check to prevent empty text issues
    guard !text.isEmpty else {
        return NSAttributedString(string: "", attributes: [.font: font, .foregroundColor: textColor])
    }

    // Optimize: Skip processing if no highlight markers are present
    if !text.contains("{{{") && !text.contains("}}}") {
        return NSAttributedString(string: text, attributes: [.font: font, .foregroundColor: textColor])
    }

    do {
        // Use the existing HighlightProcessor
        let (processed, ranges, _) = try HighlightProcessor.process(text)

        // Create base attributes
        let attributes: [NSAttributedString.Key: Any] = [
            .font: font,
            .foregroundColor: textColor,
        ]
        let attributedString = NSMutableAttributedString(string: processed, attributes: attributes)

        // Apply highlight color to ranges
        for range in ranges {
            if range.location + range.length <= processed.utf16.count { // Use utf16 count for NSRange
                attributedString.addAttribute(.foregroundColor, value: highlightColor, range: range)
            }
        }

        return attributedString
    } catch {
        Log.e("Error processing highlighted text for UILabel: \(error)")
        // On error, just return plain string without highlights
        let cleanedText = text.replacingOccurrences(of: "{{{", with: "").replacingOccurrences(of: "}}}", with: "")
        return NSAttributedString(string: cleanedText, attributes: [.font: font, .foregroundColor: textColor])
    }
}
