//
//  SearchResultListViewController+TableViewDelegate.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22. // Note: Date might need adjustment
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI // For UIHostingController in viewForHeaderInSection
import UIKit

// MARK: - UITableViewDelegate

extension SearchResultListViewController {
    // MARK: - Network Handling

    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        guard let type = sectionType(for: indexPath.section) else { return } // sectionType() is in the DataSource extension

        let items: [DisplayItem]
        let isLoading: Bool
        let hasMore: Bool

        switch type {
        case .shelf:
            items = shelfDisplayItems
            isLoading = false
            hasMore = false
        case .store:
            items = storeDisplayItems
            isLoading = searchState.isLoadingMoreStoreItems
            hasMore = searchState.hasMoreStoreResults
        }

        if items.isEmpty { return }
        if isLoading, hasMore, indexPath.row == items.count { return } // It's the loading cell
        guard indexPath.row < items.count else {
            Log.e("ERROR: Invalid row index \(indexPath.row) in didSelectRowAt")
            return
        }

        let displayItem = items[indexPath.row]
        switch displayItem {
        case let .shelfBook(book, _):
            // This is a shelf item, use existing handleBookTap logic for ShelfBook
            handleBookTap(book: book)
        case let .storeBook(book):
            // This is a store item, navigate to product detail
            Log.d("DEBUG: Tapped store book: \(book.title) (ID: \(book.productId))")
            RoutableManager.navigate(toPath: RouterName.productDetail.withParam(book.productId))
        case let .group(group):
            guard type == .shelf else { break } // Groups only in shelf section
            handleGroupTap(group: group) // Assumes handleGroupTap is accessible
        case .loading:
            break // Should not be selectable
        case .networkUnavailable:
            break // Network unavailable cell is not selectable
        }
    }

    // MARK: - Pagination Trigger / Cell Display

    func tableView(_: UITableView, willDisplay _: UITableViewCell, forRowAt indexPath: IndexPath) {
        guard let type = sectionType(for: indexPath.section) else { return } // sectionType() is in the DataSource extension

        // Only handle pagination for STORE section
        guard type == .store else { return }

        let items: [DisplayItem] = storeDisplayItems
        let isLoading: Bool = searchState.isLoadingMoreStoreItems
        let hasMore: Bool = searchState.hasMoreStoreResults
        // The loadMoreAction now directly calls the searchState method.
        let sectionIdentifier = "STORE"

        // This check is for the loading cell itself, no action needed.
        if isLoading, hasMore, indexPath.row == items.count {
            Log.d("DEBUG [\(sectionIdentifier)]: Displaying loading indicator cell at row \(indexPath.row)")
            return
        }

        // Trigger loading more items if near the end, not already loading, and more items are available.
        let distanceFromEnd = items.count - indexPath.row
        if distanceFromEnd <= 3, !isLoading, hasMore {
            Log.d("DEBUG [\(sectionIdentifier)]: Near end of list (row \(indexPath.row) of \(items.count)), triggering load more")
            DispatchQueue.main.async { [weak self] in
                self?.searchState.loadMoreStoreResults()
            }
        } else if distanceFromEnd <= 3 {
            if isLoading { Log.d("DEBUG [\(sectionIdentifier)]: Near end, but not loading more - already loading") }
            if !hasMore { Log.d("DEBUG [\(sectionIdentifier)]: Near end, but not loading more - no more results") }
        }
    }

    // MARK: - Section Headers/Footers

    func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? {
        guard section < numberOfSections(in: tableView) else { // numberOfSections is in DataSource extension
            Log.e("ERROR: Invalid section index \(section) in viewForHeaderInSection")
            return nil
        }

        guard let results = searchResults, !searchState.searchQuery.isEmpty else { return nil }
        guard let type = sectionType(for: section) else { return nil } // sectionType() is in the DataSource extension

        var title: String
        var countText: String

        switch type {
        case .shelf:
            title = String(format: "search.results.shelfSection.title".localized, results.searchTerm)
            let count = Int(searchState.totalShelfResultsCount)
            countText = String(format: "search.results.itemsFound".localized, count)
            Log.d("DEBUG: Shelf header using total count: \(count)")
        case .store:
            title = String(format: "search.results.storeSection.title".localized, results.searchTerm)
            let count = Int(searchState.totalStoreResultsCount)
            countText = String(format: "search.results.itemsFound".localized, count)
            Log.d("DEBUG: Store header using total count: \(count)")
        }

        // Use the UIKit-based SearchResultSectionHeader class
        let headerView = SearchResultSectionHeader(
            title: title,
            countText: countText
        )

        return headerView
    }

    func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat {
        guard section < numberOfSections(in: tableView) else { // numberOfSections is in DataSource extension
            Log.e("ERROR: Invalid section index \(section) in heightForHeaderInSection")
            return 0
        }
        return !searchState.searchQuery.isEmpty ? 50 : 0
    }

    func tableView(_: UITableView, viewForFooterInSection _: Int) -> UIView? {
        // The logic for backgroundView (empty state) update is handled by updateOverallEmptyState.
        // This delegate method can return a minimal view or nil if no explicit footer view is desired per section.
        // For now, keep it as it might be intended for other footer elements in the future.
        updateOverallEmptyState() // Call this to ensure empty state is updated correctly
        return UIView() // Return a plain view, or nil if no footer needed.
    }

    // MARK: - UIScrollViewDelegate Methods

    // (Often included with UITableViewDelegate as UITableView inherits from UIScrollView)

    func scrollViewDidScroll(_ scrollView: UIScrollView) {
        hasUserScrolled = true // Assumes hasUserScrolled is accessible (internal/public)
        let position = scrollView.contentOffset.y
        let contentHeight = scrollView.contentSize.height
        let screenHeight = scrollView.frame.size.height
        let distanceFromBottom = contentHeight - position - screenHeight

        // Trigger for STORE section based on scroll position
        if distanceFromBottom < 100, contentHeight > 0 { // Check contentHeight to avoid division by zero or negative infinity
            if !searchState.isLoadingMoreStoreItems, searchState.hasMoreStoreResults {
                Log.d("DEBUG: Scroll reached near bottom for STORE (\(Int(distanceFromBottom))pts from bottom), triggering load more")
                DispatchQueue.main.async { [weak self] in
                    self?.searchState.loadMoreStoreResults()
                }
            }
        }
    }

    func scrollViewWillBeginDragging(_: UIScrollView) {
        searchState.resignFocus() // searchState is accessible
        hasUserScrolled = true // Assumes hasUserScrolled is accessible
    }

    // MARK: - Table Interaction

    // Moved from main class as it's related to table view interaction
    @objc func handleTableBackgroundTap() { // Needs to be @objc if called by selector
        searchState.resignFocus()
    }
}
