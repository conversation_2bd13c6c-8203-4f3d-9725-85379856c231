//
//  BookShelfSearchBar.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

// New Component for the Top Search Bar
struct BookShelfSearchBar: View {
    // Removed Binding, now uses the shared state directly
    // @Binding var searchText: String
    @StateObject private var searchState = BookShelfSearchState.shared

    var dismissAction: (() -> Void)?

    var body: some View {
        VStack(spacing: 0) {
            HStack {
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.gray)

                    // Replace TextField with FocusableTextField
                    FocusableTextField(
                        text: $searchState.searchQuery,
                        // Bind to the centralized state
                        isFocused: $searchState.isSearchBarFocused,
                        placeholder: "search.bar.placeholder".localized,
                        onCommit: { searchState.submitSearch() },
                        // Pass the resign focus action
                        resignFocusAction: { searchState.resignFocus() }
                    )

                    // Clear button inside TextField (appears when typing)
                    // Checks the shared state's query
                    if !searchState.searchQuery.isEmpty {
                        Button {
                            searchState.clearSearch()
                            searchState.isSearchBarFocused = true // Keep focus after clearing
                        } label: {
                            Image(systemName: "multiply.circle.fill")
                                .foregroundColor(.gray)
                        }
                    }
                }
                .padding(EdgeInsets(top: 8, leading: 10, bottom: 8, trailing: 10))
                .frame(height: 36)
                .background(Color("SearchFieldBackground"))
                .cornerRadius(10)

                // Cancel Button (always visible)
                Button("common.cancel".localized) {
                    // Use the resignFocus method
                    searchState.resignFocus()
                    searchState.clearSearch() // Keep clear search
                    self.dismissAction?()
                }
                .foregroundColor(Color("wd_orange"))
                .padding(.leading, 8)
            }
            .padding(.horizontal)
            .padding(.vertical, 10) // Padding for the search bar container
            .onDisappear {
                // Optionally resign focus on disappear
                searchState.isSearchBarFocused = false
            }

            Divider() // Add a divider below the search bar
        }
//        .background(Color(.systemBackground)) // Use system background for adaptability
    }
}

// MARK: - UIViewRepresentable for Focusable Text Field

struct FocusableTextField: UIViewRepresentable {
    @Binding var text: String
    @Binding var isFocused: Bool
    var placeholder: String
    var onCommit: () -> Void
    var resignFocusAction: () -> Void // Action to call centralized resign

    func makeCoordinator() -> Coordinator {
        Coordinator(self)
    }

    func makeUIView(context: Context) -> UITextField {
        let textField = UITextField()
        textField.placeholder = placeholder
        textField.delegate = context.coordinator
        textField.returnKeyType = .search
        textField.setContentHuggingPriority(.defaultLow, for: .horizontal)
        textField.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        textField.borderStyle = .none
        textField.font = UIFont.systemFont(ofSize: 16)
        textField.tintColor = UIColor(named: "wd_orange")
        return textField
    }

    func updateUIView(_ uiView: UITextField, context _: Context) {
        // Only update text if it changed to avoid unnecessary text field updates
        if uiView.text != text {
            uiView.text = text
        }

        // Try to gain focus if binding is true and field isn't focused
        if isFocused && !uiView.isFirstResponder {
            // Ensure it runs after the current render pass
            DispatchQueue.main.async {
                uiView.becomeFirstResponder()
            }
        }

        // Resign focus if binding is false and field *is* focused
        if !isFocused && uiView.isFirstResponder {
            // Ensure it runs after the current render pass
            DispatchQueue.main.async {
                uiView.resignFirstResponder()
            }
        }
    }

    class Coordinator: NSObject, UITextFieldDelegate {
        var parent: FocusableTextField

        init(_ parent: FocusableTextField) {
            self.parent = parent
        }

        func textFieldDidChangeSelection(_ textField: UITextField) {
            // Use DispatchQueue to avoid modifying state during view update
            DispatchQueue.main.async {
                self.parent.text = textField.text ?? ""
            }
        }

        func textFieldDidBeginEditing(_: UITextField) {
            DispatchQueue.main.async {
                self.parent.isFocused = true // Update binding
            }
        }

        func textFieldDidEndEditing(_: UITextField) {
            // Update binding when editing ends
            DispatchQueue.main.async {
                self.parent.isFocused = false // Update binding
            }
        }

        func textFieldShouldReturn(_: UITextField) -> Bool {
            parent.onCommit() // Call the commit action

            // Call the centralized resign focus action
            parent.resignFocusAction()

            return true // Keep default behavior
        }
    }
}
