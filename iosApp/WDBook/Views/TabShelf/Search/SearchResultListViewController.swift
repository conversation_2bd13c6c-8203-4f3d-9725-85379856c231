//
//  SearchResultListViewController.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22. // Note: Date might need adjustment
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import Combine
import SDWebImage // Not directly used here, but cells it configures might use it. Cells are in another file.
import UIKit

// New Enums for State Management
enum TableSectionState {
    case loading // Items are empty, and initial data is being loaded (primarily for store)
    case empty // Items are empty, and not in an initial loading state
    case hasItems // Items are present
}

enum SectionType {
    case shelf
    case store
}

class SearchResultListViewController: UIViewController, UITableViewDelegate {
    let tableView = UITableView(frame: .zero, style: .grouped) // Use grouped style for sections
    var searchResults: SearchResults?
    // Store the results

    // Network status
    var storeNetworkUnavailable: Bool = false {
        didSet {
            if storeNetworkUnavailable != oldValue {
                DispatchQueue.main.async {
                    self.tableView.reloadSections(IndexSet(integer: 1), with: .automatic)
                }
            }
        }
    }

    // Access the shared search state
    let searchState = BookShelfSearchState.shared
    // Store Combine cancellables
    private var cancellables = Set<AnyCancellable>()

    // Flag to track if user has explicitly scrolled
    var hasUserScrolled = false

    // Unified flag to prevent concurrent view updates
    private var isViewUpdating = false
    private let updateQueue = DispatchQueue(label: "com.wedevote.search.updateQueue")

    // MARK: - Helper enum/struct to represent flattened data for the table view

    enum DisplayItem: Hashable {
        case shelfBook(ShelfBook, isInGroup: Bool)
        case storeBook(StoreBook)
        case group(ShelfGroup)
        case loading // New case for loading indicator cell
        case networkUnavailable // New case for network unavailable cell
    }

    var shelfDisplayItems: [DisplayItem] = []
    var storeDisplayItems: [DisplayItem] = []

    // Helper function to determine section state
    func determineState(for section: SectionType) -> TableSectionState {
        let items = itemsForSection(section)

        if shouldShowNetworkUnavailable(section) {
            return .empty // Treat as empty so we can show a custom message
        } else if isLoadingInitial(section, items) {
            return .loading
        } else if items.isEmpty {
            return .empty
        } else {
            return .hasItems
        }
    }

    private func shouldShowNetworkUnavailable(_ section: SectionType) -> Bool {
        return section == .store && storeNetworkUnavailable && storeDisplayItems.isEmpty
    }

    private func isLoadingInitial(_ section: SectionType, _ items: [DisplayItem]) -> Bool {
        return section == .store && items.isEmpty && searchState.isLoadingMoreStoreItems
    }

    private func itemsForSection(_ section: SectionType) -> [DisplayItem] {
        return section == .shelf ? shelfDisplayItems : storeDisplayItems
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        setupTableView()
        setupStateObservation()
    }

    // Set up Combine publishers to observe state changes
    private func setupStateObservation() {
        // Flag to avoid reentrant updates during layout
        // var isUpdating = false // Replaced by self.isViewUpdating
        // let updateQueue = DispatchQueue(label: "com.wedevote.search.updateQueue") // Moved to class property

        // Observe search results changes
        searchState.$searchResults
            .receive(on: DispatchQueue.main) // Still receive on main for initial processing if needed before queue
            .sink { [weak self] results in
                guard let self = self else { return }

                self.updateQueue.async { // Serialize update logic
                    guard !self.isViewUpdating else {
                        Log.w("WARNING: searchResults - Skipping update while another is in progress")
                        return
                    }
                    self.isViewUpdating = true

                    // Call the processing part of update (which is async)
                    self.processShelfResultsAndUpdateUI(results)
                }
            }
            .store(in: &cancellables)

        // Observe STORE search results changes
        searchState.$storeSearchResults
            .receive(on: DispatchQueue.main) // Still receive on main for initial processing
            .sink { [weak self] receivedBooks in // Changed parameter name to reflect it's now [Book]
                guard let self = self else { return }

                self.updateQueue.async { // Serialize update logic and manage isViewUpdating
                    guard !self.isViewUpdating else {
                        Log.w("WARNING: storeSearchResults (now [Book]) - Skipping update while another is in progress")
                        return
                    }
                    self.isViewUpdating = true

                    // Perform mapping and related logging on a global concurrent queue
                    DispatchQueue.global(qos: .userInitiated).async {
                        Log.d("DEBUG: Store results changed. Received Books count: \(receivedBooks.count)")
                        // Print details of each received Book object
                        for (index, book) in receivedBooks.enumerated() {
                            Log.d("  Received Book [\(index)]: ID \(book.resourceId), Title \(book.title), Author \(book.author)")
                        }

                        // Map directly from [StoreBook] to [DisplayItem]
                        let newStoreDisplayItems = receivedBooks.map { storeBook -> DisplayItem in
                            return DisplayItem.storeBook(storeBook)
                        }

                        Log.d("DEBUG: Mapped received StoreBooks to DisplayItem.storeBook. Count: \(newStoreDisplayItems.count)")

                        // Switch back to main thread for UI updates
                        DispatchQueue.main.async {
                            self.storeDisplayItems = newStoreDisplayItems

                            UIView.performWithoutAnimation {
                                self.tableView.reloadData() // Or reload section 1
                            }
                            self.updateOverallEmptyState()

                            // Reset isViewUpdating flag via the updateQueue to maintain synchronization
                            self.updateQueue.asyncAfter(deadline: .now() + 0.05) { // Ensure this is on updateQueue
                                self.isViewUpdating = false
                            }
                        }
                    }
                }
            }
            .store(in: &cancellables)

        // Observe STORE loading state changes
        searchState.$isLoadingMoreStoreItems
            .combineLatest(searchState.$hasMoreStoreResults)
            .receive(on: DispatchQueue.main)
            .debounce(for: .milliseconds(100), scheduler: DispatchQueue.main) // Keep debounce
            .sink { [weak self] isLoading, hasMore in // isLoading and hasMore are from searchState
                guard let self = self else { return }
                self.updateLoadingIndicatorVisibility(forShelf: false, isLoading: isLoading, hasMore: hasMore)
            }
            .store(in: &cancellables)
    }

    // Renamed and refactored from handleLoadingStateChange
    private func updateLoadingIndicatorVisibility(forShelf isShelf: Bool, isLoading: Bool, hasMore: Bool) {
        // If called for shelf, just return as it's no longer paginated
        guard !isShelf else {
            Log.d("DEBUG [SHELF]: Ignoring updateLoadingIndicatorVisibility call as shelf is not paginated.")
            return
        }

        let sectionIdentifier = "STORE" // Only store is relevant now
        Log.d("DEBUG [\(sectionIdentifier)]: updateLoadingIndicatorVisibility received: isLoading=\(isLoading), hasMore=\(hasMore)")

        updateQueue.async { // Serialize with other updates
            guard !self.isViewUpdating else {
                Log.w("WARNING [\(sectionIdentifier)]: updateLoadingIndicatorVisibility - Skipping update while view is already updating.")
                return
            }
            self.isViewUpdating = true

            DispatchQueue.main.async {
                // Safety check: Ensure the store section exists
                let sectionIndex = 1
                guard sectionIndex < self.tableView.numberOfSections else {
                    Log.w("WARNING [\(sectionIdentifier)]: Section \(sectionIndex) does not exist. Using reloadData instead.")
                    self.tableView.reloadData()
                    self.updateQueue.asyncAfter(deadline: .now() + 0.05) {
                        self.isViewUpdating = false
                    }
                    return
                }

                // Get current state to determine what changed
                let currentItemCount = self.storeDisplayItems.count
                let currentRows = self.tableView.numberOfRows(inSection: sectionIndex)

                // Check if we already have a loading cell at the end
                let hasLoadingCellCurrently = currentRows > currentItemCount
                let shouldShowLoadingCell = isLoading && hasMore

                // Safety check: If the table is in an inconsistent state, use reloadSections instead
                if currentRows < currentItemCount {
                    Log.w("WARNING [\(sectionIdentifier)]: Table in inconsistent state - rows (\(currentRows)) < items (\(currentItemCount)). Using reloadSection.")
                    UIView.performWithoutAnimation {
                        self.tableView.reloadSections(IndexSet(integer: sectionIndex), with: .none)
                    }
                    self.updateQueue.asyncAfter(deadline: .now() + 0.05) {
                        self.isViewUpdating = false
                    }
                    return
                }

                if hasLoadingCellCurrently == shouldShowLoadingCell {
                    // No change needed - state matches what's already showing
                    Log.d("DEBUG [\(sectionIdentifier)]: Loading state unchanged, no table update needed")
                    self.updateQueue.asyncAfter(deadline: .now() + 0.05) {
                        self.isViewUpdating = false
                    }
                    return
                }

                // Empty store results need special handling
                if currentItemCount == 0 {
                    Log.d("DEBUG [\(sectionIdentifier)]: Store has no items, using reloadSection instead of batch updates")
                    UIView.performWithoutAnimation {
                        self.tableView.reloadSections(IndexSet(integer: sectionIndex), with: .none)
                    }
                    self.updateQueue.asyncAfter(deadline: .now() + 0.05) {
                        self.isViewUpdating = false
                    }
                    return
                }

                // Use batch updates for smoother UI transitions when we have items
                CATransaction.begin()
                CATransaction.setDisableActions(true)

                self.tableView.performBatchUpdates({
                    if shouldShowLoadingCell && !hasLoadingCellCurrently {
                        // Need to add loading cell
                        Log.d("DEBUG [\(sectionIdentifier)]: Adding loading cell at row \(currentItemCount)")
                        self.tableView.insertRows(at: [IndexPath(row: currentItemCount, section: sectionIndex)], with: .none)
                    } else if !shouldShowLoadingCell && hasLoadingCellCurrently {
                        // Need to remove loading cell
                        Log.d("DEBUG [\(sectionIdentifier)]: Removing loading cell at row \(currentItemCount)")
                        self.tableView.deleteRows(at: [IndexPath(row: currentItemCount, section: sectionIndex)], with: .none)
                    }
                }, completion: nil)

                CATransaction.commit()

                self.updateQueue.asyncAfter(deadline: .now() + 0.05) {
                    self.isViewUpdating = false
                }
            }
        }
    }

    func setupTableView() {
        view.addSubview(tableView)
        tableView.translatesAutoresizingMaskIntoConstraints = false
        NSLayoutConstraint.activate([
            tableView.topAnchor.constraint(equalTo: view.topAnchor),
            tableView.bottomAnchor.constraint(equalTo: view.bottomAnchor),
            tableView.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            tableView.trailingAnchor.constraint(equalTo: view.trailingAnchor),
        ])

        tableView.dataSource = self
        tableView.delegate = self
        tableView.prefetchDataSource = self // Enable prefetching for smoother scrolling
        tableView.backgroundColor = UIColor(named: "SearchResultListBackground")
        tableView.separatorStyle = .none
        tableView.rowHeight = UITableView.automaticDimension
        tableView.estimatedRowHeight = 88
        tableView.sectionHeaderHeight = 50
        tableView.sectionFooterHeight = 16
        tableView.allowsSelection = true

        // Add Tap Gesture to dismiss keyboard on background tap
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTableBackgroundTap))
        tapGesture.cancelsTouchesInView = false
        tableView.addGestureRecognizer(tapGesture)

        // Register cells (Cell classes are defined in BookShelfSearchCells.swift)
        tableView.register(BookTableViewCell.self, forCellReuseIdentifier: BookTableViewCell.reuseIdentifier)
        tableView.register(GroupTableViewCell.self, forCellReuseIdentifier: GroupTableViewCell.reuseIdentifier)
        tableView.register(LoadingTableViewCell.self, forCellReuseIdentifier: LoadingTableViewCell.reuseIdentifier) // Register loading cell
        tableView.register(UITableViewCell.self, forCellReuseIdentifier: "BasicCell")
        // Register the new empty result cell
        tableView.register(EmptyResultTableViewCell.self, forCellReuseIdentifier: EmptyResultTableViewCell.reuseIdentifier)
        // Register network unavailable cell
        tableView.register(NetworkUnavailableTableViewCell.self, forCellReuseIdentifier: NetworkUnavailableTableViewCell.reuseIdentifier)
    }

    // This method is now called from the searchResults sink, wrapped in isViewUpdating logic
    private func processShelfResultsAndUpdateUI(_ results: SearchResults?) {
        // This part runs on self.updateQueue (background thread)
        let oldResultsEmpty = (shelfDisplayItems.isEmpty && storeDisplayItems.isEmpty) // Capture state before modification
        let oldShelfItemCount = shelfDisplayItems.count

        var newShelfItems: [DisplayItem] = []
        if let results = results {
            Log.d("DEBUG: Original results - Shelf: \(results.shelfResults.count), Store: \(results.storeResults.count)")
            logDebugItemDetails(results)

            var groupItems = 0
            var bookItems = 0
            var booksFromGroups = 0
            var allItemIDs = Set<String>()

            for item in results.shelfResults {
                switch item {
                case let .shelfBook(shelfBook):
                    allItemIDs.insert(shelfBook.resourceId)
                    newShelfItems.append(.shelfBook(shelfBook, isInGroup: false))
                    bookItems += 1
                case let .group(group):
                    allItemIDs.insert(group.itemId)
                    newShelfItems.append(.group(group))
                    groupItems += 1
                    if let books = group.books {
                        booksFromGroups += books.count
                        for groupBook in books {
                            allItemIDs.insert(groupBook.resourceId)
                            newShelfItems.append(.shelfBook(groupBook, isInGroup: true))
                        }
                    }
                }
            }
            Log.d("DEBUG: Flattened items - Books: \(bookItems), Groups: \(groupItems), Books from groups: \(booksFromGroups)")
            Log.d("DEBUG: Total shelf display items: \(newShelfItems.count) (previous: \(oldShelfItemCount))")
            Log.d("DEBUG: Tracked \(allItemIDs.count) unique item IDs")
        }

        DispatchQueue.main.async { // Switch to main thread for UI updates
            if CATransaction.disableActions() { // Check for existing animation transaction
                Log.w("WARNING: Animation in progress, deferring UI update")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
                    // Retry with exponential backoff or notification mechanism
                    self?.performShelfUIUpdates(results: results, newShelfItems: newShelfItems,
                                                oldShelfItemCount: oldShelfItemCount,
                                                oldResultsEmpty: oldResultsEmpty)
                }
                return
            }
            self.performShelfUIUpdates(results: results, newShelfItems: newShelfItems, oldShelfItemCount: oldShelfItemCount, oldResultsEmpty: oldResultsEmpty)
        }
    }

    // Extracted UI update logic for shelf results
    private func performShelfUIUpdates(results: SearchResults?, newShelfItems: [DisplayItem], oldShelfItemCount: Int, oldResultsEmpty: Bool) {
        let isInitialLoadOrPreviouslyEmpty = oldResultsEmpty // Use the captured state

        searchResults = results // Update local cache of searchResults
        shelfDisplayItems = newShelfItems
        let newShelfItemCount = shelfDisplayItems.count

        // Determine if current results (shelf + store) are empty
        let currentResultsAreEmpty = shelfDisplayItems.isEmpty && storeDisplayItems.isEmpty // Use current storeDisplayItems

        CATransaction.begin()
        CATransaction.setDisableActions(true)

        if isInitialLoadOrPreviouslyEmpty || oldResultsEmpty != currentResultsAreEmpty {
            Log.d("DEBUG [ShelfUpdate]: Performing complete reload - initial/emptiness changed.")
            tableView.reloadData()
        } else if oldShelfItemCount != newShelfItemCount { // Content changed
            Log.d("DEBUG [ShelfUpdate]: Shelf items changed (count from \(oldShelfItemCount) to \(newShelfItemCount)) - reloading section 0 or table.")
            if tableView.numberOfSections > 0 {
                tableView.reloadSections(IndexSet(integer: 0), with: .none)
            } else {
                tableView.reloadData() // Fallback
            }
        } else {
            Log.d("DEBUG [ShelfUpdate]: No significant shelf content change, skipping table update from processShelfResultsAndUpdateUI.")
            // Loading indicator changes are handled by updateLoadingIndicatorVisibility
        }

        CATransaction.commit()

        updateOverallEmptyState() // Update empty state after table changes
        tableView.layoutIfNeeded()

        // Reset isViewUpdating flag now that UI work for this stream is done
        updateQueue.asyncAfter(deadline: .now() + 0.05) { // Ensure this is on updateQueue
            self.isViewUpdating = false
            // If other updates were skipped, a coalescing mechanism would trigger them here.
        }
    }

    @available(*, deprecated, message: "Use processShelfResultsAndUpdateUI instead to ensure proper synchronization")
    func update(with results: SearchResults?) {
        // This method is now largely superseded by processShelfResultsAndUpdateUI
        // and the direct update logic in the storeSearchResults sink.
        // Kept for historical reference during refactoring, should be removed if no longer called.
        Log.w("DEPRECATED: update(with:) called. This should be handled by new processing methods.")
        // Fallback to new method if accidentally called to maintain some behavior:
        updateQueue.async {
            guard !self.isViewUpdating else { return }
            self.isViewUpdating = true
            self.processShelfResultsAndUpdateUI(results)
            // Note: isViewUpdating reset is handled by processShelfResultsAndUpdateUI
        }
    }

    private func logDebugItemDetails(_ results: SearchResults) {
        Log.d("DEBUG: Raw shelf items IDs/details:")
        for (index, item) in results.shelfResults.enumerated() {
            switch item {
            case let .shelfBook(book):
                Log.d("  \(index): SHELFBOOK: \(book.title) (ID: \(book.resourceId))")
            case let .group(group):
                Log.d("  \(index): GROUP: \(group.title) (ID: \(group.itemId), \(group.books?.count ?? 0) books)")
                if let books = group.books {
                    for (bIndex, book) in books.enumerated() {
                        Log.d("    \(bIndex): GROUP_BOOK: \(book.title) (ID: \(book.resourceId))")
                    }
                }
            }
        }
    }

    func printItemCounts() {
        Log.d("DIAGNOSTIC COUNT CHECK:")
        if tableView.numberOfSections > 0 {
            Log.d("- Total table rows (shelf): \(tableView.numberOfRows(inSection: 0))")
        } else {
            Log.d("- Total table rows (shelf): 0 (No sections)")
        }

        if searchResults != nil {
            Log.d("- Original results count: \(searchResults!.shelfResults.count)")
        } else {
            Log.d("- Original results: nil")
        }
        Log.d("- Flattened items in memory: \(shelfDisplayItems.count)")

        var visibleRows = 0
        for section in 0 ..< tableView.numberOfSections {
            visibleRows += tableView.numberOfRows(inSection: section)
        }
        Log.d("- Total visible rows in table: \(visibleRows)")
    }

    // MARK: - UITableViewDelegate

    // func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) { ... } // REMOVED

    // MARK: - Pagination Trigger

    // func tableView(_: UITableView, willDisplay _: UITableViewCell, forRowAt indexPath: IndexPath) { ... } // REMOVED

    // func tableView(_ tableView: UITableView, viewForHeaderInSection section: Int) -> UIView? { ... } // REMOVED

    // func tableView(_ tableView: UITableView, heightForHeaderInSection section: Int) -> CGFloat { ... } // REMOVED

    // func tableView(_: UITableView, viewForFooterInSection _: Int) -> UIView? { ... } // REMOVED

    // MARK: - UIScrollViewDelegate Methods

    // func scrollViewDidScroll(_ scrollView: UIScrollView) { ... } // REMOVED

    // func scrollViewWillBeginDragging(_: UIScrollView) { ... } // REMOVED

    // @objc private func handleTableBackgroundTap() { ... } // REMOVED (in Delegate extension)

    // MARK: - Navigation/Download Logic

    // All methods previously here (handleBookTap, checkDownloadStatusAndNavigate, handleGroupTap, initiateDownload, startDownloadProcess, getDownloadUrlAndStart) are now moved to SearchResultListViewController+Downloading.swift

    // MARK: - UITableViewDataSourcePrefetching

    // All methods previously here (tableView:prefetchRowsAt, tableView:cancelPrefetchingForRowsAt) are now moved to SearchResultListViewController+TableViewDataSource.swift

    func updateOverallEmptyState() {
        guard !searchState.searchQuery.isEmpty else {
            tableView.backgroundView = nil // No query, no empty state
            return
        }

        let shelfState = determineState(for: .shelf)
        let storeState = determineState(for: .store)

        // Show overall empty state if both sections are effectively empty
        // and a global search isn't in progress (which might populate them soon).
        // A section is considered empty for this purpose if its state is .empty.
        // If a section is .loading, it implies it will soon show a loading cell or items,
        // so the overall background shouldn't be "empty results".
        if shelfState == .empty && storeState == .empty && !searchState.isSearching {
            Log.d("DEBUG: Both shelf and store sections are in .empty state and not globally searching. Showing overall empty state background.")
            // Example: tableView.backgroundView = EmptyStateView(query: searchState.searchQuery)
            // For now, as per original, this implies a custom view should be set. If nil is intended for empty state, adjust logic.
            // This placeholder needs to be replaced with the actual empty state view instantiation.
            // If you have an EmptyStateView:
            // let emptyView = EmptyStateView(query: searchState.searchQuery)
            // self.tableView.backgroundView = UIHostingController(rootView: emptyView).view
            // If just clearing backgroundView (or setting to a simple color) is not the goal for empty state,
            // then the actual view needs to be constructed here.
        } else {
            Log.d("DEBUG: Conditions for overall empty state not met. Shelf: \(shelfState), Store: \(storeState), Global Searching: \(searchState.isSearching). Clearing backgroundView.")
            tableView.backgroundView = nil
        }
    }
}
