//
//  SearchResultListViewController+TableViewDataSource.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22. // Note: Date might need adjustment
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SDWebImage
import UIKit

// MARK: - UITableViewDataSource

extension SearchResultListViewController: UITableViewDataSource {
    // Helper to get section type, using the enum from the main class
    func sectionType(for section: Int) -> SectionType? {
        guard !searchState.searchQuery.isEmpty else { return nil } // Only show sections if there's a query
        return section == 0 ? .shelf : .store
    }

    func numberOfSections(in _: UITableView) -> Int {
        return !searchState.searchQuery.isEmpty ? 2 : 0 // Show 2 sections if query is not empty, else 0
    }

    func tableView(_: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let type = sectionType(for: section) else { return 0 }
        let state = determineState(for: type) // Use the new helper from the main class

        switch state {
        case .loading, .empty:
            return 1 // For the single placeholder cell (loading or empty)
        case .hasItems:
            var rowCount: Int
            switch type {
            case .shelf:
                rowCount = shelfDisplayItems.count
            case .store:
                rowCount = storeDisplayItems.count
                // Add extra row for pagination loading cell if needed for store
                if storeDisplayItems.count > 0 && searchState.isLoadingMoreStoreItems && searchState.hasMoreStoreResults {
                    rowCount += 1
                }
            }
            return rowCount
        }
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let type = sectionType(for: indexPath.section) else {
            Log.e("ERROR: Invalid section index \(indexPath.section) in cellForRowAt - no query or out of bounds")
            return tableView.dequeueReusableCell(withIdentifier: "BasicCell", for: indexPath) // Fallback
        }

        let currentSectionState = determineState(for: type)
        let sectionIdentifier = (type == .shelf) ? "SHELF" : "STORE"

        // Check for network unavailability for store section
        if type == .store && storeNetworkUnavailable && currentSectionState == .empty {
            Log.d("DEBUG [\(sectionIdentifier)]: Network unavailable, showing network unavailable cell")

            // Use the dedicated NetworkUnavailableTableViewCell
            guard let cell = tableView.dequeueReusableCell(withIdentifier: NetworkUnavailableTableViewCell.reuseIdentifier, for: indexPath) as? NetworkUnavailableTableViewCell else {
                fatalError("Failed to dequeue NetworkUnavailableTableViewCell")
            }

            // Configure cell with proper corner mask
            cell.configure(cornerMask: [.layerMinXMinYCorner, .layerMaxXMinYCorner, .layerMinXMaxYCorner, .layerMaxXMaxYCorner])
            return cell
        }

        switch currentSectionState {
        case .loading: // Display initial loading cell (applies mainly to store)
            Log.d("DEBUG [\(sectionIdentifier)]: Section empty, state is .loading, showing loading cell")
            guard let cell = tableView.dequeueReusableCell(withIdentifier: LoadingTableViewCell.reuseIdentifier, for: indexPath) as? LoadingTableViewCell else {
                fatalError("Failed to dequeue LoadingTableViewCell for .loading state")
            }
            cell.startLoading()
            // Apply full corner radius as it's the only cell
            cell.contentView.layer.maskedCorners = [.layerMinXMinYCorner, .layerMaxXMinYCorner, .layerMinXMaxYCorner, .layerMaxXMaxYCorner]
            cell.contentView.layer.cornerRadius = 8
            cell.contentView.clipsToBounds = true
            return cell

        case .empty: // Display empty result cell
            Log.d("DEBUG [\(sectionIdentifier)]: Section empty, state is .empty, showing empty result cell")
            guard let cell = tableView.dequeueReusableCell(withIdentifier: EmptyResultTableViewCell.reuseIdentifier, for: indexPath) as? EmptyResultTableViewCell else {
                fatalError("Failed to dequeue EmptyResultTableViewCell for .empty state")
            }
            return cell

        case .hasItems:
            let items = (type == .shelf) ? shelfDisplayItems : storeDisplayItems
            // Check if this is the pagination loading cell for the store section
            if type == .store && storeDisplayItems.count > 0 && searchState.isLoadingMoreStoreItems && searchState.hasMoreStoreResults && indexPath.row == storeDisplayItems.count {
                Log.d("DEBUG [\(sectionIdentifier)]: State is .hasItems, but this is the pagination loading cell at row \(indexPath.row)")
                guard let cell = tableView.dequeueReusableCell(withIdentifier: LoadingTableViewCell.reuseIdentifier, for: indexPath) as? LoadingTableViewCell else {
                    fatalError("Failed to dequeue LoadingTableViewCell for store pagination")
                }
                cell.startLoading()
                // Corners for last cell if it's the loading cell
                cell.contentView.layer.maskedCorners = [.layerMinXMaxYCorner, .layerMaxXMaxYCorner]
                cell.contentView.layer.cornerRadius = 8
                cell.contentView.clipsToBounds = true
                return cell
            }

            // Regular item cell
            guard indexPath.row < items.count else {
                Log.e("ERROR [\(sectionIdentifier)]: Invalid row index \(indexPath.row) for .hasItems state (count: \(items.count))")
                return tableView.dequeueReusableCell(withIdentifier: "BasicCell", for: indexPath) // Fallback
            }

            let displayItem = items[indexPath.row]
            let numberOfRows = items.count // Actual data items, not including pagination cell
            let isFirstRow = indexPath.row == 0
            // Last data row considers if a pagination cell will be shown after it (for store)
            var isLastDataRowConsideringPagination = indexPath.row == numberOfRows - 1
            if type == .store && searchState.isLoadingMoreStoreItems && searchState.hasMoreStoreResults {
                // If pagination cell is showing, then no data row is the absolute last visual row with rounded bottom corners
                isLastDataRowConsideringPagination = false
            }

            var cornerMask: CACornerMask = []
            if isFirstRow { cornerMask.insert([.layerMinXMinYCorner, .layerMaxXMinYCorner]) }
            if isLastDataRowConsideringPagination { cornerMask.insert([.layerMinXMaxYCorner, .layerMaxXMaxYCorner]) }
            // If it's the only data cell AND no pagination loader will follow
            if numberOfRows == 1 && !(type == .store && searchState.isLoadingMoreStoreItems && searchState.hasMoreStoreResults) {
                cornerMask = [.layerMinXMinYCorner, .layerMaxXMinYCorner, .layerMinXMaxYCorner, .layerMaxXMaxYCorner]
            }

            switch displayItem {
            case let .shelfBook(book, isInGroup):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: BookTableViewCell.reuseIdentifier, for: indexPath) as? BookTableViewCell else {
                    fatalError("Failed to dequeue BookTableViewCell")
                }
                cell.configure(with: book, isInGroup: isInGroup, cornerMask: cornerMask, isStoreCell: false)
                return cell

            case let .storeBook(book):
                guard let cell = tableView.dequeueReusableCell(withIdentifier: BookTableViewCell.reuseIdentifier, for: indexPath) as? BookTableViewCell else {
                    fatalError("Failed to dequeue BookTableViewCell")
                }
                cell.configure(with: book, isInGroup: false, cornerMask: cornerMask, isStoreCell: true)
                return cell

            case let .group(group):
                guard type == .shelf, // Groups only in shelf
                      let cell = tableView.dequeueReusableCell(withIdentifier: GroupTableViewCell.reuseIdentifier, for: indexPath) as? GroupTableViewCell
                else {
                    fatalError("Failed to dequeue GroupTableViewCell or wrong section type")
                }
                cell.configure(with: group, cornerMask: cornerMask)
                return cell

            case .loading: // Should not be hit if DisplayItem.loading is not used in shelf/storeDisplayItems
                Log.w("WARNING: DisplayItem.loading encountered directly in items array for .hasItems state.")
                return tableView.dequeueReusableCell(withIdentifier: "BasicCell", for: indexPath) // Fallback

            case .networkUnavailable: // Add missing case
                Log.w("WARNING: DisplayItem.networkUnavailable encountered directly in items array.")
                return tableView.dequeueReusableCell(withIdentifier: "BasicCell", for: indexPath) // Fallback
            }
        }
    }
}

// MARK: - UITableViewDataSourcePrefetching

extension SearchResultListViewController: UITableViewDataSourcePrefetching {
    func tableView(_: UITableView, prefetchRowsAt indexPaths: [IndexPath]) {
        for indexPath in indexPaths {
            guard let type = sectionType(for: indexPath.section) else { continue }

            let items: [DisplayItem]
            let isLoading: Bool
            let hasMore: Bool

            switch type {
            case .shelf:
                items = shelfDisplayItems
                isLoading = false
                hasMore = false
            case .store:
                items = storeDisplayItems
                isLoading = searchState.isLoadingMoreStoreItems
                hasMore = searchState.hasMoreStoreResults
            }

            if items.isEmpty { continue }
            if (isLoading && hasMore) && indexPath.row == items.count { continue } // Skip if it's the loading cell's placeholder
            guard indexPath.row < items.count else { continue }

            let displayItem = items[indexPath.row]
            var coverUrl: String? = nil

            switch displayItem {
            case let .shelfBook(book, _):
                coverUrl = book.coverImageUrl
            case let .storeBook(book):
                coverUrl = book.coverImageUrl
            case let .group(group):
                guard type == .shelf else { continue } // Groups are only in shelf
                coverUrl = group.books?.first?.coverImageUrl
            case .loading: // Should not be in items list
                continue
            case .networkUnavailable: // Add missing case
                continue
            }

            if let coverUrl = coverUrl, !coverUrl.isEmpty {
                let fullUrl = WDBookAppSDK.shared.getFullImageUrl(url: coverUrl)
                let finalUrlString = ImageManager.getImageUrl(fullUrl)
                if let finalUrl = URL(string: finalUrlString) {
                    SDWebImagePrefetcher.shared.prefetchURLs([finalUrl])
                }
            }
        }
    }

    func tableView(_: UITableView, cancelPrefetchingForRowsAt _: [IndexPath]) {
        // Optional: Cancel prefetching tasks if needed.
        // SDWebImagePrefetcher.shared.cancelPrefetching() // Example, if you want to cancel all
    }
}
