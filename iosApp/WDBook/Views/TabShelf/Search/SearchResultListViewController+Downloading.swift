//
//  SearchResultListViewController+Downloading.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22. // Note: Date might need adjustment
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import UIKit

// Assuming these SDKs and managers are imported in the bridging header or accessible module-wise.
// Add specific imports if direct access is needed and they are modularized (e.g., import WDBookSDKModule)

// MARK: - Navigation/Download Logic

extension SearchResultListViewController {
    func handleBookTap(book: ShelfBook) {
        guard WDBookSessionSDK.shared.isLogin else {
            AppState.shared.showLoginRegisterVAndPopToStore()
            return
        }

        if let fileId = book.fileId, !fileId.isEmpty {
            Log.d("Search Result: Using pre-fetched fileId: \(fileId) for resourceId: \(book.resourceId)")
            checkDownloadStatusAndNavigate(fileId: fileId, resourceId: book.resourceId, bookToDownload: book)
        } else {
            Log.w("Search Result: fileId not pre-fetched for resourceId: \(book.resourceId). Fetching...")
            WDBookStoreSDK.shared.getResourceFileEntity(resourceId: book.resourceId) { [weak self] result in
                guard let self = self else { return }
                switch result {
                case let .success(fileEntity):
                    guard let fetchedFileId = fileEntity?.fileId, !fetchedFileId.isEmpty else {
                        Log.e("Search Result: Could not get fileId via network for resourceId \(book.resourceId)")
                        // self.initiateDownload(for: book) // Ensure this call is removed/commented if present
                        Toaster.showToast(message: "search.toast.error.fetchBookInfoFailed".localized) // Show toast instead
                        return
                    }
                    // Now we have fileId, proceed to check status and navigate or download
                    self.checkDownloadStatusAndNavigate(fileId: fetchedFileId, resourceId: book.resourceId, bookToDownload: book)
                case let .failure(error):
                    Log.e("Search Result: Failed to get file entity for resourceId \(book.resourceId): \(error.localizedDescription)")
                    // Fallback: Attempt download which includes fetching file entity again
                    // self.initiateDownload(for: book) // Pass the original book // Removed download initiation
                    Toaster.showToast(message: "search.toast.error.fetchBookInfoFailed".localized) // Show toast instead
                }
            }
        }
    }

    func checkDownloadStatusAndNavigate(fileId: String, resourceId: String, bookToDownload _: ShelfBook) {
        let downloadInfo = WDBookDownloadSDK.shared.fetchDownloadFileEntity(fileId: fileId)
        let downloadState = downloadInfo?.downloadStatus ?? .empty // Default to .empty if nil

        DispatchQueue.main.async {
            switch downloadState {
            case .complete:
                Log.d("Search Result: Book \(resourceId) (fileId: \(fileId)) downloaded, opening.")
                RoutableManager.navigate(toPath: RouterName.readerWithId.withParam(resourceId))
            default:
                Log.d("Search Result: Book \(resourceId) (fileId: \(fileId)) has an unhandled status \(downloadState). Showing toast.")
                Toaster.showToast(message: "search.toast.info.bookNotDownloaded".localized)
            }
        }
    }

    func handleGroupTap(group: ShelfGroup) {
        guard WDBookSessionSDK.shared.isLogin else {
            AppState.shared.showLoginRegisterVAndPopToStore()
            return
        }
        // Ensure AppState and its properties are accessible
        if let index = AppState.shared.shelfCombineItems.firstIndex(where: { $0.itemId == group.itemId }) {
            Log.d("Search Result: Navigating to group list view for group: \(group.title) (ID: \(group.itemId)) at index \(index)")
            BookShelfSearchState.shared.navigatedToGroupViewFromSearch = true
            RoutableManager.showShelfGroupListView(archiveItemIndex: index)
        } else {
            Log.e("Search Result: Could not find tapped group (ID: \(group.itemId)) in AppState.shelfCombineItems. Cannot navigate.")
            Toaster.showToast(message: "search.toast.error.openGroupFailed".localized)
        }
    }
}
