//
//  HighlightedText.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22. // Note: Date might need adjustment
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit // Add UIKit import

/// A view that directly uses Text for short texts and custom solution for long texts
struct ProcessedHighlightedText: View {
    let text: String
    let highlightColor: UIColor
    let font: UIFont
    let textColor: UIColor

    init(text: String,
         highlightColor: UIColor = .orange,
         font: UIFont = .systemFont(ofSize: UIFont.labelFontSize),
         textColor: UIColor = .label)
    {
        self.text = text
        self.highlightColor = highlightColor
        self.font = font
        self.textColor = textColor
    }

    var body: some View {
        // 简化实现，不再使用GeometryReader和额外的高度计算
        HighlightableTextView(
            text: text,
            highlightColor: highlightColor,
            font: font,
            textColor: textColor
        )
    }
}

// UIViewRepresentable wrapper for UILabel to handle NSA<PERSON>ributedString
struct HighlightableTextView: UIViewRepresentable {
    var text: String
    var highlightColor: UIColor
    var font: UIFont
    var textColor: UIColor
    var lineLimit: Int = 0

    func makeUIView(context _: Context) -> UILabel {
        let label = UILabel()
        // 设置行数限制
        label.numberOfLines = lineLimit > 0 ? lineLimit : 0
        // 关键修改：改回byTruncatingTail确保在末尾显示省略号
        label.lineBreakMode = .byTruncatingTail

        // 不自动调整字体大小，确保正常换行
        label.adjustsFontSizeToFitWidth = false
        label.translatesAutoresizingMaskIntoConstraints = false

        return label
    }

    func updateUIView(_ uiView: UILabel, context _: Context) {
        // 处理文本并设置属性字符串
        uiView.attributedText = processText(text)

        // 确保UILabel有合适的宽度约束以正确换行
        if let superview = uiView.superview {
            let availableWidth = superview.bounds.width
            let maxWidth = max(200, availableWidth - 120) // 至少200pt宽度，减去图标和边距

            // 设置首选最大宽度，这对换行很重要
            uiView.preferredMaxLayoutWidth = maxWidth

            // 如果是两行限制，设置足够的高度
            if lineLimit == 2 {
                let minHeight = font.lineHeight * 2 + 4
                NSLayoutConstraint.activate([
                    // 设置高度约束
                    uiView.heightAnchor.constraint(greaterThanOrEqualToConstant: minHeight),
                ])
            }

            // 强制布局更新
            uiView.setNeedsLayout()
            uiView.layoutIfNeeded()
        }
    }

    private func processText(_ text: String) -> NSAttributedString {
        do {
            // 使用已有的HighlightProcessor进行处理
            let (processed, ranges, _) = try HighlightProcessor.process(text)

            // 创建段落样式
            let paragraphStyle = NSMutableParagraphStyle()
            // 关键修改：改回byTruncatingTail确保在末尾显示省略号
            paragraphStyle.lineBreakMode = .byTruncatingTail
            paragraphStyle.lineSpacing = 2 // 增加行间距，改善可读性

            // 创建基本属性字符串
            let attributes: [NSAttributedString.Key: Any] = [
                .font: font,
                .foregroundColor: textColor,
                .paragraphStyle: paragraphStyle,
            ]
            let attributedString = NSMutableAttributedString(string: processed, attributes: attributes)

            // 应用高亮
            for range in ranges {
                if range.location + range.length <= processed.count {
                    attributedString.addAttribute(.foregroundColor, value: highlightColor, range: range)
                    // attributedString.addAttribute(.backgroundColor, value: UIColor.orange.withAlphaComponent(0.1), range: range)
                }
            }

            return attributedString
        } catch {
            Log.e("Error processing highlighted text: \\(error)")

            // 发生错误时尝试手动清理标记
            let cleanedText = text.replacingOccurrences(of: "{{{", with: "")
                .replacingOccurrences(of: "}}}", with: "")

            return NSAttributedString(string: cleanedText, attributes: [
                .font: font,
                .foregroundColor: textColor,
            ])
        }
    }
}
