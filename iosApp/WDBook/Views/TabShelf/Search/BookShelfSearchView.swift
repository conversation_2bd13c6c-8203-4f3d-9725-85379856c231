//
//  BookShelfSearchView.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct BookShelfSearchView: View {
    // Add presentationMode environment variable
    @Environment(\.presentationMode) var presentationMode

    // Keep the original dismissAction property if needed for other purposes,
    // but we won't pass it directly to the search bar's cancel button logic.
    var dismissAction: (() -> Void)?

    // Observe the shared search state
    @StateObject private var searchState = BookShelfSearchState.shared
    // State to track initial focus
    @State private var initialFocusSet = false

    var body: some View {
        VStack(spacing: 0) {
            // Pass the dismiss action using presentationMode
            BookShelfSearchBar(dismissAction: {
                presentationMode.wrappedValue.dismiss()
                // You could also call the original dismissAction here if needed:
                // dismissAction?()
            })

            // Content Area: Switches between History and Results/Loading
            Group {
                if searchState.showingResults {
                    if searchState.isSearching {
                        ProgressView()
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    } else {
                        // Use BookShelfSearchResult without passing results directly
                        // since it now uses the shared searchState
                        BookShelfSearchResult()
                    }
                } else {
                    // Show history (which handles its own empty state)
                    BookShelfSearchHistory()
                }
            }
            .ignoresSafeArea(.keyboard, edges: .bottom)
        }
        // Apply background first
        .background(
            // Attach tap gesture to the background color view
            Color("SearchViewHeaderBackground")
                .ignoresSafeArea()
                .onTapGesture {
                    // Dismiss keyboard when tapping the background
                    UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
                }
        )
        .navigationBarHidden(true)
        .onAppear {
            // Set focus only on the initial appearance
            if !initialFocusSet {
                searchState.isSearchBarFocused = true
                initialFocusSet = true
            }

            // Check if a refresh is needed due to group edit
            if searchState.needsRefreshAfterGroupEdit {
                if !searchState.searchQuery.isEmpty {
                    Log.d("DEBUG: Refreshing search results due to group edit.")
                    searchState.performSearch(query: searchState.searchQuery)
                }
                searchState.needsRefreshAfterGroupEdit = false // Reset the flag
            }
        }
        .onDisappear {
            // Explicitly remove focus when navigating away
            searchState.isSearchBarFocused = false
        }
    }
}

struct BookShelfSearchView_Previews: PreviewProvider {
    static var previews: some View {
        BookShelfSearchView(dismissAction: { print("Dismiss requested from preview") })
    }
}
