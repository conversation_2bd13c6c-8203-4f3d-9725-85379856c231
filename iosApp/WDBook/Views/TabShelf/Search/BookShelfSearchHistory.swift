//
//  BookShelfSearchHistory.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI
import UIKit

struct BookShelfSearchHistory: View {
    @StateObject private var searchState = BookShelfSearchState.shared
    @State private var showingClearConfirmAlert = false

    var body: some View {
        // Use a Group to conditionally switch between views
        Group {
            // Access history via the singleton instance
            if !searchState.searchHistory.isEmpty {
                VStack(alignment: .leading, spacing: 15) {
                    HStack {
                        Text("search.history.title".localized) // Search History
                            .font(.headline)
                        Spacer()
                        Button {
                            // Show the confirmation alert
                            searchState.resignFocus()
                            showingClearConfirmAlert = true
                        } label: {
                            Image(systemName: "trash")
                                .foregroundColor(.secondary)
                                .frame(width: 44, height: 44) // 增加最小点击区域
                                .contentShape(Rectangle()) // 确保整个区域可点击
                        }
                    }

                    // Simple Flow Layout using GeometryReader
                    GeometryReader { geometry in
                        self.generateContent(in: geometry)
                    }
                    // Removed fixed frame height - let GeometryReader adapt
                    // .frame(height: calculateHeightNeeded())

                    Spacer() // Push content above this spacer to the top
                }
                .padding()
            } else {
                // Show the empty state view when history is empty
                BookShelfSearchEmptySearchHistoryView()
            }
        }
        .background(
            Color("SearchResultListBackground")
                .ignoresSafeArea(edges: .bottom) // Extend color past bottom safe area
                .contentShape(Rectangle()) // Make the entire background tappable
                .onTapGesture {
                    // Dismiss keyboard when tapping outside of items
                    searchState.resignFocus()
                }
        )
        .alert(isPresented: $showingClearConfirmAlert) {
            Alert(
                title: Text("search.history.clearConfirmation.title".localized),
                primaryButton: .destructive(Text("common.delete".localized)) {
                    searchState.clearHistory()
                },
                secondaryButton: .cancel(Text("common.cancel".localized))
            )
        }
    }

    private func generateContent(in g: GeometryProxy) -> some View {
        var width = CGFloat.zero
        var height = CGFloat.zero

        return ZStack(alignment: .topLeading) {
            // Use searchHistory from the singleton instance
            ForEach(searchState.searchHistory, id: \.self) { item in
                self.item(for: item)
                    .padding([.horizontal, .vertical], 3) // Adjust to account for the 6px gap
                    .alignmentGuide(.leading, computeValue: { d in
                        if abs(width - d.width) > g.size.width {
                            width = 0
                            height -= (d.height + 6) // 6px vertical gap
                        }
                        let result = width
                        // Access history via the singleton instance
                        if let lastItem = searchState.searchHistory.last, item == lastItem {
                            width = 0 // Last item
                        } else {
                            width -= (d.width + 6) // 6px horizontal gap
                        }
                        return result
                    })
                    .alignmentGuide(.top, computeValue: { _ in
                        let result = height
                        // Access history via the singleton instance
                        if let lastItem = searchState.searchHistory.last, item == lastItem {
                            height = 0 // Last item
                        }
                        return result
                    })
            }
        }
        // Add padding below the ZStack to ensure Spacer works correctly if content is short
        .padding(.bottom)
    }

    private func item(for text: String) -> some View {
        Text(text)
            .padding(.top, 6)
            .padding(.bottom, 6)
            .padding(.leading, 8)
            .padding(.trailing, 8)
            .frame(height: 32)
            .font(.init(UIFont.primaryTextRegular))
            .background(Color("SearchHistoryItemBackground"))
            .foregroundColor(Color("SearchHistoryItemText"))
            .cornerRadius(19)
            .lineLimit(1) // Prevent long text from wrapping excessively
            .onTapGesture {
                // Set search query first
                searchState.searchQuery = text

                // Dismiss the keyboard using the centralized method
                searchState.resignFocus()

                // Submit search (no delay needed now?)
                searchState.submitSearch()
            }
    }

    // Removed calculateHeightNeeded()
}

struct BookShelfSearchHistory_Previews: PreviewProvider {
    static var previews: some View {
        // Previews now automatically use the shared singleton instance.
        // We might need to manually set some history on the shared instance
        // for the preview to look right, if the persisted history is empty.
        let _ = BookShelfSearchState.shared.searchHistory = ["爱主的玛利亚", "守望的约瑟", "谦和的约拿单", "敬虔的司提反", "热情洋溢的安德烈", "勇敢的但以理", "Short", "Longer item", "Very very very long search history item example", "Item 4", "Item 5", "Item 6", "Item 7"]

        return BookShelfSearchHistory()
            // No need for .environmentObject() anymore
            .previewLayout(.sizeThatFits)
            .padding()
            .previewDisplayName("With History (Singleton)")

        // Preview for empty state
        // let _ = BookShelfSearchState.shared.searchHistory = [] // Temporarily clear for preview
        // return BookShelfSearchHistory()
        //     .previewLayout(.sizeThatFits)
        //     .padding()
        //     .previewDisplayName("Empty History (Singleton)")
    }
}
