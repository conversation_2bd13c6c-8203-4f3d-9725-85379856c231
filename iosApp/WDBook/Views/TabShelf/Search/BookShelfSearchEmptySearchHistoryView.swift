//
//  BookShelfSearchEmptySearchHistoryView.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/22.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

// Renamed Component for Empty Search History State
struct BookShelfSearchEmptySearchHistoryView: View {
    var body: some View {
        ZStack {
            VStack(spacing: 10) {
                Image("icon_empty_search_history")
                    .resizable()
                    .frame(width: 65, height: 64)
                Text("search.emptyHistory.title".localized)
                    .font(Font(UIFont.primaryTextRegular))
                    .lineSpacing(5.6)
                    .multilineTextAlignment(.center)
                    .foregroundColor(.gray)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(
            Color("SearchResultListBackground")
                .ignoresSafeArea(edges: .bottom)
        )
        .contentShape(Rectangle())
        .onTapGesture {
            // Dismiss keyboard when tapping anywhere on the empty state view
            BookShelfSearchState.shared.resignFocus()
        }
    }
}
