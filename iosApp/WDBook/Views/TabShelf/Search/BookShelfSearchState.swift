//
//  BookShelfSearchState.swift
//  WDBook
//
//  Created by <PERSON> on 2025/4/23.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import Combine
import Foundation
import Network
import shared // Import the shared Kotlin module
import UIKit

// Assuming SearchResults, Book, ShelfGroup, ShelfItem are accessible
// If not, you might need to define them here or ensure they are imported
// from where BookShelfSearchResult.swift defines them.

// --- Placeholder Data Structures (Mirroring BookShelfSearchResult for now) ---
// Remove these if the actual structs are accessible globally or via import

// Define a base protocol for common book properties
protocol BookRepresentable: Identifiable, Hashable {
    var id: UUID { get }
    var resourceId: String { get }
    var title: String { get }
    var author: String { get }
    var coverImageUrl: String? { get }
}

struct ShelfBook: BookRepresentable {
    let id = UUID()
    let resourceId: String
    let fileId: String? // Specific to ShelfBook, immutable
    let title: String
    let author: String
    let coverImageUrl: String?
    // Shelf-specific properties can be added here if any

    // Hashable & Equatable (default implementation based on properties is usually fine, or keep explicit if needed)
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(resourceId)
    }

    static func == (lhs: ShelfBook, rhs: ShelfBook) -> Bool {
        lhs.id == rhs.id && lhs.resourceId == rhs.resourceId
    }
}

struct StoreBook: BookRepresentable {
    let id = UUID()
    let productId: String // Specific to store books, non-optional
    let resourceId: String // Assuming store books also have a resourceId, if not, adjust
    let title: String
    let author: String
    let coverImageUrl: String?
    let purchased: Bool?

    // Hashable & Equatable
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(productId)
        hasher.combine(resourceId)
    }

    static func == (lhs: StoreBook, rhs: StoreBook) -> Bool {
        lhs.id == rhs.id && lhs.productId == rhs.productId && lhs.resourceId == rhs.resourceId
    }
}

struct ShelfGroup: Identifiable, Hashable {
    let id = UUID()
    var itemId: String // Add itemId property to find index later
    let title: String
    let books: [ShelfBook]? // Now contains ShelfBook

    // Explicit Hashable conformance (based on ID)
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    // Explicit Equatable conformance (based on ID)
    static func == (lhs: ShelfGroup, rhs: ShelfGroup) -> Bool {
        lhs.id == rhs.id
    }
}

enum ShelfItem: Identifiable, Hashable {
    case shelfBook(ShelfBook) // Changed from .book(Book)
    case group(ShelfGroup)

    var id: UUID {
        switch self {
        case let .shelfBook(book): return book.id
        case let .group(group): return group.id
        }
    }
}

struct SearchResults: Hashable {
    let searchTerm: String
    var shelfResults: [ShelfItem] // Contains ShelfBook via ShelfItem
    let storeResults: [StoreBook] // Changed from [Book] to [StoreBook]
}

// --- End Placeholder Data Structures ---

class BookShelfSearchState: ObservableObject {
    // Singleton instance
    static let shared = BookShelfSearchState()

    @Published var searchQuery: String = ""
    @Published var searchHistory: [String] = []
    @Published var searchResults: SearchResults? = nil
    @Published var isSearching: Bool = false
    @Published var showingResults: Bool = false // To control navigation/presentation
    @Published var isSearchBarFocused: Bool = false // Centralized focus state
    @Published var needsRefreshAfterGroupEdit: Bool = false
    var navigatedToGroupViewFromSearch: Bool = false // <-- ADD THIS LINE

    // Network status for store results
    @Published var storeNetworkUnavailable: Bool = false

    // --- Shelf Pagination State ---
    var totalShelfResultsCount: Int64 = 0 // Total number of shelf BOOK results expected

    // --- Store Pagination State ---
    @Published var storeSearchResults: [StoreBook] = [] // Store results now [StoreBook]
    @Published var isLoadingMoreStoreItems: Bool = false
    @Published var hasMoreStoreResults: Bool = true
    var totalStoreResultsCount: Int = 0 // Total number of store results expected
    private var currentStorePage: Int = 0 // Store API uses page index
    private let storeSearchLimit: Int = 10 // Store items per page

    private let userSDK = WDBookUserSDK.shared // Access the SDK
    private let storeSDK = WDBookStoreSDK.shared // Access Store SDK

    // Add network monitoring
    private var networkObserver: NSObjectProtocol?

    private func setupNetworkMonitoring() {
        // Log current network interfaces for debugging
        let interfaces = NetMonitor.currentPath.availableInterfaces
        Log.d("NETWORK DEBUG: Current interfaces: \(interfaces.map { $0.name })")
        Log.d("NETWORK DEBUG: Current path status: \(NetMonitor.currentPath.status == .satisfied ? "satisfied" : "unsatisfied")")

        // Remove any existing observer
        if let observer = networkObserver {
            NotificationCenter.default.removeObserver(observer)
        }

        // Add observer for NetMonitor's notification
        networkObserver = NotificationCenter.default.addObserver(
            forName: NetMonitor.reachabilityChangedNotification,
            object: nil,
            queue: .main
        ) { [weak self] _ in
            guard let self = self else { return }

            // Enhanced debugging
            let pathStatus = NetMonitor.currentPath.status
            let interfaces = NetMonitor.currentPath.availableInterfaces
            Log.d("NETWORK DEBUG: Path changed - status: \(pathStatus == .satisfied ? "satisfied" : "unsatisfied")")
            Log.d("NETWORK DEBUG: Available interfaces: \(interfaces.map { $0.name })")
            Log.d("NETWORK DEBUG: Expensive: \(NetMonitor.currentPath.isExpensive)")

            let isNetworkAvailable = NetMonitor.currentPath.status == .satisfied

            self.updateNetworkStatus(isUnavailable: !isNetworkAvailable)

            // If network is now available and we have a pending search, retry it
            if isNetworkAvailable && !self.searchQuery.isEmpty && self.storeNetworkUnavailable {
                // Only retry store portion if shelf results already loaded
                if self.searchResults != nil {
                    Log.d("STORE DEBUG: Network became available - retrying store search for '\(self.searchQuery)'")
                    self.retryStoreSearch()
                }
            }
        }

        // Make sure NetMonitor is started and fully initialized
        NetMonitor.startMonitor()

        // Force a network check - sometimes we need to explicitly check after monitor is started
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }

            // Get current network status directly from path
            let isNetworkAvailable = NetMonitor.currentPath.status == .satisfied

            // Log detailed network information
            Log.d("NETWORK DEBUG: Initial check - status: \(NetMonitor.currentPath.status == .satisfied ? "satisfied" : "unsatisfied")")
            Log.d("NETWORK DEBUG: Available interfaces: \(NetMonitor.currentPath.availableInterfaces.map { $0.name })")

            self.updateNetworkStatus(isUnavailable: !isNetworkAvailable)
        }
    }

    private func retryStoreSearch() {
        // Skip if already loading
        guard !isLoadingMoreStoreItems else { return }

        isLoadingMoreStoreItems = true
        currentStorePage = 0 // Reset to first page

        // Force network status to available to prevent early bailout in loadMoreStoreResults
        storeNetworkUnavailable = false

        // Instead of going through loadMoreStoreResults which has multiple guard clauses,
        // directly call the API with the current query
        Log.d("STORE DEBUG: Directly retrying store search for '\(searchQuery)'")

        storeSDK.getSearchProductList(page: 1, pageSize: storeSearchLimit, text: searchQuery) { [weak self] result in
            DispatchQueue.main.async {
                guard let self = self else { return }

                Log.d("STORE DEBUG: Processing retry store results.")
                switch result {
                case let .success(pageListEntity):
                    if let pageList = pageListEntity {
                        self.totalStoreResultsCount = Int(pageList.total)
                        let rawStoreItems = pageList.productList as? [ProductEntity] ?? []

                        // Create a batch update for all the state changes
                        self.batchUpdateStoreState {
                            self.storeSearchResults = self.mapProductEntitiesToStoreBooks(rawStoreItems)
                            self.hasMoreStoreResults = pageList.hasNext
                            self.currentStorePage = 1 // We loaded page 1
                            self.isLoadingMoreStoreItems = false
                        }

                        Log.d("STORE DEBUG: Retry load success. Found \(self.storeSearchResults.count) items. Total: \(self.totalStoreResultsCount). HasNext: \(pageList.hasNext)")
                    } else {
                        Log.d("STORE DEBUG: Retry load returned nil pageListEntity.")
                        self.storeSearchResults = []
                        self.hasMoreStoreResults = false
                        self.isLoadingMoreStoreItems = false
                    }
                case let .failure(error):
                    Log.e("STORE DEBUG: Retry load failed: \(error.localizedDescription)")
                    self.storeSearchResults = []
                    self.hasMoreStoreResults = false
                    self.isLoadingMoreStoreItems = false
                }
            }
        }
    }

    // Make initializer private for Singleton pattern
    private init() {
        loadSearchHistory()
        setupNetworkMonitoring() // Add this line
    }

    // MARK: - Network Status Handling

    private func updateNetworkStatus(isUnavailable: Bool) {
        DispatchQueue.main.async {
            self.storeNetworkUnavailable = isUnavailable
            Log.d("STORE DEBUG: Network status updated to \(isUnavailable ? "unavailable" : "available")")
        }
    }

    // MARK: - Signalling Group Edits

    func signalGroupEditOccurred() { // <-- ADD THIS METHOD
        needsRefreshAfterGroupEdit = true
        Log.d("DEBUG: Group edit signalled, needsRefreshAfterGroupEdit set to true.")
    }

    // MARK: - Search Logic

    func performSearch(query: String) {
        guard !query.trimmingCharacters(in: .whitespaces).isEmpty else {
            Log.i("Search query is empty, clearing results.")
            clearSearchInternal() // Use internal clear to avoid redundant history load
            return
        }

        // Reset state for a new search
        Log.i("Performing NEW search for: \(query)")
        searchQuery = query // Update the published query
        isSearching = true
        showingResults = true
        addToHistory(query)

        // Reset total book count for new search
        totalShelfResultsCount = 0

        // Reset Store Search State
        storeSearchResults.removeAll()
        currentStorePage = 0
        hasMoreStoreResults = true

        // IMPORTANT: Set loading flag to true but don't reset the network status
        // This ensures we keep showing results while loading more
        isLoadingMoreStoreItems = true

        // --- Initiate Shelf Search ---
        // No need for a DispatchGroup since we want to show shelf results immediately
        let shelfSearchWorkItem = DispatchWorkItem { [weak self] in
            guard let self = self else { return }
            Log.d("--- Starting Shelf Search (Fetching All) ---")
            // Call the new SDK method to get all shelf results
            let searchResult = self.userSDK.searchAllShelfEntityListByKeyword(keyword: query)

            // Store the total BOOK count for display purposes
            if let totalBookCount = searchResult?.total {
                self.totalShelfResultsCount = totalBookCount
                Log.d("SHELF DEBUG: API returned total BOOK count: \(totalBookCount)")
            } else {
                self.totalShelfResultsCount = 0
                Log.d("SHELF DEBUG: No total book count returned from API")
            }

            // Get all the top-level items from the API response
            let entities = searchResult?.items ?? []
            Log.d("SHELF DEBUG: Search returned \(entities.count) total top-level items.")

            // Map SDK results
            let allShelfItems = self.mapShelfEntitiesToItems(entities)

            // Create the initial SearchResults object (now with complete shelf results)
            let finalResults = SearchResults(
                searchTerm: query,
                shelfResults: allShelfItems,
                storeResults: [] // Store results handled separately by @Published property
            )

            // Update UI on the main thread for SHELF results
            DispatchQueue.main.async {
                // Check if the search query is still the same before updating UI
                guard self.searchQuery == query else {
                    Log.d("SHELF DEBUG: Search query changed before UI update, discarding results.")
                    return
                }
                Log.d("SHELF DEBUG: Updating UI with ALL shelf results (\(allShelfItems.count) items).")

                // Critical change: Create a placeholder store loading state in the same update
                // This ensures we only do ONE table update for the initial results

                // Create and assign results in one operation
                self.searchResults = finalResults

                // Set isSearching to false immediately after shelf results are available
                // This allows users to see and interact with shelf results while store search
                // might still be ongoing in the background, improving perceived performance
                self.isSearching = false

                // Keep isLoadingMoreStoreItems true so loading indicator shows for store section
                // DO NOT set isLoadingMoreStoreItems = false here
            }
        }

        // --- Initiate Store Search ---
        let storeSearchWorkItem = DispatchWorkItem { [weak self] in
            // Check network availability before store search
            guard NetMonitor.isConnectedToWiFiAndWiredEthernet() || NetMonitor.isConnectedToCellular() else {
                // If network is unavailable, set flag and leave search group
                DispatchQueue.main.async {
                    self?.storeNetworkUnavailable = true
                    self?.isLoadingMoreStoreItems = false // Make sure loading state is cleared
                    Log.d("STORE DEBUG: Network unavailable for store search")
                }
                return
            }

            self?.updateNetworkStatus(isUnavailable: false)

            guard let self = self else { return }

            // We already set loading state at the beginning, no need to set again

            Log.d("--- Starting Store Search --- Page: 1")
            self.storeSDK.getSearchProductList(page: 1, pageSize: self.storeSearchLimit, text: query) { result in
                DispatchQueue.main.async { [weak self] in // Explicitly mark weak self
                    // Check if self exists and the search query is still valid
                    guard let self = self else {
                        Log.d("STORE DEBUG: Self was deallocated during search, cancelling update")
                        return
                    }

                    // Now check if the query is still valid
                    guard self.searchQuery == query else {
                        Log.d("STORE DEBUG: Search query changed before UI update, discarding results.")
                        self.isLoadingMoreStoreItems = false
                        return
                    }

                    Log.d("STORE DEBUG: Processing initial store results.")
                    switch result {
                    case let .success(pageListEntity):
                        if let pageList = pageListEntity {
                            self.totalStoreResultsCount = Int(pageList.total) // Assign total count
                            let rawStoreItems = pageList.productList as? [ProductEntity] ?? []
                            // Print raw entities before mapping
                            Log.d("STORE DEBUG: Raw Entities Received (Initial Load):")
                            for (index, entity) in rawStoreItems.enumerated() {
                                Log.d("  Raw Initial [\(index)]: ID \(entity.resourceId), Title \(entity.title ?? "N/A"), Author \(entity.authorNames)")
                            }

                            // Added a slight delay to prevent UI jank when both updates happen close together
                            // This ensures shelf results are fully rendered before the store update occurs
                            DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                                // Create a batch update for all the state changes
                                self.batchUpdateStoreState {
                                    self.storeSearchResults = self.mapProductEntitiesToStoreBooks(rawStoreItems)
                                    self.hasMoreStoreResults = pageList.hasNext
                                    self.currentStorePage = 1 // We loaded page 1
                                    self.isLoadingMoreStoreItems = false
                                }

                                Log.d("STORE DEBUG: Initial load success. Found \(self.storeSearchResults.count) items. Total: \(self.totalStoreResultsCount). HasNext: \(pageList.hasNext)")
                            }
                        } else {
                            Log.d("STORE DEBUG: Initial load returned nil pageListEntity.")
                            self.storeSearchResults = []
                            self.totalStoreResultsCount = 0
                            self.hasMoreStoreResults = false
                            self.isLoadingMoreStoreItems = false
                        }
                    case let .failure(error):
                        Log.e("STORE DEBUG: Initial load failed: \(error.localizedDescription)")
                        // Handle error appropriately, maybe show a message
                        self.storeSearchResults = []
                        self.totalStoreResultsCount = 0
                        self.hasMoreStoreResults = false
                        self.isLoadingMoreStoreItems = false
                    }
                }
            }
        }

        // Run searches concurrently
        let searchQueue = DispatchQueue.global(qos: .userInitiated)
        searchQueue.async(execute: shelfSearchWorkItem)
        searchQueue.async(execute: storeSearchWorkItem)
    }

    // Helper function to map SDK entities (refactored from performSearch)
    private func mapShelfEntitiesToItems(_ shelfEntities: [HomeShelfItemCombineEntity]) -> [ShelfItem] {
        return shelfEntities.compactMap { combineEntity -> ShelfItem? in
            // Access the raw Int value from the bridged enum (assuming .type gives Int32)
            let typeValue = combineEntity.dataType.type

            // Use if/else if based on the integer values defined in Kotlin (RESOURCE=0, ARCHIVE=1)
            if typeValue == 0 { // RESOURCE
                guard let bookEntity = combineEntity.bookItemEntity else { return nil }

                // Use the text directly as the API already includes highlight markers
                let title = bookEntity.resourceName
                let author = bookEntity.authorList?.first?.name ?? "Unknown Author"

                // Extract fileId from resourceDownloadInfo
                let fileId = bookEntity.resourceDownloadInfo.fileId

                // Map ShelfBookItemEntity to ShelfBook
                return .shelfBook(ShelfBook(
                    resourceId: bookEntity.resourceId,
                    fileId: fileId,
                    title: title,
                    author: author,
                    coverImageUrl: bookEntity.cover
                ))
            } else if typeValue == 1 { // ARCHIVE
                guard let archiveEntity = combineEntity.archiveEntity else { return nil }

                // Use the archive name directly as it already includes highlight markers
                let title = archiveEntity.archiveName

                // Map matchedBooks (List<ShelfBookItemEntity>?) to [ShelfBook]?
                let mappedBooks: [ShelfBook]? = archiveEntity.matchedBooks?.compactMap { bookEntity in
                    // Convert ShelfBookItemEntity to ShelfBook
                    let fileId = bookEntity.resourceDownloadInfo.fileId

                    return ShelfBook(
                        resourceId: bookEntity.resourceId,
                        fileId: fileId,
                        title: bookEntity.resourceName,
                        author: bookEntity.authorList?.first?.name ?? "Unknown Author",
                        coverImageUrl: bookEntity.cover
                    )
                }

                // Map ShelfArchiveItemEntity to ShelfGroup (which now contains [ShelfBook]?)
                return .group(ShelfGroup(
                    itemId: archiveEntity.clientArchiveId,
                    title: title,
                    books: mappedBooks
                ))
            } else {
                // Handle unknown type values if necessary
                Log.w("Unknown ShelfDataType encountered: \(typeValue)")
                return nil
            }
        }
    }

    // Call this when user explicitly submits search (e.g., presses return key)
    func submitSearch() {
        // Perform a *new* search with the current query text
        performSearch(query: searchQuery)
    }

    // Method called by UI to clear the search
    func clearSearch() {
        Log.i("Clearing search state requested by UI")
        clearSearchInternal()
        // Ensure history/empty state is shown
        showingResults = false
    }

    // Internal method to clear results without triggering history reload etc.
    private func clearSearchInternal() {
        searchQuery = ""
        searchResults = nil
        storeSearchResults.removeAll()
        isSearching = false
        isLoadingMoreStoreItems = false
        hasMoreStoreResults = true
        currentStorePage = 0
        totalShelfResultsCount = 0
        totalStoreResultsCount = 0
    }

    // Method to handle resigning focus
    func resignFocus() {
        isSearchBarFocused = false
        // Still potentially useful to notify UIKit globally
        UIApplication.shared.sendAction(#selector(UIResponder.resignFirstResponder), to: nil, from: nil, for: nil)
    }

    // MARK: - History Management

    func addToHistory(_ term: String) {
        let cleanedTerm = term.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !cleanedTerm.isEmpty else { return }

        // Save to persistent storage via SDK
        userSDK.saveShelfSearchData(keyWord: cleanedTerm)
        Log.d("Added to history: \(cleanedTerm)")

        // Reload history from SDK
        loadSearchHistory()
    }

    func clearHistory() {
        // Clear persistent storage via SDK
        userSDK.clearShelfSearchHistory()
        Log.i("History cleared.")

        // Reload history from SDK (will be empty now)
        loadSearchHistory()
    }

    private func loadSearchHistory() {
        // Load history from SDK, limited to 8 items
        let historyItems = userSDK.getShelfSearchDataList(limit: 8) // Load up to 8 items
        // Assuming SearchItemEntity has a 'keyWords' property
        searchHistory = historyItems.map { $0.keyWords ?? "" }.filter { !$0.isEmpty }
        Log.d("Loaded history from SDK: \(searchHistory.count) items")
    }

    // --- Function to Load More Store Results ---
    func loadMoreStoreResults() {
        Log.d("STORE DEBUG: Attempting to load more store results. Current Page: \(currentStorePage), HasMore: \(hasMoreStoreResults)")

        // Capture the query at the beginning of the load more attempt.
        // This query will be used for the network request.
        let queryForThisLoadMoreRequest = searchQuery

        // Check network directly from the path status for most up-to-date info
        let networkAvailable = NetMonitor.currentPath.status == .satisfied

        // Log detailed info about current network state
        Log.d("NETWORK DEBUG: Current network while loading - Available: \(networkAvailable)")
        Log.d("NETWORK DEBUG: Path status: \(NetMonitor.currentPath.status)")
        if let interfaces = NetMonitor.currentPath.availableInterfaces as? [NWInterface] {
            Log.d("NETWORK DEBUG: Interfaces: \(interfaces.map { "\($0.name):\($0.type)" })")
        }

        guard !queryForThisLoadMoreRequest.isEmpty,
              !isLoadingMoreStoreItems,
              hasMoreStoreResults,
              networkAvailable // Simplified check
        else {
            if isLoadingMoreStoreItems { Log.d("STORE DEBUG: Already loading more store items.") }
            if !hasMoreStoreResults { Log.d("STORE DEBUG: No more store results to load.") }
            if queryForThisLoadMoreRequest.isEmpty { Log.d("STORE DEBUG: Search query (for this request) empty.") }
            if !networkAvailable {
                Log.d("STORE DEBUG: Network unavailable for loading more store items")
                updateNetworkStatus(isUnavailable: true)
            } else {
                Log.d("STORE DEBUG: Network available for loading more store items")
                updateNetworkStatus(isUnavailable: false)
            }
            return
        }

        // Prevent loading during layout cycles (similar safety check as shelf)
        if Thread.isMainThread && CATransaction.disableActions() {
            Log.w("STORE WARNING: Attempting to load more store items during layout cycle, deferring load")
            DispatchQueue.main.async { [weak self] in
                self?.loadMoreStoreResults()
            }
            return
        }

        // Set loading state before starting request
        isLoadingMoreStoreItems = true
        let nextPage = currentStorePage + 1
        Log.d("STORE DEBUG: Loading page \(nextPage) for query '\(queryForThisLoadMoreRequest)'")

        // Important: Delay slightly to ensure UI shows loading indicator
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) { [weak self] in
            guard let self = self else { return }

            self.storeSDK.getSearchProductList(page: nextPage, pageSize: self.storeSearchLimit, text: queryForThisLoadMoreRequest) { result in
                DispatchQueue.main.async { // Ensure UI updates on main thread
                    // Check if the overall searchQuery has changed since this specific loadMore request was initiated.
                    // If it has, these results are for an old query and should be discarded.
                    guard self.searchQuery == queryForThisLoadMoreRequest else {
                        Log.d("STORE DEBUG: Search query changed during load more request. Original for this request: '\(queryForThisLoadMoreRequest)', Current global query: '\(self.searchQuery)'. Discarding results.")
                        self.isLoadingMoreStoreItems = false // Still need to reset loading state for this aborted request
                        return
                    }

                    Log.d("STORE DEBUG: Processing results for page \(nextPage) of query '\(queryForThisLoadMoreRequest)'")
                    switch result {
                    case let .success(pageListEntity):
                        if let pageList = pageListEntity {
                            self.totalStoreResultsCount = Int(pageList.total) // Update total count
                            let newRawStoreItems = pageList.productList as? [ProductEntity] ?? []

                            // Process new items and update store results in one batch
                            let newMappedBooks = self.mapProductEntitiesToStoreBooks(newRawStoreItems)

                            // Update the shared state properties atomically
                            self.batchUpdateStoreState {
                                self.storeSearchResults.append(contentsOf: newMappedBooks)
                                self.hasMoreStoreResults = pageList.hasNext
                                self.currentStorePage = nextPage
                                self.isLoadingMoreStoreItems = false
                            }

                            Log.d("STORE DEBUG: Load more success. Added \(newMappedBooks.count) items. Total: \(self.totalStoreResultsCount). HasNext: \(pageList.hasNext). New Page: \(self.currentStorePage)")
                        } else {
                            Log.d("STORE DEBUG: Load more returned nil pageListEntity.")
                            self.isLoadingMoreStoreItems = false
                            self.hasMoreStoreResults = false // Assume no more if response is nil
                        }
                    case let .failure(error):
                        Log.e("STORE DEBUG: Load more failed: \(error.localizedDescription)")
                        // Don't change page number, but stop loading more for now
                        self.isLoadingMoreStoreItems = false
                        self.hasMoreStoreResults = false
                    }
                }
            }
        }
    }

    // Helper method for atomic updates to store search state
    private func batchUpdateStoreState(updates: () -> Void) {
        // Use objectWillChange to notify listeners only once for multiple property changes
        objectWillChange.send()
        updates()
    }

    // Helper function to replace em tags and clean up nested markers
    private func processHighlightMarkers(for text: String) -> String {
        // Step 1 & 2: Replace em tags with triple braces
        var processedText = text
            .replacingOccurrences(of: "<em>", with: "{{{")
            .replacingOccurrences(of: "</em>", with: "}}}")

        // Step 3 & 4: Clean up potential double markers from nested tags
        processedText = processedText
            .replacingOccurrences(of: "{{{{{{", with: "{{{") // 6 open -> 3 open
            .replacingOccurrences(of: "}}}}}}", with: "}}}") // 6 close -> 3 close
        // Add more cleanup if deeper nesting (e.g., <em><em><em>) is possible
        // processedText = processedText.replacingOccurrences(of: "{{{\({{", with: "{{{") // 9 open -> 3 open etc.

        return processedText
    }

    // Helper function to map ProductEntity to StoreBook and replace em tags
    private func mapProductEntitiesToStoreBooks(_ productEntities: [ProductEntity]) -> [StoreBook] {
        let currentSearchQuery = searchQuery.trimmingCharacters(in: .whitespacesAndNewlines)

        return productEntities.map { productEntity -> StoreBook in
            let originalTitle = productEntity.title ?? ""
            var titleForHighlighting = originalTitle // Default to original
            let processedTitleFromEm = processHighlightMarkers(for: originalTitle)

            if processedTitleFromEm.contains("{{{") { // Backend provided <em> tags
                titleForHighlighting = processedTitleFromEm
            } else if !currentSearchQuery.isEmpty && originalTitle.range(of: currentSearchQuery, options: .caseInsensitive) != nil {
                // Backend did NOT provide <em> tags, but query matches. Manually add markers to the ORIGINAL string.
                titleForHighlighting = originalTitle.replacingOccurrences(of: currentSearchQuery, with: "{{{\(currentSearchQuery)}}}", options: .caseInsensitive)
            } // Else, titleForHighlighting remains the originalTitle if no <em> and no query match

            let originalAuthorNames = productEntity.authorNames
            var authorForHighlighting = originalAuthorNames // Default to original
            let processedAuthorFromEm = processHighlightMarkers(for: originalAuthorNames)

            if processedAuthorFromEm.contains("{{{") { // Backend provided <em> tags
                authorForHighlighting = processedAuthorFromEm
            } else if !currentSearchQuery.isEmpty && originalAuthorNames.range(of: currentSearchQuery, options: .caseInsensitive) != nil {
                // Backend did NOT provide <em> tags, but query matches. Manually add markers to the ORIGINAL string.
                authorForHighlighting = originalAuthorNames.replacingOccurrences(of: currentSearchQuery, with: "{{{\(currentSearchQuery)}}}", options: .caseInsensitive)
            } // Else, authorForHighlighting remains the originalAuthorNames if no <em> and no query match

            return StoreBook(
                productId: String(productEntity.productId), // Convert Int64 to String
                resourceId: productEntity.resourceId, // Assuming ProductEntity has resourceId
                title: titleForHighlighting,
                author: authorForHighlighting,
                coverImageUrl: productEntity.cover,
                purchased: productEntity.purchased == 1
            )
        }
    }

    // Clean up in deinit
    deinit {
        if let observer = networkObserver {
            NotificationCenter.default.removeObserver(observer)
        }
    }
}
