//
//  ShelfGroupListDialog.swift
//  WDBook
//
//  Created by QK on 2021/9/17.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI

struct ShelfGroupListDialog: View {
    @Binding var showingGroupDialog:Bo<PERSON>
    @Binding var showingGroupCreatDialog:Bool
    var isShowRemoveView:Bool
    @State var isFirstLoad = true
    @Binding var isOnEdit:Bool
    @Binding var isRename:Bool
    var clientArchiveId:String = ""
    @EnvironmentObject var archiveListEntity:ShelfArchiveListEntity
    @State var shelfItems:[DownloadResourceObservable] = []
    var groupClickAction:(()->())?
    @State var shelfUploadItems:[ShelfBookItemEntity] = []
    @State private var isBookSelcted = true
    
    var body: some View {
        ZStack(alignment: .center) {
            VStack(alignment: .center, spacing: 0) {
                Spacer().frame(height:27)
                Text("书籍分组".localized)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18))
                    .frame(maxWidth: 295)
                Spacer().frame(height:18)
                if isShowRemoveView {
                    Button(action: {
                        guard WDBookSessionSDK.shared.isLogin else {
                            showingGroupDialog = false
                            AppState.shared.showLoginRegisterVAndPopToStore()
                            return
                        }
                        showingGroupDialog = false
                        isOnEdit = false
                        NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
                        for i in 0..<self.shelfItems.count {
                            if shelfItems[i].dataType == ShelfDataType.archive {
                                shelfItems[i].isSeleted = false
                            }
                        }
                        WDBookUserSDK.shared.moveShelfBookToArchive(bookList: shelfUploadItems, archiveId: "")
                        WDBookDataSyncManager.shared.syncShelfList(){
                        }
                        groupClickAction?()
                        AppState.shared.reloadShelfData()
                        Toaster.showToast(message: "已移出分组".localized)
                        }) {
                            HStack {
                                Image("item_group_remove").frame(width: 20, height: 20, alignment: .center)
                                Text("从分组中移出".localized)
                                    .lineLimit(1)
                                    .font(Font.regular(size: 16))
                                    .foregroundColor(Color(dynamicTextColor17))
                                Spacer()
                            }
                            .contentShape(Rectangle())
                            .frame(maxWidth: .infinity)
                            .frame(height: 50).padding(.horizontal,25)
                            
                        }
                        .buttonStyle(MyButtonStyle(isBookSelcted: $isBookSelcted))
                        .frame(maxWidth: .infinity)
                }
                
                Button(action: {
                    guard WDBookSessionSDK.shared.isLogin else {
                        showingGroupDialog = false
                        AppState.shared.showLoginRegisterVAndPopToStore()
                        return
                    }
                    isRename = false
                    showingGroupCreatDialog = true
                    showingGroupDialog = false
                    }) {
                        HStack {
                            Image("item_group_creat")
                                .resizable()
                                .frame(width: 24, height: 24, alignment: .center)
                                .scaledToFit()
                            Text("新建分组".localized)
                                .lineLimit(1)
                                .font(Font.regular(size: 16))
                                .foregroundColor(Color(dynamicTextColor17))
                            Spacer()
                        }
                        .frame(maxWidth: .infinity)
                        .frame(height: 50).padding(.horizontal, 22)
                        .contentShape(Rectangle())
                        
                        if AppState.shared.archiveCount == 0 {
                            Spacer().frame(height: 13)
                        }
                    }
                    .buttonStyle(MyButtonStyle(isBookSelcted: $isBookSelcted))
                    .frame(maxWidth: .infinity)
                
                ScrollView {
                    LazyVStack {
                        ForEach(shelfItems.indices, id: \.self) { i in
                            if(shelfItems[i].dataType == ShelfDataType.archive){
                                ShelfGroupListCell(shelfGroup: shelfItems[i], shelfItems: shelfItems, shelfUploadItems: shelfUploadItems, isShowRemoveView: self.isShowRemoveView, showingGroupDialog: $showingGroupDialog, isOnEdit: $isOnEdit, clientArchiveId: clientArchiveId){
                                    groupClickAction?()
                                }
                                .contentShape(Rectangle())
                                .listRowInsets(EdgeInsets()) // list里的间距
                            }
                        }
                    }
                }
                .frame(maxWidth: 295, maxHeight: CGFloat(AppState.shared.archiveCount*58) > UIScreen.main.bounds.height*2/5 ? UIScreen.main.bounds.height*2/5 : CGFloat(AppState.shared.archiveCount*58))
                
                Divider()
                    .background(Color(dynamicSpanLineColor2))
                    .scaleEffect(CGSize(width: 1, height: 1))
                
                Button(action: {
                    showingGroupDialog = false
                    isOnEdit = false
                    NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
                    groupClickAction?()
                    }) {
                        Text("取消".localized)
                            .font(Font.regular(size: 16))
                            .foregroundColor(Color(dynamicTextColor17))
                            .frame(height: 52)
                            .frame(maxWidth: 295)
                            .contentShape(Rectangle())
                    }
                    .buttonStyle(MyButtonStyle(isBookSelcted: $isBookSelcted))
                    .frame(maxWidth: .infinity)
            }
            .background(Color(dynamicBackgroundColor8))
            .cornerRadius(16)
            .frame(maxWidth: 295)
        }
        .edgesIgnoringSafeArea(.all)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(UIColor(hex:0x000000)).opacity(0.45))
        .onAppear{
            if self.isFirstLoad {
                shelfItems = []
                let tempItems = WDBookUserSDK.shared.getArchiveDataList()
                if tempItems.count > 0 {
                    for shelfItem in tempItems {
                        let combineEntity = HomeShelfItemCombineEntity()
                        combineEntity.archiveEntity = shelfItem
                        combineEntity.dataType = ShelfDataType.archive
                        self.shelfItems.append(DownloadResourceObservable.init(item: combineEntity))
                    }
                }
                
                self.isFirstLoad = false
                for i in 0..<self.shelfItems.count {
                    if shelfItems[i].dataType == ShelfDataType.archive && shelfItems[i].clientArchiveId == self.clientArchiveId{
                        shelfItems[i].isSeleted = true
                    }
                }
                for i in 0..<self.archiveListEntity.shelfItems.count {
                    if (self.archiveListEntity.shelfItems[i].isSeleted == true){
                        let tempShelfItem = ShelfBookItemEntity()
                        tempShelfItem.resourceId = archiveListEntity.shelfItems[i].resourceId
                        tempShelfItem.itemId = archiveListEntity.shelfItems[i].itemId
                        shelfUploadItems.append(tempShelfItem)
                    }
                }
            }
        }
    }
}

struct ShelfGroupListCell: View {
    @ObservedObject var shelfGroup: DownloadResourceObservable
    @State var shelfItems:[DownloadResourceObservable]
    @State var shelfUploadItems:[ShelfBookItemEntity]
    var isShowRemoveView:Bool
    @Binding var showingGroupDialog:Bool
    @Binding var isOnEdit:Bool
    var clientArchiveId:String = ""
    var groupClickAction:(()->())?
    @State private var isBookSelcted = true
    
    var body: some View {
        Button(action: {
            guard WDBookSessionSDK.shared.isLogin else {
                showingGroupDialog = false
                AppState.shared.showLoginRegisterVAndPopToStore()
                return
            }
            if clientArchiveId == shelfGroup.clientArchiveId{return}
            showingGroupDialog = false
            for i in 0..<self.shelfItems.count {
                if shelfItems[i].dataType == ShelfDataType.archive {
                    shelfItems[i].isSeleted = false
                }
            }
            shelfGroup.isSeleted = true
            isOnEdit = false
            NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
            WDBookUserSDK.shared.moveShelfBookToArchive(bookList: shelfUploadItems, archiveId: shelfGroup.clientArchiveId)
            WDBookDataSyncManager.shared.syncShelfList(){
            }
            AppState.shared.reloadShelfData()
            Toaster.showToast(message: "已移入分组".localized + "「\(shelfGroup.archiveName)」")
            groupClickAction?()
            }) {
                HStack {
                    Image("item_group_shelf").frame(width: 20, height: 20, alignment: .center)
                    Text(shelfGroup.archiveName)
                        .lineLimit(1)
                        .font(Font.regular(size: 16))
                        .foregroundColor(Color(dynamicTitleColor2))
                    Spacer()
                    if shelfGroup.isSeleted && isShowRemoveView {
                        Image("item_group_checked").frame(width: 15, height: 15, alignment: .center)
                    } else {
                        Spacer().frame(width: 15, height: 15)
                    }
                }
                .frame(maxWidth: .infinity)
                .frame(height: 45).padding(.horizontal, 25)
                .contentShape(Rectangle())
            }
            .buttonStyle(MyButtonStyle(isBookSelcted: $isBookSelcted))
       
    }
}

struct MyButtonStyle: ButtonStyle {
//    @State private var isBookSelcted:Bool = true
    @Binding var isBookSelcted:Bool
    init(isBookSelcted: Binding<Bool>) {
        self._isBookSelcted = isBookSelcted
    }
  func makeBody(configuration: Self.Configuration) -> some View {
    configuration.label
          .background(configuration.isPressed && isBookSelcted ? Color(dynamicBackgroundColor10) : Color(UIColor(hex:0x000000)).opacity(0) )
  }
}
