//
//  CreateGroupDialog.swift
//  WDBook
//
//  Created by QK on 2021/9/22.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Combine
import shared
import SwiftUI

struct CreateGroupDialog: View {
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @Binding var showingGroupCreatDialog: Bool
    @Binding var isOnEdit: Bool
    @State var isAdjustKeyboard: Bool = false
    @State var isRename: Bool
    @State var isFromGroupList: Bool = false
    @EnvironmentObject var shelfArchiveItem: DownloadResourceObservable
    @EnvironmentObject var archiveListEntity: ShelfArchiveListEntity
    var groupClickAction: (() -> Void)?
    var onSuccess: (() -> Void)?
    @State var isFirstLoad = true
    @State var text = ""
    //    @ObservedObject var text2 = TextObj()
    //    @State var limitText = LimitText()
    @Namespace var rightID
    let CharacterLimit = 20
    var contentHeight: CGFloat = 223
    @State private var keyboardHeight: CGFloat = 0

    var content: some View {
        VStack(alignment: .center, spacing: 0) {
            Spacer().frame(height: 29)
            Text(isRename ? "修改分组名".localized : "新建分组".localized)
                .foregroundColor(Color(dynamicTitleColor2))
                .font(Font.semibold(size: 18))
                .frame(maxWidth: 295)
                .frame(height: 25)
            Spacer().frame(height: 24)

            HStack {
                Text("分组名称".localized)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTextColor14))
                    .frame(alignment: .leading)
                    .frame(height: 17)
                Spacer()
            }.padding(.horizontal, 25)
            Spacer().frame(height: 5)

            HStack {
                // UIKit版本
                //                    LimitUITextField("", text: $text)
                //                        .frame(maxWidth:.infinity)
                //                        .frame(width: 222, height: 47)
                //                        .onAppear{if isRename && isFirstLoad {
                //                            isFirstLoad = false
                //                            text = shelfArchiveItem.archiveName}
                //                        }

                // TextField测试
                //                    TextField("", text: $limitText.value,onEditingChanged: { b in
                //                        debugPrint("输入框：onEditingChanged:\(b)")
                //                    })
                //                    .frame(maxWidth: .infinity, maxHeight: 47)
                //                    .lineLimit(1)
                //                    .disableAutocorrection(true)
                //                    .onChange(of: limitText.value) { newValue in
                //                        debugPrint("输入框：onChange:\(newValue)")
                //                    }
                ////                    .onReceive(text.publisher.collect()) {
                //////                        debugPrint("输入框：publisher:\(text)")
                ////                        let characterLimit = 20
                //////                        if text.count > characterLimit {
                ////                            text = String($0.prefix(characterLimit))
                //////                            Toaster.showToast(message: "输入长度不能超过\(characterLimit)个字符", position: .top)
                //////                        }
                ////                    }
                //                    .onReceive(Just(limitText.value)) { inputValue in
                //                        debugPrint("输入框：Just: text:\(limitText.value), inputValue:\(inputValue)")
                ////                        let characterLimit = 20
                ////                        if limitText.text.count > characterLimit {
                ////                            limitText.text = String(inputValue.prefix(characterLimit))
                ////                            Toaster.showToast(message: "输入长度不能超过\(characterLimit)个字符", position: .top)
                ////                        }
                //                    }

                // SwiftUI版本
                Group {
                    if #available(iOS 15, *) {
                        TextFieldFocus(text: $text)
                    } else {
                        TextField("", text: $text)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: 47)
                .lineLimit(1)
                .disableAutocorrection(true)
                .onChange(of: text) { newValue in
                    if text.count > CharacterLimit {
                        text = String(newValue.prefix(CharacterLimit))
                        Toaster.showToast(message: "输入长度不能超过%lld个字符".localizedFormat(CharacterLimit), position: .top)
                    }
                }
                .onAppear {
                    if isRename && isFirstLoad {
                        isFirstLoad = false
                        text = shelfArchiveItem.archiveName
                    }
                }

                Image("icon_clear_text").frame(alignment: .center)
                    .isHidden(text.isEmpty)
                    .onTapGesture {
                        text = ""
                    }
            }.frame(maxWidth: .infinity, maxHeight: 47)
                .padding(.horizontal, 24)

            Divider()
                .background(Color(dynamicSpanLineColor2))
                .scaleEffect(CGSize(width: 1, height: 1))
                .padding(.horizontal, 25)

            Spacer().frame(height: 24)

            HStack {
                Text("取消".localized)
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(dynamicTextColor17))
                    .frame(height: 52)
                    .frame(maxWidth: .infinity)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        showingGroupCreatDialog = false
                    }
                Text("确定".localized)
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(text.isEmpty || text.isBlank ? dynamicTextColor17.alpha(0.25) : dynamicTextColor17))
                    .frame(height: 52)
                    .frame(maxWidth: .infinity)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        guard WDBookSessionSDK.shared.isLogin else {
                            showingGroupCreatDialog = false
                            AppState.shared.showLoginRegisterVAndPopToStore()
                            return
                        }
                        if !text.isEmpty && !text.isBlank {
                            isOnEdit = false
                            NotificationCenter.default.post(name: TabShelfV.Noti_IsOnShelfEdit, object: isOnEdit)
                            if isRename {
                                // 重命名分组
                                WDBookUserSDK.shared.renameArchiveName(archiveName: text, clientArchiveId: shelfArchiveItem.clientArchiveId) { result in
                                    switch result {
                                    case .success:
                                        showingGroupCreatDialog = false
                                        WDBookDataSyncManager.shared.syncShelfList {}
                                        shelfArchiveItem.archiveName = text
                                        AppState.shared.reloadShelfData()
                                        onSuccess?()
                                    case let .failure(error):
                                        showingGroupCreatDialog = false
                                        print(error)
                                    }
                                }

                            } else {
                                // 新建分组
                                showingGroupCreatDialog = false
                                var clientArchiveId = ""
                                var shelfItems: [DownloadResourceObservable] = []
                                var shelfUploadItems: [ShelfBookItemEntity] = []
                                if isFromGroupList {
                                    for i in 0 ..< self.archiveListEntity.shelfItems.count {
                                        if self.archiveListEntity.shelfItems[i].isSeleted == true {
                                            shelfItems.append(archiveListEntity.shelfItems[i])
                                        }
                                    }
                                } else {
                                    for i in 0 ..< AppState.shared.shelfCombineItems.count {
                                        if AppState.shared.shelfCombineItems[i].isSeleted == true {
                                            shelfItems.append(AppState.shared.shelfCombineItems[i])
                                        }
                                    }
                                }
                                WDBookUserSDK.shared.createShelfArchive(archiveName: text) { result in
                                    switch result {
                                    case let .success(shelfArchiveItem):
                                        clientArchiveId = shelfArchiveItem?.clientArchiveId ?? ""
                                        for i in 0 ..< shelfItems.count {
                                            let tempShelfItem = ShelfBookItemEntity()
                                            tempShelfItem.resourceId = shelfItems[i].resourceId
                                            tempShelfItem.clientArchiveId = clientArchiveId
                                            tempShelfItem.itemId = shelfItems[i].itemId
                                            shelfUploadItems.append(tempShelfItem)
                                        }
                                        WDBookUserSDK.shared.moveShelfBookToArchive(bookList: shelfUploadItems, archiveId: clientArchiveId)
                                        WDBookDataSyncManager.shared.syncShelfList {}
                                        AppState.shared.reloadShelfData()
                                        groupClickAction?()
                                        onSuccess?()
                                    case let .failure(error):
                                        print(error)
                                    }
                                }
                            }
                        }
                    }
            }.frame(height: 52).padding(.horizontal, 25)
        }
        .frame(height: contentHeight)
        .frame(maxWidth: 295)
        .background(Color(dynamicBackgroundColor8))
        .cornerRadius(16)
    }

    var body: some View {
        if isAdjustKeyboard {
            ZStack(alignment: .center) {
                ScrollView {
                    Spacer().frame(height: (UIScreen.main.bounds.height - keyboardHeight - contentHeight - safeAreaInsets.top - safeAreaInsets.bottom) / 2)
                        .onReceive(Publishers.keyboardHeight) { self.keyboardHeight = $0 }
                    content
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.black.opacity(0.45))
            .animation(.easeOut(duration: 0.25))
        } else {
            ZStack(alignment: .center) {
                content
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color.black.opacity(0.45))
            .animation(.easeOut(duration: 0.4))
        }
    }
}

struct LimitUITextField: UIViewRepresentable {
    private var limit: Int = 20
    private var placeholder: String
    private var text: Binding<String>

    init(_ placeholder: String, text: Binding<String>, limit: Int = 20) {
        self.placeholder = placeholder
        self.text = text
        self.limit = limit
    }

    func makeCoordinator() -> LimitUITextField.Coordinator {
        Coordinator(self)
    }

    func makeUIView(context: UIViewRepresentableContext<LimitUITextField>) -> UITextField {
        let innertTextField = UITextField(frame: .zero)
        innertTextField.placeholder = placeholder
        innertTextField.text = text.wrappedValue
        innertTextField.delegate = context.coordinator
        innertTextField.becomeFirstResponder()
        innertTextField.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        context.coordinator.setup(innertTextField)

        return innertTextField
    }

    func updateUIView(_ uiView: UITextField, context _: UIViewRepresentableContext<LimitUITextField>) {
        uiView.text = text.wrappedValue
    }

    class Coordinator: NSObject, UITextFieldDelegate {
        var parent: LimitUITextField

        init(_ textFieldContainer: LimitUITextField) {
            parent = textFieldContainer
        }

        func setup(_ textField: UITextField) {
            textField.addTarget(self, action: #selector(textFieldDidChange), for: .editingChanged)
        }

        func textField(_ textField: UITextField, shouldChangeCharactersIn range: NSRange, replacementString string: String) -> Bool {
            let text = (textField.text ?? "")
            let shouldString = (text as NSString).replacingCharacters(in: range, with: string)
            if shouldString.count > parent.limit {
                Toaster.showToast(message: "输入长度不能超过%lld个字符".localizedFormat(parent.limit), position: .top)
                return shouldString.count <= text.count
            }
            return true
        }

        @objc func textFieldDidChange(_ textField: UITextField) {
            parent.text.wrappedValue = textField.text ?? ""

            let newPosition = textField.endOfDocument
            textField.selectedTextRange = textField.textRange(from: newPosition, to: newPosition)
        }
    }
}

// ObservableObject方案失败。
// class TextObj: ObservableObject {
////    let didChange = PassthroughSubject<TextObj, Never>()
//
//    var oldV:String = ""
//    @Published var value:String{ //写了published才能截取成功
//        willSet{
//            debugPrint("输入框：willSet: value:\(value), newValue:\(newValue)")
//            if value.count > 20 && oldV.count <= 20 {
//                value = oldV
//            }
//        }
//        didSet {
//            debugPrint("输入框：didSet: value:\(value), oldValue:\(oldValue)")
//            oldV = value
////            didChange.send((self))
//        }
//    }
//
//    init (value:String? = "") {
//        self.value = value!
//    }
// }

// 无法限制字数
// class TextObj: ObservableObject {
////    let didChange = PassthroughSubject<TextObj, Never>() //没有效果。
//    var _value:String = ""
//
//    @Published var value:String{ //@Published不可以和计算属性同用，编译不通过。
//        set{
//            debugPrint("输入框：set: value:\(_value), oldValue:\(newValue)")
//            if newValue.count <= 20 {
//                _value = newValue
////                didChange.send(self)
//            }
//        }
//        get{
//            debugPrint("输入框：get: \(_value)")
//            return _value
//        }
//    }
//
//    init (value:String? = "") {
//        self._value = value!
//    }
// }

// value数值没问题，但是TextField的显示是先变化的，必须手动改回去。
// class LimitText: ObservableObject, TabSelectable {
//    init() {
//        _value.register(self)
//    }
//
//    func shouldSelect(_ newValue: String) -> Bool {
//        return newValue.count <= 20
//    }
//
//    @TabSelection var value = "" {
//        willSet {
//            objectWillChange.send()
//        }
//    }
// }
