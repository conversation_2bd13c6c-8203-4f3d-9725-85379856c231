//
//  KeyboardManagment.swift
//  WDBook
//
//  Created by QK on 2021/11/18.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import Combine

struct KeyboardAwareModifier: ViewModifier {
    var contentHeight:CGFloat
    @State private var keyboardHeight: CGFloat = 0

    private var keyboardHeightPublisher: AnyPublisher<CGFloat, Never> {
        Publishers.Merge(
            NotificationCenter.default
                .publisher(for: UIResponder.keyboardWillShowNotification)
                .compactMap { $0.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? NSValue }
                .map { $0.cgRectValue.height },
            NotificationCenter.default
                .publisher(for: UIResponder.keyboardWillHideNotification)
                .map { _ in CGFloat(0) }
       ).eraseToAnyPublisher()
    }

    func body(content: Content) -> some View {
        content
            .padding(.bottom, (UIScreen.main.bounds.height - keyboardHeight - contentHeight) / 2)
//            .padding(.bottom, keyboardHeight)
            .onReceive(keyboardHeightPublisher) {
                self.keyboardHeight = $0
                debugPrint(keyboardHeight)
                debugPrint(contentHeight)
                debugPrint((UIScreen.main.bounds.height - keyboardHeight - contentHeight) / 2)
            }
    }
}

extension View {
    func keyboardAwarePadding(contentHeight:CGFloat) -> some View {
        ModifiedContent(content: self, modifier: KeyboardAwareModifier(contentHeight: contentHeight))
    }
}
