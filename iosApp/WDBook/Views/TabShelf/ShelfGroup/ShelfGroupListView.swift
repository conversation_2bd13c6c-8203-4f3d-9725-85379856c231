//
//  ShelfGroupListView.swift
//  WDBook
//  分组列表页面
//  Created by QK on 2021/9/22.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import DeviceKit
import Foundation
import shared
import SwiftUI

// import Zip
import SSZipArchive

struct ShelfGroupListView: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @Namespace var topID
    @StateObject var archiveListEntity: ShelfArchiveListEntity = .init()
    @State var shelfArchiveItem: DownloadResourceObservable
    @State private var showingGroupDialog = false
    @State private var showingGroupCreatDialog = false
    @State var isFirstLoad = true
    @State var isOnEdit: Bool = false
    @State var isBookSelcted: Bool = false
    @State var isSelectAll: Bool = false
    @State var isRename: Bool = false
    @State private var isShowAlert = false
    @State private var sourceIsSearchResults: Bool = false

    private var columns: [GridItem] {
        return [GridItem(.adaptive(minimum: TabShelfV.itemMiniWidth))]
    }

    private func refreshGroupData() {
        archiveListEntity.shelfItems.removeAll()
        refreshShelfArchiveList()
    }

    func refreshShelfArchiveList() {
        if !WDBookSessionSDK.shared.isLogin {
            return
        }
        let shelfItems = WDBookUserSDK.shared.getBookListByClientArchiveId(clientArchiveId: shelfArchiveItem.clientArchiveId, offset: Int64(archiveListEntity.shelfItems.count), limit: 20)
        if shelfItems.count > 0 {
            for shelfItem in shelfItems {
                let combineEntity = HomeShelfItemCombineEntity()
                combineEntity.bookItemEntity = shelfItem
                combineEntity.dataType = ShelfDataType.resource
                archiveListEntity.shelfItems.append(DownloadResourceObservable(item: combineEntity))
            }
            refreshShelfArchiveList()
        }
    }

    var modifyGroupV: some View {
        HStack(alignment: .bottom, spacing: 0) {
            Text("修改分组名".localized)
                .foregroundColor(Color(dynamicTitleColor2))
                .font(.system(size: 14))
                .onTapGesture {
                    self.isRename = true
                    self.showingGroupCreatDialog = true
                }
                .frame(maxWidth: 100, maxHeight: .infinity, alignment: .leading)
            Spacer()
            Text("删除分组".localized)
                .foregroundColor(Color(dynamicTitleColor2))
                .font(.system(size: 14))
                .onTapGesture {
                    self.isShowAlert = true
                }
                .frame(maxWidth: 100, maxHeight: .infinity, alignment: .trailing)
                .alert(isPresented: $isShowAlert) {
                    Alert(title: Text(""),
                          message: Text(archiveListEntity.shelfItems.count > 0 ? "删除分组后，分组内的书籍将自\n动移出到书架，不会被删除".localized : "确认删除该分组?".localized).font(.system(size: 16)),
                          primaryButton: .default(Text("取消".localized)) {
                              isShowAlert = false
                          }, secondaryButton: .default(Text("确定".localized)) {
                              isShowAlert = false
                              WDBookUserSDK.shared.deleteArchiveItem(archiveId: shelfArchiveItem.clientArchiveId)
                              WDBookDataSyncManager.shared.syncShelfList {}
                              Toaster.showToast(message: "已删除当前分组".localized)
                              refreshSearchResults()
                              presentationMode.wrappedValue.dismiss()
                              AppState.shared.reloadShelfData()
                          })
                }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 20)
        .padding(EdgeInsets(top: 20, leading: 24, bottom: 0, trailing: 24))
    }

    var content: some View {
        ZStack(alignment: .center) {
            if self.archiveListEntity.shelfItems.count > 0 {
                ScrollViewReader { scrollView in
                    ScrollView {
                        Rectangle()
                            .fill(Color.clear)
                            .frame(height: 0.5)
                            .id(topID)

                        LazyVGrid(columns: columns, alignment: .center, spacing: TabShelfV.span) {
                            ForEach(self.archiveListEntity.shelfItems, id: \.itemId) { item in
                                ShelfItemV(shelfItem: item, isOnEdit: $isOnEdit, openBookAction: {
                                    RoutableManager.navigate(toPath: RouterName.readerWithId.withParam(item.resourceId))
                                }, shelfEditAction: {
                                    self.isBookSelcted = false
                                    for i in 0 ..< self.archiveListEntity.shelfItems.count {
                                        if self.archiveListEntity.shelfItems[i].isSeleted == true {
                                            self.isBookSelcted = true
                                            return
                                        }
                                    }
                                })
                                .onLongPressGesture(minimumDuration: 0.5) {
                                    if !isOnEdit {
                                        isOnEdit = true
                                        item.isSeleted = true
                                        self.isBookSelcted = false
                                        for i in 0 ..< self.archiveListEntity.shelfItems.count {
                                            if self.archiveListEntity.shelfItems[i].isSeleted == true {
                                                self.isBookSelcted = true
                                                return
                                            }
                                        }
                                    }
                                }
                            }
                        }
                        .padding(.horizontal, 15)
                        .padding(.vertical, 12)
                        .padding(.top, -12)
                    }
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color(dynamicBackgroundColor1))
                    .onAppear {
                        //                            withAnimation {
                        scrollView.scrollTo(topID)
                        //                            }
                    }
                }
            } else {
                EmptyView()
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
//        .navigationBarHidden(self.isShowReader) //TODO: 处理
    }

    var mainContent: some View {
        ZStack(alignment: .center) {
            VStack {
                ScrollView {
                    if !isOnEdit {
                        modifyGroupV
                    }

                    content
                }.frame(maxWidth: .infinity, maxHeight: .infinity)

                if isOnEdit {
                    Button(action: {
                        guard WDBookSessionSDK.shared.isLogin else {
                            AppState.shared.showLoginRegisterVAndPopToStore()
                            return
                        }
                        guard isBookSelcted else { return }
                        withAnimation(.spring()) {
                            self.isRename = true
                            self.showingGroupDialog.toggle()
                        }
                    }) {
                        Text("分组到...".localized)
                            .foregroundColor(isBookSelcted ? Color(dynamicTextColor17) : Color(dynamicTextColor16))
                            .accentColor(Color(UIColor.primaryColor1))
                            .font(.system(size: 14))
                            .font(.headline)
                            .frame(maxWidth: .infinity, maxHeight: 57)
                            .contentShape(Rectangle()).padding(.bottom, safeAreaInsets.bottom)
                    }
                    .buttonStyle(MyButtonStyle(isBookSelcted: $isBookSelcted))
                    .frame(maxWidth: .infinity)
                    .overlay(isOnEdit ? AnyView(Text("").frame(maxWidth: .infinity).frame(height: 1).background(Color(dynamicHomeTopLineColor))) : AnyView(EmptyView()), alignment: .top)
                }
            }.frame(maxWidth: .infinity, maxHeight: .infinity)

            if self.archiveListEntity.shelfItems.count == 0 {
                VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0, content: {
                    Text("当前分组暂无书籍".localized).font(Font.medium(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                })
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .onReceive(NotificationCenter.default.publisher(for: TabShelfV.Noti_IsOnShelfEdit, object: nil)) { noti in
            if let isOnEdit = noti.object as? Bool {
                if !isOnEdit {
                    isBookSelcted = false
                    isSelectAll = false
                }
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Waiting, object: nil)) { noti in
            downloadWaitingHandler(noti: noti)
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Progress, object: nil)) { noti in
            downloadProgressHandler(noti: noti)
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Fails, object: nil)) { noti in
            downloadFailsHandler(noti: noti)
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Complete, object: nil)) { noti in
            downloadCompleteHandler(noti: noti)
        }
        .onReceive(NotificationCenter.default.publisher(for: WDReaderView.readBookWillHideNotification, object: nil)) { _ in
            DispatchQueue.main.async {
                self.archiveListEntity.shelfItems.removeAll()
                self.refreshShelfArchiveList()
            }
        }
        .onAppear {
            if self.isFirstLoad {
                self.isFirstLoad = false
                self.archiveListEntity.shelfItems.removeAll()
                self.refreshShelfArchiveList()
            }
            self.sourceIsSearchResults = BookShelfSearchState.shared.navigatedToGroupViewFromSearch
            BookShelfSearchState.shared.navigatedToGroupViewFromSearch = false
        }
    }

    var header: some View {
        HStack(alignment: .center, spacing: 0, content: {
            ZStack(alignment: .leading) {
                if self.archiveListEntity.shelfItems.count == 0 || !isOnEdit {
                    Image("back_ui")
                        .aspectRatio(contentMode: .fit)
                        .foregroundColor(Color(btnTintColor)) // ios14无效
                        .onTapGesture {
                            presentationMode.wrappedValue.dismiss()
                        }
                }
                Text(isSelectAll ? "取消全选".localized : "全选".localized)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(.system(size: 14))
                    .isHidden(self.archiveListEntity.shelfItems.count == 0 || !isOnEdit)
                    .onTapGesture {
                        guard WDBookSessionSDK.shared.isLogin else {
                            AppState.shared.showLoginRegisterVAndPopToStore()
                            return
                        }
                        isSelectAll.toggle()
                        for i in 0 ..< self.archiveListEntity.shelfItems.count {
                            self.archiveListEntity.shelfItems[i].isSeleted = isSelectAll
                        }
                        isBookSelcted = isSelectAll
                    }
                    .frame(maxWidth: 100, maxHeight: .infinity, alignment: .leading)
            }.frame(maxWidth: 100, maxHeight: .infinity, alignment: .leading)
                .padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))

            Spacer()
            Text(shelfArchiveItem.archiveName)
                .foregroundColor(Color(dynamicTitleColor2))
                .font(Font.semibold(size: 18))
            Spacer()

            if self.archiveListEntity.shelfItems.count > 0 {
                Button {
                    guard WDBookSessionSDK.shared.isLogin else {
                        AppState.shared.showLoginRegisterVAndPopToStore()
                        return
                    }
                    self.isOnEdit.toggle()
                    if !isOnEdit {
                        isSelectAll = false
                        isBookSelcted = false
                        for i in 0 ..< self.archiveListEntity.shelfItems.count {
                            self.archiveListEntity.shelfItems[i].isSeleted = false
                        }
                    }
                } label: {
                    Text(isOnEdit ? "取消".localized : "编辑".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(.system(size: 16))
                        .frame(maxWidth: 100, maxHeight: .infinity, alignment: .trailing)
                }.padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0))
            } else {
                Spacer().frame(width: 100)
            }
        }).frame(height: 45)
            .frame(maxWidth: .infinity)
            .padding(.horizontal, 20)
            .padding(.top, safeAreaInsets.top)
            .background(Color(dynamicBackgroundColor1))
            .modifier(BottomLineViewModifier(isShowBottomLine: true))
    }

    var body: some View {
        ZStack(alignment: .top, content: {
            mainContent
                .navigationBarHidden(true)
                .padding(.top, 45 + safeAreaInsets.top)

            header

            if self.showingGroupDialog, WDBookSessionSDK.shared.isLogin {
                ShelfGroupListDialog(showingGroupDialog: $showingGroupDialog, showingGroupCreatDialog: $showingGroupCreatDialog, isShowRemoveView: true, isOnEdit: $isOnEdit, isRename: $isRename, clientArchiveId: shelfArchiveItem.clientArchiveId) {
                    refreshGroupData()
                }
                .environmentObject(self.archiveListEntity)
            }

            if self.showingGroupCreatDialog, WDBookSessionSDK.shared.isLogin {
                CreateGroupDialog(showingGroupCreatDialog: $showingGroupCreatDialog, isOnEdit: $isOnEdit, isAdjustKeyboard: true, isRename: isRename, isFromGroupList: true) {
                    refreshGroupData()
                } onSuccess: {
                    refreshSearchResults()
                }
                .environmentObject(shelfArchiveItem).environmentObject(self.archiveListEntity)
            }
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .background(Color(dynamicBackgroundColor1))
    }

    func downloadWaitingHandler(noti: Notification) {
        if let downloadInfo = noti.object as? DownloadDataEntity {
            archiveListEntity.shelfItems.filter { $0.resourceId == downloadInfo.resourceId }.forEach { item in
                item.downloadState = .waiting
            }
        }
    }

    func downloadProgressHandler(noti: Notification) {
        if let downloadInfo = noti.object as? DownloadDataEntity {
            archiveListEntity.shelfItems.filter { $0.resourceId == downloadInfo.resourceId }.forEach { item in
                item.downloadProgress = downloadInfo.progress
                item.downloadState = .downloading
            }
        }
    }

    func downloadFailsHandler(noti: Notification) {
        if let downloadInfo = noti.object as? DownloadDataEntity {
            archiveListEntity.shelfItems.filter { $0.resourceId == downloadInfo.resourceId }.forEach { item in
                item.downloadProgress = 0.0
                item.downloadState = .unDownload
            }
        }
    }

    func downloadCompleteHandler(noti: Notification) {
        if let downloadInfo = noti.object as? DownloadDataEntity {
            archiveListEntity.shelfItems.filter { $0.resourceId == downloadInfo.resourceId }.forEach { item in
                item.downloadProgress = 1.0
                item.downloadState = .completed
                AppDownloadManager.shared.unzip(path: downloadInfo.fileId!) { success in
                    if success {
                        item.downloadState = .completed
                    } else {
                        item.downloadState = .unDownload
                    }
                }
            }
        }
    }

    private func refreshSearchResults() {
        if sourceIsSearchResults {
            BookShelfSearchState.shared.signalGroupEditOccurred()
        }
    }
}

class ShelfArchiveListEntity: ObservableObject {
    @Published var shelfItems: [DownloadResourceObservable] = []
}
