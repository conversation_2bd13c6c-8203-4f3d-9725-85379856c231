//
//  SearchListV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2021/12/21.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import NavigationRouter
import shared
import SnapKit
import SwiftUI
import UIKit
import WebKit

extension SearchItemEntity {
    var limitText: String {
        if keyWords.count > 12 {
            return keyWords.prefix(12) + "..."
        } else {
            return String(keyWords.prefix(12))
        }
    }
}

struct SearchListV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets) private var safeAreaInsets

    @State var isFirstLoad = true
    @State var isBecomeFirstResponderWhenInit = true
    @State var searchText = ""
    @State var products: [ProductEntity] = .init()
    @State var productsResult: ProductPageListEntity = .init()

    @State var isClickedSearch: Bool = true
    @State var searchDataList: [SearchItemEntity] = []
    @State var isShowAlert = false

    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    @State var noMore: Bool = false
    @State var pageIndex: Int = 0
    @State var isFistSearch: Bool = false

    func addHistory() {
        let entity = SearchItemEntity()
        entity.keyWords = searchText
        WDBookAppSDK.shared.saveSearchData(entity: entity)
        loadSearchHistoryData()
    }

    func loadSearchHistoryData() {
        searchDataList = Array(WDBookAppSDK.shared.getSearchDataList(limit: 20).prefix(8))
    }

    var body: some View {
        ZStack(alignment: .top, content: {
            if isClickedSearch {
                mainContent
                    .navigationBarHidden(true)
                    .padding(.top, 45 + safeAreaInsets.top)
                    .onAppear {
                        loadSearchHistoryData()
                    }
            } else {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        Spacer().frame(height: 12)
                        ForEach(products, id: \.productId) { product in
                            RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                                SearchProductListCell(product: product)
                            }.buttonStyle(ClearHighlightStyle())
                        }
                    }
                    if self.products.count > 0 {
                        if !noMore {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore: $noMore) {
                                self.loadMore()
                            }
                        } else {
                            Spacer().frame(height: 12)
                        }
                    }
                }
                .enableRefresh()
                .frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(dynamicBackgroundColor4))
                .overlay(Group {
                    if isFistSearch {
                        ActivityIndicator()
                    } else if products.count == 0 {
                        VStack(alignment: .center) {
                            Text("没有找到你想要的结果".localized).font(Font.medium(size: 18)).foregroundColor(Color(dynamicTitleColor5))
                            Spacer().frame(height: 12)
                            Text("试试别的关键词吧！".localized).font(Font.regular(size: 14)).foregroundColor(Color(dynamicTitleColor5))
                        }
                    } else {
                        EmptyView()
                    }
                })
                .padding(.top, 45 + safeAreaInsets.top)
            }

            HStack(alignment: .center, spacing: 0, content: {
                SearchBarUIKit(text: $searchText, placeholder: "搜索书名或作者".localized, isBecomeFirstResponderWhenInit: $isBecomeFirstResponderWhenInit, maxLength: 32, searchAction: { _ in
                    doSearch()
                }, textChangeAction: { searchText in
                    if searchText.isBlank {
                        isClickedSearch = true
                    }
                })
                Button {
                    presentationMode.wrappedValue.dismiss()
                } label: {
                    Text("取消".localized)
                        .font(.system(size: 14))
                        .foregroundColor(Color("wd_orange"))
                        .frame(maxHeight: .infinity)
                        .padding(.leading, 16)
                        .padding(.trailing, 20)
                }

            }).frame(height: 45)
                .frame(maxWidth: .infinity)
                .padding(.leading, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))

        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }

    func doSearch() {
        if !searchText.trim().isBlank {
            addHistory()
            isFistSearch = true
            noMore = false
            pageIndex = 0
            isClickedSearch = false
            products.removeAll()
            loadMore()
        }
    }

    func loadMore() {
        if noMore {
            footerRefreshing = false
            return
        }
        pageIndex += 1
        WDBookStoreSDK.shared.getSearchProductList(page: pageIndex, pageSize: 10, text: searchText) { result in
            self.footerRefreshing = false
            switch result {
            case let .success(pageListEntity):
                if isFistSearch {
                    self.isFistSearch = false
                }
                if pageListEntity != nil {
                    self.productsResult = pageListEntity!
                    self.products.append(contentsOf: self.productsResult.productList as? [ProductEntity] ?? [])
                    self.noMore = !self.productsResult.hasNext
                }
            case let .failure(error):
                if isFistSearch {
                    self.isFistSearch = false
                }
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    if error.code == -1009 {
                        Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                    }
                    print(error)
                }
            }
        }
    }

    var mainContent: some View {
        ScrollView(showsIndicators: false) {
            VStack(spacing: 0) {
                VStack(spacing: 16) {
                    if searchDataList.count > 0 {
                        HStack {
                            Text("历史搜索".localized).font(Font.regular(size: 16)).frame(height: 22).foregroundColor(Color(dynamicTextColor18)).padding(.leading, 16)
                            Spacer()
                            Button {
                                self.isShowAlert = true
                            } label: {
                                Image("icon_trash").frame(width: 18, height: 18).padding(4).padding(.trailing, 16)
                            }
                            .alert(isPresented: $isShowAlert) {
                                Alert(title: Text("确认删除".localized),
                                      message: Text("确认删除所有历史记录".localized),
                                      primaryButton: .default(Text("取消".localized)) {
                                          isShowAlert = false
                                      }, secondaryButton: .default(Text("删除".localized)) {
                                          WDBookAppSDK.shared.clearSearchHistory()
                                          loadSearchHistoryData()
                                          isShowAlert = false
                                      })
                            }

                        }.padding(EdgeInsets(top: 23.5, leading: 0, bottom: 0, trailing: 0))
                    }
                    // 最多两行
                    FlowLayout(mode: .scrollable, items: searchDataList) { _, item in
                        Text(item.limitText)
                            .font(.init(UIFont.primaryTextRegular))
                            .foregroundColor(Color(dynamicTextColor18))
                            .lineLimit(1)
                            .padding(.top, 6)
                            .padding(.bottom, 6)
                            .padding(.leading, 8)
                            .padding(.trailing, 8)
                            .frame(height: 32)
                            .background(Color("SearchHistoryItemBackground"))
                            .cornerRadius(19)
                            .onTapGesture {
                                searchText = item.keyWords
                                doSearch()
                                UIApplication.dismissKeyboard()
                            }
                    }.padding(.horizontal, 16)
                }

                // 为你推荐功能
//                VStack(spacing:16) {
//                    if AppState.shared.suggestWorldList.count > 0{
//                        HStack {
//                            Text("为你推荐".localized).font(Font.regular(size: 16)).frame(height:22).foregroundColor(Color(dynamicTextColor18)).padding(.leading, 16)
//                            Spacer()
//
//                        }.padding(EdgeInsets(top: 23.5, leading: 0, bottom: 0, trailing: 0))
//                    }
//                    //最多三行
//                    FlowLayout(mode: .scrollable,
//                               items: AppState.shared.suggestWorldList) {index, item in
//                        Text(item)
//                            .font(.system(size: 14))
//                            .lineLimit(1)
//                            .foregroundColor(Color(dynamicTextColor18))
//                            .padding(EdgeInsets(top: 7, leading: 16, bottom: 7, trailing: 16))
//                            .background(RoundedRectangle(cornerRadius: 100).foregroundColor(Color(dynamicBackgroundColor11)))
//                            .onTapGesture {
//                                searchText = item
//                                doSearch()
//                                UIApplication.dismissKeyboard()
//                            }
//                    }
//                               .padding(.horizontal, 16)
//                }

            }.frame(maxWidth: .infinity, maxHeight: .infinity)
                .onTapGesture {
                    UIApplication.dismissKeyboard()
                }
        }
        .onTapGesture {
            UIApplication.dismissKeyboard()
        }
        .onAppear {
            if isFirstLoad {
                isFirstLoad = false
                if !searchText.isBlank {
                    isBecomeFirstResponderWhenInit = false
                    DispatchQueue.main.async {
                        doSearch()
                    }
                }
            }
        }
    }
}

struct SearchProductListCell: View {
    var product: ProductEntity
    var height: CGFloat = 30.0
    @Environment(\.colorScheme) var colorScheme

    func getBlackColorStr10() -> String {
        return colorScheme == .light ? "#373636" : "#DEDEE3"
    }

    func getBlackColorStr18() -> String {
        return colorScheme == .light ? "#373636" : "#BCBCBC"
    }

    func getBlackColorStr13() -> String {
        return colorScheme == .light ? "#BCBCBC" : "#A5A5A6"
    }

    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 12)
            HStack(spacing: 0) {
                CoverImageView(product: product)
                Spacer().frame(width: 16)
                VStack(alignment: .leading) {
                    HTMLText(text: product.title?.trim().stripHtmlFont(color: getBlackColorStr10()), attributedText: nil, fontSize: 14, isBold: true, limitLine: 1)
                        .frame(minWidth: 0, maxWidth: .infinity, minHeight: 0, maxHeight: .infinity)
                        .frame(height: 20)
                    Spacer()
                    HTMLText(text: product.authorNames.trim().stripHtmlFont(color: getBlackColorStr18()), attributedText: nil, fontSize: 12, isBold: false, limitLine: 1)
                        .frame(height: 15)
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
                        .font(Font.regular(size: 12))
                        .isHidden(product.authorNames.isBlank)

                    Spacer()
                    ZStack {
                        HTMLText(text: product.desc.trim().stripHtmlFont(color: getBlackColorStr18()), attributedText: nil, fontSize: 12, isBold: false, limitLine: 2)
                            .foregroundColor(Color(dynamicTextColor18))
                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
                            .frame(height: 55)
                            .isHidden(product.desc.isBlank)
                        VStack(alignment: .leading, spacing: 0) {
                            Spacer().frame(height: 45)
                            HTMLText(text: (product.categories + ((product.publisherEntity?.name.isBlank)! ? "" : " | \(product.publisherEntity?.name ?? "")")).stripHtmlFont(color: getBlackColorStr13()), attributedText: nil, fontSize: 12, isBold: false, limitLine: 1)
                                .font(Font.regular(size: 12))
                                .foregroundColor(Color(dynamicTextColor13))
                                .frame(height: 15)
                                .isHidden(product.categories.isBlank)
                        }
                        .font(Font.regular(size: 12))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    }
                    .frame(height: 60)
                }
                .frame(height: 110)
            }
            Spacer().frame(height: 12)
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal, 24)
        .padding(.top, 0)
        .frame(height: 110 + 24)
        .background(Color(dynamicBackgroundColor4))
    }
}

struct HTMLStringView: UIViewRepresentable {
    let htmlContent: String

    func makeUIView(context _: Context) -> WKWebView {
        return WKWebView()
    }

    func updateUIView(_ uiView: WKWebView, context _: Context) {
        uiView.loadHTMLString(htmlContent, baseURL: nil)
    }
}

#if DEBUG
    struct SearchListV_Previews: PreviewProvider {
        static var previews: some View {
            SearchListV().environmentObject(AppState.shared)
        }
    }
#endif
