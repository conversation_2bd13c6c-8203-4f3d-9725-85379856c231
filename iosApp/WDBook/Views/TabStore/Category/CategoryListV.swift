//
//  CategoryListV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/1.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct CategoryListV: View {
    @EnvironmentObject var appState:AppState
    @State var headerRefreshing: Bool = false
    
    func categoryName(category:StoreCategoryEntity)->String{
        if category.categoryId == 0{
            return "全部书籍".localized
        }else{
            return category.categoryName
        }
    }
    var body: some View {
        BackNavigation(title: "全部分类".localized){
            ScrollView(showsIndicators: false) {
                WDRefreshHeader(refreshing: $headerRefreshing, action: {
                    WDBookDataSyncManager.shared.syncCategoryListData {
                        headerRefreshing = false
                    }
                })
                LazyVStack(spacing:0){
                    ForEach(Array(appState.allCategories.enumerated()), id:\.1.categoryId) {index, category in
                        VStack {
                            NavigationLink(destination: CategoryPageV(categoryId: category.categoryId).environmentObject(appState)) {
                                HSta<PERSON>(spacing:0) {
                                    Text(categoryName(category: category))
                                        .font(Font.medium(size: 17))
                                        .foregroundColor(Color(dynamicTextColor19))
                                        .padding(.leading, 24)
                                    Spacer()
                                    Text("\(category.productCount)")
                                        .font(Font.medium(size: 14))
                                        .foregroundColor(Color(dynamicTextColor20))
                                    Image("arrow_right_24").frame(width: 24, height: 24).padding(EdgeInsets(top: 0, leading: 4, bottom: 0, trailing: 24))
                                }.frame(maxWidth: .infinity,alignment: .leading)
                                .frame(height: 55)
                                .overlay(Color(dynamicSpanLineColor6).frame(height:0.5).padding(.leading, 16), alignment: .bottom)
                            }
                        }
                    }
                }.frame(maxWidth: .infinity,maxHeight:.infinity)
                .background(Color(dynamicBackgroundColor12))
                .padding(.top,16)
                
            }.enableRefresh()
            .background(Color(dynamicBackgroundColor12))
            .overlay(Group {
                if appState.categoryList.count == 0 {
                    ActivityIndicator()
                } else {
                    EmptyView()
                }
            })
            .navigationBarTitle("全部分类".localized,displayMode: .inline)
            .onAppear {
                WDBookDataSyncManager.shared.syncCategoryListData()
            }
        }
    }
}
