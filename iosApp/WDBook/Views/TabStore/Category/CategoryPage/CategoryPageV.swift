//
//  CategoryPageV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2021/12/24.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import Combine
import shared
import DeviceKit
import UIKit
import NavigationRouter

extension CategoryPageV{
    static let refreshNotification = Notification.Name("CategoryPageV.refreshNotification")
}

struct CategoryPageV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var initCategoryId:Int64
    @State var selectedI = 0{
        willSet{
            selectedJ = 0
        }
    }
    @State var selectedJ = 0
    @State var isExpandI = false
    @State var isExpandJ = false
    @State var isHCollectionJWiderBorder = false
    @State var expandJHeight:CGFloat = 0
    
    @State var sorts:[SortType] = [.createTime_desc,.saleCount_desc,.price_asc]
    @State var selectedSortIndex = 0
    
    @State var languages:[Language] = [.all,.zh_Hans,.zh_Hant]
    @State var selectedLanaugeIndex = 0
    @State var preSelectedLanguageIndex = 0
    @State var isExpandLanguage = false{
        didSet{
            if isExpandLanguage{ //展开时候保存临时选择索引
                preSelectedLanguageIndex = selectedLanaugeIndex
            }else{ //关闭时候
                needRefresh = preSelectedLanguageIndex != selectedLanaugeIndex
                if needRefresh{
                    debugPrint("需要刷新")
                }else{
                    debugPrint("不需要刷新")
                }
            }
        }
    }
    @State var needRefresh:Bool = true
    
    @State var headerHeight:CGFloat = 0
    @State var scrollViewOffsetY:CGFloat = 0
    
    static let resetPositionNotification = Notification.Name("CategoryPageV_resetPositionNotification")
    @Namespace var topID
    @Namespace var pageContentID
    
    //产品与刷新
    @State var pageIndex:Int = 1
    @State var productsResult:ProductPageListEntity = ProductPageListEntity()
    @State var products:[ProductEntity] = []
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    @State var noMore: Bool = false
    @State var isFirstLoad = true
    @State var isErrorState = false
    @State var isLoading = false
    
    let fontSize = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                               Device.iPodTouch7,
                                                               Device.simulator(.iPodTouch7),
                                                               Device.simulator(.iPhoneSE)]) ? 12 : 14)
    init(categoryId: Int64 = 0,
         productsResult:ProductPageListEntity? = nil,
         isPresent:Binding<Bool>? = nil) {
        self.initCategoryId = categoryId
        if productsResult != nil{
            _productsResult = State(initialValue: productsResult!)
        }
        if isTestFlight1(){
            _isExpandI = State(initialValue: true)
            _isExpandJ = State(initialValue: true)
        }
    }
    
    var categoryIdParam:Int64{
        if selectedI == 0{
            return -1
        }else{
            if selectedJ == 0{
                return AppState.shared.allCategories[selectedI].categoryId
            }else{
                return AppState.shared.allCategories[selectedI].subCategories[selectedJ].categoryId
            }
        }
    }
    
    func refresh(isShowLoading:Bool = true,isCanShowErrorState:Bool = true) {
        
        guard NetReachability.isReachability() else{
            if isCanShowErrorState { isErrorState = true }
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }
        
        if isTestFlight1(){
            if AppState.shared.categoryList.count == 0{
                WDBookDataSyncManager.shared.syncCategoryListData{
                    initSelectedCategory()
                }
            }
        }
        
        pageIndex = 1
        let params = ProductFilterEntity()
        params.page = Int32(pageIndex)
        params.categoryId = categoryIdParam
        params.language = languages[selectedLanaugeIndex].searchParam
        params.orderField = sorts[selectedSortIndex].searchParam
        
        if isShowLoading { isLoading = true }
        WDBookStoreSDK.shared.getBookProductFilter(paramsMap: params.transferToMap()) { result in
            switch result {
            case .success(let pageListEntity):
                if let productList = pageListEntity?.productList{
                    self.productsResult = pageListEntity!
                    self.products.removeAll()
                    self.products.append(contentsOf: productList as? [ProductEntity] ?? [])
                    self.noMore = !self.productsResult.hasNext
                } else {
                    self.noMore = true
                }
                isErrorState = false
            case .failure(let error):
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
//                    switch error{
//                    case .error(let kotExp):
//                        print(kotExp)
//                        if let exp = kotExp as? KotlinException{
//                            debugPrint("exp")
//                        }
//                        break
//                    }
                }
                if isCanShowErrorState { isErrorState = true }
            }
            
            headerRefreshing = false
            isFirstLoad = false
            isLoading = false
//            if isShowLoading { isLoading = false }
        }
    }
    
    func loadMore() {
        guard NetReachability.isReachability() else{
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }
        
        if self.noMore {
            self.footerRefreshing = false
            return
        }
        pageIndex += 1
        
        let params = ProductFilterEntity()
        params.page = Int32(pageIndex)
        params.categoryId = categoryIdParam
        params.language = languages[selectedLanaugeIndex].searchParam
        params.orderField = sorts[selectedSortIndex].searchParam
        
        WDBookStoreSDK.shared.getBookProductFilter(paramsMap: params.transferToMap()) { result in
            self.footerRefreshing = false
            switch result {
            case .success(let pageListEntity):
                if pageListEntity != nil {
                    self.productsResult = pageListEntity!
                    self.products.append(contentsOf: self.productsResult.productList as? [ProductEntity] ?? [])
                    self.noMore = !self.productsResult.hasNext
                }
            case .failure(let error):
                self.footerRefreshing = false
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
                }
            }
        }
    }
    
    var body : some View{
        ZStack(alignment: .top, content: {
            if AppState.shared.allCategories.count > 0{
                mainContent
                    .navigationBarHidden(true)
                    .padding(.top, (45 + UIDevice.safeArea3InsetsTop))
            }
            HStack(alignment: .center, spacing: 20, content: {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image("back_ui")
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24, alignment: .center)
                        .foregroundColor(Color(btnTintColor))
                        .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                        .opacity(isTestFlight1() ? 0 : 1)
                }
                
                Spacer()
                if AppState.shared.allCategories.count > 0{
                    Text(AppState.shared.allCategories[selectedI].categoryName)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                }
                Spacer()
                
                RoutedLink(toRoute:.search) {
                    Image("icon_search").frame(width: 24, height: 24,alignment: .center).padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                }
                
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.top, UIDevice.safeArea3InsetsTop)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        }).frame(maxWidth:.infinity,maxHeight: .infinity)
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .onAppear{
            if isTestFlight1(){
                AppState.shared.refreshCategoryList()
                if AppState.shared.categoryList.count == 0{
                    WDBookDataSyncManager.shared.syncCategoryListData()
                }
            }
        }
    }
    
    var header:some View{
        VStack(spacing:0) {
            //类别筛选
            VStack(spacing:11){
                HStack(alignment: .bottom,spacing:0) {
                    ZStack {
//                        if isExpandI{
                            FlowLayout(mode: .scrollable, items: AppState.shared.allCategories.map{$0.categoryName},itemSpacing:0) {index, item in
                                Text(item)
                                    .foregroundColor(item == AppState.shared.allCategories[selectedI].categoryName ? Color(UIColor(hex: 0xFF8A00)):Color(dynamicTitleColor4))
                                    .frame(height:26)
                                    .padding(.horizontal, 12)
                                    .background(item == AppState.shared.allCategories[selectedI].categoryName ? Color(UIColor(hex: 0xFF8A00).alpha(0.1)):Color.clear)
                                    .cornerRadius(100)
                                    .padding(.top, 0)
                                    .padding(.bottom, 2.5)
                                    .padding(.leading, index == 0 ? 0 : 2.5)
                                    .padding(.trailing, index == AppState.shared.allCategories.count - 1 ? 0 : 2.5)
                                    .onTapGesture {
                                        isExpandLanguage = false
                                        selectedI = AppState.shared.allCategories.map{$0.categoryName}.firstIndex(of: item) ?? 0
                                        refresh()
                                        NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                                    }
                            }.opacity(isExpandI ? 1 : 0).frame(maxHeight: isExpandI ? .infinity : 1)
//                        }else{
                        if !isTestFlight1(){
                            HCollectionV(id: 0, datas: AppState.shared.allCategories.map{$0.categoryName},selectedIndex: $selectedI,onSelected: { index in
                                isExpandLanguage = false
                                selectedJ = 0
                                refresh()
                                NotificationCenter.default.post(name: HCollectionV.scrollToIndexNotification, object: 1)
                            }).opacity(isExpandI ? 0 : 1)
                        }
                            
//                        }
                    }
                    
                    Button {
                        //                        withAnimation {
                        isExpandI = !isExpandI
                        //                        }
                        NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                    } label: {
                        Image(isExpandI ? "arrow_up_16":"arrow_down_16").frame(width: 16, height: 16).padding(EdgeInsets(top: 1, leading: 2, bottom: 1, trailing: 12))
                            .background(Color(dynamicBackgroundColor4))
                            .shadow(color: Color(dynamicBackgroundColor4), radius: 4, x: -3, y: 3)
                            .opacity(isTestFlight1() ? 0 : 1)
                    }
                }
                if selectedI > 0{
                    HStack(alignment: .bottom,spacing:0)  {
                        ZStack{
//                            if isExpandJ{
                                FlowLayout(id:1,mode: .scrollable,
                                           items: AppState.shared.allCategories[selectedI].subCategories.map{$0.categoryName},itemSpacing:0) {index, item in
                                    Text(item)
                                        .foregroundColor(item == AppState.shared.allCategories[selectedI].subCategories[selectedJ].categoryName ? Color(UIColor(hex: 0xFF8A00)):Color(dynamicTitleColor4))
                                        .frame(height:26)
                                        .padding(.horizontal, 12)
                                        .background(item == AppState.shared.allCategories[selectedI].subCategories[selectedJ].categoryName ? Color(UIColor(hex: 0xFF8A00).alpha(0.1)):Color.clear)
                                        .cornerRadius(100)
                                        .padding(.top, 0)
                                        .padding(.bottom, 2.5)
                                        .padding(.leading, index == 0 ? 0 : 2.5)
                                        .padding(.trailing, index == AppState.shared.allCategories[selectedI].subCategories.count - 1 ? 0 : 2.5)
                                        .onTapGesture {
                                            isExpandLanguage = false
                                            let index = AppState.shared.allCategories[selectedI].subCategories.map{$0.categoryName}.firstIndex(of: item)
                                            selectedJ = index ?? 0
                                            refresh()
                                            NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                                        }
                                }.opacity(isExpandJ ? 1 : 0).frame(maxHeight: isExpandJ ? .infinity : 1)
//                            }else{
                            if !isTestFlight1(){
                                HCollectionV(id: 1, datas: AppState.shared.allCategories[selectedI].subCategories.map{$0.categoryName},selectedIndex: $selectedJ,isWiderBorder:$isHCollectionJWiderBorder,onSelected: { index in
                                    isExpandLanguage = false
                                    refresh()
                                }).opacity(isExpandJ ? 0 : 1)
                            }
//                            }
                        }
                        
                        Button {
                            isExpandJ = !isExpandJ
                            NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                        } label: {
                            Image(isExpandJ ? "arrow_up_16":"arrow_down_16").frame(width: 16, height: 16).padding(EdgeInsets(top: 1, leading: 2, bottom: 1, trailing: 12))
                                .background(Color(dynamicBackgroundColor4))
                                .shadow(color: Color(dynamicBackgroundColor4), radius: 4, x: -3, y: 3)
                        }.opacity(isTestFlight1() ? 0 : (isNeedShowExtendJBtn ? 1 : 0))
                        .onReceive(NotificationCenter.default.publisher(for: FlowLayoutHeightDidChangedNotification)) { noti in
                            if let (id,height) = noti.object as? (Int,CGFloat),id == 1{
                                expandJHeight = height
                            }
                        }
                    }
                }
                
            }.font(Font.regular(size: 14))
            .padding(EdgeInsets(top: 0, leading: 24, bottom: 16, trailing: 0))
            .overlay(Color(UIColor(hex: 0xE3E3E3)).frame(height:0.5), alignment: .bottom)
            
            //排序与语言
            HStack {
                if !isTestFlight1(){
                    HStack(spacing: 15) {
                        ForEach(0..<sorts.count) { index in
                            Button {
                                isExpandLanguage = false
                                selectedSortIndex = index
                                refresh()
                                NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                            } label: {
                                Text(sorts[index].desc)
                                    .font(Font.regular(size: fontSize))
                                    .foregroundColor(index == selectedSortIndex ? Color(UIColor(hex: 0xFF8A00)):Color(dynamicTitleColor4))
                                    .frame(height:25)
                            }
                        }
                    }
                    Spacer()
                }
                
                Button {
                    NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: {
                        isExpandLanguage = !isExpandLanguage
                    })
                } label: {
                    HStack(spacing:0) {
                        Text(languages[selectedLanaugeIndex].desc).font(Font.regular(size: fontSize))
                            .foregroundColor(Color(dynamicTitleColor4))
                            .frame(height:17)
                            .padding(EdgeInsets(top: 5, leading: 15, bottom: 4, trailing: 4))
                        
                        Image(isExpandLanguage ? "arrow_up_16":"arrow_down_16").frame(width: 16, height: 16).padding(.trailing, 11)
                        
                    }.background(Color(dynamicBackgroundColor11))
                        .cornerRadius(radius: 100,corners: isExpandLanguage ? [.topLeft, .topRight] : [.topLeft,.topRight,.bottomLeft,.bottomRight])
                }
                if isTestFlight1(){
                    Spacer()
                }
            }.frame(height:58)
            .frame(maxWidth:.infinity)
            .padding(.horizontal,24)
            .background(isExpandLanguage ? AnyView(Color(dynamicBackgroundColor11).frame(height: (58 - 25)/2)) : AnyView(Color.clear), alignment: .bottom)
        }
    }
    
    var isNeedShowExtendJBtn:Bool{
        var needShow = true
        if isExpandJ {
            needShow = expandJHeight > 30
        }else{
            needShow = isHCollectionJWiderBorder
        }
        return needShow
    }
    
    var minHeader:some View{
        HStack(spacing:8) {
            Text(minHeaderTitle).font(Font.regular(size: 12))
            Image("arrow_down_16")
        }.frame(height:58).frame(maxWidth:.infinity).foregroundColor(Color(dynamicTitleColor5))
        .background(Color(dynamicBackgroundColor12))
        .onTapGesture {
            NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
        }
    }
    
    var minHeaderTitle:String{
        var parentTitle = ""
        if AppState.shared.allCategories.count > selectedI{
            parentTitle = AppState.shared.allCategories[selectedI].categoryName
        }
        var subTitle = ""
        if AppState.shared.allCategories.count > selectedI,
           AppState.shared.allCategories[selectedI].subCategories.count > selectedJ{
            subTitle = AppState.shared.allCategories[selectedI].subCategories[selectedJ].categoryName
        }
        
        let subTitleFull = subTitle.isEmpty ? "" : "\(subTitle)·"
        return "\(parentTitle)·\(subTitleFull)\(sorts[selectedSortIndex].desc)·\(languages[selectedLanaugeIndex].desc)"
    }
    
    var languageSelectV: some View{
        VStack(spacing:0) {
            VStack {
                ForEach(0 ..< languages.count, id: \.self) { index in
                    Button {
                        isExpandLanguage = false
                        selectedLanaugeIndex = index
                        refresh()
                        NotificationCenter.default.post(name: CategoryPageV.resetPositionNotification, object: nil)
                    } label: {
                        HStack(spacing:6) {
                            Image("icon_right").frame(width: 16, height: 16).opacity(selectedLanaugeIndex == index ? 1:0)
                            Text(languages[index].desc).font(Font.regular(size: fontSize)).foregroundColor(Color(dynamicTextColor23))
                            Spacer()
                        }.frame(height:33).padding(.horizontal, 16)
                    }
                }
            }.padding(.vertical, 8).background(Color(dynamicBackgroundColor11))
            Color(dynamicBackgroundColor13).onTapGesture {
                isExpandLanguage = false
            }
        }.edgesIgnoringSafeArea(.bottom)
    }
    
    var mainContent: some View {
        ZStack(alignment:.top) {
            ScrollViewReader { readerProxy in
                ScrollView(showsIndicators: false) {
                    
                    WDRefreshHeader(refreshing: $headerRefreshing, action: {
                        refresh(isShowLoading:false, isCanShowErrorState: false)
                    }).padding(.bottom, 15)
                        .id(topID)
                    
                    //为什么这个高度恒是20
                    GeometryReader { topProxy in
                        let offset = topProxy.frame(in: .named("scroll")).minY
                        Color(UIColor.black.alpha(0.01)).frame(height:0.01).preference(key: ViewOffsetKey.self, value: offset)//.id(topID)
                    }
                    
                    header.coordinateSpace(name: "header").background(
                        GeometryReader { headerProxy in
                            let headerHeight = headerProxy.size.height
                            Color.clear.preference(key: ViewFrameHeightKey.self, value: headerHeight)
                        }
                    )
                    
                    LazyVStack(spacing:0){
                        ForEach(self.products, id:\.productId) {  product in
                            RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                                ProductListCell(product:product)
                            }
                        }
                    }.padding(.top, -12)
                    
                    if self.isFirstLoad || isLoading{
                        HStack(spacing: 10) {
                            ActivityIndicator()
                            Text("加载中...".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                        }.padding(.top, (UIScreen.main.bounds.height - 22)/2 - (45 + UIDevice.safeArea3InsetsTop) - headerHeight)
                    }else if isErrorState{
                        Text("数据加载失败，请重试".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2)).padding(.top, (UIScreen.main.bounds.height - 22)/2 - (45 + UIDevice.safeArea3InsetsTop) - headerHeight)
                    }else if self.products.count == 0 {
                        Text("暂时没有相关书籍".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2)).padding(.top, (UIScreen.main.bounds.height - 22)/2 - (45 + UIDevice.safeArea3InsetsTop) - headerHeight)
                    } else {
                        
                    }
                    
                    if self.products.count > 0{
                        if !noMore {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                                DispatchQueue.main.async{
                                    self.loadMore()
                                }
                            }
                        } else {
                            Spacer().frame(height:12)
                        }
                    }
                }
                .enableRefresh()
                .coordinateSpace(name: "scroll")
                .background(Color(dynamicBackgroundColor4))
//                .disabled(isFirstLoad)
                .onPreferenceChange(ViewOffsetKey.self) { value in
//                    debugPrint("offsetY:\(value)")
                    DispatchQueue.main.async{
                        scrollViewOffsetY = value
                    }
//                    debugPrint("opacity:\(headerOpacity)")
//                    debugPrint("y:\(scrollViewOffsetY - headerHeight)")
                }
                .onPreferenceChange(ViewFrameHeightKey.self) { value in
//                    debugPrint("header height:\(value)")
                    DispatchQueue.main.async{
                        headerHeight = value
                    }
                }.onReceive(NotificationCenter.default.publisher(for:CategoryPageV.resetPositionNotification )) { noti in
                    withAnimation {
                        readerProxy.scrollTo(topID)
                    }
                }
            }
            minHeader.opacity(headerOpacity)
            if isExpandLanguage{
                languageSelectV.padding(.top, headerHeight)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
            self.needRefresh = true
        })
        .onReceive(NotificationCenter.default.publisher(for: CategoryPageV.refreshNotification), perform: { (obj) in
            if isTestFlight1(){
                AppState.shared.refreshCategoryList()
                if AppState.shared.categoryList.count == 0{
                    WDBookDataSyncManager.shared.syncCategoryListData{
                        initSelectedCategory()
                        refresh()
                    }
                }else{
                    initSelectedCategory()
                    refresh()
                }
                
            }
        })
        .onAppear {
            if needRefresh{
                if isFirstLoad{
                    initSelectedCategory()
                }
                self.refresh()
            }
            needRefresh = false
        }
    }
    
    func initSelectedCategory(){
        for (i,item) in AppState.shared.allCategories.enumerated(){
            if item.categoryId == initCategoryId{
                selectedI = i
                break
            }else{
                for (j,itemJ) in item.subCategories.enumerated(){
                    if itemJ.categoryId == initCategoryId {
                        selectedI = i
                        selectedJ = j
                        break
                    }
                }
            }
        }
    }
    
    var headerOpacity:CGFloat{
        if scrollViewOffsetY > 0{
            return 0
        }else if scrollViewOffsetY < -headerHeight - 58{
            return 1
        }else{
            let base = headerHeight - 58
            return abs(scrollViewOffsetY)/base
        }
    }
}

#if DEBUG
struct CategoryPageV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            CategoryPageV()
            CategoryPageV().environment(\.colorScheme, .dark)
        }
    }
}
#endif

enum Language{
    case all
    case zh_Hans
    case zh_Hant
    
    var desc:String{
        switch self{
            case .all: return "全部语言".localized
            case .zh_Hans: return "简体中文".localized
            case .zh_Hant: return "繁体中文".localized
        }
    }
    
    var searchParam:String{
        switch self{
            case .all: return ""
            case .zh_Hans: return LanguageMode.simplifiedchinese.value
            case .zh_Hant: return LanguageMode.traditionalchinese.value
        }
    }
}

enum SortType{
    case createTime_desc
    case saleCount_desc
    case price_asc
    
    var desc:String{
        switch self{
            case .createTime_desc: return "最新上架".localized
            case .saleCount_desc: return "最畅销".localized
            case .price_asc: return "价格最低".localized
        }
    }
    
    var searchParam:String{
        switch self{
        case .createTime_desc: return OrderField.createtimeDesc.value
            case .saleCount_desc: return OrderField.salecountDesc.value
            case .price_asc: return OrderField.priceAsc.value
        }
    }
}
