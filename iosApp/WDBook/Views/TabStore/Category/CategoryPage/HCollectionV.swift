//
//  HCollectionV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/1/26.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import shared

extension HCollectionV {
    static let scrollToIndexNotification = Notification.Name("HCollectionV_scrollToIndexNotification")
    static let indexChangedNotification = Notification.Name("HCollectionV_indexChangedNotification")
}

struct HCollectionV: View {
    @State var id:Int //0大类别，1子类别
    var datas:[String]
    @Binding var selectedIndex:Int{
        didSet{
            //等布局之后，再发送通知。
            DispatchQueue.main.async{
                NotificationCenter.default.post(name: HCollectionV.indexChangedNotification, object: id)
            }
        }
    }
    @Binding var isWiderBorder:Bool
    var onSelected:(Int)->()
    
    init(id:Int,datas:[String],selectedIndex:Binding<Int>,isWiderBorder:Binding<Bool> = .constant(false),onSelected:@escaping (Int)->()) {
        _id = State(initialValue: id)
        self.datas = datas
        _selectedIndex = selectedIndex
        _isWiderBorder = isWiderBorder
        self.onSelected = onSelected
    }
    
    var body: some View {
        ScrollViewReader { scrollViewProxy in
            GeometryReader { proxyOuter in
                ScrollView(.horizontal, showsIndicators: false) {
                    HStack(spacing: 5) {
                        ForEach(Array(datas.enumerated()), id: \.offset) { index, item in
                            Button {
                                selectedIndex = index
                                onSelected(index)
                                withAnimation {
                                    debugPrint("\(index)")
                                    scrollViewProxy.scrollTo(index, anchor: .center)
                                }
                            } label: {
                                Text(item)
                                    .foregroundColor(selectedIndex == index ? Color(UIColor(hex: 0xFF8A00)):Color(dynamicTitleColor4))
                                    .frame(height:26)
                                    .padding(.horizontal,12)
                                    .background(selectedIndex == index ? Color(UIColor(hex: 0xFF8A00).alpha(0.1)):Color.clear)
                                    .cornerRadius(100)
                            }.id(index)
                        }
                    }.background(
                        GeometryReader { proxy in
                            Color.clear.onAppear {
                                isWiderBorder = proxy.size.width > proxyOuter.size.width
                            }.onReceive(NotificationCenter.default.publisher(for: HCollectionV.indexChangedNotification), perform: { noti in
                                if id == 1{
                                    isWiderBorder = proxy.size.width > proxyOuter.size.width
                                }
                            })
                        }
                    )
                }.onReceive(NotificationCenter.default.publisher(for: HCollectionV.scrollToIndexNotification, object: nil), perform: { noti in
                    if let i = noti.object as? Int,i == id{
                        withAnimation {
                            scrollViewProxy.scrollTo(selectedIndex, anchor: .center)
                        }
                    }
                }).onAppear {
                    DispatchQueue.main.async{
                        scrollViewProxy.scrollTo(selectedIndex, anchor: .center)
                    }
                }
            }
        }.frame(height:26)
    }
}
