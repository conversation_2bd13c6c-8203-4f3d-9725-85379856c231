//
//  LeftRightWidget.swift
//  WDBook
//
//  Created by QK on 2021/12/30.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//
import shared
import SwiftUI
import SDWebImageSwiftUI
import DeviceKit
import NavigationRouter

struct LeftRightWidget: View {
    @EnvironmentObject var appState: AppState
    @State var containerEntity: WidgetContainerCombineEntity
    @State var btnStr:String = ""
    @State var currentPage:Int = 1
    @State var buttonType:String = DisplayButtonType.view_all.rawValue
    @State var isFirstLoad = true
    
    let itemWidth = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                         Device.iPodTouch7,
                                                         Device.simulator(.iPodTouch7),
                                                         Device.simulator(.iPhoneSE)]) ? 250 : 300)
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(alignment: .center, spacing: 4) {
                Text("%@".localizedFormat(self.containerEntity.containerTitle ?? ""))
                    .font(Font.medium(size: 18))
                    .foregroundColor(Color(titleColor2))
                    .frame(maxWidth: .infinity,alignment: Alignment.leading)
                    .frame(height:25)
                    .padding(.top,9)
                    .padding(.bottom, 25)
            }.frame(maxWidth: .infinity).padding(.horizontal, 24)
            .background(Color(dynamicBackgroundColor4))
            
            if containerEntity.suggestProductList.productList?.count > 1{
                ScrollView(.horizontal,showsIndicators: false) {
                    HStack(spacing: 24) {
                        Spacer().frame(width:1)
                        ForEach(containerEntity.suggestProductList.productList as? [ProductEntity] ?? [], id: \.productId) {p in
                            RoutedLink(to: RouterName.productDetail.withParam(p.productId)) {
                                // 1:手动编辑 2:自动获取 3:链接
                                // ==1 显示商品详情
                                LeftRightWidgetCell(product: p, isShowDetails: containerEntity.dataSourceMethod == 1).frame(width:itemWidth)
                            }
                        }
                    }
                }.frame(maxWidth: .infinity)
                .background(Color(dynamicBackgroundColor4))
            }else if containerEntity.suggestProductList.productList?.count == 1 {
                if let suggestProductEntity = containerEntity.suggestProductList.productList?.firstObject as? ProductEntity{
                    RoutedLink(to:RouterName.productDetail.withParam(suggestProductEntity.productId)) {
                        LeftRightWidgetCell(product: suggestProductEntity, isShowDetails: containerEntity.dataSourceMethod == 1).frame(width:itemWidth).padding(.leading, 24)
                    }
                }
            }else{
                ZStack{
                    Spacer()
                }.frame(maxWidth:.infinity).frame(height:130)
                .overlay(ActivityIndicator(), alignment: .center)
            }
            
            Spacer().frame(height: 24)
            
            if buttonType == DisplayButtonType.view_all.rawValue{
                Text(btnStr.localized).font(Font.medium(size: 16))
                    .foregroundColor(Color(dynamicTextColor4))
                    .frame(height: 44)
                    .frame(maxWidth: .infinity)
                    .background(Color(dynamicBtnBGColor5))
                    .cornerRadius(100)
                    .padding(.horizontal, 24).onTapGesture {
                        if let entity = containerEntity.detailEntityList?.first{
                            RoutableManager.showWidgetSectionList(title: containerEntity.containerTitle ?? "", detailEntity: entity)
                        }
                    }
                
            } else if buttonType == DisplayButtonType.exchange.rawValue{
                Button(action: {
                    currentPage = currentPage + 1
                    refreshSuggestProducts(containerEntity: containerEntity)
                    }) {
                        Text(btnStr.localized).font(Font.medium(size: 16))
                            .foregroundColor(Color(dynamicTextColor4))
                            .frame(height: 44)
                            .frame(maxWidth: .infinity)
                            .background(Color(dynamicBtnBGColor5))
                            .cornerRadius(100)
                            .padding(.horizontal, 24)
                    }
            }
            
        }.frame(maxWidth: .infinity)
        .background(Color(dynamicBackgroundColor4))
        .onAppear{
            if isFirstLoad {
                refreshSuggestProducts(containerEntity: containerEntity)
                //显示按钮 0:不展示 1:换一换 2:全部
                buttonType = containerEntity.detailEntityList?.first?.paramsMap[NewWidgetDetailEntity.companion.KEY_displayButtonType] as? String ?? ""
                switch buttonType {
                case DisplayButtonType.none.rawValue:
                    btnStr = ""
                case DisplayButtonType.exchange.rawValue:
                    btnStr = "换一换"
                case DisplayButtonType.view_all.rawValue:
                    btnStr = "查看全部"
                default:
                    btnStr = "查看全部"
                    buttonType = DisplayButtonType.view_all.rawValue
                }
            }
            isFirstLoad = false
        }
    }
    
    func refreshSuggestProducts(containerEntity : WidgetContainerCombineEntity) {
        let startT = CFAbsoluteTimeGetCurrent()
        let paramsStr = containerEntity.detailEntityList?.first?.paramsMap[NewWidgetDetailEntity.companion.KEY_requestParams] as? String ?? ""
        let api = containerEntity.detailEntityList?.first?.paramsMap[NewWidgetDetailEntity.companion.KEY_dataSource] as? String ?? ""
        WDBookStoreSDK.shared.getRecommendDataList(api: api, paramsStr: paramsStr, page: String(currentPage)) { result in
            switch result {
            case .success(let entity):
                debugPrint("性能测试：LeftRightWidget.refreshSuggestProducts -- WDBookAppSDK.shared.getRecommendDataList：\(CFAbsoluteTimeGetCurrent() - startT),  数据：\(entity?.productList?.count)")
                if entity?.productList == nil || entity?.productList?.count == 0{
                    if (currentPage != 1) {
                        currentPage = 1
                        refreshSuggestProducts(containerEntity: containerEntity)
                    }
                }else {
                    containerEntity.suggestProductList = entity!
                    self.appState.suggestProductListDic[containerEntity.containerId] = containerEntity.suggestProductList
                }

            case .failure(let error):
                Log.d(error)
            }

        }
    }
}

struct LeftRightWidgetCell: View {
    var product: ProductEntity
    @State var isShowDetails = false //显示详情或显示价格。
    @Environment(\.colorScheme) var colorScheme
    func getTextColorStr1() -> String{
        return colorScheme == .light ? "#B77D3A" : "#B58247"
    }
    var body: some View {
        HStack(spacing: 16) {
            ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(product.cover)))
                .resizable()
                .placeholder{
                    Image("cover_92*132")
                        .resizable()
                        .frame(width: 99, height: 130, alignment: .center)
                }
                .transition(.fade(duration: 0.5))
//                .scaledToFit()
                .scaledToFill()
                .frame(width: 99, height: 130, alignment: .center)
            
            if isShowDetails{
                VStack(alignment:.leading, spacing: 0) {
                    Text(product.title ?? "")
                        .font(Font.medium(size: 16))
                        .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .topLeading)
                        .frame(height:40).multilineTextAlignment(.leading)
                    Spacer().frame(height:8)
                    Text(product.authorNames)
                        .lineLimit(1)
                        .font(Font.regular(size: 12))
                        .foregroundColor(Color(dynamicTextColor22))
                        .frame(maxWidth: .infinity,alignment: .leading)
                        .frame(height:16)
                    Spacer().frame(height:8)
                    HTMLText(text:product.desc.trim().stripHtmlFont(color: getTextColorStr1()),attributedText:nil, fontSize: 12,isBold: false, limitLine: 3)
                        .foregroundColor(Color(dynamicTextColor18))
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .topLeading)
                        .frame(height:50)
                        .isHidden(product.desc.isBlank)
    //                Spacer()
                }.frame(height: 130)
            }else{
                VStack(alignment:.leading, spacing: 0) {
                    Spacer().frame(height:12)
                    Text(product.title ?? "")
                        .font(Font.medium(size: 16))
                        .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .topLeading)
                        .frame(height:40).multilineTextAlignment(.leading)
    
                    Spacer().frame(height:24)
                    VStack(alignment:.leading, spacing: 0) {
                        HStack {
                            if product.activitiesList?.count > 0{
                                Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(product.currencyText)\(product.activityAmount.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                    + Text(" (约￥%@)".localizedFormat(product.activityPriceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                            }else{
                                Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(product.currencyText)\(product.price.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                    + Text(" (约￥%@)".localizedFormat(product.priceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                            }
                        }.lineLimit(1)
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
    
                        Spacer().frame(height:2)
                        Group {
                            Text("原价：".localized) + Text(product.currencyText + String(product.originalPrice)).strikethrough()
                        }.lineLimit(1)
                    }
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,alignment: .leading)
                    .frame(height:35).opacity(isTestFlight1() ? 0 : 1)
                    Spacer().frame(height:12)
                }.frame(height: 130)
            }
        }
        .frame(height: 130)
        .background(Color(dynamicBackgroundColor4))
        .overlay(product.price > 0.0 && product.discount > 0.0 && product.discount < 1.0  ?
                 AnyView(Text("%@折".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.getDiscount * 10))!)).font(Font.regular(size: 12))
                    .foregroundColor(Color.white).padding(.horizontal, 4)
                    .background(Color(UIColor(hex: 0xE33733)))
                    .opacity(isTestFlight1() ? 0 : 1))
                 : AnyView(EmptyView()), alignment: .topLeading)
        .overlay(product.price > 0.0 ?
                 AnyView(EmptyView())
                 : AnyView(Text("免费".localized).frame(width: 38, height: 15).background(Color(UIColor(hex: 0xE33733)))
                    .foregroundColor(Color.white).font(Font.regular(size: 12))
                    .opacity(isTestFlight1() ? 0 : 1)), alignment: .topLeading)
    }
}

