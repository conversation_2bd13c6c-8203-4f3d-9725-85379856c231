//
//  BookSectionListV.swift
//  WDBook
//
//  Created by QK on 2022/2/14.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import NavigationRouter

struct BookSectionListV: View {
    @State var title:String
    @State var detailData:NewWidgetDetailEntity
    @State var needRefresh = true
    @State var productList:[ProductEntity] = [ProductEntity]()
    @State var isFirstLoad = true
    @State var page: Int = 1
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    var minCountOnFirstLoad = 10
    @State var noMore: Bool = false {
        didSet {
            if !noMore && productList.count < minCountOnFirstLoad {
                loadMore()
            }
        }
    }
    
    func refresh() {
        if !NetReachability.isReachability(){
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
        }
        
        let paramsStr = (detailData.paramsMap[NewWidgetDetailEntity.companion.KEY_requestParams] ?? "") as! String
        guard !paramsStr.isBlank else {
            isFirstLoad = false
            headerRefreshing = false
            return
        }
        WDBookStoreSDK.shared.getRecommendDataList(api: (detailData.paramsMap[NewWidgetDetailEntity.companion.KEY_dataSource] as? String ?? "").replacingOccurrences(of: "&page=1", with: "", options: .literal, range: nil), paramsStr: paramsStr , page: "\(1)") { result in
            switch result {
            case .success(let result):
                if let entity = result, let list = entity.productList as? [ProductEntity] {
                    page = 1
                    productList.removeAll()
                    productList.append(contentsOf: list)
                    let hasNext = entity.currentPage < entity.totalPage
                    self.noMore = !hasNext
//                    if hasNext && productList.count < minCountOnFirstLoad{
//                        loadMore()
//                    }
                }else{
                    self.noMore = true
                }
                
            case .failure(let error):
                Log.d(error)
            }
            self.isFirstLoad = false
            self.headerRefreshing = false
        }
    }
    
    func loadMore() {
//        guard headerRefreshing == false else {
//            return
//        }
        
        let paramsStr = (detailData.paramsMap[NewWidgetDetailEntity.companion.KEY_requestParams] ?? "")  as! String
        WDBookStoreSDK.shared.getRecommendDataList(api: (detailData.paramsMap[NewWidgetDetailEntity.companion.KEY_dataSource] as? String ?? "").replacingOccurrences(of: "&page=1", with: "", options: .literal, range: nil), paramsStr: paramsStr, page: "\(self.page + 1)") { result in
            switch result {
            case .success(let result):
                page = page + 1
                if let entity = result, let list = entity.productList as? [ProductEntity]{
                    productList.append(contentsOf: list)
                    let hasNext = entity.currentPage < entity.totalPage
                    self.noMore = !hasNext
//                    if hasNext && productList.count < minCountOnFirstLoad{
//                        loadMore()
//                    }
                }else{
                    self.noMore = true
                }
                
            case .failure(let error):
                Log.d(error)
            }
            self.isFirstLoad = false
            self.footerRefreshing = false
        }
    }
    
    var body: some View {
        BackNavigation(title: self.title){
            ScrollView{
                if self.productList.count > 0 {
                    WDRefreshHeader(refreshing: $headerRefreshing, action: {
                        self.refresh()
                    })
                    .padding(.bottom, 15)
                }
                LazyVStack(spacing:0){
                    Spacer().frame(height:12)
                    ForEach(productList, id:\.productId) { product in
                        RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                            ProductListCell(product:product)
                        }.buttonStyle(ClearHighlightStyle())
                        //解决 ios14.5 NavigationLink 自动返回bug
//                        NavigationLink(destination: EmptyView()) {
//                            EmptyView()
//                        }
                    }
                }
                if self.productList.count > 0 {
                    if !noMore {
                        if self.productList.count < minCountOnFirstLoad {
                            SimpleRefreshingView().padding()
                        } else {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore: $noMore) {
                                self.loadMore()
                            }
                        }
                    } else {
                        Spacer().frame(height: 12)
                    }
                }
            }
            .enableRefresh()
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor4))
            .navigationBarTitle(Text(self.title),displayMode: .inline)
            .overlay(Group {
                if self.isFirstLoad{
                    ActivityIndicator()
                }else if productList.count == 0 {
                    Text("未查到商品".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                } else {
                    EmptyView()
                }
            }).onAppear {
                if needRefresh{
                    refresh()
                }
                needRefresh = false
            }
            .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
                self.needRefresh = true
            })
        }
    }
}
