//
//  PromotionDetailsV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/6/7.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter
import shared

struct PromotionDetailsV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @State var activityId:Int64
    @State var activity:ProductActivityEntity = ProductActivityEntity()
    @State var products:[ProductDetailEntity] = []
    @State var isFirstLoad = true
    @State var needRefresh = true
    @State var isAllSelected:Bool = false
    @State var selectedCount:Int = 0
    @State var sumPrice:Double = 0.0
    @State var selectedProducts:[ProductDetailEntity] = []
    @State var paymentAmount: PaymentAmountEntity = PaymentAmountEntity()
    @State var isShowUnPaidAlert = false
    @State var unpaidModel:UnPaidModel = UnPaidModel()

    init(activityId:Int64) {
        _activityId = State(initialValue: activityId)
    }
    
    func resetSelectedCount(){
        selectedProducts = products.filter{$0.isSelected}
        selectedCount = products.reduce(0){$0 + ($1.isSelected ? 1 : 0)}
        //        sumPrice = products.reduce(0){$0 + ($1.isSelected ? Double($1.price) : 0.0)}
        getPayAmount()
        let canSelectCount = products.reduce(0){$0 + ($1.isPurchased ? 0 : 1)}
        if selectedCount >= canSelectCount{
            isAllSelected = true
        }else{
            isAllSelected = false
        }
    }
    
    func resetAllSelectedState(isSelected:Bool = false){
        for i in 0 ..< products.count{
            if !products[i].isPurchased {
                products[i].isSelected = isSelected
            }
        }
    }
    
    func clearState(){
        isAllSelected = false
        resetAllSelectedState(isSelected: isAllSelected)
        resetSelectedCount()
    }
    
    func refresh(isShowLoading:Bool = false) {
        if !NetReachability.isReachability(){
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
        }
        if isShowLoading {
            HUDManager.showLoadingBlockHUD()
        }
        
        WDBookUserSDK.shared.getActivityDetailData(activityId: activityId, completion: { result in
            if isShowLoading {
                HUDManager.hideLoadingHUD()
            }
            
            switch result {
            case .success(let r):
                if let entity = r{
                    self.activity = entity
                    self.products.removeAll()
                    self.products.append(contentsOf: entity.productList as? [ProductDetailEntity] ?? [])
                }
                self.isFirstLoad = false
                
                //清空状态
                isAllSelected = false
                selectedCount = 0
                sumPrice = 0
                selectedProducts = []
                getPayAmount(isShowLoading: isShowLoading)
            case .failure(let error):
                self.isFirstLoad = false
            }
        })
    }
    
    func getPayAmount(isShowLoading:Bool = true){
        if isShowLoading {HUDManager.showLoadingBlockHUD(text: "")}
        
        WDBookPaymentSDK.shared.getPayAmountEntity(productIds: selectedProducts.map{$0.productId},ignoreRepeatPurchased:true) { result in
            switch result{
            case .success(let e):
                if let entity = e{
                    paymentAmount = entity
                    sumPrice = Double(paymentAmount.actualAmount ?? 0.0)
                }
            case .failure(let error):
                switch error{
                case .error(let kotExp):
                    print(kotExp)
                    break
                }
                break
            }
            if isShowLoading {HUDManager.hideLoadingHUD()}
        }
    }

    func getPayAmountCheck(isShowLoading:Bool = true){
        if isShowLoading {HUDManager.showLoadingBlockHUD(text: "")}

        WDBookPaymentSDK.shared.getPayAmountEntity(productIds: selectedProducts.map{$0.productId},ignoreRepeatPurchased:false) { result in
            switch result{
            case .success(let e):
                if e != nil{
                    RoutableManager.push(ConfirmOrderV(selectedProducts: selectedProducts,successAction: {
                        refresh(isShowLoading: true)
                    }))
                }
                break
            case .failure(let error):
                switch error{
                case .error(let kotExp):
                    print(kotExp)
                    if let exp = kotExp as? ApiException,exp.code == ErrorInfo.unpaidorder.code,let data = exp.message?.data(using: .utf8){

                        let decoder = JSONDecoder()
                        decoder.dateDecodingStrategy = .iso8601
                        if let model = try? decoder.decode(UnPaidModel.self, from: data){
                            unpaidModel = model
                            isShowUnPaidAlert = true
                        }

                    }
                    break
                }
                break
            }
            if isShowLoading {HUDManager.hideLoadingHUD()}
        }
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }

    var body: some View{
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))

                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text(activity.activityTitle ?? "")
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height:45)

                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .overlay(Group {
            if isFirstLoad{
                ActivityIndicator()
            }else if products.count == 0 {
                Text("未查到商品".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
            } else {
                EmptyView()
            }
        })
        .overlay(
            Group {
                if isShowUnPaidAlert {
                    UnPaidOrderAlert(unpaidModel: unpaidModel, cancelHandler: {
                        HUDManager.showLoadingBlockHUD(text: "")
                        WDBookPaymentSDK.shared.cancelOrder(orderId: String(unpaidModel.orderId)) { result in
                            HUDManager.hideLoadingHUD()
                            isShowUnPaidAlert = false
                            switch result{
                            case .success(let r):
                                Toaster.showToast(message:"订单已取消")
                                break
                            case .failure(let e):
                                if let apiExp = e as? ApiException{
                                    Toaster.showToast(message: apiExp.message ?? "")
                                }
                            }
                        }
                    }, okHandler: {
                        isShowUnPaidAlert = false
                        RoutableManager.push(ConfirmOrderV(unPaidOrderId: unpaidModel.orderId,selectedProducts: selectedProducts,successAction: {
                            refresh(isShowLoading: true)
                        }))
                    })
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
    }

    var content: some View {
        VStack(spacing:0) {
            ScrollView {
                if activity.expired == 1{
                    Text("优惠价格活动已结束".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTextColor34))
                        .frame(maxWidth:.infinity,alignment:.leading)
                        .padding(EdgeInsets(top: 8, leading: 24, bottom: 8, trailing: 24))
                        .background(Color(dynamicBackgroundColor11))
                }else{
                    VStack(spacing:0) {
                        VStack(spacing:4){
                            Text(activity.activitySubTitle ?? "").font(Font.regular(size: 16).bold())
                                .foregroundColor(Color(dynamicTextColor30))
                                .frame(maxWidth:.infinity,alignment: .leading)
                                .multilineTextAlignment(.leading)
                            Text("\("活动时间：".localized)\((activity.startTime / 1000).mmdd) - \((activity.endTime / 1000).mmdd)")
                                .font(Font.regular())
                                .foregroundColor(Color(dynamicTextColor31))
                                .frame(maxWidth:.infinity,alignment: .leading)
                                .multilineTextAlignment(.leading)

                            if (activity.description_ ?? "").trim().count > 0 {
                                Text("活动介绍：".localized + "\(activity.description_ ?? "")")
                                    .font(Font.regular())
                                    .foregroundColor(Color(dynamicTextColor31))
                                    .frame(maxWidth:.infinity,alignment: .leading)
                                    .multilineTextAlignment(.leading)
                            }
                        }.frame(maxWidth:.infinity,alignment: .leading)
                            .multilineTextAlignment(.leading)
                            .padding(.horizontal,24)
                            .padding(.vertical,8)
                    }.frame(maxWidth: .infinity,maxHeight:.infinity)

                }

                LazyVStack(spacing:0) {
                    ForEach(products, id:\.productId) {  product in
                        if let index = products.firstIndex{$0.productId == product.productId}{
                            PromotionDetailsProductListCell(activityId:activity.activityId, product:$products[index]) {

                                guard NetReachability.isReachability() else{
                                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                                    return
                                }

                                guard WDBookSessionSDK.shared.isLogin else {
                                    clearState()
                                    AppState.shared.showLoginRegisterV()
                                    return
                                }

                                resetSelectedCount()
                            }
                        }

                    }
                }
            }
            HStack(spacing:0) {
                Button {
                    PromotionSelectedV.hide()

                    guard NetReachability.isReachability() else{
                        Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                        return
                    }

                    guard WDBookSessionSDK.shared.isLogin else {
                        clearState()
                        AppState.shared.showLoginRegisterV()
                        return
                    }

                    isAllSelected = !isAllSelected
                    resetAllSelectedState(isSelected: isAllSelected)
                    resetSelectedCount()
                } label: {
                    HStack(spacing:8) {
                        Image(isAllSelected ? "checkbox2_check" : "checkbox2_uncheck")
                        Text("全选".localized).font(Font.regular())
                            .foregroundColor(Color(dynamicTextColor19))
                    }.padding(.leading,16).padding(.trailing,8)
                        .frame(maxHeight:.infinity)
                }

                Spacer()
                VStack(alignment:.leading,spacing:4) {
                    Text("应付：".localized)
                        .foregroundColor(Color(dynamicTextColor19))
                    + Text("$\(sumPrice.fractionDigits2)")
                        .foregroundColor(Color(hex: 0xFF342A))
                    HStack(spacing:0) {
                        Text("已选 %d 件".localizedFormat(selectedCount))
                            .foregroundColor(Color(dynamicTextColor19))
                        Image("arrow_right_24")
                    }.onTapGesture {
                        if !PromotionSelectedV.isShowing(){
                            if selectedCount > 0{
                                PromotionSelectedV.show(products: $products,bottomPadding:80 + safeAreaInsets.bottom, on: UIApplication.shared.windows[0]
                                                        ,onDelete: {
                                    resetSelectedCount()
                                },onCancel: {

                                })
                            }
                        }else{
                            PromotionSelectedV.hide()
                        }
                    }
                }.font(Font.regular())
                    .frame(minWidth:100,alignment: .leading)
                    .padding(.trailing,16)
                Button {
                    PromotionSelectedV.hide()

                    guard NetReachability.isReachability() else{
                        Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                        return
                    }

                    guard WDBookSessionSDK.shared.isLogin else {
                        clearState()

                        AppState.shared.showLoginRegisterV()
                        return
                    }

                    getPayAmountCheck()

                } label: {
                    Text("立即结算").font(Font.regular())
                        .foregroundColor(.white)
                        .frame(width:120)
                        .frame(maxHeight:.infinity)
                        .background(selectedCount == 0 ? Color(mainUIColor.alpha(0.5)) : mainColor)
                }.disabled(selectedCount == 0)

            }.frame(maxWidth:.infinity).frame(height:80)
                .background(Color(dynamicTextColor27))
                .overlay(Color(dynamicSpanLineColor3).frame(height:0.5), alignment: .top)
        }.frame(maxWidth: .infinity,maxHeight:.infinity)
        //            .background(Color(dynamicBackgroundColor1))
            .background(Color(dynamicBackgroundColor4))
            .onReceive(NotificationCenter.default.publisher(for: AuthLoginV.loginSuccessNotification), perform: { (obj) in
                self.refresh(isShowLoading: true)
            })
        //                .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
        //                    self.needRefresh = true
        //                })
            .onAppear {
                if needRefresh{
                    refresh()
                }
                needRefresh = false
            }
    }
}

//wdbooktest://activity/51355720003585
struct PromotionDetailsProductListCell: View {
    @State var activityId:Int64
    @Binding var product: ProductDetailEntity
    var selectedAction:()->()
    
    var discount:Double{
        if product.hasActivity(){
            return product.activityDiscount
        }else{
            return Double(product.discount)
        }
    }
    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 12)
            RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                HStack(spacing: 0) {
                    ProductCoverView(purchased: Int(product.purchased), cover: product.cover)
                        .overlay((discount >= 1) ? AnyView(EmptyView())
                                 : AnyView(Text("%@折".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: discount * 10))!))
                                    .foregroundColor(.white).font(Font.regular(size: 12))
                                    .frame(height: 19).frame(minWidth:40)
                                    .background(Color(hex: 0xFF342A))
                                    .opacity(isTestFlight1() ? 0 : 1)), alignment: .topLeading)
                    Spacer().frame(width:16)
                    VStack(alignment:.leading, spacing: 8) {
                        Text(product.title )
                            .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                            .font(Font.regular(size: 16))
                            .foregroundColor(Color(dynamicTitleColor2))
                            .frame(maxWidth: .infinity,alignment: .leading)
                            .multilineTextAlignment(.leading)
                        Text(String(product.authorNames))
                            .lineLimit(1).fixedSize(horizontal: false, vertical: true)
                            .font(Font.regular(size: 12))
                            .foregroundColor(Color(dynamicTitleColor2))
                            .frame(maxWidth: .infinity,alignment: .leading)
                        
                        VStack(alignment:.leading, spacing: 0) {
                            if product.hasActivity(){
                                HStack {
                                    Text("活动价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(product.currencyText)\(product.activityAmount.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                    + Text(" (约￥%@)".localizedFormat(product.activityPriceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                    + Text(product.activityDiscount >= 1 || product.activityDiscount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.activityDiscount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                                }.lineLimit(1)
                                    .font(Font.regular(size: 12))
                                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                            }else{
                                HStack {
                                    Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(product.currencyText)\(product.price.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                    + Text(" (约￥%@)".localizedFormat(product.priceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                    + Text(product.discount >= 1 || product.discount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.discount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                                }.lineLimit(1)
                                    .font(Font.regular(size: 12))
                                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                            }
                            
                            Spacer().frame(height:2)
                            Group {
                                Text("原价：".localized) + Text(product.currencyText + String(product.originalPrice)).strikethrough()
                            }.lineLimit(1)
                        }
                        .font(Font.regular(size: 12))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,alignment: .leading)
                        .opacity(isTestFlight1() ? 0 : 1)
                    }
                }.frame(maxWidth:.infinity,maxHeight:.infinity, alignment:.leading)
                    .padding(.trailing,24)
                    .contentShape(Rectangle())
            }
            Spacer().frame(height: 12)
        }
        .frame(maxWidth: .infinity)
        .frame(height: 110 + 24)
        .padding(.leading,46)
        .background(Color(dynamicBackgroundColor4))
        .overlay(Image(product.isPurchased ? "checkbox2_disable" : ( product.isSelected ? "checkbox2_check" : "checkbox2_uncheck")).frame(maxHeight:.infinity).padding(.leading, 16).padding(.trailing, 10).onTapGesture(perform: {
            guard !product.isPurchased else {
                return
            }
            product.isSelected = !product.isSelected
            selectedAction()
        }), alignment: .leading)
    }
}

#if DEBUG
struct PromotionDetailsV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NavigationView{
                PromotionDetailsV(activityId: 45454087999489).environmentObject(AppState.shared)
                    .previewDevice("iPhone 13 Pro Max")
            }
            NavigationView{
                PromotionDetailsV(activityId: 45454087999489).environmentObject(AppState.shared)
                    .environment(\.colorScheme, .dark)
                    .previewDevice("iPhone SE (3nd generation)")
            }
        }
    }
}
#endif
