//
//  PromotionSelectedV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/6/8.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

class PromotionSelectedV: UIView,PromotionSelectedContentVDelegate {
    static var promotionSelectedV:PromotionSelectedV?
    @Binding var products:[ProductDetailEntity]
    var bottomPadding:CGFloat = 0.0
    var onDelete:(()->())?
    var onCancel:(()->())?

    var vc:UIHostingController<PromotionSelectedContentV>?

    init(products:Binding<[ProductDetailEntity]>,bottomPadding:CGFloat = 0.0,onDelete:(()->())? = nil,onCancel:(()->())? = nil) {
        _products = products
        self.bottomPadding = bottomPadding
        self.onDelete = onDelete
        self.onCancel = onCancel

        super.init(frame: CGRect.zero)
        backgroundColor = dynamicAlphaBackgroundColor1

        let btn = UIButton()
        btn.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
        addSubview(btn)
        btn.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        
        weak var weakself = self
        vc = UIHostingController(rootView: PromotionSelectedContentV(products: products, delegate:self,
        onDelete:{
            weakself?.onDelete?()
        },onCancel: {
            PromotionSelectedV.hide()
            weakself?.onCancel?()
        }))
        addSubview(vc!.view)
        vc!.view.layer.cornerRadius = 6
        vc!.view.snp.makeConstraints { make in
            make.width.equalTo(UIScreen.main.bounds.width)
            make.height.equalTo(14 + 69) //14 + 69*n
            make.bottom.equalToSuperview()
        }
    }

    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }

    static func show(products:Binding<[ProductDetailEntity]>,bottomPadding:CGFloat = 0.0,on superV:UIView,onDelete:(()->())? = nil,onCancel:(()->())? = nil) {
        hide()

        promotionSelectedV = PromotionSelectedV(products: products,bottomPadding:bottomPadding, onDelete:onDelete,onCancel: onCancel)
        superV.addSubview(promotionSelectedV!)
        promotionSelectedV!.snp.makeConstraints { (make) in
            make.top.equalToSuperview()
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.bottom.equalTo(-bottomPadding)
        }
    }

    static func hide(){
        promotionSelectedV?.removeFromSuperview()
        promotionSelectedV = nil
    }

    @objc func tapClose(){
        PromotionSelectedV.hide()
    }

    func promotionSelectedContentVOnSizeChanged(size: CGSize) {
        vc?.view.snp.updateConstraints { make in
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
        }
    }
    
    static func isShowing() -> Bool{
        return promotionSelectedV != nil
    }
}


protocol PromotionSelectedContentVDelegate{
    func promotionSelectedContentVOnSizeChanged(size:CGSize)
}
struct PromotionSelectedContentV: View {
    @Binding var products:[ProductDetailEntity]
    var delegate:PromotionSelectedContentVDelegate?
    var onDelete:(()->())?
    var onCancel:(()->())?
    @State var selectedProducts:[ProductDetailEntity]
    
    init(products:Binding<[ProductDetailEntity]>,delegate:PromotionSelectedContentVDelegate?,onDelete:(()->())? = nil,onCancel:(()->())? = nil) {
        _products = products
        self.delegate = delegate
        self.onDelete = onDelete
        self.onCancel = onCancel
        _selectedProducts = State(initialValue: _products.wrappedValue.filter{$0.isSelected})
    }
    
    func updateSelectedProducts(){
        selectedProducts = products.filter{$0.isSelected}
    }
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing:0) {
                ForEach(selectedProducts,id:\.productId) {product in
                        HStack {
                            ProductCoverView(purchased: Int(product.purchased), cover: product.cover,width: 40,height: 53).padding(.leading,16).padding(.trailing,10)
                            Text(product.title).foregroundColor(Color(dynamicTextColor19)).font(Font.regular(size: 16))
                                .lineLimit(2)
                                .frame(maxWidth:.infinity,alignment:.leading)
                                .multilineTextAlignment(.leading)
                            Spacer()
                            Text("\(product.currencyText)\(product.price.fractionDigits2)")
                                .font(Font.regular()).foregroundColor(Color(dynamicTextColor19))
                                .padding(.horizontal, 16)
                            Button {
                                if let i = products.firstIndex{$0.productId == product.productId}{
                                    products[i].isSelected = false
                                    updateSelectedProducts()
                                    onDelete?()
                                    if selectedProducts.count == 0{
                                        onCancel?()
                                    }
                                }
                            } label: {
                                Text("删除".localized).font(Font.regular()).foregroundColor(Color(hex: 0xFF342A))
                                    .frame(maxHeight:.infinity)
                                    .padding(.horizontal, 16)
                            }
                            
                        }.frame(height:69)
                    }
                
            }.ignoresSafeArea()
                .frame(maxWidth:.infinity)
                .padding(.top,14)
                .background(Color(dynamicTextColor27))
            .readSize(onChange: { size in
                if size.height > UIScreen.main.bounds.height * 0.7{
                    delegate?.promotionSelectedContentVOnSizeChanged(size: CGSize(width: size.width, height: UIScreen.main.bounds.height * 0.7))
                }else{
                    delegate?.promotionSelectedContentVOnSizeChanged(size: size)
                }
            })
        }.cornerRadius(radius: 8,corners: [.topLeft, .topRight])
    }
}

//#if DEBUG
//struct PromotionSelectedV_Previews: PreviewProvider {
//    @State static var products:[ProductDetailEntity] = {
//        var ps = test_promotionDetails.products
//        for i in 0..<5{
//            ps[i].isSelected = true
//        }
//        return ps
//    }()
//
//    static var previews: some View {
//        Group {
//            ZStack {
//                PromotionSelectedContentV(products:.constant(products), delegate: nil )
//            }.frame(maxWidth:.infinity,maxHeight: .infinity)
//                .background(Color(dynamicAlphaBackgroundColor1))
//
//            ZStack {
//                PromotionSelectedContentV(products:.constant(products), delegate: nil )
//            }.frame(maxWidth:.infinity,maxHeight: .infinity)
//                .background(Color(dynamicAlphaBackgroundColor1))
//                .environment(\.colorScheme, .dark)
//                .previewDevice("iPhone SE (2nd generation)")
//        }
//    }
//}
//#endif
