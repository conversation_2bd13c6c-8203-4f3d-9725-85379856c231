//
//  ProductListV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/2.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import NavigationRouter

struct ProductListV: View {
    var categoryId:Int64
    var title:String = ""
    @State var pageIndex:Int = 1
    
    @State var productsResult:ProductPageListEntity = ProductPageListEntity()
    @State var products:[ProductEntity] = []
    
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    @State var noMore: Bool = false
    @State var needRefresh = true
    @State var isFirstLoad = true
    
    init(categoryId: Int64,
         title:String,
         productsResult:ProductPageListEntity? = nil,
         isPresent:Binding<Bool>? = nil) {
        self.categoryId = categoryId
        self.title = title
        if productsResult != nil{
            _productsResult = State(initialValue: productsResult!)
        }
    }
    
    func refresh() {
        if !NetReachability.isReachability(){
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
        }
        
        pageIndex = 1
        WDBookStoreSDK.shared.getBookProductListByCategory(categoryId: self.categoryId, page: pageIndex) { (result) in
            switch result {
            case .success(let entity):
                if let pageListEntity = entity,let productList = pageListEntity.productList as? [ProductEntity], productList.count > 0 {
                    self.productsResult = pageListEntity
                    self.products.removeAll()
                    self.products.append(contentsOf: productList)
                    self.noMore = !self.productsResult.hasNext
                } else {
                    self.noMore = true
                }
                self.headerRefreshing = false
                self.isFirstLoad = false
            case .failure(let error):
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
                }
                self.isFirstLoad = false
            }
        }
    }
    
    func loadMore() {
        if self.noMore {
            self.footerRefreshing = false
            return
        }
        pageIndex += 1
        WDBookStoreSDK.shared.getBookProductListByCategory(categoryId: self.categoryId, page: pageIndex) { (result) in
            self.footerRefreshing = false
            switch result {
            case .success(let pageListEntity):
                if pageListEntity != nil {
                    self.productsResult = pageListEntity!
                    self.products.append(contentsOf: self.productsResult.productList as? [ProductEntity] ?? [])
                    self.noMore = !self.productsResult.hasNext
                }
            case .failure(let error):
                self.footerRefreshing = false
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
                }
            }
        }
    }
    
    var body: some View {
        BackNavigation(title: self.title){
            VStack {
                ScrollView{
                    //TODO:产品数量等于0也应能刷新。UIKit? 添加空元素，“无数据”元素
                    if self.products.count > 0 {
                        WDRefreshHeader(refreshing: $headerRefreshing, action: {
                            self.refresh()
                        })
                    }
                    
                    LazyVStack(spacing:0) {
                        Spacer().frame(height:24)
                        ForEach(self.products, id:\.productId) {  product in
                            RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                                ProductListCell(product:product)
                            }
                        }
                    }
                    
                    
                    if self.products.count > 0{
                        if !noMore {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                                self.loadMore()
                            }
                        } else {
                            Spacer().frame(height:12)
                        }
                    }
                }
                .enableRefresh()
                .frame(maxWidth: .infinity,maxHeight:.infinity)
                .background(Color(dynamicBackgroundColor4))
                
                .overlay(Group {
                    if self.isFirstLoad{
                        ActivityIndicator()
                    }else if self.products.count == 0 {
                        Text("未查到商品".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                    } else {
                        EmptyView()
                    }
                }).onAppear {
                    if needRefresh{
                        self.refresh()
                    }
                    needRefresh = false
                }
                .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
                    self.needRefresh = true
                })
            }
        }
    }
}

struct ProductListCell: View {
    var product: ProductEntity
    
    var body: some View {
        VStack(spacing: 0) {
            Spacer().frame(height: 12)
            HStack(spacing: 0) {
                CoverImageView(product: product)
                Spacer().frame(width:16)
                VStack(alignment:.leading, spacing: 8) {
                    Text(product.title ?? "")
                        .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                        .font(Font.regular(size: 16))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,alignment: .leading)
                        .multilineTextAlignment(.leading)
                    Text(String(product.authorNames))
                        .lineLimit(1).fixedSize(horizontal: false, vertical: true)
                        .font(Font.regular(size: 12))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,alignment: .leading)

                    VStack(alignment:.leading, spacing: 0) {
                        if product.hasActivity(){
                            HStack {
                                Text("活动价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                + Text("\(product.currencyText)\(product.activityAmount.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                + Text(" (约￥%@)".localizedFormat(product.activityPriceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                + Text(product.activityDiscount >= 1 || product.activityDiscount == 0 ? "": " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.activityDiscount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                            }.lineLimit(1)
                                .font(Font.regular(size: 12))
                                .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                        }else{
                            HStack {
                                Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                + Text("\(product.currencyText)\(product.price.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                + Text(" (约￥%@)".localizedFormat(product.priceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                + Text(product.discount >= 1 || product.discount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.discount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                            }.lineLimit(1)
                                .font(Font.regular(size: 12))
                                .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                        }
                        
                        Spacer().frame(height:2)
                        Group {
                            Text("原价：".localized) + Text(product.currencyText + String(product.originalPrice)).strikethrough()
                        }.lineLimit(1)
                    }
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,alignment: .leading).opacity(isTestFlight1() ? 0 : 1)
                }
            }
            Spacer().frame(height: 12)
        }
        .frame(maxWidth: .infinity)
        .padding(.horizontal,24)
        .padding(.top,0)
        .frame(height: 110 + 24)
        .background(Color(dynamicBackgroundColor4))
    }
}

//#if DEBUG
//struct ProductListV_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            NavigationView{
//                ProductListV(categoryId: 1,
//                             title:"注释书",
//                             productsResult:test_ProductList)
//                    .environmentObject(AppState.shared.initTestData())
//            }
//
//            NavigationView{
//                ProductListV(categoryId: 1,
//                             title:"注释书",
//                             productsResult:test_ProductList)
//                    .previewDevice("iPhone SE (2nd generation)")
//                    .environmentObject(AppState.shared.initTestData()).environment(\.colorScheme, .dark)
//            }
//
//        }
//    }
//}
//#endif
