//
//  UpDownWidget.swift
//  WDBook
//
//  Created by QK on 2021/12/31.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import shared
import DeviceKit
import NavigationRouter

struct UpDownWidget: View {
    @EnvironmentObject var appState: AppState
    @State var containerEntity: WidgetContainerCombineEntity
    @State var btnStr:String = ""
    @State var currentPage:Int = 1
    @State var buttonType:String = DisplayButtonType.view_all.rawValue
    @State var isFirstLoad = true
    
    var body: some View {
        VStack(alignment: .leading, spacing: 1) {
            HStack(alignment: .center, spacing: 4) {
                Text("%@".localizedFormat(self.containerEntity.containerTitle ?? ""))
                    .font(Font.medium(size: 18))
                    .foregroundColor(Color(titleColor2))
                    .frame(maxWidth: .infinity,alignment: Alignment.leading)
                    .frame(height:25)
                    .padding(.top,9)
                    .padding(.bottom, 25)
            }.frame(maxWidth: .infinity).padding(.horizontal, 24)
            .background(Color(dynamicBackgroundColor4))
            
            if containerEntity.suggestProductList.productList?.count > 1 {
                ScrollView(.horizontal,showsIndicators: false) {
                    HStack(spacing: 16) {
                        Spacer().frame(width:abs(24 - 16))
                        ForEach(containerEntity.suggestProductList.productList as? [ProductEntity] ?? [], id: \.productId) {p in
                            RoutedLink(to:RouterName.productDetail.withParam(p.productId)) {
                                UpDownWidgetCell(product: p, isShowDetails: containerEntity.dataSourceMethod == 1)
                            }
                        }
                    }
                }.frame(maxWidth: .infinity)
                    .background(Color(dynamicBackgroundColor4))
                
            }else if containerEntity.suggestProductList.productList?.count == 1 {
                if let suggestProductEntity = containerEntity.suggestProductList.productList?.firstObject as? ProductEntity{
                    RoutedLink(to:RouterName.productDetail.withParam(suggestProductEntity.productId)) {
                        UpDownWidgetCell(product: suggestProductEntity, isShowDetails: containerEntity.dataSourceMethod == 1)
                            .padding(.leading, 24)
                    }
                }
            }else{
                ZStack{
                    Spacer()
                }.frame(maxWidth:.infinity).frame(height:200)
                .overlay(ActivityIndicator(), alignment: .center)
            }
            
            Spacer().frame(height: 24)
            if buttonType == DisplayButtonType.view_all.rawValue{
                Text(btnStr.localized).font(Font.medium(size: 16))
                    .foregroundColor(Color(dynamicTextColor4))
                    .frame(height: 44)
                    .frame(maxWidth: .infinity)
                    .background(Color(dynamicBtnBGColor5))
                    .cornerRadius(100)
                    .padding(.horizontal, 24).onTapGesture {
                        if let entity = containerEntity.detailEntityList?.first{
                            debugPrint(containerEntity.containerId)
                            RoutableManager.showWidgetSectionList(title: containerEntity.containerTitle ?? "", detailEntity: entity)
                        }
                    }
                
            } else if buttonType == DisplayButtonType.exchange.rawValue{
                Button(action: {
                    currentPage = currentPage + 1
                    refreshSuggestProducts(containerEntity: containerEntity)
                    }) {
                        Text(btnStr.localized).font(Font.medium(size: 16))
                            .foregroundColor(Color(dynamicTextColor4))
                            .frame(height: 44)
                            .frame(maxWidth: .infinity)
                            .background(Color(dynamicBtnBGColor5))
                            .cornerRadius(100)
                            .padding(.horizontal, 24)
                    }
            }
            
        }.frame(maxWidth: .infinity)
            .background(Color(dynamicBackgroundColor4))
            .onAppear{
                if isFirstLoad {
                    refreshSuggestProducts(containerEntity: containerEntity)
                    //显示按钮 0:不展示 1:换一换 2:全部
                    buttonType = containerEntity.detailEntityList?.first?.paramsMap[NewWidgetDetailEntity.companion.KEY_displayButtonType] as? String ?? ""
                    switch buttonType {
                    case DisplayButtonType.none.rawValue:
                        btnStr = ""
                    case DisplayButtonType.exchange.rawValue:
                        btnStr = "换一换"
                    case DisplayButtonType.view_all.rawValue:
                        btnStr = "查看全部"
                    default:
                        btnStr = "查看全部"
                        buttonType = DisplayButtonType.view_all.rawValue
                    }
                }
                isFirstLoad = false
            }
    }
    
    func refreshSuggestProducts(containerEntity : WidgetContainerCombineEntity) {
        let startT = CFAbsoluteTimeGetCurrent()
        let paramsStr = containerEntity.detailEntityList?.first?.paramsMap[NewWidgetDetailEntity.companion.KEY_requestParams] as? String ?? ""
        let api = containerEntity.detailEntityList?.first?.paramsMap[NewWidgetDetailEntity.companion.KEY_dataSource] as? String ?? ""
        WDBookStoreSDK.shared.getRecommendDataList(api: api, paramsStr: paramsStr, page: String(currentPage)) { result in
            switch result {
            case .success(let entity):
                debugPrint("性能测试：UuDownWidget.refreshSuggestProducts -- WDBookAppSDK.shared.getRecommendDataList：\(CFAbsoluteTimeGetCurrent() - startT),  数据：\(entity?.productList?.count)")
                if entity?.productList == nil || entity?.productList?.count == 0{
                    if (currentPage != 1) {
                        currentPage = 1
                        refreshSuggestProducts(containerEntity: containerEntity)
                    }
                }else {
                    containerEntity.suggestProductList = entity!
                    self.appState.suggestProductListDic[containerEntity.containerId] = containerEntity.suggestProductList
                }
            case .failure(let error):
                Log.d(error)
            }
        }
    }
}

enum DisplayButtonType: String {
    case none = "0"
    case exchange = "1"
    case view_all = "2"
}

struct UpDownWidgetCell: View {
    var product: ProductEntity
    @State var isShowDetails = false //显示详情或显示价格。
    
    var body: some View {
        VStack(spacing: 0) {
            ScrollView{
                ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(product.cover)))
                    .resizable()
                    .placeholder{
                        Image("cover_92*132")
                            .resizable()
                            .frame(width: 98, height: 137, alignment: .center)
                    }
                    .transition(.fade(duration: 0.5))
                    .scaledToFill()
                    .frame(width: 99, height: 132, alignment: .center)
                
                Spacer().frame(height:8)
                Text(product.title ?? "")
                    .font(Font.medium(size: 12))
                    .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                    .foregroundColor(Color(dynamicTextColor18))
                    .frame(maxWidth:.infinity,alignment: .topLeading)
                    .frame(minHeight:15,maxHeight:30,alignment: .topLeading)
                    .multilineTextAlignment(.leading)
                
                Spacer().frame(height:8)
                Text(product.authorNames)
                    .lineLimit(1)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTextColor22))
                    .frame(maxWidth: .infinity,alignment: .leading)
                    .frame(height:18)
            }.disabled(true)
            
        }.overlay(product.price > 0.0 && product.discount > 0.0 && product.discount < 1.0  ?
                  AnyView(Text("%@折".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.getDiscount * 10))!)).font(Font.regular(size: 12))
                    .foregroundColor(Color.white).padding(.horizontal, 4)
                    .background(Color(UIColor(hex: 0xE33733)))
                    .opacity(isTestFlight1() ? 0 : 1))
                  : AnyView(EmptyView()), alignment: .topLeading)
        .overlay(product.price > 0.0 ? AnyView(EmptyView())
                 : AnyView(Text("免费".localized).frame(width: 38, height: 15)
                    .font(Font.regular(size: 12))
                    .background(Color(UIColor(hex: 0xE33733)))
                    .foregroundColor(Color.white)
                    .opacity(isTestFlight1() ? 0 : 1)), alignment: .topLeading)
        .frame(width:99,height: 200)
        .background(Color(dynamicBackgroundColor4))
    }
}
