//
//  PublisherDetailsPageV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/9/12.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//


import SwiftUI
import Combine
import shared
import DeviceKit
import UIKit
import NavigationRouter

struct PublisherDetailsPageV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @State var publisherId:Int64

    @State var publisherEntity:PublisherDetailsEntity = PublisherDetailsEntity()
    @State var sorts:[SortType] = [.createTime_desc,.saleCount_desc,.price_asc]
    @State var selectedSortIndex = 0

    @State var languages:[Language] = [.all,.zh_Hans,.zh_Hant]
    @State var selectedLanaugeIndex = 0
    @State var preSelectedLanguageIndex = 0
    @State var isExpandLanguage = false{
        didSet{
            if isExpandLanguage{ //展开时候保存临时选择索引
                preSelectedLanguageIndex = selectedLanaugeIndex
            }else{ //关闭时候
                needRefresh = preSelectedLanguageIndex != selectedLanaugeIndex
                if needRefresh{
                    debugPrint("需要刷新")
                }else{
                    debugPrint("不需要刷新")
                }
            }
        }
    }
    @State var needRefresh:Bool = true

    @State var publisherDescHeight:CGFloat = 10{
        didSet{
            isExpandLanguage = false
        }
    }
    @State var headerHeight:CGFloat = 50 + 24
    @State var scrollViewOffsetY:CGFloat = 0{
        didSet{
            isExpandLanguage = false
        }
    }

    static let resetPositionNotification = Notification.Name("PublisherDetailsPageV_resetPositionNotification")
    @Namespace var topID
    @Namespace var pageContentID

    //产品与刷新
    @State var totalCount:Int = 0 //所有语言的书籍个数
    @State var pageIndex:Int = 1
    @State var productsResult:ProductPageListEntity = ProductPageListEntity()
    @State var products:[ProductEntity] = []
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    @State var noMore: Bool = false
    @State var isFirstLoad = true
    @State var isErrorState = false
    @State var isLoading = false

    let fontSize = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                               Device.iPodTouch7,
                                                               Device.simulator(.iPodTouch7),
                                                               Device.simulator(.iPhoneSE)]) ? 12 : 14)
    init(publisherId: Int64 = 0,
         publisherDetailsEntity:PublisherDetailsEntity? = nil,
         productsResult:ProductPageListEntity? = nil,
         isPresent:Binding<Bool>? = nil) {
        _publisherId = State(initialValue: publisherId)
        if let entity = publisherDetailsEntity{
            publisherEntity.copy(entity: entity)
        }
        if productsResult != nil{
            _productsResult = State(initialValue: productsResult!)
        }
    }
    
    func refreshPublisher(){
        
        WDBookStoreSDK.shared.getPublisherDetails(publisherId: publisherId) { result in
            switch result {
            case .success(let r):
                if let entity = r{
                    publisherEntity.copy(entity: entity)
                }
                Log.d(r)
            case .failure(let error):
                Log.i(error)
            }
        }
    }

    func refreshCount(){
        guard NetReachability.isReachability() else{
            return
        }
        
        pageIndex = 1
        let params = ProductFilterEntity()
        params.publisherId = publisherId
        params.page = Int32(pageIndex)
        params.pageSize = 1
        params.orderField = sorts[selectedSortIndex].searchParam

        WDBookStoreSDK.shared.getBookProductFilter(paramsMap: params.transferToMap()) { result in
            switch result {
            case .success(let pageListEntity):
                if let productList = pageListEntity?.productList{
                    self.totalCount = Int(pageListEntity!.total)
                }
            case .failure(let error):
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
                }
            }
        }
    }
    
    func refresh(isShowLoading:Bool = true,isCanShowErrorState:Bool = true) {

        guard NetReachability.isReachability() else{
            if isCanShowErrorState { isErrorState = true }
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }

        refreshPublisher()
        refreshCount()
        
        pageIndex = 1
        let params = ProductFilterEntity()
        params.publisherId = publisherId
        params.page = Int32(pageIndex)
        params.language = languages[selectedLanaugeIndex].searchParam
        params.orderField = sorts[selectedSortIndex].searchParam

        if isShowLoading { isLoading = true }
        WDBookStoreSDK.shared.getBookProductFilter(paramsMap: params.transferToMap()) { result in
            switch result {
            case .success(let pageListEntity):
                if let productList = pageListEntity?.productList{
                    self.productsResult = pageListEntity!
                    self.products.removeAll()
                    self.products.append(contentsOf: productList as? [ProductEntity] ?? [])
                    self.noMore = !self.productsResult.hasNext
                } else {
                    self.noMore = true
                }
                isErrorState = false
            case .failure(let error):
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
//                    switch error{
//                    case .error(let kotExp):
//                        print(kotExp)
//                        if let exp = kotExp as? KotlinException{
//                            debugPrint("exp")
//                        }
//                        break
//                    }
                }
                if isCanShowErrorState { isErrorState = true }
            }

            headerRefreshing = false
            isFirstLoad = false
            isLoading = false
//            if isShowLoading { isLoading = false }
        }
    }

    func loadMore() {
        guard NetReachability.isReachability() else{
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }

        if self.noMore {
            self.footerRefreshing = false
            return
        }
        pageIndex += 1

        let params = ProductFilterEntity()
        params.publisherId = publisherId
        params.page = Int32(pageIndex)
        params.language = languages[selectedLanaugeIndex].searchParam
        params.orderField = sorts[selectedSortIndex].searchParam

        WDBookStoreSDK.shared.getBookProductFilter(paramsMap: params.transferToMap()) { result in
            self.footerRefreshing = false
            switch result {
            case .success(let pageListEntity):
                if pageListEntity != nil {
                    self.productsResult = pageListEntity!
                    self.products.append(contentsOf: self.productsResult.productList as? [ProductEntity] ?? [])
                    self.noMore = !self.productsResult.hasNext
                }
            case .failure(let error):
                self.footerRefreshing = false
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
                }
            }
        }
    }

    var body : some View{
        ZStack(alignment: .top, content: {
            mainContent
                .navigationBarHidden(true)
                .padding(.top, (45 + UIDevice.safeArea3InsetsTop))
            
            HStack(alignment: .center, spacing: 20, content: {
                Button(action: {
                    presentationMode.wrappedValue.dismiss()
                }) {
                    Image("back_ui")
                        .aspectRatio(contentMode: .fit)
                        .frame(width: 24, height: 24, alignment: .center)
                        .foregroundColor(Color(btnTintColor))
                        .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                }

                Spacer()

                Text(publisherEntity.name)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18))
                Spacer()

                RoutedLink(toRoute:.search) {
                    Image("icon_search").frame(width: 24, height: 24,alignment: .center).padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16)).opacity(0)
                }

            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.top, UIDevice.safeArea3InsetsTop)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        }).frame(maxWidth:.infinity,maxHeight: .infinity)
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }

    var header:some View{
        VStack(spacing:0) {
            //类别筛选
            VStack(spacing:0){
                HStack {
                    EditorPictureView(picture: publisherEntity.picture)
                    VStack(alignment: .leading,spacing: 8) {
                        Text(publisherEntity.name).font(Font.medium(size: 18)).foregroundColor(Color(dynamicTitleColor11))
                        Text("%lld个作品".localizedFormat(totalCount)).font(Font.regular(size: 14)).foregroundColor(Color(hex:0x707070))
                    }
                }.frame(maxWidth:.infinity,alignment:.leading)
                .padding(EdgeInsets(top: 24, leading: 24, bottom: 0, trailing: 24))
                .onTapGesture {
                    isExpandLanguage = false
                }
                if !publisherEntity.description0.isEmpty{
                    HTMLTRemoTagsToTextSummary(htmlContent: publisherEntity.description0)
                    .padding(EdgeInsets(top: 16, leading: 24, bottom: 24, trailing: 24))
                }else{
                    Spacer().frame(height:24)
                }
                
            }.frame(maxWidth:.infinity)
            Color(dynamicBackgroundColor16).frame(maxWidth:.infinity).frame(height:8)
            //排序与语言
            HStack {
                if !isTestFlight1(){
                    HStack(spacing: 15) {
                        ForEach(0..<sorts.count) { index in
                            Button {
                                isExpandLanguage = false
                                selectedSortIndex = index
                                refresh()
                                NotificationCenter.default.post(name: PublisherDetailsPageV.resetPositionNotification, object: nil)
                            } label: {
                                Text(sorts[index].desc)
                                    .font(Font.regular(size: fontSize))
                                    .foregroundColor(index == selectedSortIndex ? Color(UIColor(hex: 0xFF8A00)):Color(dynamicTitleColor4))
                                    .frame(height:25)
                            }
                        }
                    }
                    Spacer()
                }

                Button {
                    NotificationCenter.default.post(name: PublisherDetailsPageV.resetPositionNotification, object: nil)
                    DispatchQueue.main.asyncAfter(deadline: .now() + 0.3, execute: {
                        isExpandLanguage = !isExpandLanguage
                    })
                } label: {
                    HStack(spacing:0) {
                        Text(languages[selectedLanaugeIndex].desc).font(Font.regular(size: fontSize))
                            .foregroundColor(Color(dynamicTitleColor4))
                            .frame(height:17)
                            .padding(EdgeInsets(top: 5, leading: 15, bottom: 4, trailing: 4))

                        Image(isExpandLanguage ? "arrow_up_16":"arrow_down_16").frame(width: 16, height: 16).padding(.trailing, 11)

                    }.background(Color(dynamicBackgroundColor11))
                        .cornerRadius(radius: 100,corners: isExpandLanguage ? [.topLeft, .topRight] : [.topLeft,.topRight,.bottomLeft,.bottomRight])
                }
                if isTestFlight1(){
                    Spacer()
                }
            }.frame(height:58)
            .frame(maxWidth:.infinity)
            .padding(.horizontal,24)
            .background(Color(dynamicSpanLineColor10).frame(maxWidth:.infinity).frame(height:0.5),alignment: .bottom)
            .background(isExpandLanguage ? AnyView(Color(dynamicBackgroundColor11).frame(height: (58 - 25)/2)) : AnyView(Color.clear), alignment: .bottom)
        }
    }

    var languageSelectV: some View{
        VStack(spacing:0) {
            VStack {
                ForEach(0 ..< languages.count, id: \.self) { index in
                    Button {
                        isExpandLanguage = false
                        selectedLanaugeIndex = index
                        refresh()
                        NotificationCenter.default.post(name: PublisherDetailsPageV.resetPositionNotification, object: nil)
                    } label: {
                        HStack(spacing:6) {
                            Image("icon_right").frame(width: 16, height: 16).opacity(selectedLanaugeIndex == index ? 1:0)
                            Text(languages[index].desc).font(Font.regular(size: fontSize)).foregroundColor(Color(dynamicTextColor23))
                            Spacer()
                        }.frame(height:33).padding(.horizontal, 16)
                    }
                }
            }.padding(.vertical, 8).background(Color(dynamicBackgroundColor11))
            Color(dynamicBackgroundColor13).onTapGesture {
                isExpandLanguage = false
            }
        }.edgesIgnoringSafeArea(.bottom)
    }

    var mainContent: some View {
        ZStack(alignment:.top) {
            ScrollViewReader { readerProxy in
                ScrollView(showsIndicators: false) {

                    WDRefreshHeader(refreshing: $headerRefreshing, action: {
                        refresh(isShowLoading:false, isCanShowErrorState: false)
                    }).padding(.bottom, 15)
                        .id(topID)

                    //为什么这个高度恒是20
                    GeometryReader { topProxy in
                        let offset = topProxy.frame(in: .named("scroll")).minY
                        Color(UIColor.black.alpha(0.01)).frame(height:0.01).preference(key: ViewOffsetKey.self, value: offset)//.id(topID)
                    }

                    header.coordinateSpace(name: "header")
                        .background(
                        GeometryReader { headerProxy in
                            let headerHeight = headerProxy.size.height
                            Color.clear.preference(key: ViewFrameHeightKey.self, value: headerHeight) //headerHeight是筛选上层的高度，headerHeight - 50 - 23是作者介绍文本的高度
                        }
                    ).padding(.top, -15)

                    LazyVStack(spacing:0){
                        ForEach(self.products, id:\.productId) {  product in
                            RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                                ProductListCell(product:product)
                            }
                        }
                    }

                    if self.isFirstLoad || isLoading{
                        HStack(spacing: 10) {
                            ActivityIndicator()
                            Text("加载中...".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                        }.padding(.top, (UIScreen.main.bounds.height - 22)/2 - (45 + UIDevice.safeArea3InsetsTop) - headerHeight)
                    }else if isErrorState{
                        Text("数据加载失败，请重试".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2)).padding(.top, (UIScreen.main.bounds.height - 22)/2 - (45 + UIDevice.safeArea3InsetsTop) - headerHeight)
                    }else if self.products.count == 0 {
                        Text("暂时没有相关书籍".localized).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2)).padding(.top, (UIScreen.main.bounds.height - 22)/2 - (45 + UIDevice.safeArea3InsetsTop) - headerHeight)
                    } else {

                    }

                    if self.products.count > 0{
                        if !noMore {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                                DispatchQueue.main.async{
                                    self.loadMore()
                                }
                            }
                        } else {
                            Spacer().frame(height:12)
                        }
                    }
                }
                .enableRefresh()
                .coordinateSpace(name: "scroll")
                .background(Color(dynamicBackgroundColor4))
                .onPreferenceChange(ViewOffsetKey.self) { value in
                    DispatchQueue.main.async{
                        scrollViewOffsetY = value
                    }
                }
                .onPreferenceChange(ViewFrameHeightKey.self) { value in
                    DispatchQueue.main.async{
                        headerHeight = value
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for:PublisherDetailsPageV.resetPositionNotification )) { noti in
                    withAnimation {
                        readerProxy.scrollTo(topID)
                    }
                }
            }

            if isExpandLanguage{
                languageSelectV.padding(.top, headerHeight)
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
            self.needRefresh = true
        })
        .onAppear {
            if needRefresh{
                self.refresh()
            }
            needRefresh = false
        }
    }
}

#if DEBUG
struct PublisherDetailsPageV_Previews: PreviewProvider {
    
    static var testMode:PublisherDetailsEntity{
        let jsonString = loadJsonString(from: test_json_publisher_details)
        let entity = PublisherDetailsEntity().testModels(jsonString: jsonString)
        return entity
    }
    
    static var previews: some View {
        
        PublisherDetailsPageV(publisherId: 0,publisherDetailsEntity: testMode).environmentObject(AppState.shared)
    }
}
#endif
