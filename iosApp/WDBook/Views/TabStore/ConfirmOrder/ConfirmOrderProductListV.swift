//
//  ConfirmOrderProductListV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/2/28.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import shared

class ConfirmOrderProductListV: UIView,ConfirmOrderProductListVDelegate {
    static var productsV:ConfirmOrderProductListV?
    var products:[AccountedProductsEntity]
    var bottomPadding:CGFloat = 0.0
    var onDelete:(()->())?
    var onCancel:(()->())?
    
    var vc:UIHostingController<ConfirmOrderProductListContentV>?
    
    init(products:[AccountedProductsEntity],bottomPadding:CGFloat = 0.0,onDelete:(()->())? = nil,onCancel:(()->())? = nil) {
        self.products = products
        self.bottomPadding = bottomPadding
        self.onDelete = onDelete
        self.onCancel = onCancel
        
        super.init(frame: CGRect.zero)
        backgroundColor = dynamicAlphaBackgroundColor1
        
        let btn = UIButton()
        btn.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
        addSubview(btn)
        btn.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
        
        weak var weakself = self
        vc = UIHostingController(rootView: ConfirmOrderProductListContentV(products: products, delegate:self,
                                                                           onCancel: {
            ConfirmOrderProductListV.hide()
            weakself?.onCancel?()
        }))
        addSubview(vc!.view)
        vc!.view.layer.cornerRadius = 6
        vc!.view.snp.makeConstraints { make in
            make.width.equalTo(UIScreen.main.bounds.width)
            make.height.equalTo(14 + 69) //14 + 69*n
            make.bottom.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    static func show(products:[AccountedProductsEntity],bottomPadding:CGFloat = 0.0,on superV:UIView,onCancel:(()->())? = nil) {
        hide()
        
        productsV = ConfirmOrderProductListV(products: products,bottomPadding:bottomPadding,onCancel: onCancel)
        superV.addSubview(productsV!)
        productsV!.snp.makeConstraints { (make) in
            make.top.equalToSuperview()
            make.leading.equalToSuperview()
            make.trailing.equalToSuperview()
            make.bottom.equalTo(-bottomPadding)
        }
    }
    
    static func hide(){
        productsV?.removeFromSuperview()
        productsV = nil
    }
    
    @objc func tapClose(){
        ConfirmOrderProductListV.hide()
    }
    
    func confirmOrderProductListVOnSizeChanged(size: CGSize) {
        vc?.view.snp.updateConstraints { make in
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
        }
    }
    
    static func isShowing() -> Bool{
        return productsV != nil
    }
}


protocol ConfirmOrderProductListVDelegate{
    func confirmOrderProductListVOnSizeChanged(size:CGSize)
}
struct ConfirmOrderProductListContentV: View {
    @State var products:[AccountedProductsEntity]
    var delegate:ConfirmOrderProductListVDelegate?
    var onCancel:(()->())?
    
    
    @State var scrollHeight:CGFloat = UIScreen.main.bounds.height * 0.5
    init(products:[AccountedProductsEntity],delegate:ConfirmOrderProductListVDelegate?,onCancel:(()->())? = nil) {
        _products = State(initialValue: products)
        self.delegate = delegate
        self.onCancel = onCancel
    }
    
    var body: some View {
        LazyVStack(spacing:0) {
            HStack(spacing:0) {
                Text("商品清单").font(Font.regular(size:16)).foregroundColor(Color(dynamicTextColor18)).padding(.horizontal,16)
                Spacer()
                Image("close_24").frame(width: 24,height: 24).padding(8).onTapGesture {
                    onCancel?()
                }
            }.frame(maxWidth:.infinity).frame(height:50)
            
            ScrollView {
                VStack {
                    ForEach(products,id:\.productId) {product in
                        HStack(spacing:12) {
                            ProductCoverView(purchased: 0, cover: product.cover,width: 56,height: 74)
                            
                            VStack {
                                Text(product.title).font(Font.regular(size: 16)).lineLimit(2).multilineTextAlignment(.leading).frame(maxWidth:.infinity,alignment: .leading).foregroundColor(Color(dynamicTitleColor8))
                                Spacer().frame(minHeight: 5,idealHeight: 5)
                                Text("\(product.currencyText)\((product.price + product.activityAmount + product.couponAmount).fractionDigits2)").frame(maxWidth:.infinity,alignment: .leading).foregroundColor(Color(UIColor(hex: 0xFF342A)))
                                Spacer().frame(minHeight: 0,idealHeight: 5)
                            }.frame(maxWidth:.infinity,maxHeight:.infinity,alignment:.leading)
                        }.frame(maxWidth:.infinity).frame(height:74)
                            .padding(.vertical,8).padding(.horizontal,24).background(Color(dynamicTextColor27))
                    }
                }.frame(maxWidth:.infinity)
                .readSize(onChange: { size in
                    var scrollHeight = size.height
                    if scrollHeight > UIScreen.main.bounds.height * 0.7{
                        scrollHeight = UIScreen.main.bounds.height * 0.7
                    }
                    self.scrollHeight = scrollHeight
                    
                    delegate?.confirmOrderProductListVOnSizeChanged(size: CGSize(width: size.width, height: scrollHeight + 50))
                })
            }.frame(maxWidth:.infinity).frame(height:scrollHeight)
            
        }.ignoresSafeArea()
            .frame(maxWidth:.infinity)
            .background(Color(dynamicTextColor27))
            .cornerRadius(radius: 8,corners: [.topLeft, .topRight])
    }
}
