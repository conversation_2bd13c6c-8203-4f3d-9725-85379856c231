//
//  ConfirmOrderSelectedCouponV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/14.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

class CouponsSelectedVM: ObservableObject {
    @Published var data: [Coupon] = []
    
    init(_ data:[Coupon]? = nil) {
        if let list = data{
            self.data = list
        }
    }
}

struct ConfirmOrderSelectedCouponV: View {
    @ObservedObject var vm = CouponsSelectedVM()
    @Binding var isPresent:Bool
    var willDismissAction:((Int64)->())?
    
    var body: some View {
        ZStack(alignment:.bottom) {
            Button(action: {
                self.isPresent = false
            }) {
                Text("").frame(maxWidth: .infinity,maxHeight: .infinity)
            }.background(Color(dynamicAlphaBackgroundColor1))
            
            VStack(spacing:0) {
                Text("可用优惠券".localized).foregroundColor(Color(dynamicTitleColor7))
                    .font(Font.regular(size: 18))
                    .frame(height:64)
                if CGFloat(vm.data.count) * (95 + 16) - 16 + 108 + 64.0 > UIScreen.main.bounds.height * 0.7 {
                    ScrollView {
                        VStack(spacing:16) {
                            ForEach(vm.data,id:\.couponId) { model in
                                CouponsCellV(coupon: model)
                            }
                        }
                    }.frame(maxWidth:.infinity)
                    .frame(height:UIScreen.main.bounds.height * 0.7)
                }else{
                    VStack(spacing:16) {
                        ForEach(vm.data,id:\.couponId) { model in
                            CouponsCellV(canSelected:true,coupon: model,selectedAction: { couponId in
                                for i in vm.data.indices{
                                    if vm.data[i].couponId == couponId{
                                        vm.data[i].isSelected.toggle()
                                    }else{
                                        vm.data[i].isSelected = false
                                    }
                                }
                            })
                        }
                    }
                }
                Button(action: {
                    if let couponId = vm.data.filter{$0.isSelected}.first?.couponId{
                        willDismissAction?(couponId)
                    }else{
                        willDismissAction?(-1)
                    }
                    isPresent = false
                }) {
                    Text("确定".localized)
                    .font(Font.medium(size: 16)).frame(height:16)
                    .foregroundColor(Color.white)
                    .frame(minWidth: 0, maxWidth: .infinity)
                }
                .frame(height: 44)
                .background(Color(UIColor.primaryColor1))
                .cornerRadius(22)
                .padding(EdgeInsets(top: 32, leading: 24, bottom: 32, trailing: 24))
                
            }.frame(maxWidth:.infinity)
            .background(Color(dynamicBackgroundColor12))
            .cornerRadius(radius: 6,corners: [.topLeft, .topRight])
            
        }.frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
    }
}

#if DEBUG
struct ConfirmOrderSelectedCouponV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            
            ConfirmOrderSelectedCouponV(vm: CouponsSelectedVM(test_coupons.filter{$0.status == .unused}), isPresent: .constant(true))
                .previewDevice("iPhone 13 Pro Max")
            
            ConfirmOrderSelectedCouponV(vm: CouponsSelectedVM(test_coupons.filter{$0.status == .unused}), isPresent: .constant(true))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
        }
    }
}
#endif
