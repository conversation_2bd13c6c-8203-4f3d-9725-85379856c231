//
//  ConfirmOrderV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/6/13.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct ConfirmOrderV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @State var unPaidOrderId:Int64 = 0
    @State var paymentAmount: PaymentAmountEntity = PaymentAmountEntity()
    @State var selectedPurchasedProducts:[ProductDetailEntity] = [ProductDetailEntity]()
    @State var isPresentPurchasedProducts = false
    @State var isPresentSelectedCoupons = false

    @State var presentCoupons:[Coupon] = [Coupon]()
    @State var isFirstLoad = true
    @State var isShowChargeV = false
    @State var isShowRechargeAlert = false
    @State var isShowPaymentResultV = false
    @State var paymentResultVType:PaymentResultVType = .paymentFailedRetry
    
    //构造参数
    @State var selectedProducts:[ProductDetailEntity] = []
    @State var selectedCouponIds:[Int64] = []
//    @State var selectedActivityIds:[Int64] = []
    @State var trialCalType = 0  //默认为0。只要用户操作了优惠券，就改成1
    var successAction:()->()
    
    func refresh(isShowLoading:Bool = false,couponIds:[Int64]){
        if isShowLoading {HUDManager.showLoadingBlockHUD()}
        
        if unPaidOrderId > 0{
            WDBookPaymentSDK.shared.getPayAmountEntityWithOrderId(orderId: unPaidOrderId, completion: { result in
                switch result{
                case .success(let e):
                    if let entity = e{
                        paymentAmount = entity
                        selectedCouponIds = (paymentAmount.couponList as? [CouponEntity] ?? []).filter{$0.selected == 1}.map{$0.couponId}
    //                    selectedActivityIds = (paymentAmount.activityEntityList as? [ActivityEntity] ?? []).map{$0.activityId}
                    }
                case .failure(let error):
                    break
                }
                isFirstLoad = false
                if isShowLoading {HUDManager.hideLoadingHUD()}
            })
        }else{
            WDBookPaymentSDK.shared.getPayAmountEntity(productIds: selectedProducts.map{$0.productId}, couponIds: couponIds, activityIds: [], trialCalType: trialCalType, ignoreRepeatPurchased: false) { result in
                switch result{
                case .success(let e):
                    if let entity = e{
                        paymentAmount = entity
                        selectedCouponIds = (paymentAmount.couponList as? [CouponEntity] ?? []).filter{$0.selected == 1}.map{$0.couponId}
    //                    selectedActivityIds = (paymentAmount.activityEntityList as? [ActivityEntity] ?? []).map{$0.activityId}
                    }
                case .failure(let error):
                    break
                }
                isFirstLoad = false
                if isShowLoading {HUDManager.hideLoadingHUD()}
            }
        }
        
    }
    
    //购买 或 余额不足，请充值
    func amountButtonAction(){
        
        if AppState.shared.walletBalance >= Double(self.paymentAmount.actualAmount) {
            
            guard NetReachability.isReachability() else{
                AppState.shared.alert.showNetworkErrorAlert()
                return
            }
            
            //购买
            if unPaidOrderId > 0 {
                HUDManager.showLoadingBlockHUD()
                WDBookPaymentSDK.shared.orderPaymentiOS(orderId: unPaidOrderId, completion: { result in
                    HUDManager.hideLoadingHUD()
                    switch result {
                    case .success(let entity):
                        self.paymentResultVType = .paymentSuccess
                        self.isShowPaymentResultV = true

                        WDBookDataSyncManager.shared.syncWhenBuyNewBook()

                        for p in selectedProducts{
                            AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_PAY_SUCCESS, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_ORDER_ID, String(unPaidOrderId)])
                            if p.subProductList.count > 0 {
                                AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_PACKAGE_PAY_SUCCESS, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_ORDER_ID, String(unPaidOrderId)])
                            }
                        }
                        
                    case .failure(let error):
                        Log.d(error)
                        self.isShowPaymentResultV = true

    //                    TODO: kmm错误未抛出。
    //                    if e.errno == 406{
    //                        .priceChanged
    //                    }else if e.errno == 405{
    //                        .balanceNotEnough
    //                    }else{
    //                        //其他错误，重试。TODO：区别网络错误，超时等。
    //                        .paymentFailed
    //                    }

                    }
                })
            } else {
                HUDManager.showLoadingBlockHUD()
                WDBookPaymentSDK.shared.buyFromWallet(productIds: selectedProducts.map{$0.productId},
                                                      couponIds: selectedCouponIds,
                                                      activityIds: [],
                                                      amount: paymentAmount.actualAmount,
                                                      couponAmount: paymentAmount.couponAmount,
                                                      activityAmount: paymentAmount.activityAmount,
                                                      trialCalType: trialCalType) { result in
                    HUDManager.hideLoadingHUD()
                    switch result {
                    case .success(let entity):
                        if entity != nil{
                            self.paymentResultVType = .paymentSuccess
                            self.isShowPaymentResultV = true

                            WDBookDataSyncManager.shared.syncWhenBuyNewBook()

                            for p in selectedProducts{
                                AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_PAY_SUCCESS, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_ORDER_ID, String(entity?.orderId ?? 0)])
                                if p.subProductList.count > 0 {
                                    AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_PACKAGE_PAY_SUCCESS, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_ORDER_ID, String(entity?.orderId ?? 0)])
                                }
                            }
                        }else{
                            guard NetReachability.isReachability() else{
                                AppState.shared.alert.showNetworkErrorAlert()
                                return
                            }
                        }
                        
                    case .failure(let error):
                        Log.d(error)
                        self.isShowPaymentResultV = true

    //                    TODO: kmm错误未抛出。
    //                    if e.errno == 406{
    //                        .priceChanged
    //                    }else if e.errno == 405{
    //                        .balanceNotEnough
    //                    }else{
    //                        //其他错误，重试。TODO：区别网络错误，超时等。
    //                        .paymentFailed
    //                    }

                    }
                }
            }
            
        } else {
            //充值
            if !isTestFlight1() {
                self.isShowChargeV = true
            }
        }
    }
    
    func paymentResultBtnAction(){
        if [.paymentSuccess,.priceChanged].contains(paymentResultVType){
            //刷新
            isShowPaymentResultV = false
//            refresh(isShowLoading: true)
            
            //退出UI，并刷新上一个UI。
            //                    self.productDetails.purchased = 1
            //TODO: 书籍标记为已购，刷新书籍。
            successAction()
            presentationMode.wrappedValue.dismiss()
        }else if paymentResultVType == .paymentFailedRetry{
            //立刻购买
            amountButtonAction()
        }
        //不刷新
    }

    var productDetailsV: some View{
        VStack(spacing:0){
            Text("商品信息".localized)
                .font(Font.regular(size: 16).bold())
                .foregroundColor(Color(dynamicTextColor26))
                .frame(maxWidth:.infinity,alignment: .leading)
                .frame(height:50)
                .padding(.horizontal,16)
                .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 16), alignment: .bottom)

            ForEach(paymentAmount.accountedProducts as? [AccountedProductsEntity] ?? [],id:\.productId) { product in
                HStack(spacing:0) {
                    VStack(spacing:6) {
                        Text(product.title).lineLimit(1)
                            .foregroundColor(Color(dynamicTitleColor8))
                            .frame(maxWidth:.infinity,alignment:.leading)
//                        if product.allPurchasedSubProductsPrice > 0{
//                            HStack(spacing:0) {
//                                Button {
//                                    selectedPurchasedProducts = (product.subProductList as! [AccountedProductsEntity]).filter{$0.isPurchased}
//                                    isPresentPurchasedProducts = true
//                                } label: {
//                                    HStack(spacing:0) {
//                                        Text("已购商品：-\(product.currencyText)\(product.allPurchasedSubProductsPrice.fractionDigits2)").lineLimit(1).foregroundColor(Color(hex: 0xBCBCBC))
//                                        Image("arrow_right_24")
//                                    }.contentShape(Rectangle())
//                                }
//
//                            }.frame(maxWidth:.infinity,alignment:.leading)
//
//                        }
                    }
                    .frame(maxWidth:.infinity,alignment:.leading)
                    .padding(.trailing,24)
                    Spacer()
                    Text("\(product.currencyText)\(product.price.fractionDigits2)")
                        .font(Font.regular())
                        .foregroundColor(Color(dynamicTextColor19))
                }.font(Font.regular())
                    .frame(maxWidth:.infinity,alignment:.leading)
                    .frame(height:52)
//                    .frame(height:product.allPurchasedSubProductsPrice > 0 ? 102 : 52)
                    .padding(.leading,24)
                    .padding(.trailing,32)

            }
        }.background(Color(dynamicBackgroundColor1))
    }
    
    var productDetailsHorizontalV: some View{
        Group{
            if (paymentAmount.accountedProducts as? [AccountedProductsEntity] ?? []).count == 1 {
                HStack(spacing:0){
                    ForEach((paymentAmount.accountedProducts as? [AccountedProductsEntity] ?? []).prefix(1),id:\.productId) { product in
                        HStack(spacing:12) {
                            ProductCoverView(purchased: 0, cover: product.cover,width: 56,height: 74)
                            
                            VStack {
                                Text(product.title).font(Font.regular(size: 16)).lineLimit(2).multilineTextAlignment(.leading).frame(maxWidth:.infinity,alignment: .leading).foregroundColor(Color(dynamicTitleColor8))
                                Spacer().frame(minHeight: 5,idealHeight: 5)
                                Text("\(product.currencyText)\((product.price + product.activityAmount + product.couponAmount).fractionDigits2)").frame(maxWidth:.infinity,alignment: .leading).foregroundColor(Color(dynamicTitleColor8))
                                Spacer().frame(minHeight: 0,idealHeight: 5)
                            }.frame(maxWidth:.infinity,maxHeight:.infinity,alignment:.leading)
                        }.frame(maxWidth:.infinity).frame(height:74).background(Color(dynamicBackgroundColor1))
                    }
                }.frame(maxWidth:.infinity).frame(height:106).padding(.horizontal,24).background(Color(dynamicBackgroundColor1))
                
            }else{
                HStack(spacing:0){
                    HStack(spacing:12){
                        ForEach((paymentAmount.accountedProducts as? [AccountedProductsEntity] ?? []).prefix(4),id:\.productId) { product in
                            ProductCoverView(purchased: 0, cover: product.cover,width: 56,height: 74)
                        }
                    }.frame(maxWidth:.infinity,maxHeight: .infinity,alignment: .leading).padding(.leading,24)
                    
                    Button {
                        if !ConfirmOrderProductListV.isShowing(){
                            ConfirmOrderProductListV.show(products: (paymentAmount.accountedProducts as? [AccountedProductsEntity] ?? []),bottomPadding:0 + safeAreaInsets.bottom, on: UIApplication.shared.windows[0],onCancel: {
                                    
                                })
                        }else{
                            ConfirmOrderProductListV.hide()
                        }
                    } label: {
                        HStack(spacing:0) {
                            Text("共\((paymentAmount.accountedProducts as? [AccountedProductsEntity] ?? []).count)件").font(Font.regular()).foregroundColor(Color(dynamicTitleColor8))
                            Image("arrow_right_20").frame(width: 20,height: 20)
                        }
                    }.frame(width:90).frame(maxHeight:.infinity)

                }.frame(maxWidth:.infinity).frame(height:106).background(Color(dynamicBackgroundColor1))
            }
        }
        
    }

    var priceDetailsV: some View{
        VStack(spacing:0){
                HStack(spacing:0) {
                    Text("商品总额".localized).lineLimit(1)
                        .font(Font.regular(size:16))
                        .foregroundColor(Color(dynamicTitleColor8))
                        .frame(maxWidth:.infinity,alignment:.leading)
                        .padding(.trailing,24)
                    Spacer()
                    Text("\(paymentAmount.currencyText)\((paymentAmount.originalPrice - paymentAmount.activityAmount).fractionDigits2)")
                        .font(Font.regular())
                        .foregroundColor(Color(dynamicTitleColor8))
                }.font(Font.regular())
                    .frame(maxWidth:.infinity,alignment:.leading)
                    .frame(height:46)
                    .padding(.leading,24).padding(.trailing,32)
            
                if paymentAmount.purchasedProductList?.count > 0{
                    HStack(spacing:0) {
                        Text("已购商品".localized).lineLimit(1)
                            .font(Font.regular(size:16))
                            .foregroundColor(Color(dynamicTitleColor8))
                            .frame(maxWidth:.infinity,alignment:.leading)
                            .padding(.trailing,24)
                        Spacer()
                        
                        NavigationLink(
                            destination: PaymentAmountPurchasedProductsV(purchasedProducts: (paymentAmount.purchasedProductList as? [ProductDetailEntity] ?? [])).navigationBarHidden(true),
                            label: {
                                HStack(spacing:0) {
                                    Text("-\(paymentAmount.currencyText)\(paymentAmount.purchasedAmount.fractionDigits2)")
                                        .font(Font.regular())
                                        .foregroundColor(Color(dynamicTitleColor8))
                                    Image("arrow_right_20").frame(width: 20, height: 20)
                                }.frame(maxHeight:.infinity)
                            })
                    }.font(Font.regular())
                        .frame(maxWidth:.infinity,alignment:.leading)
                        .frame(height:50)
                        .padding(.leading,24).padding(.trailing,12)
                }
                
            if paymentAmount.couponList?.count > 0{
                HStack(spacing:0) {
                    Text("优惠券".localized).lineLimit(1)
                        .font(Font.regular(size:16))
                        .foregroundColor(Color(dynamicTitleColor8))
                        .frame(maxWidth:.infinity,alignment:.leading)
                        .padding(.trailing,24)
                    Spacer()
                    Button {
                        presentCoupons = (paymentAmount.couponList as? [CouponEntity] ?? []).map{Coupon(entity: $0)}
                        isPresentSelectedCoupons = true
                    } label: {
                        HStack(spacing:4) {
                            Text("-\(paymentAmount.currencyText)\(paymentAmount.couponAmount.fractionDigits2)")
                                .font(Font.regular())
                                .foregroundColor(Color(dynamicTitleColor8))
                            Image("arrow_right_20").frame(width: 20, height: 20)
                        }.frame(maxHeight:.infinity)
                    }.disabled(unPaidOrderId > 0)
                }.font(Font.regular())
                    .frame(maxWidth:.infinity,alignment:.leading)
                    .frame(height:46)
                    .padding(.leading,24).padding(.trailing,12)
            }
            
            HStack(spacing:0) {
                Text("应付：".localized).lineLimit(1)
                    .font(Font.regular(size:16))
                    .foregroundColor(Color(dynamicTitleColor8))
                Text("\(paymentAmount.currencyText)\(paymentAmount.actualAmount.fractionDigits2)")
                    .font(Font.medium(size: 16))
                    .foregroundColor(Color(hex: 0xFF342A))
            }.frame(maxWidth:.infinity,alignment:.trailing)
            .frame(height:46)
            .padding(.horizontal,32)
//            .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 16), alignment: .top)
            
        }.background(Color(dynamicBackgroundColor1))
    }
    
    var balanceV: some View{
        HStack(spacing:0) {
            Text("账户余额".localized)
                .font(Font.regular(size:16))
                .lineLimit(1)
                .foregroundColor(Color(dynamicTitleColor8))
                .frame(maxWidth:.infinity,alignment:.leading)
                .padding(.trailing,24)
            Spacer()
            Text(AppState.shared.walletBalanceStr)
                .font(Font.regular())
                .foregroundColor(Color(dynamicTextColor19))
        }.font(Font.regular())
            .frame(maxWidth:.infinity,alignment:.leading)
            .frame(height:46)
            .padding(.horizontal,24)
            .background(Color(dynamicBackgroundColor1))
    }
    
    @State var angle: Double = 0.0
    var foreverAnimation: Animation {
        Animation.linear(duration: 2.0)
            .repeatForever(autoreverses: false)
    }
    
    var body: some View {
        BackNavigation(title: "确认订单".localized){
            VStack(spacing:0) {
                if isFirstLoad{
                    VStack {
                        Spacer()
                        HStack(spacing:10) {
                            Image("activity_indicator_17").resizable().frame(width: 17, height:17, alignment: .center).aspectRatio(contentMode: .fit)
                                .rotationEffect(Angle(degrees: self.angle))
                                .onAppear {
                                    withAnimation(self.foreverAnimation) {
                                        self.angle += 360.0
                                    }
                                }
                            
                            Text("正在计算...".localized)
                                .foregroundColor(Color(dynamicTitleColor2))
                                .font(Font.regular(size: 16))
                                .padding(EdgeInsets(top: 4, leading: 0, bottom: 4, trailing: 8))
                                
                        }
                        Spacer()
                        Spacer()
                    }.frame(maxWidth:.infinity,maxHeight: .infinity)
                }else{
                    ScrollView {
                        VStack(spacing:8){
//                            productDetailsV
                            productDetailsHorizontalV

                            priceDetailsV
                            
                            balanceV

                            VStack(spacing:0){
                                Text("说明".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor8))
                                    .frame(height:52).frame(maxWidth:.infinity,alignment: .leading).padding(.horizontal,16)

                                Group{
                                    Text("order_description1".localized).font(Font.regular())
                                    + Text("order_description2".localized).font(Font.medium())
                                    + Text("order_description3".localized).font(Font.regular())
                                }.foregroundColor(Color(dynamicTitleColor8))
                                    .frame(maxWidth:.infinity,alignment: .leading).multilineTextAlignment(.leading).padding(.horizontal,16)
                                    .padding(.bottom,25)
                            }.background(Color(dynamicBackgroundColor1))
                            Spacer().frame(height:1)

                        }.frame(maxWidth:.infinity,maxHeight:.infinity).background(Color(dynamicBackgroundColor3))
                    }.frame(maxWidth:.infinity,maxHeight:.infinity).background(Color(dynamicBackgroundColor3))
                    
                        if !isTestFlight1(){ //正式和测试
                            HStack(spacing:0) {
                                HStack(alignment:.center, spacing:0) {
                                    Text("应付：".localized).lineLimit(1)
                                        .font(Font.regular(size:16))
                                        .foregroundColor(Color(dynamicTitleColor8))
                                    Text(paymentAmount.currencyText)
                                        .font(Font.medium(size: 16))
                                        .foregroundColor(Color(hex: 0xFF342A))
                                    Text(paymentAmount.actualAmount.fractionDigits2)
                                        .font(Font.medium(size: 24))
                                        .foregroundColor(Color(hex: 0xFF342A))
                                }.padding(.leading,24)
                                Spacer()
                                Button(action: {
                                    amountButtonAction()
                                }) {
                                    Text(AppState.shared.walletBalance >= Double(paymentAmount.actualAmount) ? "确认购买".localized:amountNotSufficient())
                                    .font(Font.medium(size: 16)).frame(height:16)
                                    .foregroundColor(Color.white)
                                    .frame(width:145,height: 40)
                                    .background(Color(UIColor.primaryColor1))
                                    .cornerRadius(20)
                                    
                                }
                                .padding(.trailing,24)
                            }.frame(maxWidth:.infinity).frame(height:40 + 10 + 10).background(Color(dynamicBackgroundColor1))

    //        //            } else if appState.walletBalance < Double(paymentAmount.actualAmount) {
                        } else { //testflight
                            Text("TestFlight 版本不支持购买，请前往网站购买。点击“复制链接”按钮，打开浏览器粘贴前往即可购买。".localized)
                                .foregroundColor(Color(UIColor(hex:0xC13013)))
                                .font(Font.regular(size: 12))
                                .frame(minWidth: 0, maxWidth: .infinity)
                                .padding(EdgeInsets(top: 24, leading: 24, bottom: 24, trailing: 24))
                                .background(Color(dynamicBackgroundColor1))

                            HStack(alignment: .center, spacing: 15){
                                Button(action: {
                                    if let product = (paymentAmount.accountedProducts as? [AccountedProductsEntity])?.first{
                                        UIPasteboard.general.string = "https://wdbook.com/dp/\(product.productId)"
                                        Toaster.showToast(message: "已复制".localized)
                                    }
                                }) {
                                    //横向顶满
                                    Text("复制链接".localized)
                                            .font(Font.medium(size: 14))
                                            .foregroundColor(Color.white)
                                            .frame(maxWidth: .infinity)
                                            .frame(height: 40, alignment: .center)
                                            .background(Color(UIColor.primaryColor1))
                                            .cornerRadius(22)
                                }

                                Button(action: {
    //                                self.isPresent = false
                                    self.presentationMode.wrappedValue.dismiss()
                                }) {
                                    ZStack(alignment: .center){
                                        Capsule().strokeBorder(Color(dynamicTextColor25), lineWidth: 1)
                                        Text("取消".localized)
                                            .font(Font.regular(size: 14))
                                            .frame(maxWidth: .infinity)
                                            .frame(height: 40, alignment: .center)
                                            .foregroundColor(Color(dynamicTextColor25))
                                    }
                                }
                            }.frame(height:40).padding(.horizontal,24).padding(.bottom,24)
                            .background(Color(dynamicBackgroundColor1))
                        }
                }
                
            }
            .background(Color(dynamicBackgroundColor3))
            .overlay(isPresentPurchasedProducts ? AnyView(ConfirmOrderPurchasedProductsV(isPresent: $isPresentPurchasedProducts, purchasedProducts: selectedPurchasedProducts)) : AnyView(EmptyView()))
            .overlay(isPresentSelectedCoupons ? AnyView(ConfirmOrderSelectedCouponV(vm: CouponsSelectedVM(presentCoupons), isPresent: $isPresentSelectedCoupons ,willDismissAction: { couponId in
                trialCalType = 1
                if couponId == -1 {
                    refresh(isShowLoading:true, couponIds: [])
                }else{
                    if !selectedCouponIds.contains(couponId){
                        refresh(isShowLoading:true, couponIds: [couponId])
                    }
                }
            })) : AnyView(EmptyView()))
            .overlay(isShowChargeV ? AnyView(TopupView(isPresent: $isShowChargeV)): AnyView(EmptyView()))
            .overlay(isShowPaymentResultV ? AnyView(PaymentResultV(type: paymentResultVType, isPresent: $isShowPaymentResultV,action: {
                self.paymentResultBtnAction()
            }, feedbackAction: {
                RoutableManager.navigate(toPath: RouterName.feedbackCreate.rawValue + "?entrance=2")
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1, execute: {
                    isShowPaymentResultV = false
                })
            })) : AnyView(EmptyView()))
            .onAppear {
                AppState.shared.refreshWalletBalance()
                if isFirstLoad{
                    refresh(couponIds: selectedCouponIds)
                }
            }
        }.onReceive(NotificationCenter.default.publisher(for: InAppPurchaseManager.rechargeFailNotification), perform: { _ in
            isShowRechargeAlert = true
        }).overlay(
            Group {
                if isShowRechargeAlert {
                    RechargeAlert(okHandler: {
                        isShowRechargeAlert = false
                    })
                    
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
    }
    
    func amountNotSufficient() -> String {
        var resultStr = "充值并购买".localized
        if isTestFlight1(){
            resultStr = "余额不足".localized
        }
        return resultStr
    }
}

#if DEBUG
struct ConfirmOrderV_Previews: PreviewProvider {
    static var previews: some View {
        ConfirmOrderV(successAction: {
            
        }).environmentObject(AppState.shared)
            .previewDevice("iPhone 13 Pro Max")

        ConfirmOrderV(successAction: {
            
        }).environmentObject(AppState.shared)
            .environment(\.colorScheme, .dark)
            .previewDevice("iPhone SE (3nd generation)")

    }
}
#endif
