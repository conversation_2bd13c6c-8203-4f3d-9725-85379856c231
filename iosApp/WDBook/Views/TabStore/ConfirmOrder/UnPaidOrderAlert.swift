//
//  UnPaidOrderAlert.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/5/26.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

class UnPaidModel:Decodable, ObservableObject{
    var orderId:Int64 = 0
    var title:String = ""
    var productId:Int64 = 0
    var expiredTime:Int64 = 0
    var errMsg:String = ""
    
    @Published var textAttributedStr:NSAttributedString = NSAttributedString()
    
    private var countdownTimer: Timer?

    enum CodingKeys: String, CodingKey {
           case orderId
           case title
           case productId
           case expiredTime
           case errMsg
       }

    init(){}
    
    required init(from decoder: Decoder) throws {
        let container = try decoder.container(keyedBy: CodingKeys.self)
        self.orderId = try container.decode(Int64.self, forKey: .orderId)
        self.title = try container.decode(String.self, forKey: .title)
        self.productId = try container.decode(Int64.self, forKey: .productId)
        self.expiredTime = try container.decode(Int64.self, forKey: .expiredTime)
        self.errMsg = try container.decode(String.self, forKey: .errMsg)

        updateTextAttributedStr()
        startCountdown()
    }
    
    deinit {
        stopCountdown()
    }

    private func startCountdown() {
        countdownTimer = Timer.scheduledTimer(withTimeInterval: 1, repeats: true) { [weak self] (_) in
            DispatchQueue.main.async {
                self?.expiredTime -= 1
                self?.updateTextAttributedStr()
            }
        }
        countdownTimer?.fire()
    }
    
    func stopCountdown() {
        countdownTimer?.invalidate()
        countdownTimer = nil
    }

    private func updateTextAttributedStr(){
        let attributedString = NSMutableAttributedString(string: "您在以往订单中购买过《%@》，请点击[继续支付]完成该订单，该订单将于 %@ 后自动关闭。".localizedFormat(title,expiredTime.ssTommss))
        let range1 = (attributedString.string as NSString).range(of: "[继续支付]".localized)
        let range2 = (attributedString.string as NSString).range(of: expiredTime.ssTommss)

        
        attributedString.addAttribute(.font, value: UIFont.regular(size: 16), range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.font, value: UIFont.semibold(size: 16), range: range1)
        attributedString.addAttribute(.foregroundColor, value: dynamicTitleColor10, range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.foregroundColor, value: UIColor(hex:0xFF342A), range: range2)
        

        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        paragraphStyle.lineSpacing = 8
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))

        textAttributedStr = attributedString
    }
}


struct UnPaidOrderAlert: View {
    @StateObject var unpaidModel:UnPaidModel
    
    var cancelHandler:()->()
    var okHandler:()->()

    @State private var textHeight: CGFloat = 0

    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text("待支付提醒".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)

                AttributedTextView(
                    attributedText: $unpaidModel.textAttributedStr,
                    height: $textHeight
                ).frame(height:textHeight + 4)
                    .padding(.bottom,20)
                    .onDisappear {
                        unpaidModel.stopCountdown()
                        unpaidModel.objectWillChange.send()
                    }
                
                HStack {
                    Button(action: {
                        cancelHandler()
                    }) {
                        Text("取消订单".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(mainColor)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(RoundedRectangle(cornerRadius: 20)
                            .strokeBorder(mainColor, lineWidth: 1))
                    .cornerRadius(20)

                    Button(action: {
                        okHandler()
                    }) {
                        Text("继续支付".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(Color.white)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(mainColor)
                    .cornerRadius(20)
                }
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal,35)

        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
            
    }
}

#if DEBUG
struct UnPaidOrderAlert_Previews: PreviewProvider {
    static var previews: some View {
        UnPaidOrderAlert(unpaidModel: test_unpaidorder, cancelHandler: {

        }, okHandler: {

        })
    }
}
#endif
