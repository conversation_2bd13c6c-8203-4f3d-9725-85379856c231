//
//  ConfirmOrderPurchasedProductsV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/13.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

import shared

struct ConfirmOrderPurchasedProductsV: View {
    @Binding var isPresent:Bool
    @State var purchasedProducts:[ProductDetailEntity]

    var body : some View {
        ZStack(alignment:.bottom) {
            Button(action: {
                self.isPresent = false
            }) {
                Text("").frame(maxWidth: .infinity,maxHeight: .infinity)
            }.background(Color(dynamicAlphaBackgroundColor1))
            
            VStack(spacing:0) {
                HStack(alignment: .center, spacing: 0, content: {
                    Text("已购商品详情".localized)
                        .foregroundColor(Color(dynamicTitleColor7))
                        .font(Font.regular(size: 18))
                }).frame(height:64)
                    .padding(.horizontal, 14)
                
                if CGFloat(purchasedProducts.count) * 44.0 + 64.0 > UIScreen.main.bounds.height * 0.7 {
                    ScrollView {
                        VStack(spacing:0){
                            ForEach(self.purchasedProducts, id:\.productId) { product in
                                HStack(content: {
                                    Text(product.title)
                                    Spacer()
                                    Text("- $\(product.price.fractionDigits2)")
                                }).foregroundColor(Color(dynamicTitleColor2))
                                    .font(Font.regular())
                                    .frame(height:44)
                                    .padding(.horizontal,24)
                            }
                        }
                    }.frame(maxWidth: .infinity)
                    .frame(height:UIScreen.main.bounds.height * 0.7)
                }else{
                    VStack(spacing:0){
                        ForEach(self.purchasedProducts, id:\.productId) { product in
                            HStack(content: {
                                Text(product.title)
                                Spacer()
                                Text("- $\(product.price.fractionDigits2)")
                            }).foregroundColor(Color(dynamicTitleColor2))
                                .font(Font.regular())
                                .frame(height:44)
                                .padding(.horizontal,24)
                        }
                    }
                }
            }.background(Color(dynamicBackgroundColor12))
                .cornerRadius(radius: 6,corners: [.topLeft, .topRight])
                .overlay(Button(action: {
                    self.isPresent = false
                }, label: {
                    Image("btn_close").frame(width:64,height: 64)
                })  ,alignment: .topTrailing)
            
        }.frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
        
    }
}

//#if DEBUG
//struct ConfirmOrderPurchasedProductsV_Previews: PreviewProvider {
//
//    static var previews: some View {
//        ConfirmOrderPurchasedProductsV(isPresent: .constant(true), purchasedProducts: test_paymentAmount.accountedProducts[0].subProducts.filter{$0.isPurchased}).environmentObject(AppState.shared)
//            .previewDevice("iPhone 13 Pro Max")
//
//        ConfirmOrderPurchasedProductsV(isPresent: .constant(true),purchasedProducts: test_paymentAmount.accountedProducts[0].subProducts.filter{$0.isPurchased}).environmentObject(AppState.shared)
//            .environment(\.colorScheme, .dark)
//            .previewDevice("iPhone SE (3nd generation)")
//    }
//}
//#endif
