//
//  BannerWidget.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/28.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct BannerWidgetPageView: View {
    @State var containerEntity:WidgetContainerCombineEntity
    
    static let WHRate:CGFloat = 327.0/137.0
    @State var imgWidth:CGFloat = (UIApplication.shared.windows.first?.bounds.width ?? UIScreen.main.bounds.width) - 24 * 2
    @State var imgHeight:CGFloat = ((UIApplication.shared.windows.first?.bounds.width ?? UIScreen.main.bounds.width) - 24 * 2) / BannerWidgetPageView.WHRate
    
    var body: some View {
        if let detailEntityList = self.containerEntity.detailEntityList, detailEntityList.count>0 {
            PageViewUIKit(pages: detailEntityList.map{PageModel(imagePath: $0.imagePath)},onTap: { index in
                URLManager.shared.parseWidgetTypeUrlView(widget: detailEntityList[index])
            })
            .frame(maxWidth: .infinity, alignment: .center)
            .frame(height: imgHeight, alignment: .center)
            .onReceive(NotificationCenter.default.publisher(for: PageVC.viewWillTransitionSizeNotification), perform: { noti in
                if let size = noti.object as? CGSize{
                    imgWidth = size.width
                    imgHeight = size.height
                }
            })
        }else{
            ZStack{
                Spacer()
            }.frame(maxWidth: .infinity)
            .frame(height: imgHeight)
            .overlay(ActivityIndicator(), alignment: .center)
            .onReceive(NotificationCenter.default.publisher(for: UIDevice.orientationDidChangeNotification), perform: { (noti) in
                imgWidth = (UIApplication.shared.windows.first?.bounds.width ?? UIScreen.main.bounds.width) - 24 * 2
                imgHeight = ((UIApplication.shared.windows.first?.bounds.width ?? UIScreen.main.bounds.width) - 24 * 2) / BannerWidgetPageView.WHRate
            })
        }
    }
}
