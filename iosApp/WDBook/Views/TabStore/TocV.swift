//
//  TocV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/3.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct TocV: View {
    var content:String
    
    var body: some View {
        BackNavigation(title: "目录".localized){
            ScrollView {
                Text(content)
                    .font(Font.regular(size: 16))
                    .lineSpacing((50 - 16)/2)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,maxHeight:.infinity,alignment: Alignment.topLeading)
                    .padding(EdgeInsets(top: 30, leading: 30, bottom: 30, trailing: 30))
                    .background(Color(dynamicBackgroundColor4))
            }.background(Color(dynamicBackgroundColor4))
        }.navigationBarTitle(Text("目录".localized),displayMode: .inline)
    }
}

#if DEBUG
struct TocV_Previews: PreviewProvider {
    static let content = "版权\n编者序\n作者序\n导论\n综览\n   第一卷\n       第一章\n   第二卷\n       第一章\n       第二章\n尾声\n"
    static let content2 = "版权\n编者序\n作者序\n导论\n综览\n   第一卷\n       第一章\n   第二卷\n       第一章\n       第二章\n尾声\n版权\n编者序\n作者序\n导论\n综览\n   第一卷\n       第一章\n   第二卷\n       第一章\n       第二章\n尾声\n"
    static var previews: some View {
        Group {
            NavigationView{
                TocV(content: content)
            }

            NavigationView{
                TocV(content: content2).previewDevice("iPhone SE (2nd generation)").environment(\.colorScheme, .dark)
            }

        }
        
    }
}
#endif
