//
//  TabStoreV.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/5/21.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import DeviceKit
import NavigationRouter

struct TabStoreV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.safeAreaInsets2) private var safeAreaInsets
    @State var isFirstLoad = true
    @State var headerRefreshing: Bool = false
    let tabLeftPadding = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                         Device.iPodTouch7,
                                                         Device.simulator(.iPodTouch7),
                                                         Device.simulator(.iPhoneSE)]) ? 24 : 20)
    
    var body : some View{
        ZStack(alignment: .top,  content: {
            mainContent
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            if appState.isShowNotificationBanner,
               let notification = appState.notificationDetailsOnBanner.first{
                NotificationNoticeV(title: notification.title,onOK: {
                    RoutableManager.push(NotificationDetailsV(messageId: notification.id, type: notification.type,mv:NotificationDetailsMV(notification)))
                    
                    appState.makeReadNotificationBanner(readId: notification.id)
                },onCancel: {
                    appState.makeReadNotificationBanner(readId: notification.id)
                })
                .padding(.top, (45 + safeAreaInsets.top))
            }
            
            HStack(alignment: .center, spacing: 0, content: {
                RoutedLink(toRoute: .category) {
                    Text("分类".localized)
                        .font(.system(size: 16))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxHeight:.infinity)
                        .padding(.leading, 16)
                        .padding(.trailing, 14)
                }
                RoutedLink(toRoute: .search) {
                    SearchBarUIKit(placeholder: "搜索书名或作者".localized, isUserInteractionEnabled: false, maxLength: 32)
                        .contentShape(Rectangle())
                }
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.trailing, 14)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
    
    var mainContent: some View {
        GeometryReader{ proxy in
            ScrollView(showsIndicators: false) {
                WDRefreshHeader3(refreshing: $headerRefreshing, action: {
                    guard NetReachability.isReachability() else{
                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
                            headerRefreshing = false
                        }
                        return
                    }
                    appState.syncAndRefreshWidgets {
                        headerRefreshing = false
                    }
                })
                VStack(spacing:16){
                    Spacer().frame(height:16)
                    if appState.storeContainers.count > 0{
                        ForEach(appState.storeContainers, id: \.self) { container in
                            container.widgetView(container:container)
                                .frame(width:proxy.size.width)
                        }
                        Spacer().frame(height: 15)
                    }else{
                        ActivityIndicator()
                    }
                }.frame(maxWidth: .infinity,maxHeight:.infinity)
                .frame(width:proxy.size.width)
            }.enableRefresh()
            .onReceive(NotificationCenter.default.publisher(for: NotificationCenterV.afterSyncNotification), perform: { noti in
                appState.checkNotificationRemindWhenReload(method: .banner)
            })
            .onReceive(NotificationCenter.default.publisher(for: NotificationDetailsV.readeNotification), perform: { noti in
                if let id = noti.object as? Int64{
                    appState.makeReadNotificationBanner(readId: id)
                }
            })
            .onReceive(NotificationCenter.default.publisher(for: NotificationCenterV.readeAllNotification), perform: { noti in
                appState.makeReadAllNotificationBanners()
            })
            .onAppear {
                if isFirstLoad {
                    isFirstLoad = false
                    appState.refreshWidgets()
                }else{
                    appState.isStoreVonDisappear = false
                    if appState.cacheStoreContainers.count > 0{
                        appState.storeContainers = appState.cacheStoreContainers
                        appState.cacheStoreContainers = [WidgetContainerCombineEntity]()
                    }
                }
                
            }.onDisappear {
                appState.isStoreVonDisappear = true
            }
        }
    }
}

#if DEBUG
struct TabStoreV_Previews: PreviewProvider {
    
    static var previews: some View {
        Group {
            TabStoreV().environmentObject(AppState.shared)
            
            TabStoreV().environmentObject(AppState.shared)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
