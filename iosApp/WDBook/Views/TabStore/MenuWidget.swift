//
//  MenuWidget.swift
//  WDBook
//
//  Created by QK on 2021/12/28.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//
import SwiftUI
import SDWeb<PERSON>mage<PERSON>wiftUI
import shared
import DeviceKit

struct MenuWidget: View {
    @State var containerEntity:WidgetContainerCombineEntity
    
    var body: some View {
        HStack(alignment: .center, spacing: 0){
            if containerEntity.detailEntityList != nil{
                ForEach(self.containerEntity.detailEntityList!.indices, id: \.self) { i in
                    MenuWidgetCell(widget: self.containerEntity.detailEntityList![i])
                        .onTapGesture {
                            if let entity = containerEntity.detailEntityList?[i]{
                                RoutableManager.showWidgetSectionList(title: entity.widgetTitle, detailEntity: entity)
                            }
                        }
                    
                    if i <  self.containerEntity.detailEntityList!.count - 1{
                        Spacer()
                    }
                }
            }else{
                EmptyView()
            }  
        }.frame(maxWidth: .infinity)
            .frame(height: 70)
            .background(Color(dynamicBackgroundColor4))
            .padding(.horizontal, 24)
    }
}

struct MenuWidgetCell: View {
    var widget: NewWidgetDetailEntity
    let itemWidth = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                         Device.iPodTouch7,
                                                         Device.simulator(.iPodTouch7),
                                                         Device.simulator(.iPhoneSE)]) ? 48 : 55)
    var body: some View {
        VStack(alignment:.center, spacing: 0) {
            ImageManager.getWebImage(url:URL(string: ImageManager.getImageUrl(WDBookAppSDK.shared.getFullImageUrl(url: widget.imagePath))))
                .resizable()
                .placeholder{
                    Rectangle().strokeBorder(Color(dynamicBorderColor3), lineWidth: 1)
                        .frame(width: 40, height: 40)
                }
                .scaledToFit()
                .transition(.fade(duration: 0.5))
                .frame(width: 40, height: 40, alignment: .center)
                .shadow(color: Color(UIColor(hex:0x1D1E20,alpha: 0.2)), radius: 6, x: 0, y: 0)
            
            Spacer().frame(height:8)
            Text(widget.widgetTitle)
                .lineLimit(1)
                .font(Font.medium(size: 12))
                .foregroundColor(Color(dynamicTextColor18))
                .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .center)
            
        }.padding(0)
        .frame(width:itemWidth, height: 70)
        .background(Color(dynamicBackgroundColor4))
    }
}

