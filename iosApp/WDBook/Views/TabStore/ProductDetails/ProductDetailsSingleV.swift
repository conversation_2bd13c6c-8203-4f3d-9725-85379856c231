//
//  ProductDetailsSingleV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/4.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

extension AttributeValueEntity{
    func nameForaCorrect(productDetails:ProductDetailEntity) -> String{
        if deepLink.contains("publisher") && deepLink.contains(String(productDetails.publisherId)){
            return productDetails.publisherName
        }
        return name
    }
}

struct ProductDetailsSingleV: View {
    var productDetails:ProductDetailEntity
    @Binding var isShowTranslatorSelectedAlert:Bool
    @Binding var translators:[AttributeValueEntity]
    @Binding var editorDesc:String
    
    var body: some View {
        VStack(spacing:14){
            VStack(alignment:.leading, spacing:14){
                ForEach(self.productDetails.basicInfoList as! [ProductAttributeEntity]) { basicInfo in
                    Group{
                        if (basicInfo.valueEntityList ?? []).count > 0{
                            HStack {
                                Text("\(basicInfo.key!)：")
                                    .font(Font.regular())
                                    .foregroundColor(Color(dynamicTextColor4))
                                    .frame(width: 100,alignment: Alignment.topTrailing)
                                
                                HStack(spacing:8) {
                                    ForEach(basicInfo.valueEntityList! as! [AttributeValueEntity], id: \.name) { attributedValueEntity in
                                        //蓝色代表有跳转。目前包含出版方和译者。
                                        if !attributedValueEntity.deepLink.isEmpty{
                                            Button(action: {
                                                if (basicInfo.valueEntityList ?? []).count == 1{
                                                    //跳转主页。
                                                    RoutableManager.navigate(toPath: attributedValueEntity.deepLink)
                                                    
                                                }else if attributedValueEntity.deepLink.contains("editor"){
                                                    //弹出选择框。
                                                    editorDesc = "译者".localized
                                                    translators.removeAll()
                                                    translators.append(contentsOf: basicInfo.valueEntityList! as! [AttributeValueEntity])
                                                    isShowTranslatorSelectedAlert = true
                                                }
                                            }) {
                                                Text(attributedValueEntity.nameForaCorrect(productDetails:productDetails))
                                                    .foregroundColor(Color(dynamicTextBlueColor))
                                                    .lineLimit(1)
                                                    .truncationMode(.tail)
                                            }
                                        }else{
                                            Text(attributedValueEntity.name)
                                                .foregroundColor(Color(dynamicTextColor4))
                                                .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                                                .frame(maxWidth:.infinity,alignment: Alignment.topLeading)
                                        }
                                    }
                                }.font(Font.regular())
                                
                            }
                        }else{
                            EmptyView()
                        }
                    }
                }
            }
            .frame(maxWidth: .infinity)
            .padding(EdgeInsets(top: 39, leading: 17, bottom: 39, trailing: 17))
        }.frame(maxWidth: .infinity)
        .background(Color(dynamicBackgroundColor1))
    }
}

#if DEBUG
struct ProductDetailsInfoV_Previews: PreviewProvider {
    static var testMode:ProductDetailEntity{
        let jsonString = loadJsonString(from: test_json_ProductDetailsOne)
        let entity = ProductDetailEntity().testModels(jsonString: jsonString)
        return entity
    }
    
    static var previews: some View {
        ProductDetailsSingleV(productDetails: testMode,isShowTranslatorSelectedAlert:.constant(false),translators: .constant([]), editorDesc:.constant("")).environmentObject(AppState.shared)
    }
}
#endif
