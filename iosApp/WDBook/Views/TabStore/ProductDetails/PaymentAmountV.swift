//
//  PaymentAmountV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/17.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct PaymentAmountV: View {
    var productId:Int64
    var productTitle:String
    var productType:Int32
    
    @State var paymentAmount:PaymentAmountEntity
    @Binding var isPresent:Bool
    var action:()->()
    
    @State private var showDetails = false
    
    var amountV:some View{
        VStack(alignment: .center, spacing: 0) {
            Text(productTitle)
            .foregroundColor(Color(titleColor2))
            .font(Font.regular(size: 18)).frame(height:18)
            .padding(EdgeInsets(top: 37, leading: 24, bottom: 24, trailing: 24))
            
            VStack(alignment: .center, spacing: 0) {
                if productType == 2 {
                    Text("总额：$%@".localizedFormat(paymentAmount.originalPrice.fractionDigits2))
                    .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.regular(size: 14)).frame(height:14)
                    Spacer().frame(height:17)

                    if (paymentAmount.originalPrice - paymentAmount.actualAmount) > 0.0{
                        NavigationLink(
                            destination: PaymentAmountPurchasedProductsV(purchasedProducts: (paymentAmount.purchasedProductList as? [ProductDetailEntity] ?? [])).navigationBarHidden(true),
                            label: {
                                HStack {
                                    Text("已购商品：- $%@".localizedFormat((paymentAmount.originalPrice - paymentAmount.actualAmount).fractionDigits2))
                                    .foregroundColor(Color(dynamicTitleColor2))
                                        .font(Font.regular(size: 14)).frame(height:14)
                                    if (paymentAmount.originalPrice - paymentAmount.actualAmount) > 0.0{
                                        Image("arrow_right_small")
                                    }
                                }
                            })
                    } else {
                        HStack {
                            Text("已购商品：- $%@".localizedFormat((paymentAmount.originalPrice - paymentAmount.actualAmount).fractionDigits2))
                            .foregroundColor(Color(dynamicTitleColor2))
                                .font(Font.regular(size: 14)).frame(height:14)
                        }
                    }

                    Spacer().frame(height:17)
                }
                
                Text("应付 $%@".localizedFormat(paymentAmount.actualAmount.fractionDigits2))
                .foregroundColor(Color(dynamicTextColor6))
                    .font(Font.medium(size: 24)).frame(height:24)

                Spacer().frame(height:30)
                if AppState.shared.walletBalance >= Double(paymentAmount.actualAmount) {
                    Text("账户余额 %@".localizedFormat(AppState.shared.walletBalanceStr))
                    .frame(height:22)
                    .foregroundColor(Color(UIColor(hex:0x0644A0)))
                    .font(Font.regular(size: 16))
                }else{
                    Text("账户余额不足".localized)
                    .frame(height:22)
                    .foregroundColor(Color(UIColor(hex:0xC13013)))
                    .font(Font.regular(size: 16))
                }
                
            }.frame(maxWidth: .infinity)
            
            if !isTestFlight1(){ //正式和测试
                Button(action: {
                    self.action()
                }) {
                    Text(AppState.shared.walletBalance >= Double(paymentAmount.actualAmount) ? "确认购买".localized:amountNotSufficient())
                    .font(Font.medium(size: 16)).frame(height:16)
                    .foregroundColor(Color.white)
                    .frame(minWidth: 0, maxWidth: .infinity)
                }
                .frame(height: 44)
                .background(Color(UIColor.primaryColor1))
                .cornerRadius(22)
                .padding(EdgeInsets(top: 46, leading: 24, bottom: 24, trailing: 24))
                .background(Color(dynamicBackgroundColor1))
                
                Text("您购买的是数字产品，不支持退货".localized)
                    .foregroundColor(Color(dynamicTextColor2))
                    .font(Font.regular(size: 12)).frame(height:12)
//            } else if appState.walletBalance < Double(paymentAmount.actualAmount) {
            } else { //testflight
                Spacer().frame(height:13)
                
                Text("TestFlight 版本不支持购买，请前往网站购买。点击“复制链接”按钮，打开浏览器粘贴前往即可购买。".localized)
                    .foregroundColor(Color(UIColor(hex:0xC13013)))
                    .font(Font.regular(size: 12))
                    .frame(minWidth: 0, maxWidth: .infinity)
                    .padding(EdgeInsets(top: 0, leading: 9, bottom: 0, trailing: 0))
                
                Spacer().frame(height:33)
                
                HStack(alignment: .center, spacing: 11){
                    Button(action: {
                        UIPasteboard.general.string = "https://wdbook.com/dp/\(self.productId)"
                        Toaster.showToast(message: "已复制".localized)
                    }) {
                        Text("复制链接".localized)
                                .font(Font.medium(size: 14))
                                .foregroundColor(Color.white)
                                .frame(width: 150, height: 40, alignment: .center)
                    }
                    .background(Color(UIColor.primaryColor1))
                    .frame(width: 150, height: 40, alignment: .center)
                    .cornerRadius(22)
                    
                    Button(action: {
                        self.isPresent = false
                    }) {
                        ZStack(alignment: .center){
                            Capsule().strokeBorder(Color(dynamicTextColor25), lineWidth: 1)
                            Text("取消".localized)
                                .font(Font.regular(size: 14))
                                .foregroundColor(Color(dynamicTextColor25))
                                .frame(width: 150, height: 40, alignment: .center)
                        }
                        .frame(width: 150, height: 40, alignment: .center)
                    }
                }
            }
            Spacer().frame(height:24)
        }.background(Color(dynamicBackgroundColor1))
    }
    
    func amountNotSufficient() -> String {
        var resultStr = "余额不足，请充值".localized
        if UIApplication.shared.canOpenURL(URL(string: "itms-beta://")!) {
            resultStr = "余额不足".localized
        }
        return resultStr
    }
    
    var body: some View {
        ZStack(alignment:.bottom) {
            Button(action: {
                self.isPresent = false
            }) {
                Text("").frame(maxWidth: .infinity,maxHeight: .infinity)
            }.background(Color(dynamicAlphaBackgroundColor1))
            
            ZStack(alignment:.bottom) {
                if productType == 2 {
                    NavigationView(content: {
                        amountV.navigationBarHidden(true)
                            .frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
                            .background(Color(dynamicBackgroundColor1))
                            
                    }).navigationViewStyle(StackNavigationViewStyle())
                    .frame(maxWidth: .infinity, alignment: .top)
                    .frame( height: 375, alignment: .bottom)
                    .background(Color(dynamicBackgroundColor1))
                    .cornerRadius(6)
                    .padding(8)
                    
                }else{
                    amountV
                        .frame(maxWidth: .infinity, alignment: .bottom)
                        .background(Color(dynamicBackgroundColor1))
                        .cornerRadius(6)
                        .padding(8)
                }
            }
            
        }.frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
        
    }
}


//#if DEBUG
//struct PaymentAmountV_Previews: PreviewProvider {
//    @State static var isShow = true
//    static var previews: some View {
//        Group {
//            PaymentAmountV(productId:0,productTitle:"丁道尔旧约圣经注释-旧约全集",productType:.single,paymentAmount: test_PaymentAmount, isPresent: $isShow,action:{
//
//            }).environmentObject(AppState.shared.initBalance())
//
//            PaymentAmountV(productId:0,productTitle:"丁道尔旧约圣经注释-旧约全集",productType:.single,paymentAmount: test_PaymentAmount, isPresent: $isShow,action:{
//
//            }).environmentObject(AppState.shared.initBalance())
//            .previewDevice("iPhone SE (2nd generation)")
//            .environment(\.colorScheme, .dark)
//
//            PaymentAmountV(productId:0,productTitle:"丁道尔旧约圣经注释-旧约全集",productType:.collection,paymentAmount: test_PaymentAmount, isPresent: $isShow,action:{
//
//            }).environmentObject(AppState.shared.initBalance())
//
//            PaymentAmountV(productId:0,productTitle:"丁道尔旧约圣经注释-旧约全集",productType:.collection,paymentAmount: test_PaymentAmount, isPresent: $isShow,action:{
//
//            }).environmentObject(AppState.shared.initBalance())
//                .environment(\.colorScheme, .dark)
//
//            PaymentAmountV(productId:0,productTitle:"丁道尔旧约圣经注释-旧约全集",productType:.collection,paymentAmount: test_PaymentAmount, isPresent: $isShow,action:{
//
//            }).environmentObject(AppState.shared.initBalance())
//                .previewDevice("iPhone SE (2nd generation)")
//                .environment(\.colorScheme, .dark)
//        }
//    }
//}
//#endif
