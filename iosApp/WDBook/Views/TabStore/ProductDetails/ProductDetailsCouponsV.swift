//
//  ProductDetailsCouponsV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

class CouponsVM:ObservableObject {
    @Published var coupons:[Coupon] = []
    init(_ array:[Coupon] = []) {
        coupons = array
    }
}

struct ProductDetailsCouponsV: View {
    @StateObject var vm:CouponsVM
    
    var body: some View {
        HStack(alignment:.top, spacing:0) {
            Text("优惠:".localized)
                .font(Font.regular())
                .foregroundColor(Color(dynamicTitleColor2))
                .frame(height:44)
            FlowLayout(mode: .scrollable, items: vm.coupons.map{$0.couponName},itemSpacing:6) {index, item in
                Text(item)
                    .font(Font.regular(size: 10))
                    .foregroundColor(Color(hex: 0xFF342A))
                    .frame(height:16)
                    .padding(.horizontal, 10)
//                    .border(Color(hex: 0xFF342A), width: 1)
                    .background(GeometryReader { geometry in
                        let itemWidth = geometry.size.width
                        let itemHeight = geometry.size.height
                        Path { path in
                            path.move(to: CGPoint(x: 0,y: 0))
                            path.addLine(to: CGPoint(x: itemWidth, y: 0))
//                            path.addLine(to: CGPoint(x: itemWidth, y: (itemHeight-7)/2))
                            
                            path.addArc(center: CGPoint(x: itemWidth, y: itemHeight/2), radius: 3.5, startAngle: .degrees(-90), endAngle: .degrees(90), clockwise: true)
                            
//                            path.addLine(to: CGPoint(x: itemWidth, y: itemHeight - (itemHeight-7)/2))
                            path.addLine(to: CGPoint(x: itemWidth, y: itemHeight))
                            path.addLine(to: CGPoint(x: 0, y: itemHeight))
                            
                            path.addArc(center: CGPoint(x: 0, y: itemHeight/2), radius: 3.5, startAngle: .degrees(90), endAngle: .degrees(-90), clockwise: true)
    
                            path.addLine(to: CGPoint(x: 0, y: 0))
                            
                        }.stroke(Color(hex: 0xFF342A), lineWidth: 1)
                    })
            }.padding(.vertical,8)
        }.padding(.horizontal, 16)
        .background(Color(dynamicBackgroundColor1))
    }
}

#if DEBUG
struct ProductDetailsCouponsV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ProductDetailsCouponsV(vm: CouponsVM(test_coupons + test_coupons + test_coupons))
                .previewDevice("iPhone 13 Pro Max")
            ProductDetailsCouponsV(vm: CouponsVM(test_coupons))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
