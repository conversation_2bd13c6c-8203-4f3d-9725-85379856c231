//
//  ProductDetailsHeader.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/3.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import shared

struct ProductDetailsHeader: View {
    @Binding var productDetails: ProductDetailEntity
    @Binding var isShowEditorSelectedAlert:Bool
    @Binding var editorDesc:String
    
    var body: some View {
        VStack(spacing:8) {
            HStack(alignment: .top, spacing: 8) {
                VStack(spacing: 0) {
                    ImageManager.getWebImage(url:URL(string:ImageManager.getImageUrl(productDetails.cover)))
                        .resizable()
                        .placeholder{
                            Image("cover_100*133").resizable().frame(width: 90, height: 126, alignment: .center)
                        }
                        .scaledToFill()
                        .frame(width: 90, height: 126, alignment: .center)
                        .overlay(Image(productDetails.purchased == 1 ? "purchased_big":""), alignment: .topTrailing)
                }
                
                VStack(alignment:.leading, spacing: 0) {
                    Text(productDetails.title)
                        .fixedSize(horizontal: false, vertical: true)
                        .font(Font.medium(size: 18))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                    
                    Spacer().frame(idealWidth: 6.0, minHeight: 6.0,maxHeight:8)
                    
//                    ExpandText(productDetails.authorNames)
                    
                    let authorCount = productDetails.authorList.count
                    if authorCount > 0{
                        HStack(spacing:6) {
                            Button(action: {
                                if authorCount == 1{
                                    //跳转主页。
                                    RoutableManager.navigate(toPath: RouterName.editorWithId.withParam((productDetails.authorList as! [AuthorEntity]).first!.authorId))
                                }else{
                                    //弹出选择框。
                                    editorDesc = "作者".localized
                                    isShowEditorSelectedAlert = true
                                }
                            }) {
//                                Text((productDetails.authorList as! [AuthorEntity]).first!.name)
                                Text(productDetails.authorNames)
                                    .foregroundColor(Color(dynamicTextBlueColor))
                                    .lineLimit(1)
                                    .truncationMode(.tail) // 添加省略号
                            }
//                            if authorCount > 1{
//                                Text("等").foregroundColor(Color(hex:0x373636))
//                            }
                        }.font(Font.regular())
                    }
                    
                    Spacer().frame(idealWidth: 6.0, minHeight: 6.0,maxHeight:8)
                        
                    VStack(alignment:.leading, spacing: 0) {
                        if productDetails.purchaseSeparately == 0 {
                            HStack {
                                Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                + Text("暂不支持单独购买".localized).foregroundColor(Color(UIColor.red))
                            }.lineLimit(1)
                            .font(Font.medium(size: 13))
                            .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                        } else {
                            Group {
                                if productDetails.activitiesList?.count > 0{
                                    Text("活动价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(productDetails.currencyText)\(productDetails.activityAmount.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                        + Text(" (约￥%@)".localizedFormat(productDetails.activityPriceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                    + Text(productDetails.activityDiscount >= 1 || productDetails.activityDiscount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: productDetails.activityDiscount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                                }else{
                                    Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                        + Text("\(productDetails.currencyText)\(productDetails.price.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                        + Text(" (约￥%@)".localizedFormat(productDetails.priceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                    + Text(productDetails.discount >= 1 || productDetails.discount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: productDetails.discount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                                }
                                
                            }.lineLimit(1)
                            .font(Font.medium(size: 13))
                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
                        }

                        Group {
                            Text("原价：".localized) + Text(productDetails.currencyText + String(productDetails.originalPrice)).strikethrough()
                        }.lineLimit(1)
                        .font(Font.medium(size: 13))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .leading)
                    }.opacity(isTestFlight1() ? 0 : 1)
                }.frame(minHeight: 126,idealHeight:126, maxHeight:136)
            }
            
            if productDetails.availableForWDBible == 1 {
                Text("本书可同时在微读圣经的研读功能中使用".localized)
                    .font(Font.regular(size: 13))
                    .foregroundColor(Color(UIColor(hex: 0x333333)))
                    .frame(maxWidth: .infinity)
                    .padding(.top, 9)
                    .padding(.bottom, 9)
                    .background(RoundedCorners(bgColor: Color(dynamicPinkColor), tl: 12, tr: 12, bl: 0, br: 0))
            } else {
                Spacer().frame(maxHeight: 1.0)
            }
        }
        .padding(EdgeInsets(top: 20, leading: 24, bottom: 20, trailing: 24))
        .frame(maxWidth: .infinity)
        .background(Color(dynamicBackgroundColor1))
    }
}

#if DEBUG
struct ProductDetailsHeader_Previews: PreviewProvider {
    static var testProductDetailEntity:ProductDetailEntity{
        let jsonString = loadJsonString(from: test_json_ProductDetailsOne)
        let model = ProductDetailEntity().testModels(jsonString: jsonString) as! ProductDetailEntity
        return model
    }
    
    static var previews: some View {
        ProductDetailsHeader(productDetails: .constant(testProductDetailEntity),isShowEditorSelectedAlert: .constant(false),editorDesc: .constant("作者")).environmentObject(AppState.shared)
    }
}
#endif
