//
//  ProductDetailsItem.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/3.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON><PERSON>SwiftUI
import shared

struct ProductDetailsItem: View {
    var authorNames:String
    var productDetails: ProductDetailEntity

    var body: some View {
        HStack(spacing: 16) {
            
            ImageManager.getWebImage(url:URL(string:ImageManager.getImageUrl(productDetails.cover)))
                .resizable()
                .placeholder{
                    Image("cover_81*108").resizable().frame(width: 81, height: 108, alignment: .center)
                }
                .transition(.fade(duration: 0.5)) // Fade Transition with duration
                .scaledToFit()
                .frame(width: 81, height: 108, alignment: .center)
                .overlay(Image(productDetails.isPurchased ? "purchased_small":""), alignment: .topTrailing)
            
            VStack(alignment:.leading, spacing: 8) {
                Text(productDetails.title)
                    .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                Text(String(productDetails.authorNames))
                    .lineLimit(1).fixedSize(horizontal: false, vertical: true)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                
                VStack(alignment:.leading, spacing: 2) {
                    Group {
                        Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                            + Text("\(productDetails.currencyText)\(productDetails.price.fractionDigits2)").foregroundColor(Color(UIColor.red))
                            + Text(" (约￥%@)".localizedFormat(productDetails.priceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                        + Text(productDetails.discount >= 1 || productDetails.discount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: productDetails.discount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                    }.lineLimit(1)
                    .font(Font.regular(size: 12))
                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                    
                    Group {
                        Text("原价：".localized) + Text(productDetails.currencyText + String(productDetails.originalPrice)).strikethrough()
                    }.lineLimit(1)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                }.opacity(isTestFlight1() ? 0 : 1)
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 108)
        .background(Color(dynamicBackgroundColor1))
    }
}

//#if DEBUG
//struct ProductDetailsItem_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            ProductDetailsItem(authorNames: test_ProductDetails.authorNames, productDetails: test_ProductDetails.subProducts.first!).environmentObject(AppState.shared)
//            
//            ProductDetailsItem(authorNames: test_ProductDetails.authorNames,productDetails: test_ProductDetails.subProducts.first!).previewDevice("iPhone SE (2nd generation)").environmentObject(AppState.shared).environment(\.colorScheme, .dark)
//        }
//    }
//}
//#endif
