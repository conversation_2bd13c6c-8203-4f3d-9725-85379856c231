//
//  ProductDetailsCouponsDrawV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/8.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

class CouponsDrawVM: ObservableObject {
    @Published var data: [Coupon] = []
    
    init(_ data:[Coupon]? = nil) {
        if let list = data{
            self.data = list
        }
    }
}

struct ProductDetailsCouponsDrawV: View {
    @StateObject var vm = CouponsDrawVM()
    @Binding var isPresent:Bool
    var takeAction:((Int64)->())?
    
    var body: some View {
        ZStack(alignment:.bottom) {
            Button(action: {
                self.isPresent = false
            }) {
                Text("").frame(maxWidth: .infinity,maxHeight: .infinity)
            }.background(Color(dynamicAlphaBackgroundColor1))
            
            VStack(spacing:0) {
                Text("可用优惠券".localized).foregroundColor(Color(dynamicTitleColor7))
                    .font(Font.regular(size: 18))
                Spacer().frame(height:12)
                
                if CGFloat(vm.data.count) * (95 + 16) + 64.0 > UIScreen.main.bounds.height * 0.7 {
                    ScrollView {
                        VStack(spacing:16) {
                            ForEach(vm.data,id:\.couponId) { model in
                                CouponsCellV(coupon: model,takeAction:takeAction)
                            }
                            Spacer().frame(height:25)
                        }
                    }.frame(maxWidth:.infinity)
                    .frame(height:UIScreen.main.bounds.height * 0.7)
                }else{
                    VStack(spacing:16) {
                        ForEach(vm.data,id:\.couponId) { model in
                            CouponsCellV(coupon: model,takeAction:takeAction)
                        }
                        Spacer().frame(height:25)
                    }
                }
                
            }.frame(maxWidth:.infinity)
            .padding(EdgeInsets(top: 24, leading: 0, bottom: 0, trailing: 0))
            .background(Color(dynamicBackgroundColor12))
            .cornerRadius(radius: 6,corners: [.topLeft, .topRight])
            
        }.frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
    }
}

#if DEBUG
struct ProductDetailsCouponsDrawV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            
            ProductDetailsCouponsDrawV(vm: CouponsDrawVM(test_coupons), isPresent: .constant(true))
                .previewDevice("iPhone 13 Pro Max")
            
            ProductDetailsCouponsDrawV(vm: CouponsDrawVM(test_coupons), isPresent: .constant(true))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
        }
        
    }
}
#endif


