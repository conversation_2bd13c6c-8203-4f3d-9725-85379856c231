//
//  PaymentResultV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/18.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

enum PaymentResultVType {
    case paymentSuccess
    case paymentFailedRetry
    case paymentFailed
    case rechargeSuccess
    case rechargeFailed
    case priceChanged
    case balanceNotEnough
}

let Noti_Payment_Complete = Notification.Name(rawValue: "Noti_Payment_Complete")
struct PaymentResultV: View {

    var type:PaymentResultVType
    var title:String
    var icon:String
    var buttonText:String
    var action:()->()
    var feedbackAction:()->()
    
    @Binding var isPresent:Bool
    
    init(title titleParam:String? = nil,type:PaymentResultVType,isPresent:Binding<Bool>,action:@escaping ()->(),feedbackAction:@escaping ()->()) {
        self.type = type
        switch type {
        case .paymentSuccess:
            title = "购买成功".localized
            icon = "payment_successful"
            buttonText = "确定".localized
            break
        case .paymentFailedRetry:
            title = "购买失败".localized
            icon = "payment_failed"
            buttonText = "重新尝试".localized
            break
        case .paymentFailed:
            title = "购买失败".localized
            icon = "payment_failed"
            buttonText = "确定".localized
            break
        case .priceChanged:
            title = "商品价格发生变化，无法完成购买。".localized
            icon = "payment_failed"
            buttonText = "确定".localized
            break
        case .balanceNotEnough:
            title = "账户余额不足，无法完成购买。".localized
            icon = "payment_failed"
            buttonText = "确定".localized
            break
        case .rechargeSuccess:
            title = "充值成功".localized
            icon = "payment_successful"
            buttonText = "继续购买".localized
            break
        case .rechargeFailed:
            title = "充值失败".localized
            icon = "payment_failed"
            buttonText = "重新尝试".localized
            break
        }
        if let t = titleParam,!t.isEmpty{
            title = t
        }
        self.action = action
        self.feedbackAction = feedbackAction
        _isPresent = isPresent
    }
    
    var body: some View {
            ZStack(alignment:.bottom) {
                Button(action: {
                    self.isPresent = false
                }) {
                    Text("").frame(maxWidth: .infinity,maxHeight: .infinity)
                }.background(Color(dynamicAlphaBackgroundColor1))
                
                VStack(alignment: .center, spacing: 0) {
                    Text(title)
                    .foregroundColor(Color(titleColor2))
                    .font(Font.regular(size: 18))
                    .padding(EdgeInsets(top: 37, leading: 24, bottom: 24, trailing: 24))
                    
                    Image(icon)
                    Button(action: {
                        self.isPresent = false
                        self.action()
                        if type == .paymentSuccess || type == .rechargeSuccess{
                            NotificationCenter.default.post(name: Noti_Payment_Complete, object: nil)
                        }
                    }) {
                        Text(buttonText)
                        .font(Font.medium(size: 16))
                        .foregroundColor(Color.white)
                        .frame(minWidth: 0, maxWidth: .infinity)
                    }
                    .frame(height: 44)
                    .background(Color(UIColor.primaryColor1))
                    .cornerRadius(22)
                    .padding(EdgeInsets(top: 46, leading: 24, bottom: 0, trailing: 24))
                    
                    if [.paymentFailedRetry,.paymentFailed,.rechargeFailed].contains(type){
                        Button(action: {
                            self.feedbackAction()
                        }) {
                            Text("我要反馈".localized)
                            .font(Font.regular())
                            .foregroundColor(Color(UIColor(hex: 0x006FFF)))
                            .frame(minWidth: 0, maxWidth: .infinity)
                        }
                        .frame(height: 20)
                        .padding(EdgeInsets(top: 8, leading: 24, bottom: 0, trailing: 24))
                    }
                    
                    Spacer().frame(height:24)
                    
                }.frame(maxWidth: .infinity)
                .background(Color(dynamicBackgroundColor1))
                .cornerRadius(6)
                    .padding(8)
                
        }.frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
            
    }
}

#if DEBUG
struct PaymentResultV_Previews: PreviewProvider {
    @State static var isShow = true
    
    static var previews: some View {
        Group {
            PaymentResultV(type: .paymentSuccess, isPresent: $isShow,action: {
                Log.d("点击购买成功")
            }, feedbackAction: {}).environmentObject(AppState.shared)
            
            PaymentResultV(type: .paymentFailedRetry, isPresent: $isShow,action: {
                Log.d("点击重试")
            }, feedbackAction: {}).environmentObject(AppState.shared)
                .previewDevice("iPhone SE (2nd generation)")
                .environment(\.colorScheme, .dark)
            
            PaymentResultV(title:"商品价格发生变化，无法完成购买。", type: .paymentFailed, isPresent: $isShow, action: {
                Log.d("点击商品价格发生变化确定")
            }, feedbackAction: {}).environmentObject(AppState.shared)
            
            PaymentResultV(title:"账户余额不足，无法完成购买。", type: .paymentFailed, isPresent: $isShow,action: {
                Log.d("点击账户余额不足确定")
            }, feedbackAction: {}).environmentObject(AppState.shared)
                .previewDevice("iPhone SE (2nd generation)")
                .environment(\.colorScheme, .dark)
            
            PaymentResultV(title:"优惠券已失效，无法完成购买。", type: .paymentFailed, isPresent: $isShow,action: {
                Log.d("点击优惠券已失效确定")
            }, feedbackAction: {}).environmentObject(AppState.shared)
            
            PaymentResultV(type: .rechargeSuccess, isPresent: $isShow,action: {
                Log.d("点击充值成功")
            }, feedbackAction: {}).environmentObject(AppState.shared)
            
            PaymentResultV(type: .rechargeFailed, isPresent: $isShow,action: {
                Log.d("点击充值失败")
            }, feedbackAction: {}).environmentObject(AppState.shared)
        }
    }
}
#endif
