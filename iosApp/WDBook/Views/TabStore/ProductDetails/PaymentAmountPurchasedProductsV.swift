//
//  PaymentAmountPurchasedProductsV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/24.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct PaymentAmountPurchasedProductsV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    @State var purchasedProducts:[ProductDetailEntity]
    var btnBack : some View { Button(action: {
        self.presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
            Image("arrow_left_small")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                .frame(width: 24, height: 24, alignment: /*@START_MENU_TOKEN@*/.center/*@END_MENU_TOKEN@*/)
            }
        }
    }

    var body : some View {
        ScrollView {
            VStack(spacing:0){
                HStack(alignment: .center, spacing: /*@START_MENU_TOKEN@*/nil/*@END_MENU_TOKEN@*/, content: {
                    btnBack
                    Spacer()
                    Text("部分商品已购详情".localized)
                        .foregroundColor(Color(titleColor2))
                        .font(Font.regular(size: 18))
                    Spacer()
                    btnBack.opacity(0)
                }).frame(height:64)
                .padding(.horizontal, 14)
                
                VStack(spacing:24) {
                    ForEach(self.purchasedProducts, id:\.productId) { product in
                        HStack(content: {
                            Text(product.title ?? "")
                            Spacer()
                            Text("- $\(product.price.fractionDigits2)")
                        }).foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.regular())
                    }
                }.frame(maxWidth: .infinity,maxHeight:.infinity)
                .padding(EdgeInsets(top: 12, leading: 24, bottom: 12, trailing: 20))
            }
        }.frame(maxWidth: .infinity,maxHeight:.infinity)
        .background(Color(dynamicBackgroundColor1))
    }
}

//#if DEBUG
//struct PaymentAmountPurchasedProductsV_Previews: PreviewProvider {
//    static var previews: some View {
//    
//        PaymentAmountPurchasedProductsV(purchasedProducts: test_PaymentAmount.purchasedProducts).environmentObject(AppState.shared.initBalance())
//        
//        PaymentAmountPurchasedProductsV(purchasedProducts: test_PaymentAmount.purchasedProducts).environmentObject(AppState.shared.initBalance())
//        .previewDevice("iPhone SE (2nd generation)")
//        .environment(\.colorScheme, .dark)
//    }
//}
//#endif
