//
//  ProductDetailsOneV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/4.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct ProductDetailsOneV: View {
    var productDetails:ProductDetails
    
    var body: some View {
        VStack(spacing: 0) {
            NavigationLink(destination: TocV(content: self.productDetails.items.first!.toc)) {
                Text("查看目录")
                .font(Font.regular())
                .foregroundColor(Color(dynamicBtnForegroundColor1))
                .frame(minWidth: 0, maxWidth: .infinity)
            }
            .frame(height: 50)
            .background(Color(dynamicBackgroundColor1))

            VStack(spacing:14){
                VStack(alignment:.leading, spacing:14){
                    ForEach(self.productDetails.items.first!.attributes) { attribute in
                        Group{
                            if !attribute.valuesDesc.isEmpty{
                                HStack {
                                    Text("\(attribute.key)：").frame(width: 100,alignment: Alignment.trailing)
                                    Text("\(attribute.valuesDesc)").lineLimit(2).frame(maxWidth:.infinity,alignment: Alignment.leading)
                                }
                            }else{
                                EmptyView()
                            }
                        }
                    }
                }
                .frame(maxWidth: .infinity)
                .padding(EdgeInsets(top: 39, leading: 17, bottom: 39, trailing: 17))

            }.frame(maxWidth: .infinity)
            .background(Color(dynamicBackgroundColor1))
        }
        
    }
}

struct ProductDetailsInfoV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NavigationView{
                ProductDetailsOneV(productDetails: test_ProductDetails).environmentObject(AppState.shared)
            }

            NavigationView{
                ProductDetailsOneV(productDetails: test_ProductDetails).previewDevice("iPhone SE (2nd generation)").environmentObject(AppState.shared).environment(\.colorScheme, .dark)
            }

        }
    }
}
