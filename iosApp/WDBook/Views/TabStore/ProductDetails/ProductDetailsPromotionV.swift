//
//  ProductDetailsPromotionV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/6.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter

struct ProductDetailsPromotionV: View {
    @State var promotions:[Promotion] = []
    
    var body: some View {
        VStack(spacing: 0) {
            ForEach(promotions,id: \.activityId) { p in
                RoutedLink(to: RouterName.activityWithId.withParam(p.activityId)) {
                    HStack(spacing:0.0) {
                        Text("活动".localized + "：" + p.activityTitle)
                            .font(Font.regular(size: 16))
                            .foregroundColor(Color(dynamicTitleColor2))
                            .lineLimit(1)
                        Spacer()
                        
                        Text("查看".localized)
                            .font(Font.regular(size: 13))
                            .foregroundColor(Color.secondary)
                        Image("arrow_right_20_2").frame(width: 20, height: 20, alignment: .center)
                            .foregroundColor(Color.secondary)
                        
                    }.frame(width:.infinity, height: 44.0)
                        .padding(.leading,16)
                        .padding(.trailing,12)
                        .overlay(Color(dynamicNavigationBarBottomLineColor).frame(maxWidth:.infinity).frame(height:0.5),alignment:.bottom)
                        .contentShape(Rectangle())
                }
                
            }
        }.frame(maxWidth:.infinity)
        .background(Color(dynamicBackgroundColor1))
        
    }
}

#if DEBUG
struct ProductDetailsPromotionV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ProductDetailsPromotionV(promotions: test_promotions)
                .previewDevice("iPhone 13 Pro Max")
            ProductDetailsPromotionV(promotions: [test_promotions.first!])
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
