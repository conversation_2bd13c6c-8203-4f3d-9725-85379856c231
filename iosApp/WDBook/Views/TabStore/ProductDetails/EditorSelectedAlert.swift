//
//  EditorSelectedAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/9/5.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct EditorSelectedAlert: View {
    @State var desc:String = "作者".localized
    @State var editors:[AuthorEntity] = []
    var selectedHandler:(Int64)->()
    var cancelHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text(desc).font(Font.medium(size: 17)).bold().foregroundColor(Color(dynamicTitleColor11))
                    .frame(height:42)

                Color(dynamicSpanLineColor10).frame(maxWidth:.infinity).frame(height:0.5)
                
                ForEach(editors, id: \.authorId) { editor in
                    Button(action: {
                        selectedHandler(editor.authorId)
                    }) {
                        Text(editor.name).font(Font.regular(size: 15))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(Color(dynamicTextBlueColor))
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:42)
                }
                
            }.padding(10)
                .background(Color(dynamicTextColor27)).cornerRadius(10)
                .frame(width:300)
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
            .ignoresSafeArea()
            .onTapGesture {
                cancelHandler()
            }
    }
}

struct TranslatorSelectedAlert: View {
    @State var desc:String = "译者".localized
    @State var editors:[AttributeValueEntity] = []
    var selectedHandler:(String)->()
    var cancelHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text(desc).font(Font.medium(size: 17)).bold().foregroundColor(Color(dynamicTitleColor11)) //TODO:373636
                    .frame(height:42)

                Color(dynamicSpanLineColor10).frame(maxWidth:.infinity).frame(height:0.5)
                
                ForEach(editors, id: \.deepLink) { editor in
                    Button(action: {
                        selectedHandler(editor.deepLink)
                    }) {
                        Text(editor.name).font(Font.regular(size: 15))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(Color(dynamicTextBlueColor))
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:42)
                }
                
            }.padding(10)
                .background(Color(dynamicTextColor27)).cornerRadius(10)
                .frame(width:300)
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
            .ignoresSafeArea()
            .onTapGesture {
                cancelHandler()
            }
    }
}

struct EditorSelectedAlert_Previews: PreviewProvider {
    static var previews: some View {
        EditorSelectedAlert(selectedHandler: {_ in }, cancelHandler: {})
    }
}
