//
//  ProductDetailsV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/3.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import SwiftyStoreKit
import NavigationRouter

struct ProductDetailsV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    var productId:Int64
    @State var isOnAppear:Bool = false
    @State var isFirstLoad = true
    
    @State var productDetails:ProductDetailEntity = ProductDetailEntity()
    @State var productTitle:String = ""
    @State var progress:Float = 0.0
    @State var state:AppDownloadState = .unDownload
    @State var canTouch:Bool = true
    @State var downloadInfo:ResourceDownloadInfo?
    
    @State var isPresentCouponV = false
    @State var isShowPaymentAmountV = false
    @State var paymentAmount = PaymentAmountEntity()
    @State var isShowPaymentResultV = false
    @State var paymentResultVType:PaymentResultVType = .paymentFailedRetry
    @State var isShowChargeV = false
    @State var isShowAlert = false
    @State var alertType = AlertType.showPayment
    @State var paymentAlertType:PaymentAlertType = .priceChanged
    @State var hasRelatedSuit: Bool = false
    @State var isTestFlightAlert: Bool = false
    @State var isShowUnPaidAlert = false
    @State var unpaidModel:UnPaidModel = UnPaidModel()
    
    @State var isShowEditorSelectedAlert = false
    @State var isShowTranslatorSelectedAlert = false
    @State var translators:[AttributeValueEntity] = []
    @State var editorDesc = ""

    enum AlertType{
        case showPayment
        case repeatPurchase
    }
    
    enum PaymentAlertType {
        case priceChanged
        case balanceNotEnough
        
        var msg:String{
            switch self {
            case .priceChanged:
                return "此商品价格发生变化，请重新购买".localized
            case .balanceNotEnough:
                return "您的帐户余额发生变化，请核实后再购买。".localized
            }
        }
    }
    
    init(productId:Int64, productDetails:ProductDetailEntity = ProductDetailEntity()) {
        self.productId = productId
        _productDetails = State(initialValue: productDetails)
        updateState()
    }
    
    func refresh(isShowLoading:Bool = false,success:(()->())? = nil){
        if isShowLoading {
            HUDManager.showLoadingBlockHUD()
        }
        let startT = Date()
        WDBookStoreSDK.shared.getBookDetailInfo(productId: self.productId) { (result) in
            switch result {
            case .success(let bookDetail):
                Log.d("获取产品详情成功,持续时间: \(Date().timeIntervalSince1970 - startT.timeIntervalSince1970)")
                Log.d(result)
                if let detail = bookDetail{
                    self.updateProductDetails(p: detail)
                }else{
                    if productDetails.productId == 0{
                        if !NetReachability.isReachability() {
                            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                        }
                    }
                }
                if isFirstLoad { sendAnalysisEvent() }
                self.isFirstLoad = false
                if isShowLoading {
                    HUDManager.hideLoadingHUD()
                }
            case .failure(let error):
                Log.i(error)
                self.isFirstLoad = false
                if isShowLoading {
                    HUDManager.hideLoadingHUD()
                }
            }
        }
    }
    func updateProductDetails(p:ProductDetailEntity){
        productDetails = p
        if productDetails.isPurchased {
            self.downloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: productDetails.resourceId)
        }
        updateState()
    }
    
    func updateState(){
        productTitle = productDetails.title
        hasRelatedSuit = productDetails.relatedProducts.count > 0
        if productDetails.isPurchased, self.downloadInfo?.downloadInfo != nil {
            // 已购
            progress = self.downloadInfo!.progress
            if self.downloadInfo!.downloadStatus == DownloadStatus.complete || self.downloadInfo!.downloadStatus == DownloadStatus.update {
                state = .completed
            } else {
                state = .unDownload
            }
        }
    }
    
    func toggleFavorite(){
        guard NetReachability.isReachability() else{
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }
        
        guard WDBookSessionSDK.shared.isLogin else {
            AppState.shared.showLoginRegisterV()
            return
        }
        
//        HUDManager.showLoadingBlockHUD(text: "")
        if productDetails.inFavorite == 1{
            WDBookUserSDK.shared.removeFavoriteBook(productId: productDetails.productId) { result in
                HUDManager.hideLoadingHUD()
                switch result {
                case .success(let r):
                    refresh()
                    AppState.shared.refreshFavoriteListCount()
                    Toaster.showToast(message: "已移除收藏".localized,duration: 1.5)
                    NotificationCenter.default.post(name: FavoriteListV.removeFavoriteNotification, object: nil)
                case .failure(let error):
                    debugPrint(error)
                    break
                }
            }
        }else{
            WDBookUserSDK.shared.addFavoriteBook(productId: productDetails.productId) { result in
                HUDManager.hideLoadingHUD()
                switch result {
                case .success(let r):
                    refresh()
                    AppState.shared.refreshFavoriteListCount()
                    Toaster.showToast(message: "已添加收藏".localized,duration: 1.5)
                    NotificationCenter.default.post(name: FavoriteListV.addFavoriteNotification, object: nil)
                case .failure(let error):
                    debugPrint(error)
                    break
                }
            }
        }
    }
    
    func getPaymentAmount(){
        HUDManager.showLoadingBlockHUD()
        WDBookPaymentSDK.shared.getPayAmountEntity(productIds: [self.productId],ignoreRepeatPurchased:false) { result in
            HUDManager.hideLoadingHUD()
            switch result {
            case .success(let paymentAmountEntity):
                if let entity = paymentAmountEntity{
                    RoutableManager.push(ConfirmOrderV(selectedProducts:[productDetails], successAction: {
                        refresh(isShowLoading: true)
                    }))
                }
            case .failure(let error):
                Log.d("\(error)")
                switch error{
                case .error(let kotExp):
                    print(kotExp)
                    if let exp = kotExp as? ApiException,exp.code == ErrorInfo.repeatPurchase.code{
                        alertType = .repeatPurchase
                        isShowAlert = true
                    }else if let exp = kotExp as? ApiException,exp.code == ErrorInfo.unpaidorder.code,let data = exp.message?.data(using: .utf8){
                        
                        let decoder = JSONDecoder()
                        decoder.dateDecodingStrategy = .iso8601
                        if let model = try? decoder.decode(UnPaidModel.self, from: data){
                            unpaidModel = model
                            isShowUnPaidAlert = true
                        }
                        
                    }
                    break
                }
            }
        }
        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_CLICK_BUY, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_PRODUCT_ID, String(self.productId)])
    }
    
    func sendAnalysisEvent(){
        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_DETAIL_OPEN, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_PRODUCT_ID, String(self.productId)])
        if productDetails.subProductList.count > 0 {
            AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_PRODUCT_PACKAGE_OPEN, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_PRODUCT_ID, String(self.productId)])
        }
    }
    
    func startDownload(){
        self.downloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: productDetails.resourceId)
        WDBookStoreSDK.shared.getResourceFileEntity(resourceId: productDetails.resourceId) { result in
            switch result {
            case .success(let fileEntity):
                if let fileId = fileEntity?.fileId {
                    self.downloadInfo?.downloadInfo?.md5 = fileEntity?.md5 ?? ""
                    download(resourceId: productDetails.resourceId, fileId: fileId,md5: fileEntity?.md5 ?? "")
                } else {
                    // showTips
                    self.canTouch = true
                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                }
            case .failure(_):
                // showTips
                self.canTouch = true
                Toaster.showToast(message: "没有获取到文件信息！".localized)
            }
        }
        
    }
    
    func download(resourceId:String,fileId:String,md5:String){
        if productDetails.isPurchased {
            if self.downloadInfo!.needUpgradeApp {
                AppState.shared.alert.showFormatVersionAlert()
                return
            }
            self.canTouch = false
            WDBookStoreSDK.shared.getResourceFileEntity(resourceId: resourceId) { result in
                switch result {
                case .success(let fileEntity):
                    if let fileId = fileEntity?.fileId {
                        self.downloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: resourceId)
                        self.downloadInfo?.downloadInfo?.md5 = fileEntity?.md5 ?? ""
                        
                        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in
                            switch result {
                            case .success(let entity):
                                self.canTouch = true
                                if let fileDownloadEntity = entity{
                                    Log.d("获取downloadurl 成功了")
                                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                                        switch result {
                                        case .success(_):
                                            Log.d("开始下载了")
                                            
                                            AppDownloadManager.shared.start(key: fileId, url: fileDownloadEntity.downloadUrl, destinationFilePath:PathManager.zipPathRelative(fileId),md5:md5)
                                        case .failure(_):
                                            Log.d("获取加密key失败")
                                        }
                                    }
                                }else{
                                    self.canTouch = true
                                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                                }
                            case .failure:
                                self.canTouch = true
                                Log.d("获取 downloadurl 失败")
                                Toaster.showToast(message: "没有获取到下载链接！".localized)
                            }
                        }
                        
                    } else {
                        // showTips
                        self.canTouch = true
                        Toaster.showToast(message: "没有获取到文件信息！".localized)
                    }
                case .failure(_):
                    // showTips
                    self.canTouch = true
                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                }
            }
            
            
        } else {
            self.canTouch = true
        }
    }
    
    private func getButtonTitle() -> String {
        var buttonTitle = ""
        if productDetails.isPurchased {
            if AppState.shared.enableDownloadProgress{
                if self.state == .completed {
                    buttonTitle = "阅读".localized
                } else if self.canTouch {
                    buttonTitle = "下载".localized
                }
            }else{
                buttonTitle = "阅读".localized
            }

        } else {
            if productDetails.purchaseSeparately == 1 {
                if productDetails.price > 0.0 {
                    buttonTitle = "购买".localized
                } else {
                    buttonTitle = "0元购买".localized
                }
            } else {
                buttonTitle = "请前往套装购买".localized
            }
        }
        return buttonTitle
    }
    
    var buyBtn: some View{
        Button(action: {
            //购买，或者下载时候，判断网络。
            if !productDetails.isPurchased || self.state == .unDownload {
                guard NetReachability.isReachability() else{
                    AppState.shared.alert.showNetworkErrorAlert()
                    return
                }
            }
            
            guard WDBookSessionSDK.shared.isLogin else {
                AppState.shared.showLoginRegisterV()
                return
            }
            
//            guard !isTestFlight1() else {
//                isTestFlightAlert = true
//                return
//            }
            
            guard productDetails.isPurchased else{
                AppState.shared.refreshWalletBalance()
                
                //先判断get
                getPaymentAmount()
                return
            }
            guard self.canTouch else{
                return
            }
            if self.state == .completed {
                RoutableManager.navigate(toPath: RouterName.readerWithId.withParam(downloadInfo!.resourceId))
            } else if self.state == .downloading || self.state == .waiting{
                Log.d("正在下载")
                guard NetReachability.isReachability() else{
                    AppState.shared.alert.showNetworkErrorAlert()
                    return
                }
                Toaster.showToast(message: "书籍资源正在下载中".localized)
            } else {
//                if AppState.shared.enableDownloadProgress{
                    guard NetReachability.isReachability() else{
                        AppState.shared.alert.showNetworkErrorAlert()
                        return
                    }
                    
                    if !AppState.shared.isHide4GDownloadControl && NetReachability.isCellular() && AppState.shared.downloadOnlyOnWifi{
                        AppState.shared.alert.showCanCellularDownloadAlert {
                            startDownload()
                        }
                    }else{
                        startDownload()
                    }
//                }else{
//                    Toaster.showToast(message: "书籍资源正在下载中".localized)
//                }
            }
        }) {
            if AppState.shared.enableDownloadProgress && (self.state == .downloading || self.state == .waiting) && self.canTouch {
                DownloadProgressView(progress: self.$progress,trackTintColor:.constant(UIColor(hex:0xD7DADE)))
                .frame(minWidth: 0, maxWidth: .infinity)
            } else {
                ZStack {
                    //阅读，下载，购买
                    Text(getButtonTitle())
                        .font(Font.medium(size: 16))
                        .foregroundColor(Color.white)
                        .frame(minWidth: 0, maxWidth: .infinity)
                    if !self.canTouch{
                        ActivityIndicator()
                    }
                }
            }
        }.disabled(productDetails.purchaseSeparately != 1 && !productDetails.isPurchased)
        .frame(height: 44)
        .background((productDetails.purchaseSeparately == 1 || productDetails.isPurchased) ? Color(UIColor.primaryColor1) : Color(UIColor.primaryColor1).opacity(0.4))
        .cornerRadius(22)
        .padding(EdgeInsets(top: 18, leading: 16, bottom: 14, trailing: 16))
        .background(Color(dynamicBackgroundColor1))
    }
    
    var buyAndFavoriteBtns: some View {
        Group{
            if isTestFlight1(){
                VStack(alignment: .center, spacing: 0) {
                    Button {
                        toggleFavorite()
                    } label: {
                        if productDetails.inFavorite == 1{
                            Text("已收藏".localized).font(Font.regular()).bold()
                                .foregroundColor(mainColor).background(Color.clear)
                                .frame(maxWidth: .infinity)
                                .frame(height:40)
                                .background(Color(UIColor.clear))
                                .overlay(RoundedRectangle(cornerRadius: 100).stroke(mainColor, lineWidth: 1))
                                .padding(.horizontal,43)
                                .padding(.top,12)
                        }else{
                            Text("收藏".localized).font(Font.regular()).bold()
                                .foregroundColor(Color.white)
                                .frame(maxWidth: .infinity)
                                .frame(height:40)
                                .background(Color(UIColor.primaryColor1))
                                .cornerRadius(20)
                                .padding(.horizontal,43)
                        }
                    }.padding(.vertical,12)

//                    Text("该版本不支持购买，请前往微读书城网站购买".localized)
//                        .lineLimit(2)
//                        .font(Font.regular(size: 13))
//                        .foregroundColor(Color(UIColor(hex: 0xE33733)))
//                        .padding(EdgeInsets(top: 10, leading: 20, bottom: 30, trailing: 20))
//                        .background(Color(dynamicBackgroundColor1))

                }.overlay(Color(dynamicLineOnDayColor).frame(height:0.5),alignment: .top)
            }else{
                HStack(alignment: .center, spacing: 0) {
                    Image(productDetails.inFavorite == 1 ? "heart_like" : "heart_unlike").onTapGesture {
                        toggleFavorite()
                    }.padding(.leading, 16)
                    buyBtn
                }.overlay(Color(dynamicLineOnDayColor).frame(height:0.5),alignment: .top)
            }
        }
        
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        Toaster.hideToast()
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, 45)
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .overlay(
            Group {
                if isShowUnPaidAlert {
                    UnPaidOrderAlert(unpaidModel: unpaidModel, cancelHandler: {
                        HUDManager.showLoadingBlockHUD(text: "")
                        WDBookPaymentSDK.shared.cancelOrder(orderId: String(unpaidModel.orderId)) { result in
                            HUDManager.hideLoadingHUD()
                            isShowUnPaidAlert = false
                            switch result{
                            case .success(let r):
                                Toaster.showToast(message:"订单已取消")
                                break
                            case .failure(let e):
                                if let apiExp = e as? ApiException{
                                    Toaster.showToast(message: apiExp.message ?? "")
                                }
                            }
                        }
                    }, okHandler: {
                        isShowUnPaidAlert = false
                        RoutableManager.push(ConfirmOrderV(unPaidOrderId: unpaidModel.orderId,selectedProducts:[productDetails], successAction: {
                            refresh(isShowLoading: true)
                        }))
                    })
                }else{
                    EmptyView()
                }
            }.ignoresSafeArea()
            , alignment: .center)
        .overlay(isPresentCouponV ? AnyView(ProductDetailsCouponsDrawV(vm:CouponsDrawVM((productDetails.couponsList as! [CouponEntity]).map{Coupon(entity: $0)}),isPresent: $isPresentCouponV, takeAction:{ cid in
            for c in productDetails.couponsList as! [CouponEntity]{
                if c.couponId == cid{
                    c.status = Int32(CouponStatus.unused.rawValue)
                }
            }
        })): AnyView(EmptyView()))
        .overlay(
            Group {
                if isShowEditorSelectedAlert {
                    
                    EditorSelectedAlert(desc:editorDesc,
                                        editors:(productDetails.authorList as! [AuthorEntity]),
                                        selectedHandler: { editorId in
                        isShowEditorSelectedAlert = false
                        RoutableManager.navigate(toPath: RouterName.editorWithId.withParam(editorId))
                    }, cancelHandler: {
                        isShowEditorSelectedAlert = false
                    })
                }else{
                    EmptyView()
                }
            }, alignment: .center)
        .overlay(
            Group {
                if isShowTranslatorSelectedAlert {
                    
                    TranslatorSelectedAlert(desc:editorDesc,
                                        editors:translators,
                                        selectedHandler: { deepLink in
                        isShowTranslatorSelectedAlert = false
                        RoutableManager.navigate(toPath: deepLink)
                    }, cancelHandler: {
                        isShowTranslatorSelectedAlert = false
                    })
                }else{
                    EmptyView()
                }
            }, alignment: .center)
    }
    var content: some View {
        VStack(spacing:0.5) {
            if productDetails.productId == 0{
                ActivityIndicator()
            //未上架
            }else if !(productDetails.status == 1) {

                VStack(alignment: .center, spacing: 0, content: {
                    Image("error_empty2").frame(width: 161, height: 64)
                    Spacer().frame(height:54)
                    Text("页面不存在！".localized).font(Font.medium(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                })

            } else {
                ScrollView {
                    VStack(spacing:0.5){
                        ProductDetailsHeader(productDetails: $productDetails,isShowEditorSelectedAlert:$isShowEditorSelectedAlert,editorDesc:$editorDesc)
                        //优惠券
                        if !isTestFlight1() && productDetails.couponsList?.count > 0{
                            ProductDetailsCouponsV(vm:CouponsVM((productDetails.couponsList as! [CouponEntity]).map{Coupon(entity: $0)}))
                                .onTapGesture {
                                    guard NetReachability.isReachability() else{
                                        Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                                        return
                                    }
                                    
                                    guard WDBookSessionSDK.shared.isLogin else {
                                        AppState.shared.showLoginRegisterV()
                                        return
                                    }
                                    
                                isPresentCouponV = true
                            }
                        }
                        
                        //套装
                        if hasRelatedSuit {
                            ProductSuitView(products: productDetails.relatedProducts as! [ProductDetailEntity])
                        }
                        
                        //活动
                        if !isTestFlight1() && productDetails.activitiesList?.count > 0{
                            ProductDetailsPromotionV(promotions: (productDetails.activitiesList as! [ActivityEntity])
                                .filter{$0.hidden == 0}
                                .map{Promotion(entity: $0)})
                        }
                        
                        if !productDetails.description_.isEmpty{
                            // productType 1:single 2:collection
                            if productDetails.productType == 1 {
                                VStack(spacing:0) {
                                    ProductDetailsSummary(content: productDetails.description_)
                                    if !productDetails.toc.isEmpty {
                                        NavigationLink(destination: TocV(content: productDetails.toc)) {
                                            Text("查看目录".localized)
                                            .font(Font.regular())
                                            .foregroundColor(Color(dynamicBtnForegroundColor1))
                                            .frame(minWidth: 0, maxWidth: .infinity)
                                        }
                                        .frame(height: 50)
                                        .background(Color(dynamicBackgroundColor1))
                                    }
                                }
                            }else{
                                ProductDetailsSummary(content: productDetails.description_)
                            }
                        }else{
                            if !productDetails.toc.isEmpty{
                                NavigationLink(destination: TocV(content: productDetails.toc)) {
                                    Text("查看目录".localized)
                                    .font(Font.regular())
                                    .foregroundColor(Color(dynamicBtnForegroundColor1))
                                    .frame(minWidth: 0, maxWidth: .infinity)
                                }
                                .frame(height: 50)
                                .background(Color(dynamicBackgroundColor1))
                            }
                        }
                        
                        if productDetails.productType == 2 && productDetails.subProductList.count > 0 {
                            ProductDetailsItemList(productDetails: productDetails)
                        } else if productDetails.productType == 1 {
                            ProductDetailsSingleV(productDetails: productDetails,isShowTranslatorSelectedAlert:$isShowTranslatorSelectedAlert,translators:$translators,editorDesc:$editorDesc)
                        }
                    }.frame(maxWidth: .infinity)
                    .background(Color(dynamicNavigationBarBottomLineColor))
                }.frame(maxWidth: .infinity,maxHeight:.infinity)
                .background(Color(dynamicBackgroundColor1))
                
                if productDetails.productType == 2 {
                    if productDetails.statusAvailable && !productDetails.isAllPurchased {
                        buyAndFavoriteBtns
                        
                    }else{
                        Text("温馨提示：请分别点击套装中的书籍进行下载或阅读".localized)
                            .lineLimit(2)
                            .font(Font.regular(size: 12))
                            .foregroundColor(Color(dynamicTextColor8))
                            .padding(EdgeInsets(top: 29.5, leading: 50, bottom: 30, trailing: 50))
                            .background(Color(dynamicBackgroundColor1))
                            .padding(.top, 0.5)
                            .background(Color(dynamicNavigationBarBottomLineColor))
                    }
                } else {
                    //已购显示 下载，阅读。
                    //非已购且 状态1，显示购买
                            //非状态1，显示提示
                    buyAndFavoriteBtns
                }
            }
        }.frame(maxWidth: .infinity,maxHeight:.infinity)
        .background(Color(dynamicBackgroundColor1))
        .navigationBarTitle(Text(""),displayMode: .inline)
        .onAppear {
            self.isOnAppear = true
            self.refresh()
        }
        .onDisappear(perform: {
            self.isOnAppear = false
        })
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Complete), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == downloadInfo?.fileId {
                self.state = .completed
            }
        })
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Fails), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == downloadInfo?.fileId {
                self.state = .unDownload
            }
        })
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Waiting), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == downloadInfo?.fileId {
                self.state = .waiting
            }
        })
        .onReceive(NotificationCenter.default.publisher(for: Noti_Download_Progress), perform: { (obj) in
            if let downloadingInfo = obj.object as? DownloadDataEntity, downloadingInfo.fileId == downloadInfo?.fileId {
                self.state = .downloading
                self.progress = downloadingInfo.progress
            }
        })
        .onReceive(NotificationCenter.default.publisher(for: AuthLoginV.loginSuccessNotification), perform: { (obj) in
            self.refresh()
        }).alert(isPresented: self.$isShowAlert){
            switch alertType{
            case .showPayment:
                return Alert(simpleMessage: self.paymentAlertType.msg) {
                    self.isShowAlert = false
                    self.refresh(isShowLoading: true)
                }
            case .repeatPurchase:
                return Alert(title: Text("温馨提示".localized),
                             message: Text("你已经购买过该图书".localized),
                      dismissButton: .default(Text("我知道了".localized),action: {
                    self.isShowAlert = false
                    self.refresh(isShowLoading: true)
                }))
            }
        }.alert(isPresented: self.$isTestFlightAlert){
                Alert(title: Text(""),
                             message: Text("您的版本属于内测版，暂不支持购买".localized),
                      dismissButton: .default(Text("确定".localized),action: {
                    self.isTestFlightAlert = false
                }))
        }
    }
}

struct ProductDetailsSummary: View {
    @State var content: String
    @State private var htmlStr: String = ""
    
    var body: some View {
        VStack {
            if htmlStr.count > 0 {
                ExpandArrowText(text:htmlStr,fgColor: dynamicTextColor10,bgColor: dynamicBackgroundColor1,font: UIFont.regular(size: 16), horizontalPadding: 24,compactMaxLineNum: 4)
                    .padding(EdgeInsets(top: 20, leading: 24, bottom: 20, trailing: 24))
                    .background(Color(dynamicBackgroundColor1))
            } else {
                ProgressView()
                    .onAppear(perform: loadHTMLContent)
            }
        }
    }
    
    func loadHTMLContent() {
        DispatchQueue.global(qos: .userInitiated).async {
            let str = content.trim().attributeStringFromHTML().string.trim()
            DispatchQueue.main.async {
                htmlStr = str
            }
        }
    }
}

struct ProductSuitView: View {
    var products: [ProductDetailEntity]
    var body: some View {
        VStack {
            ForEach(self.products, id:\.productId) {  product in
                RoutedLink(to: RouterName.productDetail.withParam(product.productId)) {
                    ProdutctSuitCell(product: product)
                }
            }
        }
        .background(Color(dynamicBackgroundColor1))
    }
}

struct ProdutctSuitCell: View {
    var product: ProductDetailEntity
    
    var body: some View {
        HStack(spacing:0) {
            Text("套装".localized)
                .padding(EdgeInsets(top: 3, leading: 8, bottom: 3, trailing: 8))
                .background(Color.red)
                .foregroundColor(Color.white)
                .cornerRadius(5.0)
            Spacer().frame(width:8)
            Text(product.title)
                .foregroundColor(Color(dynamicTitleColor2))
                .font(Font.regular(size: 16))
                .lineLimit(1)
            Spacer()

            Text(isTestFlight1() ? "查看".localized : "去购买".localized)
                    .foregroundColor(Color.secondary)
            Image("arrow_right").frame(width: 20, height: 20, alignment: .center)
        }.frame(width:.infinity).frame(height: 24.0)
        .font(Font.system(Font.TextStyle.footnote))
        .padding(EdgeInsets(top: 9, leading: 16, bottom: 9, trailing: 12))
        .contentShape(Rectangle())
    }
}
