//
//  ProductDetailsItemList.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/4.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import NavigationRouter

struct ProductDetailsItemList: View {
    var productDetails:ProductDetailEntity
    
    var body: some View {
        VStack(spacing:16) {
            
            if self.productDetails.subProductList.count >= 2 {
                ForEach((self.productDetails.subProductList as! [ProductDetailEntity]), id:\.productId) {  subProduct in
                    RoutedLink(to: RouterName.productDetail.withParam(subProduct.productId)) {
                        ProductDetailsItem(authorNames: subProduct.authorNames, productDetails: subProduct)
                    }.buttonStyle(PlainButtonStyle())
                }
            } else {
                ForEach(0 ..< 2) { (i) in
                    if i == 0 {
                        if let subProduct = (self.productDetails.subProductList as! [ProductDetailEntity]).first{
                            RoutedLink(to: RouterName.productDetail.withParam(subProduct.productId)) {
                                ProductDetailsItem(authorNames: subProduct.authorNames, productDetails: subProduct).buttonStyle(PlainButtonStyle())
                            }.buttonStyle(PlainButtonStyle())
                        }else{
                            EmptyView().buttonStyle(PlainButtonStyle())
                        }
                        
                    } else {
                        EmptyView().buttonStyle(PlainButtonStyle())
                    }
                }
            }
        }
        .frame(maxWidth: .infinity)
        .padding(EdgeInsets(top: 14, leading: 24, bottom: 14, trailing: 24))
        .background(Color(dynamicBackgroundColor1))
    }
}

//#if DEBUG
//struct ProductDetailsMutliV_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            NavigationView{
//                ProductDetailsItemList(productDetails: test_ProductDetailsMutli).environmentObject(AppState.shared)
//            }
//
//            NavigationView{
//                ProductDetailsItemList(productDetails: test_ProductDetailsMutli).previewDevice("iPhone SE (2nd generation)").environmentObject(AppState.shared).environment(\.colorScheme, .dark)
//            }
//
//        }
//    }
//}
//#endif
