//
//  ContentView.swift
//  WDBook
//
//  Created by <PERSON> on 5/18/20.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Combine
import shared
import SwiftUI
import SwiftyUserDefaults

let TabChangeNotification = Notification.Name(rawValue: "TabChangeNotification")

struct ContentView: View {
    @EnvironmentObject var appState: AppState

    @State var isFirstLoad = true
    @State var tabSelect: TabSelect = .init() {
        didSet {
            appState.tabSelect = tabSelect
        }
    }

    @State private var isOnEdit = false
    @State private var isRename = false
    @State private var showingGroupDialog = false
    @State private var showingGroupCreatDialog = false
    @State private var isBookSelcted = false
    @ObservedObject var archiveEntity = ShelfArchiveListEntity()

    func convertToArchiveListEntity() {
        archiveEntity.shelfItems.removeAll()
        for i in 0 ..< appState.shelfCombineItems.count {
            if appState.shelfCombineItems[i].dataType == ShelfDataType.resource {
                archiveEntity.shelfItems.append(appState.shelfCombineItems[i])
            }
        }
    }

    var isInitPhoneLogin: Bool {
        if let countryCode = Defaults[key: DefaultsKeys.LAST_LOGIN_COUNTRY_CODE],!countryCode.isEmpty,
           let phone = Defaults[key: DefaultsKeys.LAST_LOGIN_PHONE],!phone.isEmpty
        {
            return true
        } else {
            return false
        }
    }

    var body: some View {
        GeometryReader { geo in
            NavigationView {
                ZStack {
                    VStack(spacing: 0) {
                        ZStack(alignment: .center) {
                            Spacer().frame(maxWidth: .infinity, maxHeight: .infinity)
                            TabShelfV().opacity(tabSelect.selection == .bookshelf ? 1 : 0)
                            if isTestFlight1() {
                                CategoryPageV().opacity(tabSelect.selection == .store ? 1 : 0)
                            } else {
                                TabStoreV().opacity(tabSelect.selection == .store ? 1 : 0)
                            }
                            TabAccountV().opacity(tabSelect.selection == .account ? 1 : 0)
                        }.frame(maxWidth: .infinity, maxHeight: .infinity)

                        if !showingGroupCreatDialog {
                            ZStack(alignment: .center) {
                                Button(action: {
                                    guard WDBookSessionSDK.shared.isLogin else {
                                        AppState.shared.showLoginRegisterV()
                                        return
                                    }
                                    guard isBookSelcted else { return }
                                    convertToArchiveListEntity()
                                    withAnimation(.spring()) {
                                        self.showingGroupDialog.toggle()
                                    }
                                }) {
                                    Text("分组到...".localized)
                                        .foregroundColor(isBookSelcted ? Color(dynamicTextColor17) : Color(dynamicTextColor16))
                                        .accentColor(Color(UIColor.primaryColor1))
                                        .font(.system(size: 14))
                                        .isHidden(!isOnEdit || appState.shelfCombineItems.count == 0 || !WDBookSessionSDK.shared.isLogin)
                                        .font(.headline)
                                        .frame(maxWidth: .infinity)
                                        .frame(height: 50)
                                        .contentShape(Rectangle())
                                }
                                .buttonStyle(MyButtonStyle(isBookSelcted: $isBookSelcted))
                                .frame(maxWidth: .infinity)

                                TabBarUIKit(selection: self.$tabSelect.selection, hasDot2: $appState.hasNewFeedbackOrNotification,
                                            tabbarItems: [UITabBarItem(title: TabName.bookshelf.title, image: UIImage(named: "tab_shelf"), tag: TabName.bookshelf.rawValue),
                                                          UITabBarItem(title: TabName.store.title, image: UIImage(named: "tab_shopping"), tag: TabName.store.rawValue),
                                                          UITabBarItem(title: TabName.account.title, image: UIImage(named: "tab_account"), tag: TabName.account.rawValue)],
                                            shouldChange: { tag in
                                                tabSelect.shouldSelect(TabName(rawValue: tag)!)
                                            })
                                            .accentColor(Color(UIColor.primaryColor1))
                                            .font(.headline)
                                            .isHidden(isOnEdit && appState.shelfCombineItems.count > 0)
                            }
                            .overlay(isOnEdit ? AnyView(Text("").frame(maxWidth: .infinity).frame(height: 1).background(Color(dynamicHomeTopLineColor))) : AnyView(EmptyView()), alignment: .top)
                        }

                    }.frame(maxWidth: .infinity, maxHeight: .infinity)

                    if self.showingGroupDialog && WDBookSessionSDK.shared.isLogin {
                        ShelfGroupListDialog(showingGroupDialog: $showingGroupDialog, showingGroupCreatDialog: $showingGroupCreatDialog, isShowRemoveView: false, isOnEdit: $isOnEdit, isRename: $isRename).environmentObject(self.archiveEntity).frame(maxWidth: .infinity, maxHeight: .infinity)
                    }

                    if self.showingGroupCreatDialog && WDBookSessionSDK.shared.isLogin {
                        CreateGroupDialog(showingGroupCreatDialog: $showingGroupCreatDialog, isOnEdit: $isOnEdit, isRename: false).environmentObject(DownloadResourceObservable(item: HomeShelfItemCombineEntity()))
                    }
                }.frame(maxWidth: .infinity, maxHeight: .infinity)
            }.navigationBarTitle(Text(self.tabSelect.selection.title), displayMode: .inline)
                .navigationBarHidden(true)
                .navigationViewStyle(StackNavigationViewStyle())
                .onReceive(NotificationCenter.default.publisher(for: TabChangeNotification), perform: { noti in
                    appState.popToRootOnlyForSwiftUI()
                    isOnEdit = false
                    if let select = noti.object as? TabName {
                        if self.tabSelect.selection != select {
                            let tab = TabSelect()
                            tab.selection = select
                            self.tabSelect = tab
                        }
                    }
                })
                .onReceive(NotificationCenter.default.publisher(for: TabShelfV.Noti_IsOnShelfEdit, object: nil)) { noti in
                    if let isOnEdit = noti.object as? Bool {
                        self.isOnEdit = isOnEdit
                        if !isOnEdit {
                            isBookSelcted = false
                            for i in 0 ..< self.appState.shelfCombineItems.count {
                                self.appState.shelfCombineItems[i].isSeleted = false
                            }
                        }
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: TabShelfV.Noti_IsShelfBookSelcted, object: nil)) { noti in
                    if let isBookSelcted = noti.object as? Bool {
                        self.isBookSelcted = isBookSelcted
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: ContentView.Noti_IsLogOut, object: nil)) { _ in
                    isOnEdit = false
                    isBookSelcted = false
                    for i in 0 ..< self.appState.shelfCombineItems.count {
                        self.appState.shelfCombineItems[i].isSeleted = false
                    }
                }
                .fullScreenCover(isPresented: $appState.isShowLoginV, onDismiss: {
                    // 点击或下滑退出
                    if !WDBookSessionSDK.shared.isLogin {
                        AppState.shared.hideLoginRegisterV()
                    }
                    // 登录成功，隐藏ui
                    // 不做处理
                    // 登录成功，进入设备管理前
                    // 不做处理
                }) {
                    EmailLoginV(isShowPhoneLoginV: isInitPhoneLogin).environmentObject(AppState.shared)
                }
                .fullScreenCover(isPresented: $appState.isShowRegisterV, onDismiss: {
                    // 点击或下滑退出
                    AppState.shared.isShowRegisterV = false
                }) {
                    EmailRegisterV().environmentObject(AppState.shared)
                }
                .alert(isPresented: self.$appState.alert.isShowAlert) {
                    switch self.appState.alert.alertType {
                    case .defaultAlertType:
                        return Alert(title: Text(self.appState.alert.alertTitle),
                                     message: Text(self.appState.alert.alertMsg),
                                     primaryButton: .cancel(Text("取消".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }),
                                     secondaryButton: .default(Text("继续".localized), action: { self.appState.alert.tapOKAlert()
                                     }))
                    case .formatVersionAlertType:
                        return Alert(title: Text(""),
                                     message: Text("当前客户端版本较低，暂不支持该文件格式，请升级到最新版APP。".localized),
                                     primaryButton: .cancel(Text("取消".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }),
                                     secondaryButton: .default(Text("去升级".localized), action: {
                                         UIApplication.shared.open(URL(string: "https://apps.apple.com/cn/app/wei-du-shu-cheng/id1528441683")!, options: [:]) { _ in
                                         }
                                     }))
                    case .onlyOkBtnAlertType:
                        return Alert(simpleMessage: self.appState.alert.alertMsg) {
                            self.appState.alert.tapOKAlert()
                        }
                    case .bookUpdateAlertType:
                        return Alert(title: Text(self.appState.alert.alertTitle),
                                     message: Text(self.appState.alert.alertMsg),
                                     primaryButton: .cancel(Text("取消".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }),
                                     secondaryButton: .default(Text("更新".localized), action: { self.appState.alert.tapOKAlert()
                                     }))
                    case .bookUpdateFailureAlertType:
                        return Alert(title: Text(self.appState.alert.alertTitle),
                                     message: Text(self.appState.alert.alertMsg),
                                     primaryButton: .cancel(Text("取消".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }),
                                     secondaryButton: .default(Text("重试".localized), action: { self.appState.alert.tapOKAlert()
                                     }))
                    case .bookNoteDownloadAlertType:
                        return Alert(title: Text(self.appState.alert.alertTitle),
                                     message: Text(self.appState.alert.alertMsg),
                                     primaryButton: .cancel(Text("取消".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }),
                                     secondaryButton: .default(Text("下载本书".localized), action: { self.appState.alert.tapOKAlert()
                                     }))
                    case .bookNoteDeleteAlertType:
                        return Alert(title: Text(self.appState.alert.alertTitle),
                                     message: Text(self.appState.alert.alertMsg),
                                     primaryButton: .cancel(Text("取消".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }),
                                     secondaryButton: .destructive(Text("删除".localized), action: { self.appState.alert.tapOKAlert()
                                     }))
                    case .appUpgradeAlertType:
                        if Defaults[key: DefaultsKeys.APP_FORCE_UPGRADE] {
                            // 强制升级
                            return Alert(title: Text(self.appState.alert.alertTitle),
                                         message: Text(self.appState.alert.alertMsg),
                                         primaryButton: .destructive(Text("立即更新".localized), action: {
                                                self.appState.alert.tapOKAlert()
                                            }),
                                         secondaryButton: .cancel(Text("关闭应用".localized), action: {
                                                self.appState.alert.tapCancelAlert()
                                                exit(0)
                                            }))
                        } else {
                            // 普通升级提示
                            return Alert(title: Text(self.appState.alert.alertTitle),
                                         message: Text(self.appState.alert.alertMsg),
                                         primaryButton: .cancel(Text("稍后".localized), action: {
                                             self.appState.alert.tapCancelAlert()
                                         }),
                                         secondaryButton: .destructive(Text("立即更新".localized), action: { self.appState.alert.tapOKAlert()
                                         }))
                        }
                    case .notificationType:
                        return Alert(title: Text(self.appState.alert.alertTitle),
                                     message: Text(self.appState.alert.alertMsg).font(Font.regular(size: 16)),
                                     primaryButton: .default(Text("查看".localized), action: {
                                         self.appState.alert.tapOKAlert()
                                     }),
                                     secondaryButton: .cancel(Text("关闭".localized), action: { self.appState.alert.tapCancelAlert()
                                     }))
                    case .networkErrorType:
                        return Alert(title: Text("提示".localized),
                                     message: Text("网络连接异常，请检查设置再重试".localized).font(Font.regular(size: 16)),
                                     dismissButton: .default(Text("确定".localized), action: {
                                         self.appState.alert.tapCancelAlert()
                                     }))
                    }

                }.onAppear {
                    appState.geometry = geo
                    appState.refreshFeedbackStatus()
                    appState.refreshNotificationStatus()
                    appState.refreshCouponCount()
                }
        }
    }
}

extension ContentView {
    static let Noti_IsLogOut = Notification.Name("Noti_IsLogOut")
}

#if DEBUG
    struct ContentView_Previews: PreviewProvider {
        static var previews: some View {
            ContentView().environmentObject(AppState.shared)
        }
    }
#endif

struct ContentView2: View {
    @State private var text1: String = ""
    @State private var text2: String = ""

    var body: some View {
        NavigationView {
            VStack {
                TextField("Text 1", text: $text1)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding()

                TextField("Text 2", text: $text2)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .padding()

                Spacer()
            }.navigationBarHidden(true)
        }

//        .padding(.top, UIApplication.shared.windows.first?.safeAreaInsets.top) // 调整顶部padding以避免被状态栏遮挡
//        .background(KeyboardAvoiding()) // 使用KeyboardAvoiding修饰符
    }
}
