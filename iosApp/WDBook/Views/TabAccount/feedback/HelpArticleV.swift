//
//  HelpArticleV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2022/5/6.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

class HelpArticleMV: ObservableObject {
    @Published var article: HelpArticle
    
    init(_ article:HelpArticle? = nil) {
        self.article = article ?? HelpArticle(entity: HelpArticleEntity())
    }
}

struct HelpArticleV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @State var articleId:Int64
    @ObservedObject var mv = HelpArticleMV()
    @State var didStart:Bool = false
    
    func btnBack() -> some View { Button(action: {
            self.presentationMode.wrappedValue.dismiss()  //单独使用
        }) {
            HStack {
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor))
            }
        }
    }
    
    var content: some View{
        ZStack {
            if !mv.article.content.isEmpty{
                LightWebViewUIKit(url:"",content: mv.article.content, didStartProvisionalNavigation: {
                    didStart = true
                })
            }
            
            if !didStart{
                ActivityIndicator()
            }
        }.frame(maxWidth:.infinity,maxHeight: .infinity)
        .onAppear {
            if articleId > 0{
                WDBookUserSDK.shared.getHelpArticle(articleId: articleId, completion: { result in
                    didStart = true
                    switch result{
                    case .success(let r):
                        if let entity = r{
                            mv.article = HelpArticle(entity: entity)
                        }else{
                            Toaster.showToast(message: "网络加载失败，请重新尝试！".localized,duration: 2.0) {(success) in
                                self.presentationMode.wrappedValue.dismiss()
                            }
                        }
                    case .failure(let error):
                        Toaster.showToast(message: "网络加载失败，请重新尝试！".localized,duration: 2.0) {(success) in
                            self.presentationMode.wrappedValue.dismiss()
                        }
                        break
                    }
                })
            }
        }
    }
    
    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))

                    Spacer()
                    Text(LocalizedStringKey(mv.article.title))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack().padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)

                }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine:true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
}

#if DEBUG
struct HelpArticleV_Previews: PreviewProvider {
    static var previews: some View {
        HelpArticleV(articleId:0)
    }
}
#endif
