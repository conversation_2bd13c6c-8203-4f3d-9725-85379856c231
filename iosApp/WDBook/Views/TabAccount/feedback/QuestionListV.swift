//
//  QuestionListV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/5/5.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter
import shared

class HelpQuestionsMV: ObservableObject {
    @Published var questions: [HelpQuestion] = []
    
    init(_ questions:[HelpQuestion]? = nil) {
        if let ques = questions{
            self.questions = ques
        }
    }
}

struct QuestionListV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @EnvironmentObject var appState:AppState
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    
    @ObservedObject var mv = HelpQuestionsMV()
    @State var isFirstLoad = true
    @State var isNetworkError = false
    
    @State var angle: Double = 0.0
    @State private var isAnimating = false
    @State private var showProgress = false
    var foreverAnimation: Animation {
        Animation.linear(duration: 2.0)
            .repeatForever(autoreverses: false)
    }
    @State private var isPresent = false
    
    func onTapRight(){
        isAnimating = true
        WDBookUserSDK.shared.getFeedbackTitleList { result in
            isAnimating = false
            switch result{
            case .success(let list):
                if let entities = list,entities.count > 0{
                    //TODO: 把数据传进来。不再进行网络请求。
                    RoutableManager.navigate(toRoute: .feedbacks)
                }else{
                    RoutableManager.navigate(toRoute: .feedbackCreate)
                }
            case .failure(let error):
                isPresent = true
                break
            }
        }
    }
    
    func btnBack(left:Bool = true) -> some View {
        Button(action: {
            self.presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                    .aspectRatio(contentMode: .fit)
                    .foregroundColor(Color(btnTintColor))
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var content: some View{
        ScrollView {
            LazyVStack(alignment:.leading, spacing: 0) {
                ForEach(mv.questions,id: \.id) { question in
                    Text(question.title).foregroundColor(Color(UIColor(hex: 0xBCBCBC))).font(Font.regular(size: 14)).padding(EdgeInsets(top: 16, leading: 8, bottom: 4, trailing: 8))
                    
                    VStack(alignment:.leading, spacing: 0) {
                        ForEach(question.articles,id: \.id) { article in
                            RoutedLink(to: RouterName.helpArticleWithId.withParam(article.id)) {
                                HStack(alignment:.center, spacing:0) {
                                    Text(article.title).font(Font.regular(size: 16))
                                        .foregroundColor(Color(dynamicTextColor19))
                                        .lineSpacing(2)
                                        .frame(maxWidth:.infinity,alignment:.leading)
                                        .padding(EdgeInsets(top: 15, leading: 16, bottom: 15, trailing: 0))
                                    Image("arrow_right_24").frame(width: 24, height: 24).padding(EdgeInsets(top: 0, leading: 4, bottom: 0, trailing: 8))
                                }.frame(maxWidth:.infinity)
                                .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 16), alignment: .bottom)
                                .contentShape(Rectangle())
                            }
                        }
                    }.background(Color(dynamicBackgroundColor14))
                }
            }.padding(.bottom,40)
        }.frame(maxWidth:.infinity,maxHeight: .infinity)
        .background(Color(dynamicBtnBGColor))
        .overlay(Group {
            if self.isFirstLoad{
                ActivityIndicator()
            }else if isNetworkError{
                Text("网络连接失败，请稍后再试".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
            }else if mv.questions.count == 0 {
                Text("未查到数据".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
            } else {
                EmptyView()
            }
        })
        .onAppear {
            if mv.questions.count == 0{
                WDBookUserSDK.shared.getHelpFaqList(completion: { result in
                    switch result{
                    case .success(let list):
                        isNetworkError = false
                        if let entities = list, entities.count > 0 {
                            mv.questions.removeAll()
                            mv.questions.append(contentsOf: entities.map{HelpQuestion(entity: $0)})
                        }else{
                            //空页面
                        }
                    case .failure(let error):
                        isNetworkError = true
                        break
                    }
                    isFirstLoad = false
                })
            }
            AppState.shared.refreshFeedbackStatus()
        }
    }
    
    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 10, bottom: 0, trailing: 20))
                
                Spacer()
                
                Button {
                    guard WDBookSessionSDK.shared.isLogin else{
                        AppState.shared.showLoginRegisterV()
                        return
                    }
                    if !isAnimating{
                        onTapRight()
                    }
                } label: {
                    HStack(spacing:5) {
                        if isAnimating{
                            Image("activity_indicator_17").resizable().frame(width: 17, height:17, alignment: .center).aspectRatio(contentMode: .fit)
                                .rotationEffect(Angle(degrees: self.isAnimating ? self.angle : 0.0))
                                .onAppear {
                                    withAnimation(self.foreverAnimation) {
                                        self.angle += 360.0
                                    }
                                }
                        }
                        
                        Text("我要反馈".localized)
                            .foregroundColor(Color(dynamicTitleColor2))
                            .font(Font.regular(size: 16))
                            .padding(EdgeInsets(top: 4, leading: 0, bottom: 4, trailing: 8))
                            .hasRedDot(hasDot: $appState.hasNewFeedback)
                            
                    }.contentShape(Rectangle())
                }
            }).frame(height:45)
            .frame(maxWidth:.infinity)
            .padding(.horizontal, 10)
            .modifier(BottomLineViewModifier(isShowBottomLine: true))
            .overlay(Text("帮助中心".localized).frame(maxHeight:.infinity)
                .foregroundColor(Color(dynamicTitleColor2))
                .font(Font.semibold(size: 18)), alignment: .center)
            .padding(.top, safeAreaInsets.top)
            .background(Color(dynamicBackgroundColor1))
            
            //                .navigationBarHidden(true)
            //                .edgesIgnoringSafeArea(.top)
        }).navigationBarHidden(true)
        .ignoresSafeArea()
        .alert(isPresented: $isPresent) {
            Alert(title: Text(""),
                  message: Text("网络连接失败，请稍后再试".localized),
                  primaryButton: .cancel(Text("取消".localized),action: {
                
            }),
                  secondaryButton: .default(Text("确定".localized),action:{
                
            }))
        }
    }
}


#if DEBUG
struct QuestionListV_Previews: PreviewProvider {
    static var previews: some View {
        
        Group {
            QuestionListV(mv:HelpQuestionsMV(test_QuestionList)).environmentObject(AppState.shared)
            
            QuestionListV(mv:HelpQuestionsMV(test_QuestionList)).environmentObject(AppState.shared)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
