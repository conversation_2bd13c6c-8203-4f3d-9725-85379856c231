//
//  FeedbackListV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/5/6.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter
import shared

class FeedbackListModelView: ObservableObject {
    @Published var feedbacks: [Feedback] = []
    
    init(_ feedbacks:[Feedback]? = nil) {
        if let list = feedbacks{
            self.feedbacks = list
        }
    }
}

struct FeedbackListV: View {
    static let refreshFeedbackTitleListNotification = Notification.Name(rawValue: "FeedbackListV.refreshFeedbackTitleListNotification")
    @State var isFirstLoad = true
    @ObservedObject var mv = FeedbackListModelView()
    
    func refresh(){
        WDBookUserSDK.shared.getFeedbackTitleList { result in
            switch result{
            case .success(let list):
                if let entities = list,entities.count > 0{
                    mv.feedbacks.removeAll()
                    mv.feedbacks.append(contentsOf: entities.map{Feedback(entity: $0)})
                }else{
                    
                }
                
            case .failure(let error):
                break
            }
            
            isFirstLoad = false
        }
    }
    
    var body: some View {
        BackNavigation(title: "我的反馈".localized){
            VStack(spacing:0) {
                ScrollView{
                    VStack(spacing:0) {
                        ForEach(mv.feedbacks, id:\.feedbackId) { feedback in
                            RoutedLink(to: RouterName.feedbackWithId.withParam(feedback.feedbackId)) {
                                HStack(spacing: 0) {
                                    VStack(alignment: .leading, spacing: 0) {
                                        Text(feedback.messageText).font(Font.regular(size: 15).bold()).lineLimit(2).foregroundColor(Color(dynamicTextColor26))
                                            .frame(maxWidth:.infinity,alignment:.leading)
                                            .padding(EdgeInsets(top: 10, leading: 24, bottom: 10, trailing: 0))
                                            .overlay(Circle().fill(feedback.hasNewMessage == 1 ? Color.red : Color.clear).frame(width: 8, height: 8).padding(.leading,12).padding(.top,15),alignment:.topLeading)
                                        Text(feedback.lastReplyTime.yyyymmdd_hhmm).font(Font.regular(size: 13)).foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                                            .padding(.leading, 18).padding(.bottom,8)
                                    }
                                    VStack{
                                        Spacer()
                                        HStack(spacing:4) {
                                            Image("icon_chat").frame(width: 20, height: 20)
                                            Text(String(feedback.replyCount)).font(Font.regular(size: 13)).foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                                        }.frame(maxWidth:.infinity).padding(.vertical,8.5)
                                    }.frame(width:57)
                                }
                                .overlay(Color(dynamicSpanLineColor6).frame(height:0.5).padding(.leading, 12), alignment: .bottom)
                                .contentShape(Rectangle())
                            }
                        }
                    }
                    if !isFirstLoad {
                        Text("仅显示您近半年的反馈".localized).font(Font.regular()).foregroundColor(Color(UIColor(hex: 0xBCBCBC))).padding(.vertical, 12)
                    }
                }
                .frame(maxWidth: .infinity,maxHeight:.infinity)
                .background(Color(dynamicBackgroundColor4))
                .overlay(Group {
                    if self.isFirstLoad{
                        ActivityIndicator()
                    }else if self.mv.feedbacks.count == 0 {
                        EmptyView()
//                        Text("未查到商品".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                    } else {
                        EmptyView()
                    }
                }).onReceive(NotificationCenter.default.publisher(for: FeedbackListV.refreshFeedbackTitleListNotification), perform: { noti in
                    //收到通知，可进入或返回页面前刷新，增强体验。
                    refresh()
                }).onAppear {
                    refresh()
                }
                
                Spacer()
                RoutedLink(toRoute: .feedbackCreate) {
                    Text("新问题反馈".localized).font(Font.regular().bold()).foregroundColor(mainColor).background(Color.clear)
                        .frame(height:40).frame(maxWidth:.infinity)
                        .overlay(RoundedRectangle(cornerRadius: 100).stroke(mainColor, lineWidth: 1))
                        .padding(.horizontal, 50)
                        .padding(.bottom,30)
                }
            }
        }
    }
}

#if DEBUG
struct FeedbackListV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            FeedbackListV(mv:FeedbackListModelView(test_Feedbacks))

            FeedbackListV(mv:FeedbackListModelView(test_Feedbacks))
            .environment(\.colorScheme, .dark)
            .previewDevice("iPhone SE (2nd generation)")
           
        }
    }
}
#endif
