//
//  ReaderFeedbackV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/5/10.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SnapKit
import UIKit
import shared

class ReaderFeedbackV: UIView,ReaderFeedbackContentVDelegate {
    static var feedbackView:ReaderFeedbackV?
    var jsonDic:[String:Any]
    var onCommit:(()->())? = nil
    var onCancel:(()->())? = nil
    
    var vc:UIHostingController<ReaderFeedbackContentV>?
    
    init(jsonDic:[String:Any],onCommit:(()->())? = nil,onCancel:(()->())? = nil) {
        self.jsonDic = jsonDic
        self.onCommit = onCommit
        self.onCancel = onCancel
        
        super.init(frame: CGRect.zero)
        backgroundColor = dynamicAlphaBackgroundColor1
        
        //        let btn = UIButton()
        //        btn.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
        //        addSubview(btn)
        //        btn.snp.makeConstraints { (make) in
        //            make.edges.equalToSuperview()
        //        }
        weak var weakself = self
        vc = UIHostingController(rootView: ReaderFeedbackContentV(delegate:self, jsonDic: jsonDic,
           onCommit: {
            ReaderFeedbackV.hide()
            weakself?.onCommit?()
        }, onCancel:{
            ReaderFeedbackV.hide()
            weakself?.onCancel?()
        }))
        addSubview(vc!.view)
        vc!.view.layer.cornerRadius = 6
        vc!.view.snp.makeConstraints { make in
            make.width.equalTo(275)
            make.height.equalTo(345)
            make.center.equalTo(self)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    static func show(jsonDic:[String:Any],on superV:UIView,onCommit:(()->())? = nil,onCancel:(()->())? = nil) {
        hide()
        
        feedbackView = ReaderFeedbackV(jsonDic: jsonDic, onCommit:onCommit,onCancel:onCancel)
        superV.addSubview(feedbackView!)
        feedbackView!.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
    }
    
    static func hide(){
        feedbackView?.removeFromSuperview()
        feedbackView = nil
    }
    
    @objc func tapClose(){
        ReaderFeedbackV.hide()
    }
    
    func readerFeedbackContentVOnSizeChanged(size: CGSize) {
        vc?.view.snp.updateConstraints { make in
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
        }
    }
}


protocol ReaderFeedbackContentVDelegate{
    func readerFeedbackContentVOnSizeChanged(size:CGSize)
}
struct ReaderFeedbackContentV: View {
    @ObservedObject var mv = FeedbackTagsMV()
    var delegate:ReaderFeedbackContentVDelegate?
    var jsonDic:[String:Any]
    var onCommit:(()->())? = nil
    var onCancel:(()->())? = nil
    
//    init(mv:FeedbackTagsMV = FeedbackTagsMV(),jsonString:String,delegate:ReaderFeedbackContentVDelegate?,onCommit:(()->())? = nil,onCancel:(()->())? = nil) {
//        _mv = ObservedObject(initialValue: mv)
//        self.jsonString = jsonString
//        debugPrint("测试ReaderFeedbackContentV1:\(jsonString)")
//        debugPrint("测试ReaderFeedbackContentV2:\(self.jsonString)")
//        self.delegate = delegate
//        self.onCommit = onCommit
//        self.onCancel = onCancel
//    }
    
    func getFeedbackTagList(){
        WDBookUserSDK.shared.getFeedbackTagList(entrance:3) { result in
            switch result{
            case .success(let list):
                if let entities = list, entities.count > 0 {
                    mv.feedbackTags.removeAll()
                    mv.feedbackTags.append(contentsOf: entities.map{FeedbackTag(entity: $0)})
                }else{
                    //空页面
                }
            case .failure(let error):
                break
            }
        }
    }
    
    func createFeedback(){
        var dic = jsonDic
        dic["tagIds"] = mv.feedbackTags.filter{$0.checked}.map{$0.tagId}
        dic["readType"] = mv.feedbackTags.filter{$0.checked}.map{$0.tagName}
        if let jsonData = try? JSONSerialization.data(withJSONObject: dic, options: []),
           let jsonString = String(data: jsonData, encoding: .utf8){
            HUDManager.showLoadingBlockHUD(text: "")
            let entity = FeedbackSaveEntity()
            entity.entrance = 3
            entity.tagIdList = NSMutableArray(array: mv.feedbackTags.filter{$0.checked}.map{$0.tagId})
            entity.messageText = jsonString
            WDBookUserSDK.shared.saveFeedback(entity: entity) { result in
                HUDManager.hideLoadingHUD()
                switch result{
                case .success(let feedbackId):
                    Toaster.showToast(message: "感谢您的反馈".localized, duration: 1.2) { success in
                        onCommit?()
                    }
                    break
                case .failure(let error):
                    Toaster.showToast(message: error.localizedDescription, position: .center)
                    break
                }
            }
        }
        
        
    }
    
    var body: some View {
        ZStack {
            
            LazyVStack(spacing:0) {
                Text("纠错").font(Font.regular(size: 18).bold()).foregroundColor(Color(dynamicTextColor19)).frame(height:55).frame(maxWidth:.infinity)
                
                if mv.feedbackTags.count > 0{
                    ForEach(Array(mv.feedbackTags.enumerated()),id: \.1.tagId) { index,tag in
                        HStack {
                            Text(tag.tagName).foregroundColor(Color(dynamicTextColor19)).font(Font.regular(size: 16))
                                .padding(.leading, 16)
                            Spacer()

                            Image(tag.checked ? "checkbox16_check" : "checkbox16_uncheck").frame(width: 16, height: 16)
                                    .padding(.horizontal, 8)
                                    .frame(maxHeight:.infinity)
                            
                        }.frame(height:50)
                            .overlay(Color(index == mv.feedbackTags.count - 1 ? UIColor.clear : dynamicBorderColor5).frame(height:0.5).padding(.leading, 12), alignment: .bottom)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                mv.feedbackTags[index].checked = !tag.checked
                            }
                        
                    }
                }else{
                    ActivityIndicator().frame(height:50)
                }
                
                HStack {
                    Button {
                        onCancel?()
                    } label: {
                        Text("取消".localized).frame(maxWidth:.infinity)
                    }
                    Button {
                        guard mv.feedbackTags.filter({$0.checked}).count > 0 else{
                            onCancel?()
                            return
                        }
                        createFeedback()
                        
                    } label: {
                        Text("提交".localized).frame(maxWidth:.infinity)
                    }
                    
                }.font(Font.regular()).foregroundColor(.white).frame(height:40).background(mainColor)
            }.ignoresSafeArea()
                .frame(width: 275)
                .background(Color(dynamicBackgroundColor15))
                .cornerRadius(6)
            //        }.ignoresSafeArea()
            //            .frame(maxWidth:.infinity,maxHeight: .infinity)
            //            .background(Color(dynamicAlphaBackgroundColor1))
            //        .onTapGesture {
            //            presentationMode.wrappedValue.dismiss()
            //        }
            
        }
        .readSize(onChange: { size in
            delegate?.readerFeedbackContentVOnSizeChanged(size: size)
        }).onAppear {
            getFeedbackTagList()
        }
    }
}

#if DEBUG
struct ReaderFeedbackV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            ZStack {
                ReaderFeedbackContentV(mv:FeedbackTagsMV(test_ReaderFeedbackTags), jsonDic: [:])
            }.frame(maxWidth:.infinity,maxHeight: .infinity)
                .background(Color(dynamicAlphaBackgroundColor1))
            
            ZStack {
                ReaderFeedbackContentV(mv:FeedbackTagsMV(test_ReaderFeedbackTags), jsonDic: [:])
            }.frame(maxWidth:.infinity,maxHeight: .infinity)
                .background(Color(dynamicAlphaBackgroundColor1))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (2nd generation)")
        }
    }
}
#endif

struct SizePreferenceKey: PreferenceKey {
    static var defaultValue: CGSize = .zero
    static func reduce(value: inout CGSize, nextValue: () -> CGSize) {}
}

extension View {
    func readSize(onChange: @escaping (CGSize) -> Void) -> some View {
        background(
            GeometryReader { geometryProxy in
                Color.clear
                    .preference(key: SizePreferenceKey.self, value: geometryProxy.size)
            }
        )
        .onPreferenceChange(SizePreferenceKey.self, perform: onChange)
    }
}
