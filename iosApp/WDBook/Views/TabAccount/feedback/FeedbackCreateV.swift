//
//  FeedbackCreateV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2022/5/7.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import Combine
import shared
import SwiftUI

class FeedbackTagsMV: ObservableObject {
    @Published var feedbackTags: [FeedbackTag] = []

    init(_ feedbackTags: [FeedbackTag]? = nil) {
        if let list = feedbackTags {
            self.feedbackTags = list
        }
    }
}

struct FeedbackCreateV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @State var isFirstLoad = true
    @ObservedObject var mv = FeedbackTagsMV()
    @State var selectedIndex = 0
    @State var text = ""
    @State var entrance = 1
    let CharacterLimit = 3000
    @Namespace var TopID
    @Namespace var EditorID
    @State var isPresentEmpty = false

    func getFeedbackTagList() {
        WDBookUserSDK.shared.getFeedbackTagList(entrance: Int32(entrance)) { result in
            switch result {
            case let .success(list):
                if let entities = list, entities.count > 0 {
                    mv.feedbackTags.removeAll()
                    mv.feedbackTags.append(contentsOf: entities.map { FeedbackTag(entity: $0) })
                } else {
                    // 空页面
                    isPresentEmpty = true
                }
            case let .failure(error):
                isPresentEmpty = true
            }
            isFirstLoad = false
        }
    }

    func createFeedback() {
        HUDManager.showLoadingBlockHUD(text: "")
        let entity = FeedbackSaveEntity()
        entity.entrance = 1
        entity.tagIdList = [mv.feedbackTags[selectedIndex].tagId]
        entity.messageText = text
        WDBookUserSDK.shared.saveFeedback(entity: entity) { result in
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(feedbackId):
                Toaster.showToast(message: "感谢您的反馈".localized, duration: 1.5) { _ in
                    presentationMode.wrappedValue.dismiss()
                }
            case let .failure(error):
                Toaster.showToast(message: error.localizedDescription, position: .center)
            }
        }
    }

    var body: some View {
        BackNavigation(title: "意见反馈".localized) {
            ScrollViewReader { (proxy: ScrollViewProxy) in
                //            GeometryReader { proxy in
                ScrollView {
                    ZStack {
                        if isFirstLoad {
                            EmptyView()
                        } else {
                            VStack(spacing: 0) {
                                Text("问题类型".localized).font(Font.regular().bold()).foregroundColor(Color(dynamicTextColor19))
                                    .frame(maxWidth: .infinity, alignment: .leading).id(TopID)
                                Spacer().frame(height: 10)
                                FlowLayout(mode: .scrollable,
                                           items: mv.feedbackTags) { index, item in
                                    Text(item.tagName)
                                        .font(.system(size: 14))
                                        .foregroundColor(Color(index == selectedIndex ? UIColor.white : dynamicTextColor26))
                                        .lineLimit(1)
                                        .padding(EdgeInsets(top: 7, leading: 16, bottom: 7, trailing: 16))
                                        .background(RoundedRectangle(cornerRadius: 100).foregroundColor(Color(index == selectedIndex ? UIColor(hex: 0x24B144) : dynamicBtnBGColor4)))
                                        .onTapGesture {
                                            selectedIndex = index
                                            UIApplication.dismissKeyboard()
                                        }
                                }
                                Spacer().frame(height: 24)
                                Text("问题和意见（必填）".localized).font(Font.regular().bold()).foregroundColor(Color(dynamicTextColor19))
                                    .frame(maxWidth: .infinity, alignment: .leading)
                                Spacer().frame(height: 10)

                                ZStack {
                                    TextEditor(text: $text).font(Font.regular())
                                        .disableAutocorrection(true)
                                        .padding(5)
                                        .onChange(of: text, perform: { newValue in
                                            if text.count > CharacterLimit {
                                                text = String(newValue.prefix(CharacterLimit))
                                                Toaster.showToast(message: "输入长度不能超过%lld个字符".localizedFormat(CharacterLimit), position: .center)
                                            }
                                        })
                                    //                                .onSubmit {
                                    //                                    debugPrint(content)
                                    //                                }
                                }.frame(maxWidth: .infinity)
                                    .frame(height: 158)
                                    .background(ZStack {
                                        Color(dynamicBackgroundColor11)
                                        Text("我们重视您的每一条反馈，请描述您遇到的问题".localized)
                                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                                            .padding(12)
                                            .font(Font.regular()).foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                                            .opacity(text.isEmpty ? 1 : 0)
                                    }).cornerRadius(8)
                                    .id(EditorID)

                                Spacer().frame(height: 24)
                                Button {
                                    createFeedback()
                                } label: {
                                    Text("提交".localized).font(Font.regular(size: 14))
                                        .disabled(text.isEmpty)
                                        .foregroundColor(.white)
                                        .frame(height: 40).frame(maxWidth: .infinity)
                                        .background(text.isBlank ? Color(mainUIColor.alpha(0.5)) : mainColor).cornerRadius(20)
                                        .padding(.horizontal, 50)
                                }.disabled(text.isBlank || mv.feedbackTags.count == 0)
                            }.padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                                .background(Color.clear.frame(maxWidth: .infinity, maxHeight: .infinity)
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        UIApplication.dismissKeyboard()
                                    })
                        }

                    }.frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color.clear.frame(maxWidth: .infinity, maxHeight: .infinity))

                }.frame(maxWidth: .infinity, maxHeight: .infinity)
                    .background(Color(dynamicBackgroundColor12))
                    .onReceive(Publishers.keyboardHeight) { keyboardHeight in
                        withAnimation {
                            if keyboardHeight > 0 {
                                proxy.scrollTo(EditorID)
                            } else {
                                proxy.scrollTo(TopID)
                            }
                        }

                        //                        debugPrint("键盘高度:\(keyboardHeight)")
                        //                        debugPrint("Scrollview高度:\(proxy.size.height)")
                    }
                    .animation(.easeOut(duration: 0.16))
                    .overlay(Group {
                        if self.isFirstLoad {
                            ActivityIndicator()
                        } else if self.mv.feedbackTags.count == 0 {
                            EmptyView()
//                            Text("未查到数据".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                        } else {
                            EmptyView()
                        }
                    })
                    .alert(isPresented: $isPresentEmpty, content: {
                        Alert(title: Text(""),
                              message: Text("网络异常，暂时无法反馈".localized),
                              dismissButton: .cancel(Text("确定".localized)) {
                                  presentationMode.wrappedValue.dismiss()
                              })
                    }).onAppear {
                        UITextView.appearance().backgroundColor = .clear

                        // 正式环境
                        if mv.feedbackTags.count == 0 {
                            getFeedbackTagList()
                        }
                    }
            }
        }
    }
}

#if DEBUG
    struct FeedbackCreateV_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                FeedbackCreateV(mv: FeedbackTagsMV(test_FeedbackTags))

                FeedbackCreateV(mv: FeedbackTagsMV(test_FeedbackTags))
                    .environment(\.colorScheme, .dark)
                    .previewDevice("iPhone SE (2nd generation)")
            }
        }
    }
#endif
