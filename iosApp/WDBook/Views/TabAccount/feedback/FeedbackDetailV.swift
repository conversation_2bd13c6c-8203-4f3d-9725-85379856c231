//
//  FeedbackDetailV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2022/5/10.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import CoreGraphics
import shared
import SwiftUI

class FeedbackDetailMV: ObservableObject {
    @Published var feedbackDetails: [FeedbackDetail] = []

    init(_ params: [FeedbackDetail]? = nil) {
        if let datas = params {
            feedbackDetails = datas
        }
    }
}

struct FeedbackDetailV: View {
    @EnvironmentObject var appState: AppState
    @ObservedObject var mv = FeedbackDetailMV()
    @State var feedbackId: Int64
    @State var content = ""
    @State var isFirstLoad = true
    @State var textEditorHeight: CGFloat = 32
    @State var headerRefreshing = false
    let CharacterLimit = 3000
    @State var needScrollToBottom = false

    func getFeedbackDetailList(isScrollToBottom: Bool = false) {
        WDBookUserSDK.shared.getFeedbackDetailList(feedbackId: feedbackId) { result in
            switch result {
            case let .success(list):
                if let entities = list, entities.count > 0 {
                    mv.feedbackDetails.removeAll()
                    mv.feedbackDetails.append(contentsOf: entities.map { FeedbackDetail(entity: $0) })
                    needScrollToBottom = isScrollToBottom
                }
            case let .failure(error):
                break
            }
            isFirstLoad = false
            headerRefreshing = false
            HUDManager.hideLoadingHUD()
        }
    }

    func replyFeedback() {
        HUDManager.showLoadingBlockHUD(text: "")
        let entity = FeedbackSaveEntity()
        entity.feedbackId = feedbackId
        entity.messageText = content
        WDBookUserSDK.shared.saveFeedback(entity: entity) { result in
            switch result {
            case let .success(feedbackId):
                getFeedbackDetailList(isScrollToBottom: true)
                content = ""
                UIApplication.dismissKeyboard()
            case let .failure(error):
                HUDManager.hideLoadingHUD()
                Toaster.showToast(message: error.localizedDescription, position: .center)
            }
        }
    }

    @Namespace var bottomID
    var body: some View {
        BackNavigation(title: "反馈详情".localized) {
            VStack(spacing: 0) {
                ScrollViewReader { proxy in
                    ScrollView {
                        WDRefreshHeader(refreshing: $headerRefreshing, action: {
                            getFeedbackDetailList()
                            NotificationCenter.default.post(name: FeedbackListV.refreshFeedbackTitleListNotification, object: nil)
                        }).padding(.bottom, 15)

                        LazyVStack(spacing: 0) {
                            ForEach(Array(mv.feedbackDetails.enumerated()), id: \.1.id) { index, item in
                                if index == 0 {
                                    VStack(spacing: 0) {
                                        HStack(spacing: 10) {
                                            UserAvatar().frame(width: 40, height: 40, alignment: .center)
                                            VStack(spacing: 8) {
                                                Text(item.messageType == 1 ? "我".localized : "微读书城系统客服".localized)
                                                    .font(Font.regular()).foregroundColor(Color(dynamicTextColor19)).frame(maxWidth: .infinity, alignment: .leading)
                                                Text(item.createTime.yyyymmdd_hhmm)
                                                    .font(Font.regular(size: 12))
                                                    .foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                                                    .frame(maxWidth: .infinity, alignment: .leading)
                                            }
                                            Spacer()
                                        }.padding(EdgeInsets(top: 10, leading: 16, bottom: 16, trailing: 10))

                                        Text(item.messageText).font(Font.regular().bold()).foregroundColor(Color(dynamicTextColor19))
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .multilineTextAlignment(.leading)
                                            .padding(EdgeInsets(top: 0, leading: 16, bottom: 16, trailing: 10))

                                    }.frame(maxWidth: .infinity)
                                        .overlay(Color(dynamicSpanLineColor6).frame(height: 0.5).padding(.leading, 12), alignment: .bottom)
                                } else {
                                    VStack(spacing: 0) {
                                        HStack(spacing: 10) {
                                            if item.messageType == 1 {
                                                UserAvatar().frame(width: 32, height: 32)
                                            } else {
                                                Image("avatar_system").frame(width: 32, height: 32)
                                            }
                                            Text(item.messageType == 1 ? "我".localized : "微读书城系统客服".localized)
                                                .font(Font.regular())
                                                .foregroundColor(item.messageType == 1 ? Color(dynamicTextColor19) : mainColor)
                                            Text(item.createTime.yyyymmdd_hhmm)
                                                .font(Font.regular(size: 12))
                                                .foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                                                .frame(width: 110)
                                            Spacer()
                                        }.frame(maxWidth: .infinity)
                                            .padding(EdgeInsets(top: 10, leading: 16, bottom: 16, trailing: 4))

                                        Text(item.messageText).font(Font.regular()).foregroundColor(Color(dynamicTextColor19))
                                            .frame(maxWidth: .infinity, alignment: .leading)
                                            .multilineTextAlignment(.leading)
                                            .padding(EdgeInsets(top: 0, leading: 56, bottom: 16, trailing: 10))

                                    }.frame(maxWidth: .infinity)
                                        .overlay(Color(dynamicSpanLineColor6).frame(height: 0.5).padding(.leading, 12), alignment: .bottom)
                                }
                            }
                        }.frame(maxWidth: .infinity, maxHeight: .infinity)
                        Color(dynamicBackgroundColor14).frame(width: 0, height: 0).id(bottomID)
                    }.enableRefresh()
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color(dynamicBackgroundColor14))
                        .contentShape(Rectangle())
                        .onTapGesture {
                            UIApplication.dismissKeyboard()
                        }
                        .onChange(of: mv.feedbackDetails.count) { _ in
                            withAnimation {
                                if needScrollToBottom {
                                    //                                proxy.scrollTo(mv.feedbackDetails.last?.id)
                                    proxy.scrollTo(bottomID)
                                    needScrollToBottom = false
                                }
                            }
                        }
                }

                HStack(alignment: .bottom, spacing: 12) {
                    ZStack {
                        TextEditor(text: $content).font(Font.regular()).disableAutocorrection(true)
                            .frame(height: textEditorHeight).cornerRadius(4)
                            .onChange(of: content, perform: { newValue in
                                if content.count > CharacterLimit {
                                    content = String(newValue.prefix(CharacterLimit))
                                    Toaster.showToast(message: "输入长度不能超过%lld个字符".localizedFormat(CharacterLimit), position: .center)
                                    return
                                }

                                let size = content.boundingRect(with: CGSize(width: UIScreen.main.bounds.width - 88 - (5 * 2), height: .greatestFiniteMagnitude), options: NSStringDrawingOptions.usesLineFragmentOrigin, attributes: [NSAttributedString.Key.font: UIFont.regular()], context: nil).size
                                debugPrint("size:\(size.height)")
                                var height = size.height
                                switch height {
                                case 0 ..< 20: height = 32
                                case 20 ..< 40: height += 6.2 * 2
                                case 40 ..< 60: height += 6.2 * 2
                                default: height = 74
                                }

                                textEditorHeight = height
                                debugPrint(textEditorHeight)
                            })
                    }.background(ZStack {
                        Color(dynamicTextColor27)
                        Text("请输入您的问题或建议".localized)
                            .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                            .padding(EdgeInsets(top: 5, leading: 5.5, bottom: 5, trailing: 5.5))
                            .font(Font.regular()).foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                            .opacity(content.isEmpty ? 1 : 0)
                    }).cornerRadius(8)

                    Button {
                        replyFeedback()
                    } label: {
                        Text("发送".localized).font(Font.regular(size: 12))
                            .frame(width: 56, height: 32)
                            .foregroundColor(Color.white)
                            .background(content.isBlank ? Color(mainUIColor.alpha(0.5)) : mainColor)
                            .cornerRadius(100)
                    }.disabled(content.isBlank)

                }.frame(maxWidth: .infinity)
                    .padding(EdgeInsets(top: 14, leading: 12, bottom: 14, trailing: 8))
                    .background(Color(dynamicSpanLineColor6))
                    .opacity(!isFirstLoad && mv.feedbackDetails.count > 0 ? 1 : 0)

            }.overlay(Group {
                if self.isFirstLoad {
                    ActivityIndicator()
                } else if mv.feedbackDetails.count == 0 {
                    Text("网络连接失败，请稍后再试".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
                } else {
                    EmptyView()
                }
            })
            .onAppear {
                getFeedbackDetailList()
                UITextView.appearance().backgroundColor = .clear
            }
        }
    }
}

#if DEBUG
    struct FeedbackDetailV_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                FeedbackDetailV(mv: FeedbackDetailMV(test_FeedbackDetails), feedbackId: 0)

                FeedbackDetailV(mv: FeedbackDetailMV(test_FeedbackDetails), feedbackId: 0)
                    .environment(\.colorScheme, .dark)
                    .previewDevice("iPhone SE (2nd generation)")
            }
        }
    }
#endif
