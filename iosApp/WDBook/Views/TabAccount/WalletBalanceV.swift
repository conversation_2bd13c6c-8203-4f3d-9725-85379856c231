//
//  WalletBalanceV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/16.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter

struct WalletSettingRow: View {
    @State var title:String
    
    var body: some View {
        HStack {
            Text(title)
                .font(Font.regular(size: 16))
                .foregroundColor(Color(dynamicTitleColor2))
            Spacer()
            Image("arrow_right")
                .foregroundColor(Color(dynamicTitleColor))
        }.frame(minWidth: 0, maxWidth: .infinity)
            .frame(height: 64).padding(.horizontal, 24)
            .background(Color(dynamicBackgroundColor1))
    }
}

struct WalletBalanceV: View {
    @EnvironmentObject var appState:AppState
    @State var isShowTopupV = false
    @State var needShowRecharge = false
    @State var isFirstLoad = true
    @State var isShowRechargeAlert = false
    
    func tapAction(){
        if AppState.shared.iapProductList.count > 0 {
            self.isShowTopupV = true
        } else {
            
            HUDManager.showLoadingBlockHUD(text: "")
            appState.syncIapPurchaseProducts {
                HUDManager.hideLoadingHUD()
                if AppState.shared.iapProductList.count > 0{
                    self.isShowTopupV = true
                }else{
                    Toaster.showToast(message: "未获取到充值订单商品".localized)
                    appState.syncIapPurchaseProducts()
                }
            }
        }
    }

    var body: some View {
        BackNavigation(title: "账户".localized) {
            VStack(alignment: .center, spacing: 0) {
                ScrollView {
                    Spacer().frame(height:24)
                    Text("以下仅显示通过IOS平台充值的余额".localized)
                        .foregroundColor(Color(dynamicTextColor5))
                        .font(Font.regular(size: 12)).frame(height:17)
                    
                    VStack(alignment: .center, spacing: 0){

                        Text("账户余额".localized)
                        .frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:17)
                        .background(Color(dynamicBackgroundColor1))
                        .foregroundColor(Color(dynamicTextColor6))
                        .font(Font.regular(size: 12))
                        
                        Spacer().frame(height:11)
                        
                        Text(appState.walletBalanceStr)
                            .frame(minWidth: 0, maxWidth: .infinity)
                            .frame(height:50)
                            .background(Color(dynamicBackgroundColor1))
                            .foregroundColor(Color(dynamicTitleColor2))
                            .font(Font.semibold(size: 36))
                        
                        Spacer().frame(height:18)
                        if !isTestFlight1() {
                            Button(action: {
                                tapAction()
                            }) {
                                Text("充值".localized).font(Font.medium(size: 16))
                                    .frame(maxWidth: .infinity,maxHeight: .infinity)
                                    .foregroundColor(Color.white)
                            }.frame(minWidth: 0, maxWidth: .infinity)
                                .frame(width:160,height:40)
                                .background(Color(UIColor.primaryColor1))
                                .cornerRadius(20)
                        }
                    }
                    .frame(height: 184)
                    .background(Color(dynamicBackgroundColor1))
                    .cornerRadius(8)
                    .shadow(color: Color(UIColor(hex:0x1D1E20,alpha: 0.1)), radius: 9, x: 0, y: 4)
                    .padding(24)

                    VStack(alignment: .center, spacing: 1){

                        NavigationLink(destination: WalletTranscationsV()) {
                            WalletSettingRow(title: "充值记录".localized)
                        }
                        
                        RoutedLink(toRoute: .purchaseRecords) {
                            WalletSettingRow(title: "购买记录".localized)
                        }

                        Spacer()
                    }
                }.background(Color(dynamicBackgroundColor3))
            }
            .navigationBarTitle("帐户".localized,displayMode: .inline)
            .onAppear {
                appState.refreshWalletBalance()
                if appState.iapProductList.count == 0{
                    appState.syncIapPurchaseProducts()
                }
            }
            .overlay(self.isShowTopupV ? AnyView(TopupView(isPresent: self.$isShowTopupV)) : AnyView(EmptyView()))
            
        }.onReceive(NotificationCenter.default.publisher(for: InAppPurchaseManager.rechargeFailNotification), perform: { _ in
            isShowRechargeAlert = true
        }).overlay(
            Group {
                if isShowRechargeAlert {
                    RechargeAlert(okHandler: {
                        isShowRechargeAlert = false
                    })
                    
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
        
    }
}

#if DEBUG
struct WalletBalanceV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NavigationView{
                WalletBalanceV().environmentObject(AppState.shared)
            }
            NavigationView{
                WalletBalanceV().environmentObject(AppState.shared)
                    .previewDevice("iPhone SE (2nd generation)")
                    .environment(\.colorScheme, .dark)
            }

        }
    }
}
#endif
