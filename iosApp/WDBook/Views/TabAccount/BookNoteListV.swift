//
//  BookNoteListV.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/7/29.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import SDWebImageSwiftUI
import DeviceKit
import NavigationRouter

struct BookNoteListV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.viewController) private var holder
    @StateObject var dataObserable: NoteListObservable
    @State var resourceId:String
    @State var bookNoteCounterEntity: BookNoteCountEntity
    @State private var headerRefreshing: Bool = false
    @State private var footerRefreshing: Bool = false
    @State private var noMore: Bool = true
    @State var showingNoteEditV: Bool = false
    @State var selectedBookNote: Note?
    @State var isFirstLoad = true
    
    init(resourceId:String) {
        _resourceId = State(initialValue: resourceId)
        //        _dataObserable = StateObject(wrappedValue: dataObserable)
        _dataObserable = StateObject(wrappedValue: NoteListObservable(resourceId))
        if let entity = WDBookUserSDK.shared.getBookNoteCountEntity(resourceId: resourceId) {
            _bookNoteCounterEntity = State(initialValue: entity)
        }else if let resource = WDBookUserSDK.shared.getPurchasedResourceEntity(resourceId: resourceId){
            var countEntity = BookNoteCountEntity()
            countEntity.resourceId = resourceId
            countEntity.name = resource.name ?? ""
            countEntity.cover = resource.cover ?? ""
//            countEntity.authorEntityList = resource.authorList //赋值失败
            countEntity.objcAuthorNames = resource.authorNames
            _bookNoteCounterEntity = State(initialValue: countEntity)
        }else{
            _bookNoteCounterEntity = State(initialValue: BookNoteCountEntity())
        }
    }
    
    private func loadData() {
        if let bookNoteCounterEntity = WDBookUserSDK.shared.getBookNoteCountEntity(resourceId: resourceId) {
            self.bookNoteCounterEntity = bookNoteCounterEntity
        }else{
            self.bookNoteCounterEntity.count = 0
        }
        self.dataObserable.refreshNotesData()
        noMore = bookNoteCounterEntity.count == dataObserable.bookNoteList.count
        headerRefreshing = false
    }
    
    private func dataRefresh() {
        WDBookSyncSDK.shared.syncNoteData { [self] result in
            switch result {
            case .success:
                loadData()
            case .failure:
                headerRefreshing = false
                Toaster.showToast(message: "刷新失败，请重新尝试！".localized)
            }
        }
    }
    
    private func dataLoadMore() {
        DispatchQueue.main.asyncAfter(deadline: .now()+1) {
            if !noMore {
                let noteList = dataObserable.getMoreNotesData(offset: dataObserable.bookNoteList.count, limit: 10)
                dataObserable.bookNoteList.append(contentsOf: noteList)
                noMore = bookNoteCounterEntity.count == dataObserable.bookNoteList.count
            }
            footerRefreshing = false
        }
    }
    
    private func openBook(note: Note) {
        self.selectedBookNote = note
        RoutableManager.pushBookReader(resourceId: note.noteEntity!.resourceId,chapterPath: selectedBookNote!.chapterPath,contentOffset: selectedBookNote!.range.location)
    }
    
    var body: some View {
        BackNavigation(title: "笔记列表".localized,isHideBackButton:false,isHideBottomLine:true) {
            VStack {
                List {
                    if Device.current.isSimulator{
                        WDRefreshHeader(refreshing: $headerRefreshing, action: {
                            self.dataRefresh()
                        }).listRowInsets(EdgeInsets.zero)
                    }else{
                        let insets:UIEdgeInsets = UIApplication.shared.windows[0].safeAreaInsets
                        WDRefreshHeader2(refreshing: $headerRefreshing, action: {
                            self.dataRefresh()
                        }).frame(height:insets.bottom > 0 ? 88 : 64).listRowInsets(EdgeInsets.zero)
                    }
                    
                    BookNoteListHeader(bookNoteListEntity: $bookNoteCounterEntity).listRowInsets(EdgeInsets.zero)
                    
                    ForEach(Array(dataObserable.bookNoteList.enumerated()), id: \.1.id) { (index, note) in
                        BookNoteListCell(bookNote: note, noteIndex: index) {
                            let resourceDownloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: note.noteEntity!.resourceId)
                            if resourceDownloadInfo?.downloadStatus == .complete {
                                openBook(note: note)
                            } else {
                                guard NetReachability.isReachability() else{
                                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                                    return
                                }
                                
                                guard WDBookSessionSDK.shared.isLogin else{
                                    AppState.shared.showLoginRegisterVAndPopToStore()
                                    return
                                }

                                AppState.shared.alert.bookNoteDownloadAlert(title: "提示".localized, msg: "请先下载本书，才可进入阅读模式".localized) { [self] in
                                    Log.d("开始下载")
                                    if(AppState.shared.shelfItems.filter( {$0.resourceId == note.noteEntity?.resourceId}).first == nil){
                                        AppState.shared.shelfItems.append(getShelfBookEntity(resourceId: note.noteEntity?.resourceId ?? ""))
                                    }
                                    if let item = AppState.shared.shelfItems.filter( {$0.resourceId == note.noteEntity?.resourceId}).first{
                                        holder?.present(builder: {
                                            BookDownloadingView(bookItem: item) {
                                                openBook(note: note)
                                            }
                                        })
                                    }
                                    
                                } cancelHandler: {
                                    Log.d("不下载，取消")
                                }
                            }
                        } editNoteAction: { [self] in
                            self.dataObserable.selectedIndex = index
                            self.showingNoteEditV = true
                        } deleteNoteAction: {
                            AppState.shared.alert.deleteNoteAlert(title: "删除笔记".localized, msg: "该笔记删除后，可在笔记回收站找回".localized) { [self] in
                                DispatchQueue.main.async {
                                    WDBookUserSDK.shared.removeNote(dataId: note.id)
                                    self.dataObserable.refreshNotesData()
                                    if let bookNoteCounterEntity = WDBookUserSDK.shared.getBookNoteCountEntity(resourceId: bookNoteCounterEntity.resourceId) {
                                        self.bookNoteCounterEntity = bookNoteCounterEntity
                                    }
                                    NotificationCenter.default.post(name: NoteBookmarkNotifications.noteDeletedNotification, object: note)
                                    if dataObserable.bookNoteList.count == 0 {
                                        self.presentationMode.wrappedValue.dismiss()
                                    }
                                }
                            } cancelHandler: {
                                Log.d("取消")
                            }
                        }
                        .listRowInsets(EdgeInsets.zero)
                        .onTapGesture {
                            selectedBookNote = dataObserable.bookNoteList[index]
                            RoutableManager.showNoteDetailV(noteDataId: selectedBookNote?.id ?? "", dataObserable: dataObserable)
                        }
                    }.onDelete(perform: removeRow).listRowInsets(EdgeInsets.zero)
                    
                    if dataObserable.bookNoteList.count > 0 {
                        if !noMore {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                                self.dataLoadMore()
                            }
                        }
                    }
                }
                .enableRefresh()
                .listStyle(PlainListStyle())
                .frame(maxWidth:.infinity,maxHeight: .infinity)
                
                Divider()
                    .background(Color(dynamicSpanLineColor2))
                    .scaleEffect(CGSize(width: 1, height: 1))
                
                RoutedLink(to: RouterName.notesRecycleWithResourceId.withParam(bookNoteCounterEntity.resourceId)) {
                    Text("笔记回收站".localized)
                        .font(.system(size: 14))
                        .padding(.top, 16)
                        .padding(.bottom, 32)
                        .foregroundColor(Color(dynamicTextColor11))
                }
                
            }.frame(maxWidth:.infinity,maxHeight: .infinity)
                .onAppear(perform: {
                    if isFirstLoad {
                        loadData()
                    }
                    isFirstLoad = false
                })
                .onReceive(NotificationCenter.default.publisher(for: NoteEditerVC.noteChangedNotification), perform: { noti in
                    loadData()
                })
                .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteDeletedNotification), perform: { noti in
                    loadData()
                })
                .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteResumeNotification), perform: { noti in
                    loadData()
                })
                .sheet(isPresented: self.$showingNoteEditV){
                    NoteEditerVCUIKit(note: dataObserable.bookNoteList[self.dataObserable.selectedIndex], needPostNoti: true)
                }
        }
    }
    
    func removeRow(at offsets: IndexSet) {
        offsets.forEach { offset in
            let note = dataObserable.bookNoteList[offset]
            WDBookUserSDK.shared.removeNote(dataId: note.id)
            self.dataObserable.refreshNotesData()
            if let bookNoteCounterEntity = WDBookUserSDK.shared.getBookNoteCountEntity(resourceId: bookNoteCounterEntity.resourceId) {
                self.bookNoteCounterEntity = bookNoteCounterEntity
            }
            NotificationCenter.default.post(name: NoteBookmarkNotifications.noteDeletedNotification, object: note)
        }
    }
}

struct BookNoteListHeader: View {
    @Binding var bookNoteListEntity: BookNoteCountEntity
    var body: some View {
        HStack(alignment: .center, spacing: 20) {
            ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(self.bookNoteListEntity.cover)))
                .resizable()
                .placeholder{
                    Image("cover_92*132").resizable().frame(width: NoteCollectionV.itemMiniWidth, height: NoteCollectionV.itemMinHeight, alignment: .center)
                }
                .transition(.fade(duration: 0.5)) // Fade Transition with duration
                .cornerRadius(4)
                .frame(width: NoteCollectionV.itemMiniWidth, height: NoteCollectionV.itemMinHeight, alignment: .center)
            
            VStack(spacing: 10) {
                Text(bookNoteListEntity.name)
                    .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                    .font(Font.medium(size: 20))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)
                
                Spacer()
                Text(bookNoteListEntity.authorNames)
                    .foregroundColor(Color.secondary)
                    .font(Font.medium(size: 14))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
                
                Text("%lld 条笔记".localizedFormat(bookNoteListEntity.count))
                    .font(Font.medium(size: 14))
                    .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .leading)
            }.foregroundColor(Color(dynamicTitleColor2))
            Spacer()
        }.padding(.top, 8)
            .padding(.bottom, 8)
            .padding(.horizontal, 24)
    }
}

struct BookNoteListCell: View {
    @ObservedObject var bookNote: Note
    @State var noteIndex: Int
    
    var openBookAction: (() -> ())?
    var editNoteAction: (() -> ())?
    var deleteNoteAction: (() -> ())?
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack(spacing: 12) {
                Color(bookNote.style.showColor)
                    .frame(width: 8, height: 8, alignment: .center)
                    .cornerRadius(4.0)
                
                Text("\(Int64(bookNote.createTime.timeIntervalSince1970).yyyymmddhhmm)")
                    .font(.regular(size: 12))
                    .foregroundColor(Color(dynamicTextColor14))
                    .frame(alignment: .center)
                
                Spacer()
                
                Button {
                    self.openBookAction?()
                } label: {
                    Image("mine_note_read")
                        .foregroundColor(Color.gray)
                        .frame(width: 24.0, height: 24.0, alignment: .center)
                }.buttonStyle(BorderlessButtonStyle())
                
                Button {
                    self.editNoteAction?()
                } label: {
                    Image("mine_note_edit")
                        .foregroundColor(Color.gray)
                        .frame(width: 24.0, height: 24.0, alignment: .center)
                }.buttonStyle(BorderlessButtonStyle())
                
                Button {
                    self.deleteNoteAction?()
                } label: {
                    Image("mine_note_delete")
                        .foregroundColor(Color.gray)
                        .frame(width: 24.0, height: 24.0, alignment: .center)
                }.buttonStyle(BorderlessButtonStyle())
            }
            .frame(alignment: .bottomLeading)
            
            if bookNote.noteText == "" {
                LTRLabel(text: bookNote.summary,textColor: dynamicTitleColor2,font: UIFont.systemFont(ofSize: 16),lineLimit: 3,lineSpacing: 4,horizontalPadding:24)
                    .lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
                    .font(.system(size: 16))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .lineSpacing(4)
            } else {
                HStack(spacing: 4) {
                    Rectangle()
                        .fill(Color(dynamicSpanLineColor4))
                        .frame(width: 2)
                    
                    Text("引用 | %@".localizedFormat(bookNote.summary))
                        .lineLimit(2)
                        .fixedSize(horizontal: false, vertical: true)
                        .font(.regular(size: 14))
                        .foregroundColor(Color(dynamicSpanLineColor4))
                        .lineSpacing(4)
                }
                
                Group{
                    if bookNote.isConflict{
                        Text("冲突笔记".localized).foregroundColor(Color(UIColor(hex: 0xDD5B56))) + Text("  | \(bookNote.noteText)").foregroundColor(Color(dynamicTitleColor2))
                    }else{
                        LTRLabel(text: bookNote.noteText,textColor: dynamicTitleColor2,font: UIFont.systemFont(ofSize: 16),lineLimit: 3,lineSpacing: 4,horizontalPadding:24)
                            .foregroundColor(Color(dynamicTitleColor2))
                    }
                }.lineLimit(3)
                    .fixedSize(horizontal: false, vertical: true)
                    .font(.system(size: 16))
                    .lineSpacing(4)
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.vertical,10)
        .padding(.horizontal, 24)
    }
}
