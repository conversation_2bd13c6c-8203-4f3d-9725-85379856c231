//
//  OrderDetailsV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/23.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct OrderDetailsV: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    var order:OrderEntity
    @State var isShowBindingEmailAlert = false
    @State var isShowOrderReceiptAlert = false
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
                Button(action: {
                    if appState.userInfo.email.isBlank{
                        isShowBindingEmailAlert = true
                        
                    }else{
                        HUDManager.showLoadingBlockHUD(text: "")
                        WDBookUserSDK.shared.getOrderReceipt(orderId: order.orderId) { result in
                            HUDManager.hideLoadingHUD()
                            switch result{
                            case .success(let r):
                                isShowOrderReceiptAlert = true
                            case .failure(let e):
                                break
                            }
                        }
                    }
                    
                }) {
                    HStack {
                        Text("获取收据".localized).font(Font.regular())
                        .foregroundColor(Color(dynamicTextColor34))
                    }.frame(maxHeight:.infinity).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 4))
                }
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .overlay(
                Text("订单详情".localized)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18)), alignment: .center)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .overlay(
            Group {
                if isShowBindingEmailAlert {
                    BindingEmailAlert(cancelHandler: {
                        isShowBindingEmailAlert = false
                    }, okHandler: {
                        isShowBindingEmailAlert = false
                        RoutableManager.showBindingEmailV(returnView: OrderDetailsV.self)
                    })
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
        .overlay(
            Group {
                if isShowOrderReceiptAlert {
                    OrderReceiptAlert(email: appState.userInfo.email) {
                        isShowOrderReceiptAlert = false
                    }
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
    }
    
    var content: some View {
            ScrollView() {
                Spacer().frame(height:8)
                if order.type == 1{
                    OrderGiftItem(o: order,isSimpleNum: true)
                }else{
                    LazyVStack(spacing:0) {
                        ForEach((order.products ?? []),id:\.productId) { p in
                            OrderProductItem(p: p,isSimpleNum: true)
                        }
                    }
                }
                
                Spacer().frame(height:8)
                
                VStack(alignment: .leading, spacing: 8, content: {
                    OrderDetail("订单时间：".localized, orderDetail: order.lastUpdateTime.yyyymmddhhmm)
                    OrderDetail("订单编号：".localized, orderDetail: order.orderId)
                    if order.payMethod != "-"{
                        OrderDetail("支付方式：".localized, orderDetail: order.payMethod)
                    }
                    
                })
                .frame(maxWidth:.infinity)
                .padding(EdgeInsets(top: 16, leading: 24, bottom: 16, trailing:24))
                .background(Color(dynamicTextColor27))
                Spacer().frame(height:8)
                
                VStack(alignment: .leading, spacing: 8, content: {
//                    OrderDetailRow("商品总额：", orderDetail: "$\((order.totalAmount + order.couponAmount + order.deductedAmount + order.activityAmount + order.giftCardAmount + order.walletBalance).fractionDigitsDot2)")
                    OrderDetailRow("商品总额：".localized, orderDetail: "$\(order.originalPrice.fractionDigitsDot2)")
//                    if order.deductedAmount > 0{
//                        NavigationLink(
//                            destination: OrderDeductedProductsV(products: order.deductedProducts as? [OrderEntity.OrderDeductedProduct] ?? []),
//                            label: {
//                                OrderDetailRow("已购商品：".localized, orderDetail: "- $\(order.deductedAmount.fractionDigitsDot2)")
//                            })
//                    }
                    if order.type != 1{
                        if order.deductedAmount > 0{
                            OrderDetailRow("已购书籍".localized, orderDetail: "-$\(order.deductedAmount.fractionDigitsDot2)")
                        }
                        
                        if order.couponAmount > 0{
                            OrderDetailRow("优惠券".localized, orderDetail: "-$\(order.couponAmount.fractionDigitsDot2)")
                        }
                        
                        if order.giftCardAmount > 0{
                            OrderDetailRow("礼品卡".localized, orderDetail: "-$\(order.giftCardAmount.fractionDigitsDot2)")
                        }
                    }
                    
                    if order.walletBalance > 0{
                        OrderDetailRow("账户余额".localized, orderDetail: "-$\(order.walletBalance.fractionDigitsDot2)")
                    }
                    
                    HStack{
                        Spacer()
                        Text("实付款：".localized).foregroundColor(Color(dynamicTextColor19)).font(Font.regular())
                        Text("$\(order.totalAmount.fractionDigitsDot2)").foregroundColor(Color(hex:0xFF342A)).font(Font.regular().bold())
                    }
                })
                .frame(maxWidth:.infinity)
                .padding(EdgeInsets(top: 16, leading: 24, bottom: 16, trailing:24))
                .background(Color(dynamicTextColor27))
            }
            .frame(maxWidth:.infinity,maxHeight: .infinity,alignment: .top)
            .background(Color(dynamicBackgroundColor3))
    }

    private func OrderDetailRow(_ title: String, orderDetail: String) -> some View {
        HStack{
            Text(title).foregroundColor(Color(hex:0x999999)).font(Font.regular())
            Spacer()
            Text(orderDetail).foregroundColor(Color(dynamicTextColor19)).font(Font.regular().bold())
        }
    }
    
    private func OrderDetail(_ title: String, orderDetail: String) -> some View {
        HStack{
            Text(title).foregroundColor(Color(hex:0x999999)).font(Font.regular())
            Text(orderDetail).foregroundColor(Color(dynamicTextColor19)).font(Font.regular().bold())
            Spacer()
        }
    }
}

#if DEBUG
struct OrderDetailsV_Previews: PreviewProvider {
    static var testOrderList:[OrderEntity]{
        let jsonString = loadJsonString(from: test_json_OrderList)
        let list = OrderEntity().testModels(jsonString: jsonString) as! [OrderEntity]
        return list
    }
    
    static var previews: some View {
        OrderDetailsV(order: testOrderList[2]).environmentObject(AppState.shared)
    }
}
#endif
