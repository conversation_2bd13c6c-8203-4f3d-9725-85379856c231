//
//  RechargeAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2024/1/15.
//  Copyright © 2024 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct RechargeAlert: View {
    var okHandler:()->()
    @State private var textHeight: CGFloat = 0
    
    var textAttributedStr:NSAttributedString{
        let attributedString = NSMutableAttributedString(string: "很抱歉，您的充值未能成功。请访问微读书城官网继续购买，或联系客服**************寻求帮助。".localized)
        let range1 = (attributedString.string as NSString).range(of: "微读书城官网".localized)
        
        attributedString.addAttribute(.link, value: "https://wdbook.com", range: range1)
        
        attributedString.addAttribute(.font, value: UIFont.regular(size: 16), range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.foregroundColor, value: dynamicTitleColor10, range: NSRange(location: 0, length: attributedString.length))
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        paragraphStyle.lineSpacing = 8
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))
        
        return attributedString
    }
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text("提示".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                AttributedTextView(
                    attributedText: .constant(textAttributedStr),
                    height: $textHeight,
                    linkTextAttributes: [.foregroundColor: UIColor(hex:0x4D95F7)],
                    onLinkTapped: { url in
                        print("Link tapped: \(url.absoluteString)")
                        UIApplication.shared.open(url)
                        okHandler()
                    }
                ).frame(height:textHeight + 4)
                    .padding(.bottom,20)
                
                Button(action: {
                    okHandler()
                }) {
                    Text("确定".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:40)
                    .background(mainColor)
                    .cornerRadius(20)
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal, UIDevice.alertAdjustHorizontalPadding)
                .frame(width: UIDevice.alertAdjustFrameWidth)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
    }
}

#if DEBUG
#Preview {
    RechargeAlert {
        
    }
}
#endif
