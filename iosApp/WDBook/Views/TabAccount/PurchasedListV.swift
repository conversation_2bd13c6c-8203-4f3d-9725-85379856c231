//
//  PurchasedListV.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/5/21.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import shared

struct PurchasedListV: View {
    @EnvironmentObject var appState:AppState
    @State var isNavigationHide:Bool = false
    
    var body: some View {
        BackNavigation(title: isTestFlight1() ? "我的书籍".localized : "已购书籍".localized){
            ZStack {
                ScrollView {
                    ForEach(appState.purchasedListResources, id:\.resourceId) {  resource in
                        PurchasedListRow(item:resource) {
                            RoutableManager.navigate(toPath: RouterName.readerWithId.withParam(resource.resourceId))
                        }
                    }
                }.padding(.bottom, 16)
                
            }
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor4))
            .overlay(Group {
                
                if appState.purchasedListResources.count == 0 {
                    Text("您还没有购买过书籍".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                } else {
                    EmptyView()
                }
            }).onAppear {
                
            }
        }.ignoresSafeArea()
    }
}

struct PurchasedListRow: View {
    @ObservedObject var downloadObservable: DownloadResourceObservable
    
    @State var canTouch:Bool = true
    
    var openBookAction:(()->())?
    
    init(item:DownloadResourceObservable, action:(()->())? = nil) {
        downloadObservable = item
        openBookAction = action
    }
    
    func startDownload(){
        if self.downloadObservable.needUpgradeApp {
            AppState.shared.alert.showFormatVersionAlert()
            return
        }
        self.canTouch = false
        if self.downloadObservable.fileId == "" {
            WDBookStoreSDK.shared.getResourceFileEntity(resourceId: self.downloadObservable.resourceId) { result in
                switch result {
                case .success(let fileEntity):
                    if let fileId = fileEntity?.fileId {
                        self.downloadObservable.md5 = fileEntity!.md5
                        download(resourceId: self.downloadObservable.resourceId, fileId: fileId)
                    } else {
                        // showTips
                        self.canTouch = true
                        Toaster.showToast(message: "没有获取到文件信息！".localized)
                    }
                case .failure(_):
                    // showTips
                    self.canTouch = true
                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                }
            }
        } else {
            download(resourceId: self.downloadObservable.resourceId, fileId: self.downloadObservable.fileId)
        }
    }
    
    func download(resourceId:String,fileId:String) {
        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in
            self.canTouch = true
            switch result {
            case .success(let entity):
                if let fileDownloadEntity = entity{
                    Log.d("获取downloadurl 成功了")

                    //3.0.4加上的修补逻辑
                    //修补FileDownload表
                    if let downloadDataInfo = WDBookDownloadSDK.shared.fetchDownloadFileEntity(fileId: fileId),
                       downloadDataInfo.downloadStatus == .complete,
                       downloadDataInfo.resourceId.isEmpty{
                        downloadDataInfo.resourceId = resourceId
                        downloadDataInfo.resourceTypeId = "eBook"
                        WDBookDownloadSDK.shared.saveDownloadData(entity: downloadDataInfo)

                        AppState.shared.reloadShelfData()
                        return
                    }

                    //修补UserFileInfo表
                    if let fileinfo = WDBookUserSDK.shared.getUserFileInfo(fileId: fileId){
                       if fileinfo.resourceId.isEmpty{
                          WDBookUserSDK.shared.updatUserFileInfo(fileId: fileId, setResourceId: resourceId)
                          AppState.shared.reloadShelfData()
                          return
                       }
                    }

                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case .success(_):
                            Log.d("开始下载了")
                            AppDownloadManager.shared.start(key: fileId, url: fileDownloadEntity.downloadUrl, destinationFilePath:PathManager.zipPathRelative(fileId), md5:self.downloadObservable.md5)
                        case .failure(_):
                            Log.d("获取加密key失败")
                        }
                    }
                }else{
                    // showTips
                    Toaster.showToast(message: "没有获取到文件信息！".localized)
                }
            case .failure(_):
                Log.d("获取 downloadurl 失败")
                Toaster.showToast(message: "没有获取到下载链接！".localized)
            }
        }
    }

    var btnTitle:String{
        if AppState.shared.enableDownloadProgress{
            return self.downloadObservable.downloadState == .completed ? "阅读".localized : (self.canTouch ? "下载".localized : "")
        }else{
            return "阅读".localized
        }
    }

    var body: some View {
        //TODO:改成List
        ZStack {
            HStack(spacing: 0) {
                WebImage(url:URL(string:ImageManager.getImageUrl(downloadObservable.imageCover)))
                .resizable()
                .placeholder{
                    Image("cover_81*108").resizable().frame(width: 81, height: 108, alignment: .center)
                }
                .transition(.fade(duration: 0.5)) // Fade Transition with duration
                .scaledToFit()
                .frame(width: 81, height: 108, alignment: .center)
                
                Spacer().frame(width:16)
                VStack(alignment:.leading) {
                    Text(downloadObservable.title)
                        .lineLimit(1).fixedSize(horizontal: false, vertical: true)
                        .font(Font.regular(size: 16))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                    Spacer().frame(maxHeight:16)
                    Text(String(downloadObservable.authorNames))
                        .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                        .font(Font.regular(size: 12))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                    Spacer().frame(maxHeight:16)
                    HStack {
                        Spacer()
                        Button(action: {
                            guard self.canTouch else{
                                return
                            }
                            if self.downloadObservable.downloadState == .completed {
                                self.openBookAction?()
                                Log.d("已下载")
                            } else if downloadObservable.downloadState == .downloading || downloadObservable.downloadState == .waiting {
                                Log.d("正在下载")
                                guard NetReachability.isReachability() else{
                                    AppState.shared.alert.showNetworkErrorAlert()
                                    return
                                }
                                Toaster.showToast(message: "书籍资源正在下载中".localized)
                            } else {
                                Log.d("未下载")
//                                if AppState.shared.enableDownloadProgress {
                                    guard NetReachability.isReachability() else{
                                        AppState.shared.alert.showNetworkErrorAlert()
                                        return
                                    }
                                    if !AppState.shared.isHide4GDownloadControl && NetReachability.isCellular() && AppState.shared.downloadOnlyOnWifi{
                                        AppState.shared.alert.showCanCellularDownloadAlert {
                                            startDownload()
                                        }
                                    }else{
                                        startDownload()
                                    }
//                                }else{
//                                    Toaster.showToast(message: "书籍资源正在下载中".localized)
//                                }
                            }
                        }) {
                            if AppState.shared.enableDownloadProgress && (downloadObservable.downloadState == .downloading || downloadObservable.downloadState == .waiting) && canTouch {
                                DownloadProgressView(progress: $downloadObservable.downloadProgress)
                            } else {
                                ZStack {
                                    Text(btnTitle)
                                    .font(Font.medium(size: 12))
                                    .foregroundColor(Color(UIColor.primaryColor1))
                                    .frame(maxWidth: .infinity,alignment: .center)
                                    if !self.canTouch{
                                        ActivityIndicator()
                                    }
                                }
                            }
                        }
                        .frame(width:80, height: 32)
                        .background(Color(dynamicBtnBGColor3))
                        .cornerRadius(16)
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .strokeBorder(Color(UIColor.primaryColor1), lineWidth: 1)
                        )
                    }
                }
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 108)
        .padding(EdgeInsets(top: 24, leading: 24, bottom: 0, trailing: 24))
        .background(Color(dynamicBackgroundColor4))
    }
}

//#if DEBUG
//struct PurchasedList_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
////            NavigationView{
//            PurchasedListV().environmentObject(AppState.shared.initPurchasedListData())
////            }
////            NavigationView{
//            PurchasedListV().environmentObject(AppState.shared.initPurchasedListData())
//                    .previewDevice("iPhone SE (2nd generation)")
//                    .environment(\.colorScheme, .dark)
////            }
//        }
//    }
//}
//#endif
