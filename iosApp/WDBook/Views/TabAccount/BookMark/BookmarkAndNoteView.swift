//
//  BookNoteAndMarkView.swift
//  bookDemo
//
//  Created by QK on 2021/3/26.
//

import shared
import SwiftUI

struct BookmarkAndNoteView: View {
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @Environment(\.presentationMode) var presentationMode
    var dismissAction: (() -> ())?
    var jumpkAction: ((String, Int) -> ())?
    @EnvironmentObject var userData: NoteMarkUserData
    @State var selectIndex:CGFloat = 0
    @State var isPresentRecyleGuide = false
    @State var isShowRecyleSheet: Bool = false
    
    init(isShowBookmark:Bool = false,dismissAction: (() -> ())? = nil, jumpAction: ((String, Int) -> ())? = nil) {
        UITableView.appearance().separatorStyle = .none
        UITableViewCell.appearance().selectionStyle = .none
        if isShowBookmark{
            _selectIndex = State(initialValue: 1)
        }
        self.dismissAction = dismissAction
        self.jumpkAction = jumpAction
    }
    
    var body: some View {
        VStack {
            NoteAndBookmarkTitleBar(dismissAction: dismissAction, selectIndex:$selectIndex)
                .navigationBarHidden(true)
                .padding(.top, (15 + safeAreaInsets.top))
                .overlay(Divider().background(Color(dynamicSpanLineColor2)).frame(height:0.5), alignment: .bottom)
            
            GeometryReader { geometry in
                HStack(alignment: .center, spacing: 0) {
                    BookNoteListView(isShowRecyleSheet: $isShowRecyleSheet, actionShowRecyleGuide: {
                        self.isPresentRecyleGuide = true
                    }, jumpNoteAction: { note in
                        self.presentationMode.wrappedValue.dismiss()
                        self.jumpkAction?(note.chapterPath, note.showRange.location)
                    }).id(0)
                        .environmentObject(userData)
                        .frame(maxHeight: .infinity)
                        .frame(width:geometry.size.width)
                    BookmarkListView(jumpBookmarkAction: { bookmarkEntity in
                        self.presentationMode.wrappedValue.dismiss()
                        self.jumpkAction?(bookmarkEntity.pagePath, Int(bookmarkEntity.firstWordOffset))
                    }).id(1)
                        .environmentObject(userData)
                        .frame(maxHeight: .infinity)
                        .frame(width:geometry.size.width)
                }.frame(width: geometry.size.width * 2, height: geometry.size.height)
                    .background(Color(dynamicBackgroundColor8))
                    .offset(x: -selectIndex * geometry.size.width, y: 0)
            }
        }.frame(width:UIScreen.main.bounds.width)
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.all)
        .clipped()
        .background(Color(dynamicBackgroundColor8))
        .overlay(Group { if self.isPresentRecyleGuide {
            RecycleGuideUIKit(action: {
                self.isPresentRecyleGuide = false
                self.isShowRecyleSheet = true
            }, dismiss: {
                self.isPresentRecyleGuide = false
            }).edgesIgnoringSafeArea([.all])
        } else { EmptyView() }})
    }
}

#if DEBUG
struct BookmarkAndNoteView_Previews: PreviewProvider {
    static var previews: some View {
        BookmarkAndNoteView {}
    }
}
#endif

extension View {
    @ViewBuilder func isHidden(_ hidden: Bool, remove: Bool = false) -> some View {
        if hidden {
            if !remove {
                self.hidden()
            }
        } else {
            self
        }
    }
}
