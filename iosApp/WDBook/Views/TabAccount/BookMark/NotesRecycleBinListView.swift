//
//  NotesRecycleBinListV.swift
//  WDBook
//
//  Created by QK on 2021/5/20.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI

struct NotesRecycleBinListView: View {
    @Environment(\.presentationMode) var presentationMode
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @ObservedObject var userData: NoteMarkUserData
    @State private var isShowAlert = false
    @State private var isShowClearAll = false
    @Environment(\.colorScheme) var colorScheme
    @State private var selectedIndex:Int = 0
    
    init(resourceId:String) {
        userData = NoteMarkUserData(resourceId)
    }
    
    var body: some View {
        ZStack(alignment: .center) {
            VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                HStack(alignment: .center, spacing: 0) {
                    Spacer().frame(width: 32)
                    Spacer()
                    Text("笔记回收站".localized)
                        .font(.system(size: 18))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .bold()
                    Spacer()
                    Button(action: {
                        self.presentationMode.wrappedValue.dismiss()
                    }) {
                        Image(uiImage: UIImage(named: colorScheme == .light ? "icon_close":"icon_close_dark")!)
                            .resizable()
                            .scaledToFit()
                            .frame(width: 12, height: 12)
                            .padding(15)
                    }
                    .frame(width: 12, height: 12)
                    .padding(.trailing, 20)
                }
                .frame(minWidth: /*@START_MENU_TOKEN@*/0/*@END_MENU_TOKEN@*/, maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/, minHeight: /*@START_MENU_TOKEN@*/0/*@END_MENU_TOKEN@*/, idealHeight: 60, maxHeight: 60)
                .padding(.top,safeAreaInsets.top)

                Divider()
                    .background(Color(dynamicSpanLineColor2))
                    .scaleEffect(CGSize(width: 1, height: 1))

                VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                    VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                        ScrollView {
                            LazyVStack {
                                ForEach(userData.recycleNoteListData.indices, id: \.self) { i in
                                    NotesRecycleCell(bookNote: userData.recycleNoteListData[i])
                                        .contentShape(Rectangle())
                                        .onTapGesture {
                                            NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                                            selectedIndex = i
                                            self.isShowAlert = true
                                        }
                                        .alert(isPresented: $isShowAlert) {
                                            Alert(title: Text("不能查看".localized),
                                                  message: Text("\n\("若要查看或编辑，请先恢复该条笔记".localized)\n"),
                                                  primaryButton: .default(Text("取消".localized)) {
                                                      isShowAlert = false
                                            }, secondaryButton: .default(Text("恢复".localized)) {
                                                      isShowAlert = false
                                                        let note = userData.recycleNoteListData[selectedIndex]
                                                      WDBookUserSDK.shared.recoverNote(dataId: note.dataId ?? "")
                                                      self.userData.recycleNoteListData.remove(at: selectedIndex)
                                                      NotificationCenter.default.post(name: NoteBookmarkNotifications.noteResumeNotification, object: note)
                                                  })
                                        }
                                        .listRowInsets(EdgeInsets()) // list里的间距
                                        .onSwipe(trailing: [
                                            Slot(
                                                title: {
                                                    Text("恢复".localized).embedInAnyView()
                                                },
                                                action: {
                                                    let resumeNote = userData.recycleNoteListData[i]
                                                    WDBookUserSDK.shared.recoverNote(dataId: userData.recycleNoteListData[i].dataId ?? "")
                                                    self.userData.recycleNoteListData.remove(at: i)
                                                    NotificationCenter.default.post(name: NoteBookmarkNotifications.noteResumeNotification, object: resumeNote)
                                                },
                                                style: .init(background: Color(UIColor(hex: 0x4E4E4E))),
                                                alertTyle: .RECOVER
                                            ),
                                            Slot(
                                                title: {
                                                    Text("删除".localized).embedInAnyView()
                                                },
                                                action: {
                                                    WDBookUserSDK.shared.deleteNote(dataId: userData.recycleNoteListData[i].dataId ?? "")
                                                    self.userData.recycleNoteListData.remove(at: i)
                                                },
                                                style: .init(background: Color(dynamicBackgroundColor7)),
                                                alertTyle: .DELETE
                                            )
                                        ])
                                }
                            }
                        }
                        .onAppear {
                            NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                            userData.refreshRecycleNotesData()
                        }
                    }

                    Divider()
                        .background(Color(dynamicSpanLineColor2))
                        .scaleEffect(CGSize(width: 1, height: 1))

                    Button(action: {
                        NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                        self.isShowClearAll = true
                    }) {
                        Text("清空回收站".localized)
                            .font(.system(size: 14))
                            .padding(.top, 17)
                            .padding(.bottom, 32)
                            .foregroundColor(Color(UIColor(hex: 0xC13013)))
                    }
                    .alert(isPresented: $isShowClearAll) {
                        Alert(title: Text("清空".localized),
                              message: Text("\n\("确定清空回收站所有笔记吗?".localized)\n"),
                              primaryButton: .cancel(Text("取消".localized)) {
                                  isShowClearAll = false
                        }, secondaryButton: .default(Text("清空".localized)) {
                                  isShowClearAll = false
                                  userData.recycleNoteListData.forEach { it in
                                      WDBookUserSDK.shared.deleteNote(dataId: it.dataId ?? "")
                                  }
                                  userData.refreshRecycleNotesData()
                              })
                    }
                }
                .edgesIgnoringSafeArea(.bottom)
                .isHidden(userData.recycleNoteListData.count>0 ? false:true)
            }
            VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                Text("暂无内容".localized)
                    .font(.system(size: 18))
                    .padding(15)
                    .foregroundColor(Color(dynamicTextColor12))

                Text("删除的笔记可在这里找回".localized)
                    .font(.system(size: 14))
                    .foregroundColor(Color(dynamicTextColor13))
            }
            .isHidden(userData.recycleNoteListData.count>0 ? true:false)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(dynamicBackgroundColor8))
        .navigationBarHidden(true)
    }
}

struct NotesRecycleCell: View {
    let bookNote: NoteEntity
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 0) {
                Circle()
                    .frame(width: 8, height: 8)
                    .foregroundColor(Color(HighlightStyle.generate(type: bookNote.highlightColorType, isUnderLine: bookNote.markStyle == 1 ? true:false).showColor))

                LTRLabel(text: bookNote.tocTitle,textColor: dynamicTextColor14,font: .systemFont(ofSize: 12),horizontalPadding: 24 + 4)
                    .padding(.leading, 8)
                    .font(.system(size: 12))
                    .foregroundColor(Color(dynamicTextColor14))
            }
            .padding(.bottom, 10)
            .frame(alignment: .bottomLeading)

            if bookNote.noteText.isEmpty{
                LTRLabel(text: bookNote.summary,textColor: dynamicTitleColor2,font: .systemFont(ofSize: 16),lineLimit: 3,lineSpacing: 4,horizontalPadding:24)
                    .font(.system(size: 16))
                    .lineLimit(3)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .lineSpacing(4)
            }else{
                HStack(spacing: 0) {
                    Text("引用 | %@".localizedFormat(bookNote.summary))
                        .font(.regular(size: 14))
                        .lineLimit(2)
                        .lineSpacing(4)
                        .padding(.leading, 5)
                        .overlay(RoundedRectangle(cornerRadius: 3)
                                    .fill(Color(dynamicSpanLineColor4))
                                    .frame(width: 2)
                                    .frame(maxHeight:.infinity), alignment: .leading)
                }
                .foregroundColor(Color(dynamicSpanLineColor4))
                
                LTRLabel(text: bookNote.noteText,textColor: dynamicTitleColor2,font: .systemFont(ofSize: 16),lineLimit: 3,lineSpacing: 4,horizontalPadding:24)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(.regular(size: 16))
                    .lineLimit(3)
                    .lineSpacing(4)
                    .frame(maxWidth:.infinity,alignment: .leading)
            }

            Text("\((bookNote.createTime / 1000).yyyymmddhhmm)")
                .padding(.top, 5)
                .padding(.bottom, 8)
                .font(.system(size: 12))
                .foregroundColor(Color(UIColor(hex: 0x8A8A8A)))
                .frame(minWidth: /*@START_MENU_TOKEN@*/0/*@END_MENU_TOKEN@*/, maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/, alignment: .trailing)

            Divider()
                .background(Color(dynamicSpanLineColor2))
                .scaleEffect(CGSize(width: 1, height: 1))
        }
        .padding(.top, 10)
        .padding(.leading, 24)
        .padding(.trailing, 24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#if DEBUG
struct NotesRecycleBinList_Previews: PreviewProvider {
    static var previews: some View {
        NotesRecycleBinListView(resourceId:WDReaderConfig.resourceId)
    }
}
#endif
