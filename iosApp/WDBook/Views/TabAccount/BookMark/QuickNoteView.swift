//
//  QuickNoteView.swift
//  WDBook
//
//  Created by QK on 2021/6/11.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI

struct QuickNoteView: View {
    var dismissAction: (() -> ())?
    var jumpAction:((String,Int) -> ())?
    @Environment(\.presentationMode) var presentationMode
    @EnvironmentObject var userData: NoteMarkUserData
    @State private var isShowEditView = false
    @State private var isShowClearAll = false
    var body: some View {
        ZStack(alignment: .center) {
            VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                HStack(alignment: .center, spacing: 0) {
                    Spacer().frame(width: 32)
                    Spacer()
                    Text("笔记".localized + " " + String(userData.quickNoteListData.count))
                        .font(.system(size: 18))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .bold()
                    Spacer()
                    But<PERSON>(action: {
                        self.presentationMode.wrappedValue.dismiss()
                        dismissAction?()
                    }) {
                            Image(uiImage: UIImage(named: "btn_close")!)
                                .resizable()
                                .scaledToFit()
                                .frame(width: 12, height: 12)
                                .padding(15)
                    }
                    .frame(width: 12, height: 12)
                    .padding(.trailing, 20)
                }
                .frame(minWidth: /*@START_MENU_TOKEN@*/0/*@END_MENU_TOKEN@*/, maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/, minHeight: /*@START_MENU_TOKEN@*/0/*@END_MENU_TOKEN@*/, idealHeight: 60, maxHeight: 60)

                Divider()
                    .background(Color(dynamicSpanLineColor2))
                    .scaleEffect(CGSize(width: 1, height: 1))

                VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                    VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                        ScrollView {
                            LazyVStack {
                                ForEach(0 ..< userData.quickNoteListData.count, id: \.self) { i in
                                    QuickNotesRecycleCell(bookNote: userData.quickNoteListData[i])
                                        .contentShape(Rectangle())
                                        .onTapGesture {
                                            self.userData.selectedIndex = i
                                            self.isShowEditView = true
//                                            self.presentationMode.wrappedValue.dismiss()
//                                            self.jumpAction?(userData.quickNoteListData[i].chapterPath,userData.quickNoteListData[i].range.location)
                                        }
                                        .listRowInsets(EdgeInsets()) // list里的间距
                                }
                            }
                        }
                        .onAppear {
//                            userData.refreshNotesData()
                        }
                    }
                }
                .edgesIgnoringSafeArea(.bottom)
                .isHidden(userData.quickNoteListData.count>0 ? false:true)
            }
            VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
                Text("暂无内容".localized)
                    .font(.system(size: 18))
                    .padding(15)
                    .foregroundColor(Color(dynamicTextColor12))
            }
            .isHidden(userData.quickNoteListData.count>0 ? true:false)
        }
        .sheet(isPresented: $isShowEditView) {
            NoteEditerVCUIKit(note: self.userData.quickNoteListData[self.userData.selectedIndex], needPostNoti: false)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

struct QuickNotesRecycleCell: View {
    @StateObject var bookNote: Note
    var body: some View {
        HStack(spacing: 0) {
            VStack(alignment: .leading, spacing: 0) {
                Text(bookNote.summary)
                    .underline(bookNote.style.isUnderline, color: Color(bookNote.style.showColor))
                    .font(.system(size: 14))
                    .lineLimit(3)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .lineSpacing(4)
                    .background(Color(bookNote.style.isUnderline ? UIColor.clear : bookNote.style.showColor))

                HStack(spacing: 0) {
                    Rectangle()
                        .fill(Color(dynamicSpanLineColor4))
                        .frame(width: 2, height: 34)
                        .edgesIgnoringSafeArea(.horizontal)

                    Text("笔记".localized + "  | \(bookNote.noteText)")
                        .font(.system(size: 12))
                        .lineLimit(2)
                        .foregroundColor(Color(dynamicTextColor7))
                        .padding(.leading, 5)
                        .lineSpacing(4)
                }
                .frame(height: bookNote.noteText.isEmpty ? 0:40, alignment: .center)
                .padding(.top, 7)
                .isHidden(bookNote.noteText.isEmpty)

                Divider()
                    .background(Color(dynamicSpanLineColor2))
                    .scaleEffect(CGSize(width: 1, height: 1))
                    .padding(.top, 15)
            }
            .padding(.top, 10)
            .padding(.leading, 24)
            .padding(.trailing, 24)
            .frame(maxWidth: .infinity, maxHeight: .infinity)
        }
    }
}

#if DEBUG
struct QuickNotesRecycleBinList_Previews: PreviewProvider {
    static var previews: some View {
        QuickNoteView()
    }
}
#endif
