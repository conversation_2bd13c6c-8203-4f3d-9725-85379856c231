//
//  BookmarkListViewiew.swift
//  bookDemo
//
//  Created by QK on 2021/3/30.
//

import shared
import SwiftUI

struct BookmarkListView: View {
    @EnvironmentObject var userData: NoteMarkUserData
    @State var footerRefreshing: Bool = false
    @State var headerRefreshing: Bool = false
    @State var isFirstLoad = true
    var minCountOnFirstLoad = 10
    var jumpBookmarkAction: ((BookmarkEntity) -> ())?

    @State var noMoreNote: Bool = false {
        didSet {
            if !noMoreNote && userData.bookmarkListData.count < minCountOnFirstLoad {
                loadMoreNoteData()
            }
        }
    }

    func refresh() {
        debugPrint("header正在刷新。")
        WDBookSyncSDK.shared.syncBookmarkData { [self] result in
            switch result {
            case .success(_):
                loadData()
            case .failure(_):
                Toaster.showToast(message: "刷新失败，请重新尝试！".localized)
                headerRefreshing = false
            }
        }
    }
    
    func loadData(){
        userData.refreshBookmarkData()
        footerRefreshing = false
        headerRefreshing = false
        let hasNext = userData.bookmarkListData.count >= minCountOnFirstLoad
        noMoreNote = !hasNext
    }

    func loadMoreNoteData() {
        guard headerRefreshing == false else {
            debugPrint("header正在刷新，等待。")
            return
        }

        let hasNext = userData.getMoreBookmarkData(limit: Int64(minCountOnFirstLoad))
        footerRefreshing = false
        noMoreNote = !hasNext
        debugPrint("footter正在刷hasNext" + String(hasNext))
    }

    var body: some View {
        ZStack(alignment: .center) {
            GeometryReader(content: { proxy in
                ScrollView {
                    WDRefreshHeader(refreshing: $headerRefreshing, action: {
                        self.refresh()
                    }).padding(.bottom, 15)
                    
                    if userData.bookmarkListData.count > 0{
                        LazyVStack {
                            ForEach(userData.bookmarkListData.indices, id: \.self) { i in
                                BookmarkCell(bookmark: userData.bookmarkListData[i])
                                    .contentShape(Rectangle())
                                    .listRowInsets(EdgeInsets()) // list里的间距
                                    .onSwipe(trailing: [
                                        Slot(
                                            title: {
                                                Text("删除".localized).embedInAnyView()
                                            },
                                            action: {
                                                // 删除书签数据
                                                WDBookUserSDK.shared.deleteBookmark(dataId: userData.bookmarkListData[i].dataId ?? "")
                                                self.userData.bookmarkListData.remove(at: i)
                                                NotificationCenter.default.post(name: NoteBookmarkNotifications.noteDeletedNotification, object: nil)
                                            },
                                            style: .init(background: Color(dynamicBackgroundColor7)),
                                            alertTyle: .NONE
                                        )
                                    ]).onTapGesture {
                                        self.jumpBookmarkAction?(self.userData.bookmarkListData[i])
                                    }
                            }
                        }
                    }else{
                        VStack {
                            Text("暂时没有书签".localized)
                                .font(.system(size: 18))
                                .padding(15)
                                .foregroundColor(Color(dynamicTextColor12))
                            Text("点击[书签]按钮添加书签".localized)
                                .font(.system(size: 14))
                                .foregroundColor(Color(dynamicTextColor13))
                            Spacer().frame(height:69)
                        }.frame(height:proxy.size.height)
                    }
                    
                    if userData.bookmarkListData.count > 0 {
                        if !noMoreNote {
                            if userData.bookmarkListData.count < minCountOnFirstLoad {
                                SimpleRefreshingView().padding()
                            } else {
                                WDRefreshFooter(refreshing: $footerRefreshing, noMore: $noMoreNote) {
                                    self.loadMoreNoteData()
                                }
                            }
                        } else {
                            Spacer().frame(height: 12).listRowInsets(EdgeInsets())
                        }
                    }
                }
                .enableRefresh()
                .onAppear {
                    if isFirstLoad {
                        loadData()
                    }
                    isFirstLoad = false
                }
            })
        }
    }
}

struct BookmarkCell: View {
    let bookmark: BookmarkEntity

    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            HStack(spacing: 0) {
                Image(uiImage: UIImage(named: "bookmark_gray")!)
                    .resizable()
                    .scaledToFit()
                    .frame(width: 22, height: 22)
                Text(bookmark.tocTitle)
                    .bold()
                    .padding(.leading, 6)
                    .font(.system(size: 14))
                    .foregroundColor(Color(dynamicTitleColor2))
            }
            .padding(.bottom, 6)
            .frame(alignment: .bottomLeading)

            Text(bookmark.summary)
                .padding(.bottom, 7)
                .padding(.leading, 25)
                .font(.system(size: 14))
                .lineLimit(2)
                .foregroundColor(Color(dynamicTitleColor2))
                .lineSpacing(4)

            Text("\((bookmark.createTime / 1000).yyyymmddhhmm)")
                .padding(.top, 5)
                .padding(.bottom, 8)
                .font(.system(size: 12))
                .foregroundColor(Color(UIColor(hex: 0x8A8A8A)))
                .frame(minWidth: /*@START_MENU_TOKEN@*/0/*@END_MENU_TOKEN@*/, maxWidth: /*@START_MENU_TOKEN@*/ .infinity/*@END_MENU_TOKEN@*/, alignment: .trailing)

            Divider()
                .background(Color(dynamicSpanLineColor2))
                .scaleEffect(CGSize(width: 1, height: 1))
        }
        .padding(.top, 6)
        .padding(.leading, 24)
        .padding(.trailing, 24)
        .frame(maxWidth: .infinity, maxHeight: .infinity)
    }
}

#if DEBUG
struct BookmarkListViewiew_Previews: PreviewProvider {
    static var previews: some View {
        BookmarkListView()
    }
}
#endif
