//
//  bookNoteList.swift
//  bookDemo
//
//  Created by QK on 2021/3/26.
//

import shared
import SwiftUI
import SwiftyUserDefaults
import NavigationRouter

struct BookNoteListView: View {
    @StateObject var dataObserable = NoteListObservable(WDReaderConfig.resourceId)
    @Binding var isShowRecyleSheet: Bool
    @State var footerRefreshing: Bool = false
    @State var headerRefreshing: Bool = false
    @State private var isShowEditView = false
    @State var isFirstLoard = true
    var onePageLimit = 10
    var actionShowRecyleGuide: (() -> ())?
    var jumpNoteAction: ((Note) -> ())?

    @State var noMoreNote: Bool = false

    func refresh() {
        debugPrint("header正在刷新。")
        WDBookSyncSDK.shared.syncNoteData { [self] result in
            switch result {
            case .success(_):
                loadData()
                NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: nil)
            case .failure(_):
                Toaster.showToast(message: "刷新失败，请重新尝试！".localized)
                headerRefreshing = false
            }
        }
    }

    func loadData(){
        dataObserable.refreshNotesData()
        footerRefreshing = false
        headerRefreshing = false
        let hasNext = dataObserable.bookNoteList.count >= onePageLimit
        noMoreNote = !hasNext
    }
    
    func loadMoreNoteData() {
        guard headerRefreshing == false else {
            debugPrint("header正在刷新，等待。")
            return
        }

        let listData = dataObserable.getMoreNotesData(offset: dataObserable.bookNoteList.count, limit: onePageLimit)
        dataObserable.bookNoteList.append(contentsOf: listData)
        footerRefreshing = false
        let hasNext = listData.count >= onePageLimit
        noMoreNote = !hasNext
        debugPrint("footter正在刷hasNext" + String(hasNext))
    }

    var body: some View {
        VStack(alignment: /*@START_MENU_TOKEN@*/ .center/*@END_MENU_TOKEN@*/, spacing: 0) {
            GeometryReader(content: { proxy in
                ScrollView {
                    WDRefreshHeader(refreshing: $headerRefreshing, action: {
                        self.refresh()
                    }).padding(.bottom, 15)
                    
                    if dataObserable.bookNoteList.count > 0{
                        LazyVStack {
                            ForEach(dataObserable.bookNoteList.indices, id: \.self) { i in
                                BookNoteCell(bookNote: dataObserable.bookNoteList[i])
                                    .contentShape(Rectangle())
                                    .onTapGesture {
                                        if dataObserable.bookNoteList[i].isConflict {
                                            self.dataObserable.selectedIndex = i
                                            self.isShowEditView = true
                                        }else{
                                            self.jumpNoteAction?(dataObserable.bookNoteList[i])
                                        }
                                    }
                                    .listRowInsets(EdgeInsets()) // list里的间距
                                    .onSwipe(trailing: [
                                        Slot(
                                            title: {
                                                Text("删除".localized).embedInAnyView()
                                            },
                                            action: {
                                                // 移除笔记数据到笔记回收站
                                                let deletedNote = dataObserable.bookNoteList[i]
                                                WDBookUserSDK.shared.removeNote(dataId: dataObserable.bookNoteList[i].noteEntity?.dataId ?? "")
                                                self.dataObserable.bookNoteList.remove(at: i)
                                                if !Defaults[key: DefaultsKeys.IS_MARKED_RECYLE_GUIDE] {
                                                    self.actionShowRecyleGuide?()
                                                    Defaults[key: DefaultsKeys.IS_MARKED_RECYLE_GUIDE] = true
                                                }
                                                NotificationCenter.default.post(name: NoteBookmarkNotifications.noteDeletedNotification, object: deletedNote)
                                            },
                                            style: .init(background: Color(dynamicBackgroundColor7)),
                                            alertTyle: .NONE
                                        )
                                    ])
                            }
                        }
                    }else{
                        VStack {
                            Text("暂时没有笔记".localized)
                                .font(.system(size: 18))
                                .padding(15)
                                .foregroundColor(Color(dynamicTextColor12))
                            Text("阅读时选中文本，点击[笔记]即可创建".localized)
                                .font(.system(size: 14))
                                .foregroundColor(Color(dynamicTextColor13))
                        }.frame(height:proxy.size.height)
                    }
                    
                    if dataObserable.bookNoteList.count > 0 {
                        if !noMoreNote {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore: $noMoreNote) {
                                self.loadMoreNoteData()
                            }
                        } else {
                            Spacer().frame(height: 12).listRowInsets(EdgeInsets())
                        }
                    }
                }.enableRefresh()
                .onAppear {
                    if isFirstLoard {
                        loadData()
                    }
                    isFirstLoard = false
                }
            })

            Divider()
                .background(Color(dynamicSpanLineColor2))
                .scaleEffect(CGSize(width: 1, height: 1))

            RoutedLink(to:RouterName.notesRecycleWithResourceId.withParam(WDReaderConfig.resourceId)) {
                Text("笔记回收站".localized).frame(height:20)
                    .font(.system(size: 14))
                    .padding(.top, 17)
                    .padding(.bottom, 32)
                    .foregroundColor(Color(dynamicTextColor11))
            }
        }
        .onReceive(NotificationCenter.default.publisher(for: NoteEditerVC.noteChangedNotification), perform: { noti in
            loadData()
        })
        .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteResumeNotification), perform: { noti in
            loadData()
        })
        .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteDeletedNotification), perform: { noti in
            loadData()
        })
        .sheet(isPresented: $isShowEditView) {
            NoteEditerVCUIKit(note: self.dataObserable.bookNoteList[self.dataObserable.selectedIndex], needPostNoti: true)
        }
    }
}

struct BookNoteCell: View {
    @ObservedObject var bookNote: Note
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack(spacing: 0) {
                Circle()
                    .frame(width: 8, height: 8)
                    .foregroundColor(Color(bookNote.style.showColor))

                LTRLabel(text: bookNote.tocTitle,textColor: dynamicTextColor14,font: UIFont.regular(size: 14),horizontalPadding: 24 + 4)
                    .padding(.leading, 8)
                    .font(.regular(size: 14))
                    .foregroundColor(Color(dynamicTextColor14))
            }
            .frame(alignment: .bottomLeading)

            if bookNote.noteText.isEmpty{
                LTRLabel(text: bookNote.summary,textColor: dynamicTitleColor2,font: UIFont.systemFont(ofSize: 16),lineLimit: 3,lineSpacing: 4,horizontalPadding:24)
                    .font(.system(size: 16))
                    .lineLimit(3)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .lineSpacing(4)
            }else{
                HStack(spacing: 0) {
                    Text("引用 | %@".localizedFormat(bookNote.summary))
                        .font(.regular(size: 14))
                        .lineLimit(2)
                        .lineSpacing(4)
                        .padding(.leading, 5)
                        .overlay(RoundedRectangle(cornerRadius: 3)
                                    .fill(Color(dynamicSpanLineColor4))
                                    .frame(width: 2)
                                    .frame(maxHeight:.infinity), alignment: .leading)
                }
                .foregroundColor(Color(dynamicSpanLineColor4))
                
                Group{
                    if bookNote.isConflict{
                        Text("冲突笔记：".localized).foregroundColor(Color(UIColor(hex: 0xDD5B56))) + Text("\(bookNote.noteText)").foregroundColor(Color(dynamicTitleColor2))
                    }else{
                        LTRLabel(text: bookNote.noteText,textColor: dynamicTitleColor2,font: UIFont.regular(size: 16),lineLimit: 3,lineSpacing: 4,horizontalPadding:24)
                            .foregroundColor(Color(dynamicTitleColor2))
                    }
                }.font(.regular(size: 16))
                .lineLimit(3)
                .lineSpacing(4)
                .frame(maxWidth:.infinity,alignment: .leading)
            }
            
            Text("\(Int64(bookNote.createTime.timeIntervalSince1970).yyyymmddhhmm)")
                .frame(height:20)
                .font(.regular(size: 12))
                .frame(maxWidth:  .infinity, alignment: .trailing)
                .foregroundColor(Color(UIColor(hex: 0x8A8A8A)))
            
            Divider()
                .background(Color(dynamicSpanLineColor2))
                .scaleEffect(CGSize(width: 1, height: 1))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding(.top, 8)
        .frame(width:UIScreen.main.bounds.width - 24 * 2)
        .padding(.horizontal,24)
    }
}

#if DEBUG
struct BookNoteList_Previews: PreviewProvider {
    @State static var isShowRecyleSheet: Bool = false
    static var previews: some View {
        BookNoteListView(isShowRecyleSheet: $isShowRecyleSheet)
    }
}
#endif
