import Foundation
import shared

class NoteMarkUserData: ObservableObject {
    var resourceId: String
    @Published var bookNoteListData: [Note] = []
    @Published var bookmarkListData: [BookmarkEntity] = []
    @Published var recycleNoteListData: [NoteEntity] = []
    @Published var quickNoteListData: [Note] = []
    @Published var selectedIndex: Int = 0
    
    init(_ resourceId: String) {
        self.resourceId = resourceId
    }

    func refreshRecycleNotesData() {
        recycleNoteListData = WDBookUserSDK.shared.getRemovedNoteEntityList(resourceId: resourceId) // 获取笔记回收站列表数据
    }

    func refreshNotesData() {
        // 获取笔记列表数据
        bookNoteListData = WDBookUserSDK.shared.getNoteEntityList(resourceId: resourceId, offset: 0, limit: 10).map { Note(noteEntity: $0) }
    }

    func getMoreNotesData(offset: Int, limit: Int) -> [Note] {
        // 获取笔记列表数据
        return WDBookUserSDK.shared.getNoteEntityList(resourceId: resourceId, offset: offset, limit: limit).map { Note(noteEntity: $0) }
    }

    func refreshBookmarkData() {
        bookmarkListData = WDBookUserSDK.shared.getBookmarkEntityList(resourceId: resourceId, offset: 0, limit: 10) // 获取书签数据
    }

    func getMoreBookmarkData(limit: Int64) -> Bool {
        // 获取书签列表数据
        let listData = WDBookUserSDK.shared.getBookmarkEntityList(resourceId: resourceId, offset: Int64(bookmarkListData.count), limit: limit)
        bookmarkListData.append(contentsOf: listData)
        return listData.count > 0
    }

    func setQuickNotes(notes: [Note]) {
        quickNoteListData = notes
    }

    func hasBookmarkData() -> Bool {
        return bookmarkListData.count > 0
    }
}

class NoteListObservable: ObservableObject {
    @Published var bookNoteList: [Note] = []
    @Published var selectedIndex: Int = 0
    var bookResourceId: String
    
    init(_ bookResourceId: String) {
        self.bookResourceId = bookResourceId
    }

    func refreshNotesData() {
        // 获取笔记列表数据
        bookNoteList = WDBookUserSDK.shared.getNoteEntityList(resourceId: bookResourceId, offset: 0, limit: 10).map { Note(noteEntity: $0) }
    }

    func getMoreNotesData(offset: Int, limit: Int) -> [Note] {
        // 获取笔记列表数据
        return WDBookUserSDK.shared.getNoteEntityList(resourceId: bookResourceId, offset: offset, limit: limit).map { Note(noteEntity: $0) }
    }
}
