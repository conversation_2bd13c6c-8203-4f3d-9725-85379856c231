import Foundation
import SwiftUI

private let kLabelWidth: CGFloat = 124
private let kButtonHeight: CGFloat = 40
private let kTextWidth: CGFloat = 248
struct NoteAndBookmarkTitleBar: View {
    var dismissAction: (() -> Void)?
    @Binding var selectIndex: CGFloat // 0 for left, 1 for right
    @Environment(\.presentationMode) var presentationMode // 存储在presentationMode环境的presentationMode值
    @Environment(\.colorScheme) var colorScheme
    var body: some View {
        HStack(alignment: .center, spacing: 0) {
            Spacer().frame(width: 32)
            Spacer()
            ZStack {
                RoundedRectangle(cornerRadius: 6)
                    .foregroundColor(Color(dynamicNavigationBarBGColor))
                    .frame(width: 121, height: 28)
                    .offset(x: kTextWidth * (self.selectIndex - 0.5) + kLabelWidth * (0.5 - self.selectIndex))

                HStack(alignment: .center, spacing: 0) {
                    Button {
                        NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                        withAnimation {
                            self.selectIndex = 0
                        }
                    } label: {
                        Text("笔记".localized)
                            .bold()
                            .frame(width: kLabelWidth, height: kButtonHeight)
                            .foregroundColor(Color(dynamicSegmentTitleColor))
                            .contentShape(Rectangle())
                    }

                    Button {
                        NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                        withAnimation {
                            self.selectIndex = 1
                        }
                    } label: {
                        Text("书签".localized)
                            .bold()
                            .frame(width: kLabelWidth, height: kButtonHeight)
                            .foregroundColor(Color(dynamicSegmentTitleColor))
                            .contentShape(Rectangle())
                    }
                }
                .font(.system(size: 14))
            }
            .frame(width: kTextWidth, height: 32)
            .background(Color(dynamicBackgroundColor9))
            .cornerRadius(8)

            Spacer()
            Button(action: {
                self.presentationMode.wrappedValue.dismiss()
                dismissAction?()
            }) {
                Image(colorScheme == .light ? "icon_close" : "icon_close_dark")
                    .resizable()
                    .scaledToFit()
                    .frame(width: 12, height: 12)
                    .padding(20)
                    .contentShape(Rectangle())
            }
        }.frame(height: 60)
            .frame(maxWidth: .infinity)
            .zIndex(1)
    }
}
