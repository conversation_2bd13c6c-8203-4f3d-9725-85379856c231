//
//  NotificationCenterV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/5/26.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter
import shared

extension NotificationCenterV{
    static let readeAllNotification = Notification.Name(rawValue: "NotificationCenterV_readeAllNotification")
    static let afterSyncNotification = Notification.Name(rawValue: "NotificationCenterV_afterSyncNotification") //全部或类型任意同步成功完毕后
}

struct NotificationCenterV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @StateObject var mv = NotificationListMV()
    @State var isPresentRead = false
    @State var isFirstLoad = true
    @State var offset:Int64 = 0
    @State var noMore = false
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    
    func refresh(){
        var typeList:[KotlinInt]? = nil
        if appState.acceptDiscout {
            typeList = nil
        }else{
            typeList = [KotlinInt.init(value: Int32(NotificationMsgType.platform.rawValue))]
        }
        
        WDBookSyncSDK.shared.syncAllNotificationData(typeList: typeList) { result in
            switch result{
            case .success(let syncResult):
                if let list = WDBookUserSDK.shared.loadALlNotificationListFromDB(offset: 0),list.count > 0{
                    mv.items.removeAll()
                    mv.items.append(contentsOf: list.map{NotificationListItem(entity: $0)})
                    offset = Int64(list.count)
                    noMore = false
                } else {
                    mv.items.removeAll()
                    offset = 0
                    noMore = true
                }
                
                appState.refreshNotificationStatus()
                self.headerRefreshing = false
                
                NotificationCenter.default.post(name: NotificationCenterV.afterSyncNotification, object: nil)
            case .failure(let error):
                break
            }
            isFirstLoad = false
        }
    }
    
    func loadMore() {
        if noMore {
            footerRefreshing = false
            return
        }
        
        if let list = WDBookUserSDK.shared.loadALlNotificationListFromDB(offset: offset),list.count > 0{
            mv.items.append(contentsOf: list.map{NotificationListItem(entity: $0)})
            offset += Int64(list.count)
            noMore = false
        } else {
            noMore = true
        }
        self.footerRefreshing = false
    }
    
    func btnBack(left:Bool = true) -> some View {
        Button(action: {
            self.presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                    .aspectRatio(contentMode: .fit)
                    .foregroundColor(Color(btnTintColor))
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    func readAllNotifications(){
        WDBookUserSDK.shared.makeNotificationRead { result in
            switch result{
            case .success(let msg):
                if mv.items.count > 0{
                    WDBookUserSDK.shared.makeNotificationReadStatusAll()
                }
                appState.makeReadAllNotifications()
                NotificationCenter.default.post(name: NotificationCenterV.readeAllNotification, object: nil)
            case .failure(let error):
                break
            }
        }
    }
    
    var content: some View {
        VStack(spacing: 0) {
            if mv.items.count > 0{
                HStack {
                    //通知个数
                    RoutedLink(to: RouterName.messagesTypeWithId.withParam(NotificationMsgType.platform.rawValue)) {
                        VStack {
                            Image("icon_notification_mail")
                                .redDot(num: $appState.notificationStatusPlatformCount)
                            Text("平台通知".localized)
                                .foregroundColor(Color(dynamicTextColor29))
                                .font(Font.regular())
                                .frame(maxWidth:.infinity)
                        }.frame(maxWidth:.infinity,maxHeight: .infinity)
                            .contentShape(Rectangle())
                    }
                    
                    RoutedLink(to: RouterName.messagesTypeWithId.withParam(NotificationMsgType.discount.rawValue)) {
                        VStack {
                            if appState.acceptDiscout {
                                Image("icon_notification_good")
                                    .redDot(num: $appState.notificationStatusDiscountCount)
                            }else{
                                Image("icon_notification_good")
                            }
                            
                            Text("优惠推荐".localized)
                                .foregroundColor(Color(dynamicTextColor29))
                                .font(Font.regular())
                                .frame(maxWidth:.infinity)
                        }.frame(maxWidth:.infinity,maxHeight: .infinity)
                            .contentShape(Rectangle())
                    }
                }
                .frame(height: 80)
                .frame(maxWidth:.infinity)
                .background(Color(dynamicBackgroundColor1))
            }
            
            
            ScrollView{
                WDRefreshHeader(refreshing: $headerRefreshing, action: {
                    self.refresh()
                }).padding(.bottom, 15)
                
                LazyVStack(spacing:0) {
                    ForEach(mv.items, id:\.id) { item in
                        RoutedLink(to: RouterName.messageWithId.withParam(item.id,queryParams: ["type":item.type.rawValue])) {
                            NotificationListItemV(item: item)
                        }
                    }
                }.onReceive(NotificationCenter.default.publisher(for: NotificationDetailsV.readeNotification)) { noti in
                    if let notificationId = noti.object as? Int64{
                        for i in mv.items.indices{
                            let item = mv.items[i]
                            if  item.id == notificationId{
                                mv.items[i].read = 1
                            }
                        }
                    }
                }
                .onReceive(NotificationCenter.default.publisher(for: NotificationCenterV.readeAllNotification)) { noti in
                    for i in mv.items.indices{
                        mv.items[i].read = 1
                    }
                }
                if mv.items.count > 0{
                    if !noMore {
                        WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                            self.loadMore()
                        }
                    } else {
                        Spacer().frame(height:12)
                    }
                }
            }
            .enableRefresh()
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor3))
            .clipped()
            
        }.frame(maxWidth:.infinity,maxHeight: .infinity)
            .overlay(Group {
                if self.isFirstLoad{
                    
                    HStack(spacing:12) {
                        ActivityIndicator().foregroundColor(Color(dynamicTitleColor7))
                        Text("数据获取中".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
                    }.padding(.bottom, UIScreen.main.bounds.height * 0.25)
                }else if self.mv.items.count == 0 {
                    VStack(spacing:15) {
                        Text("当前暂无通知".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTitleColor7))
                        Text("通知的消息会显示在这里".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
                    }.padding(.bottom, UIScreen.main.bounds.height * 0.25)
                } else {
                    EmptyView()
                }
            }).onAppear {
                if isFirstLoad{
                    refresh()
                }
            }
    }
    
    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 10, bottom: 0, trailing: 20))
                
                Spacer()
                
                Button {
                    if appState.hasNewNotification{
                        isPresentRead = true
                    }else{
                        Toaster.showToast(message: "暂无未读消息".localized)
                    }
                } label: {
                    Image("ic_notification_clear").frame(width: 24, height:24, alignment: .center)
                        .padding(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 8)).contentShape(Rectangle())
                }
                
                NavigationLink(destination: NotificationSettingV().environmentObject(appState)) {
                    Image("ic_notification_setting").frame(width: 24, height:24, alignment: .center)
                        .padding(EdgeInsets(top: 8, leading: 8, bottom: 8, trailing: 16)).contentShape(Rectangle())
                }
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 10)
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
                .overlay(Text("通知中心".localized).frame(maxHeight:.infinity)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18)), alignment: .center)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
            
            //                .navigationBarHidden(true)
            //                .edgesIgnoringSafeArea(.top)
        }).frame(maxWidth:.infinity,maxHeight: .infinity)
            .navigationBarHidden(true)
            .edgesIgnoringSafeArea(.top)
            .alert(isPresented: $isPresentRead) {
                Alert(title: Text(""),
                             message: Text("确定将所有未读消息标记为已读？".localized),
                             primaryButton: .cancel(Text("取消".localized),action: {
                    
                }),
                             secondaryButton: .default(Text("确定".localized),action:{
                    readAllNotifications()
                }))
            }
    }
}

#if DEBUG
struct NotificationCenterV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NotificationCenterV(mv:NotificationListMV(test_notificationList),isFirstLoad: false).environmentObject(AppState.shared)
            
            NotificationCenterV(mv:NotificationListMV(test_notificationList),isFirstLoad: false).environmentObject(AppState.shared)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
            
            NotificationCenterV(mv:NotificationListMV([]),isFirstLoad: false).environmentObject(AppState.shared)
            
            NotificationCenterV(mv:NotificationListMV([]),isFirstLoad: false).environmentObject(AppState.shared)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
            NotificationCenterV(mv:NotificationListMV([]),isFirstLoad: true).environmentObject(AppState.shared)
            
            NotificationCenterV(mv:NotificationListMV([]),isFirstLoad: true).environmentObject(AppState.shared)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
