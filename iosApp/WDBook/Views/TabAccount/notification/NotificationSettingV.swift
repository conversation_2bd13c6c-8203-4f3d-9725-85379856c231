//
//  NotificationSettingV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/5/20.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct NotificationSettingV: View {
    @EnvironmentObject var appState:AppState
    
    var body: some View {
        BackNavigation(title: "通知设置".localized){
            VStack(alignment: .center, spacing: 0) {
                
                //隐藏开关
                VStack(spacing:4) {
                    HStack(spacing:4) {
                        Text("接收消息通知".localized)
                            .font(Font.regular(size: 16))
                            .foregroundColor(Color(dynamicTitleColor2))
                        Spacer()
                        Text(appState.allowNotification ? "已开启".localized : "未开启".localized)
                            .font(Font.regular(size: 12))
                            .foregroundColor(Color(dynamicTextColor20))
                        Image("arrow_right").frame(width: 24, height: 24, alignment: .center)
                            .foregroundColor(Color(dynamicTitleColor))
                    }.contentShape(Rectangle())
                        .onTapGesture {
                            UIApplication.shared.open(URL(string: UIApplication.openSettingsURLString)!, options: [:], completionHandler: nil)
                        }
                    Text("开启或关闭推送通知，需去系统设置-通知中更改".localized)
                        .font(Font.regular(size: 14))
                        .foregroundColor(Color(UIColor(hex: 0xBCBCBC)))
                        .frame(maxWidth:.infinity,alignment: .leading)

                }.frame(maxWidth: .infinity)
                    .frame(height: 64)
                    .padding(.leading, 16)
                    .padding(.trailing,8)
                    .background(Color(dynamicBackgroundColor1))
                    .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 12), alignment: .bottom)
                
                //关闭优惠提醒的设置功能
//                HStack {
//                    Text("接收优惠促销提醒".localized)
//                        .font(Font.regular(size: 16))
//                        .foregroundColor(Color(dynamicTitleColor2))
//                    Spacer()
//                    Toggle("", isOn:$appState.acceptDiscout).toggleStyle(SwitchToggleStyle(tint: Color(UIColor.primaryColor1)))
//
//                }.frame(maxWidth: .infinity)
//                    .frame(height: 64)
//                    .padding(.leading, 16)
//                    .padding(.trailing,8)
//                    .background(Color(dynamicBackgroundColor1))
//                    .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 12), alignment: .bottom)
                
                Spacer()
                
            }.background(Color(dynamicBackgroundColor3))
        }.onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification), perform: { noti in
            appState.refreshSystemNotificationStatus()
        })
        .onAppear {
            appState.refreshSystemNotificationStatus()
        }
    }
}

#if DEBUG
struct NotificationSettingV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NavigationView{
                NotificationSettingV().environmentObject(AppState.shared)
            }
            NavigationView{
                NotificationSettingV().environmentObject(AppState.shared).previewDevice("iPhone SE (3nd generation)")
                    .environment(\.colorScheme, .dark)
            }
        }
    }
}
#endif
