//
//  NotificationSystemNoticeV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/11/3.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

class NotificationSystemNoticeSwiftUIV: UIView{
    var title:String
    var desc:String
    var onOK:(()->())? = nil
    
    static var vc:UIHostingController<NotificationSystemNoticeV>?
    static var hideWorkItem:DispatchWorkItem?
    
    static let contentHeight:CGFloat = 60.0
    
    init(title:String,desc:String,onOK:(()->())? = nil) {
        self.title = title
        self.desc = desc
        self.onOK = onOK
        
        super.init(frame: CGRect.zero)
        backgroundColor = dynamicAlphaBackgroundColor1
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    static func show(title:String,desc:String,on superV:UIView,onOK:(()->())? = nil) {
        hide()
        
        vc = UIHostingController(rootView: NotificationSystemNoticeV(title: title,desc:desc,
           onOK: {
            NotificationAlertV.hide()
            onOK?()
        }))
        superV.addSubview(vc!.view)
        vc!.view.layer.cornerRadius = 12
        
        vc!.view.snp.makeConstraints { make in
            make.top.equalTo(-60.0)
            make.leading.equalTo(18)
            make.trailing.equalTo(-18)
            make.height.equalTo(60)
        }
//        superV.layoutIfNeeded()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
            UIView.animate(withDuration: 0.5) {
                vc!.view.snp.updateConstraints { make in
                    make.top.equalTo(UIDevice.safeArea3InsetsTop)
//                    superV.layoutIfNeeded()
                }
            } completion: { finished in
                self.hideWorkItem = DispatchWorkItem {
                    self.hide(animation: true)
                }
                DispatchQueue.main.asyncAfter(deadline: .now() + 2, execute: hideWorkItem!)
            }
        }
        
    }
    
    static func hide(animation:Bool = false){
        if vc == nil{
            return
        }
        
        if animation {
            UIView.animate(withDuration: 0.5) {
                vc!.view.snp.updateConstraints { make in
                    make.top.equalTo(-contentHeight)
                }
                vc!.view.superview!.layoutIfNeeded()
            } completion: { finished in
                vc?.view.removeFromSuperview()
                vc = nil
            }
        }else{
            vc?.view.removeFromSuperview()
            vc = nil
        }
        
    }
    
}

struct NotificationSystemNoticeV: View {
    @State var title:String
    @State var desc:String
    
    var onOK:(()->())?
    
    var body: some View {
        HStack(spacing:10) {
            Image("logo").resizable().frame(width:38,height:38)
            VStack {
                Text(title)
                    .font(Font.regular(size: 16)).fontWeight(Font.Weight.medium)
                    .foregroundColor(Color(btnTintColor))
                    .frame(maxWidth:.infinity,alignment: .leading)
                    .lineLimit(1)
                    .background(Color.clear)
                
                Text(desc)
                    .foregroundColor(Color(btnTintColor))
                    .frame(maxWidth:.infinity,alignment: .leading)
                    .lineLimit(1)
                    .background(Color.clear)
//                    .padding(.trailing,22)
            }.frame(maxWidth:.infinity,maxHeight:.infinity,alignment: .leading)
                .background(Color.clear)
            
        }.frame(height:38)
        .padding(11)
        .background(Color(dynamicTitleColor9).blur(radius: 5,opaque: false))
        .cornerRadius(17)
        .contentShape(Rectangle())
        .onTapGesture {
            onOK?()
        }
    }
}

#if DEBUG
struct NotificationSystemNoticeV_Previews: PreviewProvider {
    static var previews: some View {
        NotificationSystemNoticeV(title: "优惠促销", desc:"圣诞特惠｜第2波，新版《游子吟》游子吟，游子吟。")
    }
}
#endif

