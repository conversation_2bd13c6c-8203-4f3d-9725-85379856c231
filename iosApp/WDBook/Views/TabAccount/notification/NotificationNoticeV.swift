//
//  NotificationNoticeV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/5/25.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI



struct NotificationNoticeV: View {
    @State var title:String
    var onOK:(()->())?
    var onCancel:(()->())?
    
    var body: some View {
        HStack(spacing:0) {
            Text(title)
                .font(Font.regular())
                .foregroundColor(Color(hex: 0x4E4E4E))
                .frame(maxWidth:.infinity,alignment: .leading)
                .padding(.leading, 12)
            Button {
                onCancel?()
            } label: {
                Image("notification_close")
                    .frame(width: 24, height: 24)
                    .frame(maxHeight:.infinity)
                    .padding(.leading, 4)
                    .padding(.trailing,8)
            }
            
        }.frame(height:38)
        .background(Color(hex: 0xFFEBEA))
        .onTapGesture {
            onOK?()
        }
    }
    
    static func show(){
        
    }
}

#if DEBUG
struct NotificationNoticeV_Previews: PreviewProvider {
    static var previews: some View {
        NotificationNoticeV(title: "公告：为了更好地维护你的权益，我们近期优化了用户")
    }
}
#endif
