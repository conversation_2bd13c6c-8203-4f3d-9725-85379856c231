//
//  NotificationAlertContentV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/5/25.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

import SwiftUI
import SnapKit
import UIKit
import shared

class NotificationAlertV: UIView,NotificationAlertContentVDelegate {
    static var notificationV:NotificationAlertV?
    var title:String
    var detail:String
    var onOK:(()->())? = nil
    var onCancel:(()->())? = nil
    
    var vc:UIHostingController<NotificationAlertContentV>?
    
    init(title:String,detail:String,onOK:(()->())? = nil,onCancel:(()->())? = nil) {
        self.title = title
        self.detail = detail
        self.onOK = onOK
        self.onCancel = onCancel
        
        super.init(frame: CGRect.zero)
        backgroundColor = dynamicAlphaBackgroundColor1
        
        //        let btn = UIButton()
        //        btn.addTarget(self, action: #selector(tapClose), for: .touchUpInside)
        //        addSubview(btn)
        //        btn.snp.makeConstraints { (make) in
        //            make.edges.equalToSuperview()
        //        }
        weak var weakself = self
        vc = UIHostingController(rootView: NotificationAlertContentV(delegate:self, title: title,detail:detail,
           onOK: {
            NotificationAlertV.hide()
            weakself?.onOK?()
        }, onCancel:{
            NotificationAlertV.hide()
            weakself?.onCancel?()
        }))
        addSubview(vc!.view)
        vc!.view.layer.cornerRadius = 12
        vc!.view.snp.makeConstraints { make in
            make.width.equalTo(270)
            make.height.equalTo(345)
            make.center.equalTo(self)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    static func show(title:String,detail:String,on superV:UIView,onOK:(()->())? = nil,onCancel:(()->())? = nil) {
        hide()
        
        notificationV = NotificationAlertV(title: title, detail:detail, onOK:onOK,onCancel:onCancel)
        superV.addSubview(notificationV!)
        notificationV!.snp.makeConstraints { (make) in
            make.edges.equalToSuperview()
        }
    }
    
    static func hide(){
        notificationV?.removeFromSuperview()
        notificationV = nil
    }
    
    @objc func tapClose(){
        NotificationAlertV.hide()
    }
    
    func notificationAlertContentVOnSizeChanged(size: CGSize) {
        vc?.view.snp.updateConstraints { make in
            make.width.equalTo(size.width)
            make.height.equalTo(size.height)
        }
    }
}

protocol NotificationAlertContentVDelegate{
    func notificationAlertContentVOnSizeChanged(size:CGSize)
}

struct NotificationAlertContentV: View {
    var delegate:NotificationAlertContentVDelegate?
    @State var title:String
    @State var detail:String
    var onOK:(()->())? = nil
    var onCancel:(()->())? = nil
    
    @State var detailHeight:CGFloat = 0
    var body: some View {
        VStack(spacing:0) {
            Spacer().frame(height:27)
            Text(title).font(Font.regular(size: 20)).foregroundColor(Color(dynamicTextColor28))
            Spacer().frame(height:10)
            TextViewForAlert(text: $detail, dynamicHeight: $detailHeight,textColor: dynamicTextColor28)
                .foregroundColor(Color(dynamicTextColor28))
                .padding(.horizontal,24)
                .frame(height:detailHeight)
//            Text(detail).lineSpacing(5).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTextColor28))
//                .padding(.horizontal,24)
            Spacer().frame(height:20)
            
            HStack(spacing: 0) {
                Button {
                    onCancel?()
                } label: {
                    Text("关闭".localized).frame(maxWidth:.infinity)
                }
                Color(dynamicSpanLineColor8).frame(width:0.5).frame(maxHeight:.infinity)
                Button {
                    onOK?()
                } label: {
                    Text("查看".localized).frame(maxWidth:.infinity)
                }
            }.font(Font.regular(size: 17)).foregroundColor(Color(UIColor(hex: 0x007AFF))).frame(height:44)
                .overlay(Color(dynamicSpanLineColor8).frame(height:0.5).frame(maxWidth:.infinity), alignment: .top)
            
        }.frame(width:270).background(Color(dynamicBackgroundColor11))
            .cornerRadius(12)  //TODO: 黑色模式边角未透明问题。
        .readSize(onChange: { size in
            delegate?.notificationAlertContentVOnSizeChanged(size: size)
        })
    }
}

struct LabelView: UIViewRepresentable {
    @Binding var text: String
    @Binding var dynamicHeight: CGFloat
    
    func makeUIView(context: UIViewRepresentableContext<LabelView>) -> UILabel {
        let label = UILabel()
        label.font = UIFont.regular(size: 16)
        label.numberOfLines = 0
        label.attributedText = attributedString
        label.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
        label.setContentCompressionResistancePriority(.defaultLow, for: .vertical)
        
        return label
    }
    
    func updateUIView(_ uiView: UILabel, context: UIViewRepresentableContext<LabelView>) {
        uiView.attributedText = attributedString
        DispatchQueue.main.async {
            dynamicHeight = uiView.sizeThatFits(CGSize(width: uiView.bounds.width, height: CGFloat.greatestFiniteMagnitude)).height
        }
    }

    var attributedString:NSAttributedString{
        let p = NSMutableParagraphStyle()
        p.lineSpacing = 5
        p.alignment = .justified
        return NSAttributedString(string: text, attributes: [.paragraphStyle:p])
    }
}

struct TextViewForAlert: UIViewRepresentable {
    @Binding var text: String
    @Binding var dynamicHeight: CGFloat
    var textColor:UIColor
    
    func makeUIView(context: UIViewRepresentableContext<TextViewForAlert>) -> UITextView {
        let tv = UITextView()
        tv.backgroundColor = UIColor.clear
        tv.font = UIFont.regular(size: 16)
        tv.attributedText = attributedString
        tv.showsVerticalScrollIndicator = false
        tv.isEditable = false
//        tv.setContentCompressionResistancePriority(.defaultLow, for: .horizontal)
//        tv.setContentCompressionResistancePriority(.defaultLow, for: .vertical)
        
        return tv
    }
    
    func updateUIView(_ uiView: UITextView, context: UIViewRepresentableContext<TextViewForAlert>) {
        uiView.attributedText = attributedString
        DispatchQueue.main.async {
            let actHeight = uiView.sizeThatFits(CGSize(width: uiView.bounds.width, height: CGFloat.greatestFiniteMagnitude)).height
            dynamicHeight = min(actHeight, UIScreen.main.bounds.height * 0.35)
        }
    }
    
    var attributedString:NSAttributedString{
        let p = NSMutableParagraphStyle()
        p.lineSpacing = 5
        p.alignment = .justified
        return NSAttributedString(string: text, attributes: [.paragraphStyle:p,
                                                             .font:UIFont.regular(size: 16),
                                                             .foregroundColor:textColor])
    }
}

#if DEBUG
import DTCoreText

struct NotificationAlertContentV_Previews: PreviewProvider {
    static var t =
"""
<!DOCTYPE html>\n<html lang=\"zh-cn\">\t\t\n\t<head>\n\t\t<meta charset=\"UTF-8\">\n\t\t<title>弹框测试</title>\n\t\t<meta charset=\"utf-8\">\n\t\t<meta name=\"viewport\" content=\"width=device-width,initial-scale=1,minimum-scale=1,maximum-scale=1,user-scalable=no,viewport-fit=cover\">\n    \t<style>\n\t\t\t@media (prefers-color-scheme: dark) {\n\t\t\t\t.dark-scheme {\n\t\t\t\t\tbackground-color: #0E0E0E;\n\t\t\t\t\tcolor: #F5F5F5;\n\t\t\t\t}\n\n\t\t\t\ta {\n\t\t\t\t\tcolor: #529DFF;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t@media (prefers-color-scheme: light) {\n\t\t\t\t.light-scheme {\n\t\t\t\t\tbackground-color: #F5F5F5;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t}\n\n\t\t\t\ta {\n\t\t\t\t\tcolor: #007AFF;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t:root {\n\t\t\t\tcolor-scheme: light dark;\n\t\t\t\tsupported-color-schemes: dark light;\n\t\t\t}\n\n\t\t\tbody {\n\t\t\t\t--duration: 0.5s;\n\t\t\t\t--timing: ease;\n\t\t\t\tcolor: var(--color);\n\t\t\t\tbackground-color: var(--background-color);\n\t\t\t\ttransition: color var(--duration) var(--timing), background-color var(--duration) var(--timing);\n\t\t\t}\n\n\t\t\t.container {\n\t\t\t\tpadding: 20px;\n\t\t\t\tword-break: break-all;\n\t\t\t\twhite-space: normal;\n\t\t\t}\n\n\t\t\timg {\n\t\t\t\tvertical-align: middle;\n\t\t\t\tmax-width: 100%;\n\t\t\t\theight: auto;\n\t\t\t}\n    \t</style>\t\t\n\t</head>\t\t\n\t\t<body>\n\t\t\t<div id=\"main-content\" class=\"container\">\n\t\t\t\t<h2>第三方第<strong>三方地方</strong>第三方第三方</h2><p><i>第三方第三方<strong>生打发的的dd</strong></i></p><ol><li><i><strong>第三方商店分都是分手的</strong></i></li><li>图片<img src=\"https://t.img.wedevote.net/media/download/images/2022/09/29/e6a50d813ece46b740a4b0e10b1c12df.png\"></li><li>表格</li></ol><figure class=\"table\"><table><tbody><tr><td>1</td><td>2</td><td>3</td><td>哈哈</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>你好吗</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>31</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td></tr><tr><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>&nbsp;</td><td>55</td></tr></tbody></table></figure><blockquote><p>水电费水电费是的<a href=\"https://baidu.com\">https://baidu.com</a></p><p>sdfdsfsd&nbsp;</p></blockquote>\n\t\t\t</div>\n\t\t</body>\t\n</html>\t
"""
    
    static var previews: some View {
        Group {
            NotificationAlertContentV(title: "《颠覆性祷告》限免中", detail:DTHTMLAttributedStringBuilder.init(html: t.data(using: .utf8)!, options: [:], documentAttributes: nil).generatedAttributedString().string)
            
            NotificationAlertContentV(title:"《颠覆性祷告》限免中", detail: "每一个上帝国度运动都始于祷告。经典的祷告辅助材料《颠覆性祷告》限时免费中！优惠仅到12/8，机会难得！")
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
