//
//  NotificationListV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/5/19.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWebImageSwiftUI
import NavigationRouter

class NotificationListMV: ObservableObject {
    @Published var items: [NotificationListItem] = []
//    @Published var type:NotificationMsgType

    init(_ items:[NotificationListItem]? = nil) {
        if let list = items{
            self.items = list
        }
    }
}

struct NotificationListV: View {
    @EnvironmentObject var appState:AppState
    @State var type:NotificationMsgType
    @State var isFirstLoad = true
    @StateObject var mv = NotificationListMV()
    @State var offset:Int64 = 0
    @State var noMore = false
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    
    func refresh(){
        WDBookSyncSDK.shared.syncNotificationDataByType(type:type.rawValue) { result in
            switch result{
            case .success(let syncResult):
                if let list = WDBookUserSDK.shared.loadTypeNotificationListFromDB(type: type.rawValue, offset: 0),
                   list.count > 0,
                   appState.acceptDiscout{
                    mv.items.removeAll()
                    mv.items.append(contentsOf: list.map{NotificationListItem(entity: $0)})
                    offset = Int64(list.count)
                    noMore = false
                } else {
                    mv.items.removeAll()
                    offset = 0
                    noMore = true
                }
                self.headerRefreshing = false
                NotificationCenter.default.post(name: NotificationCenterV.afterSyncNotification, object: nil)
            case .failure(let error):
                break
            }
            isFirstLoad = false
            appState.refreshNotificationStatus()
        }
    }
    
    func loadMore() {
        if noMore {
            footerRefreshing = false
            return
        }
        
        if let list = WDBookUserSDK.shared.loadTypeNotificationListFromDB(type: type.rawValue,offset: offset),list.count > 0{
            mv.items.append(contentsOf: list.map{NotificationListItem(entity: $0)})
            offset += Int64(list.count)
            noMore = false
        } else {
            noMore = true
        }
        self.footerRefreshing = false
    }
    
    var body: some View {
        BackNavigation(title: type.title){
            ScrollView{
                WDRefreshHeader(refreshing: $headerRefreshing, action: {
                    refresh()
                }).padding(.bottom, 15)
                
                VStack(spacing:0) {
                    ForEach(mv.items, id:\.id) { item in
                        RoutedLink(to: RouterName.messageWithId.withParam(item.id,queryParams: ["type":item.type.rawValue])) {
                            NotificationListItemV(item: item)
                        }
                    }
                }.onReceive(NotificationCenter.default.publisher(for: NotificationDetailsV.readeNotification)) { noti in
                    if let notificationId = noti.object as? Int64{
                        for i in mv.items.indices{
                            if  mv.items[i].id == notificationId{
                                mv.items[i].read = 1
                            }
                        }
                    }
                }.onReceive(NotificationCenter.default.publisher(for: NotificationCenterV.readeAllNotification)) { noti in
                    for i in mv.items.indices{
                        mv.items[i].read = 1
                    }
                }
            }.enableRefresh()
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor3))
            .overlay(Group {
                if self.isFirstLoad{
                    ActivityIndicator()
                }else if self.mv.items.count == 0 {
                    VStack(spacing:15) {
                        Text("当前暂无通知".localized).font(Font.regular(size: 18)).foregroundColor(Color(dynamicTitleColor7))
                        Text("通知的消息会显示在这里".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
                    }.padding(.bottom, UIScreen.main.bounds.height * 0.25)
                } else {
                    EmptyView()
                }
            }).onAppear {
                if isFirstLoad{
                    refresh()
                }
            }
        }
    }
}


struct NotificationListItemV:View{
    @StateObject var item:NotificationListItem
    
    var body: some View {
        VStack(spacing:0) {
            Text(item.lastUpdateTime.yyyymmdd_AmPmhhmm)
                .font(Font.medium(size: 10))
                .foregroundColor(Color(dynamicTitleColor5))
                .frame(height:14)
                .padding(EdgeInsets(top: 24, leading: 0, bottom: 8, trailing: 0))
            VStack(spacing:0) {
                Text(item.title)
                    .font(Font.regular(size: 16).bold())
                    .foregroundColor(Color(dynamicTextColor19))
                    .frame(maxWidth:.infinity,alignment:.leading)
                    .overlay(Circle().fill(item.read == 0 ? Color.red : Color.clear).frame(width: 8, height: 8).padding(.leading,-12),alignment:.leading)
                    .padding(EdgeInsets(top: 20, leading: item.read == 0 ? 12 + 16 : 16, bottom: 0, trailing: 16))
                if item.cover.isEmpty{
                    Text(item.subTitle)
                        .font(Font.regular())
                        .foregroundColor(Color(dynamicTitleColor6))
                        .frame(maxWidth:.infinity,alignment:.leading)
                        .padding(EdgeInsets(top: 10, leading: 16, bottom: 20, trailing: 16))
                    HStack {
                        Text("查看详情".localized)
                            .font(Font.regular())
                            .foregroundColor(Color(dynamicTitleColor5))
                        Spacer()
                        Image("arrow_right_24")
                    }
                    .padding(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 5))
                    .overlay(Color(dynamicSpanLineColor3).frame(height:0.5), alignment: .top)
                }else{
                    ImageManager.getWebImage(url:URL(string: WDBookAppSDK.shared.getFullImageUrl(url: item.cover)))
                        .resizable()
                    //                                            .placeholder{
                    //                                                Image("cover_92*132")
                    //                                                    .resizable()
                    //                                                    .frame(width: 99, height: 132, alignment: .center)
                    //                                            }
                        .transition(.fade(duration: 0.5))
                    //                                            .scaledToFit()
                    //                                            .scaledToFill()
                        .frame(height: 132, alignment: .center)
                        .cornerRadius(10)
                        .padding(EdgeInsets(top: 10, leading: 16, bottom: 14, trailing: 16))
                    
                    HStack {
                        Text(item.subTitle)
                            .font(Font.regular())
                            .foregroundColor(Color(dynamicTitleColor5))
                            .lineLimit(1)
                        Spacer()
                        Image("arrow_right_24_2")
                    }
                    .padding(EdgeInsets(top: 0, leading: 16, bottom: 8, trailing: 5))
                }
                
            }
            .frame(maxWidth:.infinity)
            .background(Color(dynamicBackgroundColor1))
            .cornerRadius(8)
            .padding(.horizontal, 16)
            
        }.frame(maxWidth:.infinity)
        .contentShape(Rectangle())
        .onReceive(NotificationCenter.default.publisher(for: NotificationDetailsV.readeNotification)) { noti in
            if let notificationId = noti.object as? Int64{
                if item.id == notificationId{
                    if item.read == 0{
                        item.read = 1
                    }
                }
            }
        }.onReceive(NotificationCenter.default.publisher(for: NotificationCenterV.readeAllNotification)) { noti in
            item.read = 1
        }
    }
}

#if DEBUG
struct NotificationListV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NotificationListV(type: .platform,mv:NotificationListMV(test_notificationList))
            
            NotificationListV(type:.discount,mv:NotificationListMV(test_notificationList))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
            NotificationListV(type: .platform,mv:NotificationListMV([]))
            
            NotificationListV(type:.discount,mv:NotificationListMV([]))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
