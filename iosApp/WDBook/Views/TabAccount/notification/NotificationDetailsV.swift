//
//  NotificationDetailsV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/5/26.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

//访问details后，后台标记已读，过一段时间status才会变化。

class NotificationDetailsMV: ObservableObject {
    @Published var data: NotificationDetails
    
    init(_ data:NotificationDetails? = nil) {
        self.data = data ?? NotificationDetails(entity: NotificationDetailEntity()) //test_notificationDetails
    }
}

extension NotificationDetailsV{
    static let readeNotification = Notification.Name(rawValue: "NotificationDetailsV_readeNotification")
}

struct NotificationDetailsV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @State var messageId:Int64
    @State var type:NotificationMsgType
    @ObservedObject var mv = NotificationDetailsMV()
    @State var didStart:Bool = false
    @State private var isSharePresented: Bool = false
    
    var btnBack : some View { Button(action: {
            self.presentationMode.wrappedValue.dismiss()  //单独使用
        }) {
            HStack {
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor))
            }
        }
    }
    
    var shareBack : some View { Button(action: {
            isSharePresented = true
        }) {
            Image("icon_share")
                .aspectRatio(contentMode: .fit)
                .frame(width: 24, height: 24)
                .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 0))
                .contentShape(Rectangle())
        }.sheet(isPresented: $isSharePresented) {
            ActivityViewController(activityItems: [mv.data.title + " " + mv.data.url], applicationActivities: nil)
        }
    }
    
    var content: some View{
        ZStack {
            if !mv.data.content.isEmpty{
                LightWebViewUIKit(url:"",content: mv.data.content, didStartProvisionalNavigation: {
                    didStart = true
                })
            }
            
            if !didStart{
                ActivityIndicator()
            }
        }.frame(maxWidth:.infinity,maxHeight: .infinity)
        .onAppear {
            if messageId > 0{
                WDBookUserSDK.shared.getNotificationDetail(id: messageId, type: type.rawValue, completion: { result in
                    switch result{
                    case .success(let r):
                        if let entity = r{
                            mv.data = NotificationDetails(entity: entity)
                            if let noti = WDBookUserSDK.shared.getNotificationMessage(id: mv.data.id),noti.read == 0{
                                WDBookUserSDK.shared.makeLocalNotificationReadById(id: mv.data.id)
                                AppState.shared.makeReadOneNotification(type: mv.data.type)
                                NotificationCenter.default.post(name: NotificationDetailsV.readeNotification, object: mv.data.id)
                            }
                            
                        }else{
                            //空页面
                        }
                    case .failure(let error):
                        break
                    }
                })
            }
        }
    }
    
    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack.padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))

                    Spacer()
                    Text("详情".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    shareBack.opacity(mv.data.allowShare == 1 ? 1 : 0)

                }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine:true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
}

struct ActivityViewController: UIViewControllerRepresentable {

    var activityItems: [Any]
    var applicationActivities: [UIActivity]? = nil

    func makeUIViewController(context: UIViewControllerRepresentableContext<ActivityViewController>) -> UIActivityViewController {
        let controller = UIActivityViewController(activityItems: activityItems, applicationActivities: applicationActivities)
        return controller
    }

    func updateUIViewController(_ uiViewController: UIActivityViewController, context: UIViewControllerRepresentableContext<ActivityViewController>) {}

}

#if DEBUG
struct NotificationDetailsV_Previews: PreviewProvider {
    static var previews: some View {
        NotificationDetailsV(messageId: 0, type: .platform,mv:NotificationDetailsMV(test_notificationDetails))
    }
}
#endif
