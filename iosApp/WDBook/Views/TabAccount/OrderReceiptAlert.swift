//
//  OrderReceiptAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/6/8.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct OrderReceiptAlert: View {
    var email:String
    var okHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text("收据已发送至邮箱".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                Text("收据已发送到 %@ 请查收。".localizedFormat(email))
                    .foregroundColor(Color(dynamicTitleColor10)).font(Font.regular(size: 16))
                    .lineSpacing(10).frame(maxWidth:.infinity,alignment:.center)
                    .multilineTextAlignment(.center)
                    .padding(.bottom,24)
                
                Button(action: {
                    okHandler()
                }) {
                    Text("确定".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:40)
                    .background(mainColor)
                    .cornerRadius(20)
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal, UIDevice.alertAdjustHorizontalPadding)
                .frame(width: UIDevice.alertAdjustFrameWidth)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
    }
}

#if DEBUG
struct OrderReceiptAlert_Previews: PreviewProvider {
    static var previews: some View {
        OrderReceiptAlert(email: "<EMAIL>", okHandler: {})
    }
}
#endif
