//
//  WalletTranscationsV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/22.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct WalletTranscationsV: View {
    
    @State var recharges:[WalletTransactionEntity] = []
    init(recharges:[WalletTransactionEntity]? = nil) {
        if let res = recharges{
            _recharges = State(initialValue: res)
        }
    }
    
    var body: some View {
        BackNavigation(title: "充值记录".localized){
            //List嵌套ForEach,可设置格式问题。
            ScrollView{
                LazyVStack(spacing:0){
                    Spacer().frame(height:12)
                    ForEach(self.recharges, id:\.transactionId) {  recharge in
                        RechargeItem(recharge:recharge)
                    }
                }
                
            }
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor1))
            .navigationBarTitle(Text("充值记录".localized),displayMode: .inline)
            .overlay(Group {
                if self.recharges.count == 0 {

                    Text("当前暂无充值记录".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                } else {
                    EmptyView()
                }
            })
            .onAppear {
                self.recharges = WDBookUserSDK.shared.getWalletTransactions() ?? []
            }
        }
    }
}

struct RechargeItem: View {
    var recharge: WalletTransactionEntity
    
    var body: some View {
        VStack(spacing: 0) {
            Text("账户充值".localized)
                .font(Font.regular())
                .foregroundColor(Color(dynamicTitleColor2))
                .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                .frame(height: 20)
            Spacer().frame(height:10)
            
            Text("\(Formatter.yyyymmddhhmmFormatter.string(from: Date(timeIntervalSince1970: TimeInterval(recharge.lastUpdateTime/1000))))")
                .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                .font(Font.regular(size: 12))
                .foregroundColor(Color(dynamicTextColor4))
                .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                .frame(height: 17)
            
        }.overlay(Text((recharge.transactionType == .recharge ? "+" : "-") + " $\(recharge.amount.fractionDigits2)")
                    .font(Font.medium(size: 16))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(alignment: .trailing)
                    .padding(EdgeInsets(top: 0, leading: 0, bottom: 4, trailing: 0)), alignment: .bottomTrailing)
        .frame(maxWidth: .infinity)
        .frame(height: 20 + 10 + 17)
        .padding(EdgeInsets(top: 12, leading: 24, bottom: 12, trailing: 24))
        .background(Color(dynamicBackgroundColor1))
    }
}

//#if DEBUG
//struct WalletTranscationsV_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            NavigationView{
//                WalletTranscationsV(recharges: test_WalletTranscations()).environmentObject(AppState.shared)
//            }
//            NavigationView{
//                WalletTranscationsV(recharges: test_WalletTranscations()).environmentObject(AppState.shared)
//                    .previewDevice("iPhone SE (2nd generation)")
//                    .environment(\.colorScheme, .dark)
//            }
//
//        }
//    }
//}
//#endif
