//
//  PECropV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/5/9.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Foundation
import SwiftUI
import UIKit

struct PECropV: UIViewControllerRepresentable {
    @Environment(\.presentationMode) private var presentationMode
    let image: UIImage
    let onImagePicked: (UIImage) -> Void
    
    func makeUIViewController(context: Context) -> UIViewController {
        let controller = PECropViewController()
        controller.delegate = self
        controller.scale = 1
        controller.disableChangeScale = true
        controller.image = image
        controller.cancelText = "取消".localized
        controller.finishText = "完成".localized
        controller.bgColor = UIColor.white
        controller.textColor = UIColor.gray

        let navigationController = UINavigationController(rootViewController: controller)
        if UIDevice.current.userInterfaceIdiom == .pad{
            navigationController.modalPresentationStyle = .formSheet
        }else{
            navigationController.modalPresentationStyle = .fullScreen
        }
//            [strongSelf presentViewController:navigationController animated:YES completion:NULL];

        controller.delegate = context.coordinator
        return navigationController
    }
    
    func updateUIViewController(_ uiViewController: UIViewController, context: Context) {
        
    }
    
    func makeCoordinator() -> Coordinator {
        Coordinator(
            ondismiss: { self.presentationMode.wrappedValue.dismiss() },
            onImagePicked: self.onImagePicked
        )
    }
    
    final public class Coordinator: NSObject, UINavigationControllerDelegate, PECropViewControllerDelegate {
        private let ondismiss: () -> Void
        private let onImagePicked: (UIImage) -> Void
        
        init(ondismiss: @escaping () -> Void, onImagePicked: @escaping (UIImage) -> Void) {
            self.ondismiss = ondismiss
            self.onImagePicked = onImagePicked
        }
        
        public func cropViewController(_ controller: PECropViewController!, didFinishCroppingImage croppedImage: UIImage!) {
            var resultImage = croppedImage
            let AVATAR_MAX_WIDTH:CGFloat = 320
            if AVATAR_MAX_WIDTH < croppedImage.size.width{
                resultImage = resultImage?.imageCompressFitSizeScale(withTargetSize: CGSize(width: AVATAR_MAX_WIDTH, height: AVATAR_MAX_WIDTH * croppedImage.size.height / croppedImage.size.width))
            }
            self.onImagePicked(resultImage ?? croppedImage)
            self.ondismiss()
        }
        
        public func cropViewControllerDidCancel(_ controller: PECropViewController!) {
            self.ondismiss()
        }
    }
}
