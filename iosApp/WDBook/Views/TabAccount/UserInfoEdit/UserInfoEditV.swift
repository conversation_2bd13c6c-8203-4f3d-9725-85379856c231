//
//  UserInfoEditV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/4.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter
import shared

//struct PopoverView: View {
//    var body: some View {
//        //Your View code here
//    }
//}

struct PopoverView<Content: View>: View {
    let content: () -> Content
    
    init(@ViewBuilder content: @escaping () -> Content) {
        self.content = content
    }
    
    var body: some View {
        VStack(alignment: .leading) {
            HStack {
                Spacer()
                Button(action: {
//                    self.isPresented = false
                }){
                    Image(systemName: "xmark")
                }
            }.padding(.top)
            
            content()
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color.white)
        .cornerRadius(8)
    }
}


struct UserInfoEditV: View {
    @EnvironmentObject var appState:AppState
    @State var isShowActionSheet = false
    @State var isShowImagePicker = false
    @State var imagePickerSourceType:UIImagePickerController.SourceType = .photoLibrary
    
    @State var pickerImage:UIImage = UIImage()
    @State var cropImage:UIImage = UIImage()
    @State var isShowCropImageV = false
    
    var content:some View{
        BackNavigation(title: "编辑资料".localized){
            VStack(spacing:0) {
                HStack(spacing:0) {
                    Text("头像".localized).frame(maxWidth:.infinity,alignment:.leading)
                    
                    HStack(spacing:16) {
                        UserAvatar(usePlaceholderCache: true).frame(width: 90, height: 90, alignment: .center)
                        Image("arrow_right_24_2").frame(width:24,height:24)
                    }
                }.frame(height:122).padding(.horizontal,16)
                    .background(Color(dynamicTextColor27))
                    .padding(.vertical,8)
                    .contentShape(Rectangle())
                    .onTapGesture {
                        isShowActionSheet = true
                    }
                
                RoutedLink(toRoute: .editNickname) {
                    HStack(spacing:0) {
                        Text("名字".localized).frame(maxWidth:.infinity,alignment:.leading)
                        
                        HStack(spacing:0) {
                            Text(appState.userInfo.nickname ?? "")
                            Image("arrow_right_24_2").frame(width:24,height:24)
                        }
                    }.frame(height:64).padding(.horizontal,16)
                        .background(Color(dynamicTextColor27))
                        .contentShape(Rectangle())
                }
                
                Image(uiImage: pickerImage).resizable().aspectRatio(contentMode: .fit).frame(width:1,height:1).opacity(0) //必须显示pickerImage，PECropView才不闪退。
                
                Spacer()
            }
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor3))
            .fullScreenCover(isPresented: $isShowImagePicker, onDismiss: {
                
            }){
                ImagePickerV(sourceType: imagePickerSourceType) { image in
                    pickerImage = image
                    isShowCropImageV = true
                }
            }
            .fullScreenCover(isPresented: $isShowCropImageV, onDismiss: {
                
            }){
                PECropV(image: pickerImage, onImagePicked: { image in
                    cropImage = image
                    if let imageData = image.jpegData(compressionQuality: 0.8){
                        
                        let kotlinByteArray = imageData.toByteArray()
                        
                        //                    kotlinByteArray = KMMiOSUtils.shared.uiImageToByteArray(image: image)
                        HUDManager.showLoadingBlockHUD(text: "")
                        WDBookUserSDK.shared.uploadAvator(byteArray: kotlinByteArray) { result in
                            HUDManager.hideLoadingHUD()
                            switch result{
                            case .success(let u):
                                if let user = u{
                                    AppState.shared.userInfo = UserInfo(entity: user)
                                }
                                Toaster.showToast(message: "保存成功".localized) { b in
                                    //                                    RoutableManager.popViewController()
                                }
                                break
                            case .failure(let error):
                                Toaster.showToast(message: error.msg)
                                break
                            }
                        }
                    }
                    
                })
            }
        }
    }
    
    var body: some View {
        if UIDevice.current.userInterfaceIdiom == .pad {
            content
                .overlay(
                    Group {
                        if isShowActionSheet {
                            PhotoSelectedAlert(takePhotoHandler: {
                                isShowActionSheet = false
                                imagePickerSourceType = .camera
                                isShowImagePicker = true
                            }, selectFromAlbumHandler: {
                                isShowActionSheet = false
                                imagePickerSourceType = .photoLibrary
                                isShowImagePicker = true
                            }, cancelHandler: {
                                isShowActionSheet = false
                            })
                        }else{
                            EmptyView()
                        }
                    }
                    , alignment: .center)
//                .popSheet(isPresented: self.$isShowActionSheet, content: {
//                    PopSheet(title: Text("请选择图片来源".localized), buttons: [
//                        PopSheet.Button(kind: .default, label: Text("拍照".localized), action: {
//
//                        }),
//                        PopSheet.Button(kind: .default, label: Text("从相册选取".localized), action: {
//
//                        }),
//                        PopSheet.Button(kind: .cancel, label: Text("取消".localized), action: {})
//                    ])
//                })
        }else{
            content.actionSheet(isPresented: $isShowActionSheet) {
                ActionSheet(title: Text("请选择图片来源".localized), buttons: [
                    .default(Text("拍照".localized)) {
                        isShowActionSheet = false
                        imagePickerSourceType = .camera
                        isShowImagePicker = true
                    },
                    .default(Text("从相册选取".localized)) {
                        isShowActionSheet = false
                        imagePickerSourceType = .photoLibrary
                        isShowImagePicker = true
                    },
                    .cancel()
                ])
            }
        }
    }
}

#if DEBUG
struct UserInfoEditV_Previews: PreviewProvider {
    static var previews: some View {
        UserInfoEditV().environmentObject(AppState.shared)
    }
}
#endif



extension View {
    func popSheet(isPresented: Binding<Bool>, arrowEdge: Edge = .bottom, content: @escaping () -> PopSheet) -> some View {
        Group {
            if UIDevice.current.userInterfaceIdiom == .pad {
                popover(isPresented: isPresented, attachmentAnchor: .point(.topTrailing), arrowEdge: arrowEdge, content: { content().popover(isPresented: isPresented) })
            } else {
                actionSheet(isPresented: isPresented, content: { content().actionSheet() })
            }
        }
    }
}

struct PopSheet {
    let title: Text
    let message: Text?
    let buttons: [PopSheet.Button]
    
    public init(title: Text, message: Text? = nil, buttons: [PopSheet.Button] = [.cancel()]) {
        self.title = title
        self.message = message
        self.buttons = buttons
    }
    
    
    func actionSheet() -> ActionSheet {
        ActionSheet(title: title, message: message, buttons: buttons.map({ popButton in
            switch popButton.kind {
            case .default: return .default(popButton.label, action: popButton.action)
            case .cancel: return .cancel(popButton.label, action: popButton.action)
            case .destructive: return .destructive(popButton.label, action: popButton.action)
            }
        }))
    }
    
    func popover(isPresented: Binding<Bool>) -> some View {
        VStack {
            self.title.padding(.top)
            Divider()
            List {
                ForEach(Array(self.buttons.enumerated()), id: \.offset) { (offset, button) in
                    VStack {
                        SwiftUI.Button(action: {
                            isPresented.wrappedValue = false
                            DispatchQueue.main.async {
                                button.action?()
                            }
                        }, label: {
                            button.label.font(.subheadline)
                        })
                    }
                }
            }
        }
    }
    
    public struct Button {
        let kind: Kind
        let label: Text
        let action: (() -> Void)?
        enum Kind { case `default`, cancel, destructive }
        
        /// Creates a `Button` with the default style.
        public static func `default`(_ label: Text, action: (() -> Void)? = {}) -> Self {
            Self(kind: .default, label: label, action: action)
        }
        
        /// Creates a `Button` that indicates cancellation of some operation.
        public static func cancel(_ label: Text, action: (() -> Void)? = {}) -> Self {
            Self(kind: .cancel, label: label, action: action)
        }
        
        /// Creates an `Alert.Button` that indicates cancellation of some operation.
        public static func cancel(_ action: (() -> Void)? = {}) -> Self {
            Self(kind: .cancel, label: Text("Cancel"), action: action)
        }
        
        /// Creates an `Alert.Button` with a style indicating destruction of some data.
        public static func destructive(_ label: Text, action: (() -> Void)? = {}) -> Self {
            Self(kind: .destructive, label: label, action: action)
        }
    }
}
