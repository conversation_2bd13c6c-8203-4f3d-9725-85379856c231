//
//  UIImage+SizeScale.h
//  wedevotebible
//
//  Created by jack on 24/11/2016.
//  Copyright © 2016 WD Bible Team. All rights reserved.
//

#import <UIKit/UIKit.h>

@interface UIImage (SizeScale)
- (UIImage *)imageCompressFitSizeScaleWithTargetSize:(CGSize)size;
- (UIImage *)clipImageInRect:(CGRect)rect;
- (UIImage *)imageScaledToSize:(CGSize)newSize;

- (instancetype)circleImage;

- (UIImage *)imageWithRoundedCornersSize:(float)cornerRadius;
@end
