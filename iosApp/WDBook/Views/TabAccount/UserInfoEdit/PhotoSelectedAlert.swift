//
//  PhotoSelectedAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/7/24.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct PhotoSelectedAlert: View {
    
    var takePhotoHandler:()->()
    var selectFromAlbumHandler:()->()
    var cancelHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:10) {
                Text("请选择图片来源".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)

                But<PERSON>(action: {
                    takePhotoHandler()
                }) {
                    Text("拍照".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(mainColor)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:40)
                    .background(RoundedRectangle(cornerRadius: 20)
                        .strokeBorder(mainColor, lineWidth: 1))
                .cornerRadius(20)
                
                <PERSON><PERSON>(action: {
                    selectFromAlbumHandler()
                }) {
                    Text("从相册选取".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(mainColor)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:40)
                    .background(RoundedRectangle(cornerRadius: 20)
                        .strokeBorder(mainColor, lineWidth: 1))
                .cornerRadius(20)
                
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .frame(width:300)
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
            .onTapGesture {
                cancelHandler()
            }
    }
}

#if DEBUG
struct PhotoSelectedAlert_Previews: PreviewProvider {
    static var previews: some View {
        PhotoSelectedAlert(takePhotoHandler: {}, selectFromAlbumHandler: {}, cancelHandler: {})
    }
}
#endif
