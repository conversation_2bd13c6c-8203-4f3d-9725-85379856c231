//
//  NicknameEditV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/4.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct NicknameEditV: View {
    @EnvironmentObject var appState: AppState
    @State var nickname = "" // appState.userInfo.nickname ?? ""
    @State var nicknameVerified = VerifyType.none
    @State var nicknameTFFocused = false

    var isDisable: Bool {
        nickname.isBlank
    }

    var body: some View {
        BackNavigation(title: "名字".localized) {
            VStack(spacing: 0) {
                VStack {
                    HStack {
                        TextField("请输入名字，30字符以内".localized, text: $nickname) { focused in
                            nicknameTFFocused = focused
//                            if !focused{
//                                nicknameVerified = Validator.nickname(nickname)
//                            }else{
//                                nicknameVerified = .none
//                            }
                        } onCommit: {}.autocorrectionDisabled(true)
                            .modifier(ClearButtonMode(text: $nickname, focused: $nicknameTFFocused))
                            .onChange(of: nickname) { _ in
                                nicknameVerified = .none
                                if nickname.count > 30 {
                                    nickname = String(nickname.prefix(30))
                                    Toaster.showToast(message: "您最多只能输入30个字符".localized)
                                }
                            }

                        switch nicknameVerified {
                        case .success:
                            EmptyView()
                        case let .failure(msg):
                            Image("icon_exclamation").frame(width: 24, height: 24)
                        case .none:
                            EmptyView()
                        }
                    }.frame(height: 46)
                        .padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(nicknameVerified.borderColor(), lineWidth: 1)
                        )

                    switch nicknameVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }
                Spacer().frame(height: 32)

                Button(action: {
                    if nickname.count > 30 {
                        nicknameVerified = .failure("用户名太长".localized)
                        return
                    }

                    HUDManager.showLoadingBlockHUD(text: "")

                    WDBookUserSDK.shared.updateNickname(nickname: nickname) { result in
                        nicknameVerified = .none
                        HUDManager.hideLoadingHUD()
                        switch result {
                        case let .success(u):
                            if let user = u {
                                AppState.shared.userInfo = UserInfo(entity: user)
                            }
                            Toaster.showToast(message: "保存成功".localized) { _ in
                                RoutableManager.popViewController()
                            }
                        case let .failure(error):
//                            if error.errno == ErrorInfo.invalidverificationcode.code{
//                                Toaster.showToast(message: "验证码输入错误或已过期")
//                            }else{
                            Toaster.showToast(message: error.msg)
//                            }
                        }
                    }

                }) {
                    Text("保存".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height: 44)
                    .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                    .cornerRadius(22)
                    .disabled(isDisable)

                Spacer()
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(24)
            .background(Color(dynamicBackgroundColor3))
            .contentShape(Rectangle()).onTapGesture {
                UIApplication.dismissKeyboard()
            }
            .onAppear {}
        }
    }
}

#if DEBUG
    struct NicknameEditV_Previews: PreviewProvider {
        static var previews: some View {
            NicknameEditV().environmentObject(AppState.shared)
        }
    }
#endif
