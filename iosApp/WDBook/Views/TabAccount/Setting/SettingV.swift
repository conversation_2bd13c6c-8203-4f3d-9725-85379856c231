//
//  SettingV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/22.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import NavigationRouter
import shared
import SwiftUI

struct SettingV: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @State private var showGreeting = true
    @State var presentComment: Bool = false
    @State var detail: String = ""

    var body: some View {
        BackNavigation(title: "应用设置") {
            VStack(alignment: .center, spacing: 0) {
                ScrollView {
                    VStack(alignment: .center, spacing: 8) {
                        VStack(alignment: .center, spacing: 0) {
                            Text("主题").font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                                .frame(maxWidth: .infinity, alignment: .leading).frame(height: 64)

                            ThemeSelectSwiftUIV().frame(maxWidth: .infinity).frame(height: 86)
                            Spacer().frame(height: 42)

                        }.padding(EdgeInsets(top: 0, leading: 32, bottom: 0, trailing: 32))
                            .frame(height: 192, alignment: .center)
                            .background(Color(dynamicBackgroundColor1))

                        VStack(spacing: 0) {
                            NavigationLink {
                                SelectedLanguageV().environmentObject(appState)
                            } label: {
                                SettingRow(title: "多语言", detail: $appState.selectedLanguageText)
                            }
                        }

                        VStack(spacing: 0) {
                            SwitchRow(title: "显示纸书页码", isOn: $appState.showPaperBookPageNum)
                            SwitchRow(title: "阅读期间不自动锁屏", isOn: $appState.notAutoLockScreenOnRead)
                            if !appState.isHide4GDownloadControl {
                                SwitchRow(title: "只在连接WiFi时下载", isOn: $appState.downloadOnlyOnWifi)
                            }
                        }

                        VStack(spacing: 0) {
                            NavigationLink(destination: AboutUsV()) {
                                SettingRow(title: "关于我们")
                            }
                            SettingRow(title: "检查更新", detail: $detail)
                                .onTapGesture(perform: checkAppUpgrade)
                            if LogUtils.shared.isLogRedirectEnabled() {
                                NavigationLink(destination: FileContentView()) {
                                    SettingRow(title: "Console Log")
                                }
                            }
                        }

                        VStack(spacing: 0) {
                            NavigationLink(destination: SpeedupVCWrapper()) {
                                SettingRow(title: "提高网络稳定性".localized)
                                    .contentShape(Rectangle())
                            }
                        }

                        if WDBookSessionSDK.shared.isLogin {
                            VStack(spacing: 0) {
                                RoutedLink(toRoute: .accountAndSecurity) {
                                    SettingRow(title: "账号与安全")
                                        .contentShape(Rectangle())
                                }
                            }
                        }

                        Spacer()
                    }
                }.background(Color(dynamicBackgroundColor3))

                if WDBookSessionSDK.shared.isLogin {
                    Button(action: {
                        WDBookDataSyncManager.shared.signOut(msg: nil) {
                            self.presentationMode.wrappedValue.dismiss()
                            AppState.shared.cleanup()
                            AppState.shared.changeTab(tabName: .store)
                        }
                    }) {
                        Text("退出登录".localized).font(Font.medium(size: 14))
                            .foregroundColor(Color(dynamicRedColor))
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height: 50)
                        .background(Color(dynamicBackgroundColor1))
                }
            }
        }.environment(\.locale, appState.locale)
    }

    private func checkAppUpgrade() {
        if detail.isEmpty {
            HUDManager.showLoadingBlockHUD()
            let oldVersionStr = Bundle.main.infoDictionary!["CFBundleVersion"]! as! String
            var appChannel = APPDistributionChannel.ios
            if isTestFlight1() {
                appChannel = APPDistributionChannel.testFlight
            }
            WDBookAppSDK.shared.checkAppVersion(channel: appChannel) { result in
                switch result {
                case let .success(entity):
                    HUDManager.hideLoadingHUD()
                    if let upgradeEntity = entity {
                        if oldVersionStr == String(upgradeEntity.versionCode) {
                            detail = "已是最新版本".localized
                        } else {
                            AppUtils.showAppUpgradeAlert(upgradeEntity)
                        }
                    } else {
                        detail = "已是最新版本".localized
                    }
                case let .failure(error):
                    HUDManager.hideLoadingHUD()
                    if let sdkException = error as? SDKException {
                        Log.d(sdkException)
                    } else {
                        Log.d(error)
                    }
                }
            }
        } else {
            // showToast 当前已是最新版本
            Toaster.showToast(message: "当前已是最新版本".localized)
        }
    }
}

struct SwitchRow: View {
    @State var title: String
    @Binding var isOn: Bool

    var body: some View {
        HStack {
            Text(LocalizedStringKey(title))
                .font(Font.regular(size: 16))
                .foregroundColor(Color(dynamicTitleColor2))
            Spacer()
            Toggle("", isOn: $isOn).toggleStyle(SwitchToggleStyle(tint: Color(UIColor.primaryColor1)))

        }.frame(minWidth: 0, maxWidth: .infinity)
            .frame(height: 64)
            .padding(.leading, 16)
            .padding(.trailing, 8)
            .background(Color(dynamicBackgroundColor1))
            .overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
    }
}

struct SettingRow: View {
    @State var title: String
    @Binding var detail: String

    init(title: String, detail: Binding<String> = .constant("")) {
        _title = State(initialValue: title)
        _detail = detail
    }

    var body: some View {
        HStack {
            Text(LocalizedStringKey(title))
                .font(Font.regular(size: 16))
                .foregroundColor(Color(dynamicTitleColor2))
            Spacer()
            Text(LocalizedStringKey(detail))
                .font(Font.regular(size: 15))
                .foregroundColor(.secondary)
            Image("arrow_right").frame(width: 24, height: 24, alignment: .center)
                .foregroundColor(Color(dynamicTitleColor))
        }.frame(minWidth: 0, maxWidth: .infinity)
            .frame(height: 64)
            .padding(.leading, 16)
            .padding(.trailing, 8)
            .background(Color(dynamicBackgroundColor1))
            .overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
    }
}

struct SettingSubTitleRow: View {
    @State var title: String
    @State var subTitle: String

    var body: some View {
        HStack {
            VStack {
                Text(LocalizedStringKey(title))
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity, alignment: .leading)
                Text(LocalizedStringKey(subTitle))
                    .font(Font.regular(size: 14))
                    .foregroundColor(.secondary)
                    .frame(maxWidth: .infinity, alignment: .leading)
            }.frame(maxWidth: .infinity)

            Spacer()
            Image("arrow_right").frame(width: 24, height: 24, alignment: .center)
                .foregroundColor(Color(dynamicTitleColor))
        }.frame(minWidth: 0, maxWidth: .infinity)
            .frame(height: 64)
            .padding(.leading, 16)
            .padding(.trailing, 8)
            .background(Color(dynamicBackgroundColor1))
            .overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
    }
}

#if DEBUG
    struct SettingV_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                NavigationView {
                    SettingV().environmentObject(AppState.shared)
                        .environment(\.locale, Locale(identifier: "zh-Hant"))
                }
                NavigationView {
                    SettingV().environmentObject(AppState.shared).previewDevice("iPhone SE (3nd generation)")
                        .environment(\.colorScheme, .dark)
                        .environment(\.locale, Locale(identifier: "zh-Hans"))
                }
            }
        }
    }
#endif
