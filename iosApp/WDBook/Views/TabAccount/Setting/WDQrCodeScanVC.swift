//
//  WDQrCodeScanVC.swift
//  wedevotebible
//
//  Created by 杜文泽 on 2024/1/22.
//  Copyright © 2024 WD Bible Team. All rights reserved.
//

import Foundation
import AVFoundation
import Photos

class WDQrCodeScanVC: UIViewController, AVCaptureMetadataOutputObjectsDelegate {
    var captureSession: AVCaptureSession!
    var previewLayer: AVCaptureVideoPreviewLayer!
    var scanLine: UIView!
    var scanFrame: UIView!
    var scannerCornerLength: CGFloat = 20.0
    var scannerCornerWidth: CGFloat = 4.0
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationItem.title = "扫描二维码".localized
        view.backgroundColor = UIColor.black
        
        // 检查相机权限
        switch AVCaptureDevice.authorizationStatus(for: .video) {
            case .authorized: // 用户已授权
                setupCaptureSession()
            case .notDetermined: // 用户尚未请求权限
                AVCaptureDevice.requestAccess(for: .video) { granted in
                    if granted {
                        DispatchQueue.main.async {
                            self.setupCaptureSession()
                        }
                    }
                }
            case .denied: // 用户已拒绝
            let message = "相机访问被限制或拒绝。请在设备的设置中允许应用访问相机，以使用扫描功能。".localized
            Toaster.showToast(message: message)
                Log.d("AVCaptureDevice.Status.denied:\(message)")
                return // 或者显示一个提示告诉用户如何在设置中打开权限
            case .restricted: // 系统限制
                let message = "相机访问受限。你可以在设置中管理应用的访问权限，以提供更完整的功能。".localized
                Toaster.showToast(message: message)
                Log.d("AVCaptureDevice.Status.restricted:\(message)")
                return // 或者显示一个提示
            @unknown default:
                fatalError()
        }
    }
    
    func addPhotoLibraryButton() {
        let photoLibraryBtn = UIButton(type: .custom)
        photoLibraryBtn.setImage(UIImage.init(named: "icon_photo_library"), for: .normal)
        photoLibraryBtn.addTarget(self, action: #selector(selectPhotoFromAlbum), for: .touchUpInside)
        view.addSubview(photoLibraryBtn)
        
        let textBtn = UIButton(type: .custom)
        textBtn.setTitle("从相册选取".localized, for: .normal)
        textBtn.setTitleColor(.white, for: .normal)
        textBtn.titleLabel?.font = .boldSystemFont(ofSize: 14)
        textBtn.frame = CGRectMake(0, 0, 90, 20)
        textBtn.contentHorizontalAlignment = .center
        textBtn.addTarget(self, action: #selector(selectPhotoFromAlbum), for: .touchUpInside)
        view.addSubview(textBtn)
        
        photoLibraryBtn.snp.makeConstraints { make in
            make.right.equalToSuperview().inset(30)
            make.bottom.equalToSuperview().inset(UIDevice.isFullScreen ? 60 : 50)
            make.width.height.equalTo(50)
        }
        
        textBtn.snp.makeConstraints { make in
            make.top.equalTo(photoLibraryBtn.snp.bottom).offset(5)
            make.centerX.equalTo(photoLibraryBtn)
            make.width.equalTo(90)
            make.height.equalTo(20)
        }
        
        view.bringSubviewToFront(photoLibraryBtn)
        view.bringSubviewToFront(textBtn)
    }
    
    @objc func selectPhotoFromAlbum() {
        // 检查照片访问权限
        let photoAuthorizationStatus = PHPhotoLibrary.authorizationStatus()
        switch photoAuthorizationStatus {
        case .authorized:
            self.presentPhotoPicker()
        case .notDetermined:
            PHPhotoLibrary.requestAuthorization({ (newStatus) in
                if newStatus == .authorized {
                    self.presentPhotoPicker()
                }
            })
        case .restricted:
            // 当权限受限时，通常是由于家长控制或企业政策等原因，用户可能无法更改这些设置。
            let message = "照片访问权限受限。由于系统限制，应用无法访问您的照片库。请联系您的设备管理员或检查家长控制设置。".localized
            Toaster.showToast(message: message)
            Log.d("PHPhotoLibrary.Status.restricted:\(message)")
            break
        case .denied:
            // 当权限被拒绝时，用户需要去设置中手动允许应用访问照片库。
            let message = "照片访问权限被拒绝。请前往设置 > 隐私 > 照片，找到应用并开启访问权限，以便使用照片选择和保存功能。".localized
            Toaster.showToast(message: message)
            Log.d("PHPhotoLibrary.Status.denied:\(message)")
            break
        case .limited:
            // 当权限受限时，用户只允许访问部分照片。iOS 14及以上版本中，用户可以选择更多照片或更改权限。
            let message = "照片访问权限受限。您已选择只允许访问部分照片。如果想要更改访问权限或选择更多照片，请前往设置。".localized
            Toaster.showToast(message: message)
            Log.d("PHPhotoLibrary.Status.limited:\(message)")
            break
        @unknown default:
            // Handle any new cases
            break
        }
    }
    
    func presentPhotoPicker() {
        DispatchQueue.main.async {
            let picker = UIImagePickerController()
            picker.sourceType = .photoLibrary
            picker.delegate = self
            self.present(picker, animated: true, completion: nil)
        }
    }

    // 将设置捕捉会话的代码移动到一个单独的方法中
    func setupCaptureSession() {
        // ... (捕捉会话的设置代码)
        captureSession = AVCaptureSession()

        guard let videoCaptureDevice = AVCaptureDevice.default(for: .video) else { return }
        let videoInput: AVCaptureDeviceInput

        do {
            videoInput = try AVCaptureDeviceInput(device: videoCaptureDevice)
        } catch {
            // Handle the error appropriately.
            return
        }

        if (captureSession.canAddInput(videoInput)) {
            captureSession.addInput(videoInput)
        } else {
            // Handle the failure.
            return
        }

        let metadataOutput = AVCaptureMetadataOutput()

        if (captureSession.canAddOutput(metadataOutput)) {
            captureSession.addOutput(metadataOutput)

            metadataOutput.setMetadataObjectsDelegate(self, queue: DispatchQueue.main)
            metadataOutput.metadataObjectTypes = [.qr] // Here we are interested in QR codes only.
        } else {
            // Handle the failure.
            return
        }

        // Initialize the video preview layer and add it as a sublayer to the viewPreview view's layer.
        previewLayer = AVCaptureVideoPreviewLayer(session: captureSession)
        previewLayer.frame = view.layer.bounds
        previewLayer.videoGravity = .resizeAspectFill
        view.layer.addSublayer(previewLayer)

        addScanFrame()
        startScanning()
    }
    
    // 添加扫描框
    func addScanFrame() {
        // 假设你想要将扫描框放在视图的中心，并且大小为视图宽度的80%
        let screenSize = view.bounds.size
        var scanFrameWidth = min(screenSize.width, screenSize.height)
        if UIDevice.isIpad() {
            scanFrameWidth = 400
        }
        let scanFrameSize = CGSize(width: scanFrameWidth * 0.8, height: scanFrameWidth * 0.8)
        let scannerframe = CGRect(
            x: (screenSize.width - scanFrameSize.width) / 2,
            y: (screenSize.height - scanFrameSize.height) / 2 - 64.0,
            width: scanFrameSize.width,
            height: scanFrameSize.height
        )
        scannerCornerLength = scanFrameWidth/6
        scannerCornerWidth = 6.0
        
        let scannerOverlayView = ScannerOverlayView(frame: scannerframe, cornerLength: scannerCornerLength, cornerWidth: scannerCornerWidth, cornerColor: .white)
        view.addSubview(scannerOverlayView)
        view.bringSubviewToFront(scannerOverlayView) // 确保扫描框在最前面
        self.scanFrame = scannerOverlayView
        
        // 扫描动画
        scanLine = UIView()
        scanLine.frame = CGRect(x: scannerCornerWidth, y: scannerCornerWidth, width: scanFrame.bounds.width-scannerCornerWidth*2, height: 3)
        scanLine.backgroundColor = UIColor.red
        scanFrame.addSubview(scanLine)
        
        addPhotoLibraryButton()
    }

    func addScanLineAnimation() {
        let animation = CABasicAnimation(keyPath: "transform.translation.y")
        animation.fromValue = scannerCornerWidth
        animation.toValue = scanFrame.bounds.height - scanLine.frame.height - scannerCornerWidth*2
        animation.duration = 2.0
        animation.repeatCount = Float.infinity
        animation.autoreverses = true
        scanLine.layer.add(animation, forKey: "scanLineAnimation")
    }
    
    func startScanning() {
        // Start video capture.
        DispatchQueue.global(qos: .background).async { [self] in
            captureSession.startRunning()
        }
        // scan animation
        addScanLineAnimation()
    }

    func stopScanning() {
        captureSession.stopRunning()
        scanLine.layer.removeAllAnimations()
    }
    
    func metadataOutput(_ output: AVCaptureMetadataOutput, didOutput metadataObjects: [AVMetadataObject], from connection: AVCaptureConnection) {
        captureSession.stopRunning()

        if let metadataObject = metadataObjects.first {
            guard let readableObject = metadataObject as? AVMetadataMachineReadableCodeObject else { return }
            guard let stringValue = readableObject.stringValue else { return }
            AudioServicesPlaySystemSound(SystemSoundID(kSystemSoundID_Vibrate))
            found(code: stringValue)
        }
        // dismiss(animated: true)
    }

    func found(code: String) {
        // 需要判断二维码内容合法性，如果有效，返回上一级页面；没有效果，则停留当前页面继续扫描
        if code.hasPrefix("wdcft://") {
            scanLine.layer.removeAllAnimations()
            let qrCodeContent = code.replacingOccurrences(of: "wdcft://", with: "")
            let contentArr = qrCodeContent.components(separatedBy: ":")
            let codeTitle = contentArr.first
            let codeContent = contentArr.last
            let createTime = Int64(Date().timeIntervalSince1970)
            WDBookApiServerSDK.shared.addSpeedUpServer(title: codeTitle ?? "", key: codeContent ?? "")
            self.navigationController?.popViewController(animated: true)
        } else {
            qrCodeIsNotCompliant(withCode: code)
        }
    }

    func qrCodeIsNotCompliant(withCode code: String) {
        let alert = UIAlertController(title: "", message: "您扫描到的二维码无效".localized, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "确定".localized, style: .default, handler: { [self] _ in
            // 如果你希望用户点击确定后继续扫描，这里需要重新启动捕捉会话
            startScanning()
        }))
        present(alert, animated: true)
    }
}

extension WDQrCodeScanVC: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true, completion: nil)
        guard let image = info[.originalImage] as? UIImage else { return }
        detectQRCode(image)
    }

    func detectQRCode(_ image: UIImage) {
        guard let ciImage = CIImage(image: image) else { return }
        
        let context = CIContext()
        let detector = CIDetector(ofType: CIDetectorTypeQRCode, context: context, options: [CIDetectorAccuracy: CIDetectorAccuracyHigh])
        
        let features = detector?.features(in: ciImage) as? [CIQRCodeFeature]
        
        let qrCodes = features?.compactMap { $0.messageString }.filter { !$0.isEmpty }
        
        // 可以处理多个二维码，这里只处理第一个
        if let qrCode = qrCodes?.first {
            // 在这里处理扫描到的二维码
            AudioServicesPlaySystemSound(SystemSoundID(kSystemSoundID_Vibrate))
            found(code: qrCode)
        }
    }
}

class ScannerOverlayView: UIView {

    var cornerLength: CGFloat = 20.0
    var cornerWidth: CGFloat = 4.0
    var cornerColor: UIColor = .green

    override func draw(_ rect: CGRect) {
        super.draw(rect)

        // 创建路径
        let path = UIBezierPath()

        // 绘制四个角
        // 左上角
        path.move(to: CGPoint(x: rect.minX, y: rect.minY + cornerLength))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.minX + cornerLength, y: rect.minY))

        // 右上角
        path.move(to: CGPoint(x: rect.maxX - cornerLength, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.minY + cornerLength))

        // 右下角
        path.move(to: CGPoint(x: rect.maxX, y: rect.maxY - cornerLength))
        path.addLine(to: CGPoint(x: rect.maxX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.maxX - cornerLength, y: rect.maxY))

        // 左下角
        path.move(to: CGPoint(x: rect.minX + cornerLength, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY))
        path.addLine(to: CGPoint(x: rect.minX, y: rect.maxY - cornerLength))

        // 设置角的样式
        cornerColor.setStroke()
        path.lineWidth = cornerWidth
        path.stroke()
    }

    // 初始化方法
    init(frame: CGRect, cornerLength: CGFloat, cornerWidth: CGFloat, cornerColor: UIColor) {
        self.cornerLength = cornerLength
        self.cornerWidth = cornerWidth
        self.cornerColor = cornerColor
        super.init(frame: frame)
        self.backgroundColor = .clear
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)
    }
}


