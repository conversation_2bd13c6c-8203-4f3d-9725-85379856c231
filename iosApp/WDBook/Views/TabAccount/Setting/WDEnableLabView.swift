//
//  WDEnableLabView.swift
//  WDBook
//
//  Created by 杜文泽 on 2024/6/20.
//  Copyright © 2024 WeDevote Bible. All rights reserved.
//

import SwiftUI
import wdebug

struct WDEnableLabView: View {
    @State private var useTestCftParamConfig: Bool = WDBookSessionSDK.shared.useTestCftParamConfig
    @State private var logSwitchOn: Bool = WDBookSessionSDK.shared.showCftLog
    
    var body: some View {
        BackNavigation(title: "WDEnableLabView") {
            ScrollView {
                VStack(spacing: 20) {
                    VStack(spacing: 10) {
                        Text("CFT参数下发环境")
                            .font(.headline)
                            .foregroundColor(.black)
                        
                        Picker("CFT参数下发环境", selection: $useTestCftParamConfig) {
                            Text("正式环境（用户）").tag(false)
                            Text("正式环境（测试）").tag(true)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: useTestCftParamConfig) { newValue in
                            WDBookSessionSDK.shared.useTestCftParamConfig = newValue
                        }
                    }
                    .padding(.top, 20)
                    
                    VStack(spacing: 10) {
                        Text("CFT打印日志开关")
                            .font(.headline)
                            .foregroundColor(.black)
                        
                        Picker("CFT打印日志开关", selection: $logSwitchOn) {
                            Text("CFT日志（开）").tag(true)
                            Text("CFT日志（关）").tag(false)
                        }
                        .pickerStyle(SegmentedPickerStyle())
                        .onChange(of: logSwitchOn) { newValue in
                            WDBookSessionSDK.shared.showCftLog = newValue
                            WDebug.shared().isOn = newValue
                        }
                    }
                }
                .padding()
            }
            .frame(maxWidth: .infinity)
            .background(Color(dynamicBackgroundColor4))
        }
    }
}

struct WDEnableLabView_Previews: PreviewProvider {
    static var previews: some View {
        WDEnableLabView()
    }
}
