//
//  AboutUsV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/10.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter

struct AboutUsV: View {
    @State private var textClickCount = 0
    @State private var imageClickCount = 0
    @State private var navigateToWDEnableLabView = false

    var body: some View {
        BackNavigation(title: "关于我们".localized) {
            VStack(alignment: .center, spacing: 0) {
                Spacer()
                
                VStack {
                    Image("logo")
                        .frame(width: 94, height: 94)
                        .onTapGesture {
                            if textClickCount >= 7 {
                                imageClickCount += 1
                                if imageClickCount == 3 {
                                    performAction()
                                }
                            }
                        }
                    Spacer().frame(height: 23)
                    Text("微读书城".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.medium(size: 24))
                        .onTapGesture {
                            textClickCount += 1
                        }
                    
                    Spacer().frame(height: 10)
                    Text("V\(Bundle.main.infoDictionary!["CFBundleShortVersionString"]! as! String)")
                        .foregroundColor(Color(dynamicTextColor6))
                        .font(Font.regular())
                }
                Spacer()
                Spacer()
                VStack(spacing: 10) {
                    Text("WeDevote Bible 版权所有".localized)
                    
                    HStack {
                        Text("Copyright")
                        Image("copyright").foregroundColor(Color(dynamicTextColor7))
                        Text("2020-2030 WeDevote Bible. All rights reserved.")
                    }
                    
                    HStack(spacing: 8) {
                        RoutedLink(toRoute: .agreement) {
                            Text("用户协议".localized).underline()
                        }
                        RoutedLink(toRoute: .privacyPolicy) {
                            Text("隐私条款".localized).underline()
                        }
                    }
                }
                .foregroundColor(Color(dynamicTextColor7))
                .font(Font.regular(size: 12))
                
                Spacer().frame(height: 30)
            }
            .frame(maxWidth: .infinity)
            .background(Color(dynamicBackgroundColor4))
            .navigationBarTitle("关于我们".localized, displayMode: .inline)
            .background(
                NavigationLink(destination: WDEnableLabView(), isActive: $navigateToWDEnableLabView) {
                    EmptyView()
                }
            )
        }
    }
    
    private func performAction() {
        // 在这里执行跳转操作
        imageClickCount = 0
        textClickCount = 0
        navigateToWDEnableLabView = true
    }
}

#if DEBUG
struct AboutUsV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            NavigationView {
                AboutUsV()
            }
            NavigationView {
                AboutUsV()
                    .previewDevice("iPhone SE (2nd generation)")
                    .environment(\.colorScheme, .dark)
            }
        }
    }
}
#endif
