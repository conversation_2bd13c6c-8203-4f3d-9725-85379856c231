//
//  WDInternetSpeedupVC.swift
//  wedevotebible
//
//  Created by 杜文泽 on 2024/1/18.
//  Copyright © 2024 WD Bible Team. All rights reserved.
//

import Foundation
import shared
import SwiftyUserDefaults
import SwiftUI

// view dynamicBackgroundColor3
// Title dynamicTitleColor2
// 灰色 .secondary
// 红色 UIColor(hex: 0xE33733)
// 绿色
// cell dynamicBackgroundColor1

extension DefaultsKeys {
    var internetSpeedupServer: DefaultsKey<String?> { .init("internetSpeedupServer", defaultValue: nil) }
}

class WDInternetSpeedupVC: UIViewController {
    var collectionView: UICollectionView?
    var emptyView: UIView?
    var serviceArray: Array<Any>?
    var collectionLayout: UICollectionViewFlowLayout!
    private var speedUpServer: String = Defaults[\.internetSpeedupServer] ?? ""
    private var indexPath: IndexPath?
    private var connectStatus: Int = 2
    
    var closeAction: (() -> Void)?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        self.navigationItem.title = "提高网络稳定性".localized
        self.navigationItem.backButtonTitle = "返回".localized
        view.backgroundColor = dynamicBackgroundColor3
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        configureNavigationBar()
        configureResourceData()
        refreshInterfaceView()
    }
    
    func configureNavigationBar() {
        navigationController?.navigationBar.isTranslucent = false
        
        navigationController?.navigationBar.shadowImage = UIImage.creatImageWithColor(color: UIColor.clear)
        navigationController?.navigationBar.barTintColor = dynamicBackgroundColor1
        navigationController?.navigationBar.tintColor = dynamicSegmentTitleColor
        navigationController?.navigationBar.titleTextAttributes = [NSAttributedString.Key.foregroundColor: dynamicSegmentTitleColor]
        
        navigationItem.setLeftBarButtonItems([UIBarButtonItem(image: UIImage(named: "back_ui"), style: .plain,  target: self, action: #selector(backAction))], animated: false)
        
        if #available(iOS 15.0, *) {
            let navigationBarAppearance = UINavigationBarAppearance()
            navigationBarAppearance.backgroundColor = dynamicBackgroundColor1
            navigationBarAppearance.backgroundImage = nil
            navigationBarAppearance.backgroundEffect = nil
            navigationBarAppearance.titleTextAttributes = [NSAttributedString.Key.foregroundColor: dynamicSegmentTitleColor]
            navigationController?.navigationBar.scrollEdgeAppearance = navigationBarAppearance
            navigationController?.navigationBar.standardAppearance = navigationBarAppearance
        }
    }
    
    @objc func backAction() {
        // 执行返回动作
        closeAction?()
    }
    
    func configureResourceData() {
        if serviceArray == nil {
            serviceArray = Array()
        }
        serviceArray?.removeAll()
        
        // 获取本地数据
        
        let serverList = WDBookApiServerSDK.shared.getSpeedUpServerList()
        
        serviceArray?.append(contentsOf: serverList)
        if serviceArray!.count > 0 {
            if speedUpServer.count == 0 {
                let server = serviceArray!.first as! SpeedUpServerEntity
                updateSpeedupService(server.key)
            } else {
                connectStatus = NetworkUtils.shared.isCftStarted ? 1 : 0
            }
        }
    }
    
    func updateSpeedupService(_ serverStr: String) {
        Defaults[\.internetSpeedupServer] = serverStr
        speedUpServer = serverStr
        
        NetworkUtils.shared.stopProxyServer()
        NetworkUtils.shared.updateNetworkConnection { [self] isConnect in
            if isConnect {
                connectStatus = 1
            } else {
                connectStatus = 0
            }
            DispatchQueue.main.async { [self] in
                collectionView?.reloadData()
            }
        }
    }
    
    func refreshInterfaceView() {
        for subview in view.subviews {
            subview.removeFromSuperview()
        }
        if serviceArray!.count > 0 {
            addServiceView()
        } else {
            addEmptyView()
        }
    }
    
    func addServiceView() {
        if emptyView?.superview != nil {
            emptyView?.removeFromSuperview()
        }
        let layout = UICollectionViewFlowLayout.init()
        layout.minimumInteritemSpacing = 16
        layout.minimumLineSpacing = 16
        layout.scrollDirection = .vertical
        layout.sectionInset = UIEdgeInsets.init(top: 0, left: 0, bottom: 0, right: 0)
        // 设置分区头视图和尾视图宽高
        layout.headerReferenceSize = CGSize.init(width: 0, height: 0)
        layout.footerReferenceSize = CGSize.init(width: 0, height: 0)
        self.collectionLayout = layout

        collectionView = UICollectionView(frame: .zero, collectionViewLayout: self.collectionLayout)
        collectionView?.delegate = self
        collectionView?.dataSource = self
        collectionView?.showsHorizontalScrollIndicator = false
        collectionView?.backgroundColor = .clear
        collectionView?.register(WDInternetSpeedupCell.self, forCellWithReuseIdentifier: "WDInternetSpeedupCell")
        collectionView?.register(WDInternetSpeedupFooterView.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionFooter, withReuseIdentifier: WDInternetSpeedupFooterView.reuseIdentifier)
        let insets = UIDevice.isIpad() ? 20.0 : 16.0
        collectionView?.contentInset = UIEdgeInsets(top: insets, left: insets, bottom: insets, right: insets)
        view.addSubview(collectionView!)
        collectionView!.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func addEmptyView() {
        let emptyView = UIView()
        emptyView.backgroundColor = .clear
        view.addSubview(emptyView)
        self.emptyView = emptyView
        emptyView.snp.makeConstraints { make in
            make.centerY.equalToSuperview().offset(-64)
            if UIDevice.isIpad() {
                make.centerX.equalToSuperview()
                make.width.height.equalTo(400)
            } else {
                make.left.equalToSuperview().offset(20)
                make.right.equalToSuperview().offset(-20)
                make.height.equalTo(emptyView.snp.width)
            }
        }
        
        let tipLabel = UILabel()
        tipLabel.textColor = dynamicTextColor4
        tipLabel.textAlignment = .center
        tipLabel.font = .systemFont(ofSize: 14)
        tipLabel.numberOfLines = 0
        tipLabel.text = "请扫描给定的二维码，提升您的网络连接稳定性。".localized
        emptyView.addSubview(tipLabel)
        tipLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.5)
        }
        
        let addServiceBtn = UIButton(type: .custom)
        addServiceBtn.backgroundColor = dynamicBackgroundColor1
        addServiceBtn.layer.cornerRadius = 22.0
        addServiceBtn.layer.masksToBounds = true
        addServiceBtn.layer.borderWidth = 1.0
        addServiceBtn.layer.borderColor = dynamicBorderColor.cgColor
        addServiceBtn.addTarget(self, action: #selector(addSpeedupService), for: .touchUpInside)
        addServiceBtn.setTitle("添加二维码".localized, for: .normal)
        addServiceBtn.setTitleColor(dynamicTextColor4, for: .normal)
        emptyView.addSubview(addServiceBtn)
        addServiceBtn.snp.makeConstraints { make in
            make.top.equalTo(tipLabel.snp.bottom).offset(30)
            make.left.right.equalTo(tipLabel)
            make.height.equalTo(44)
        }
    }
    
    @objc func addSpeedupService() {
        let qrcodeScanVC = WDQrCodeScanVC()
        self.navigationController?.pushViewController(qrcodeScanVC, animated: true)
    }
}

extension WDInternetSpeedupVC: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return 1
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return serviceArray?.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        var collectionWidth = collectionView.frame.size.width-33
        if UIDevice.isIpad() {
            if isAppRunningInSplitView() {
                // 分屏模式下
                if collectionView.frame.size.width <= UIScreen.main.bounds.size.width/2 {
                    if collectionWidth > 375.0 {
                        collectionWidth = 375.0
                    }
                } else {
                    collectionWidth = (collectionView.frame.size.width-72)/2
                }
            } else {
                // 全屏模式下
                var width = collectionView.frame.width
                if collectionView.frame.width < collectionView.frame.height {
                    // 竖屏
                    width = collectionView.frame.height
                }
                collectionWidth = (width-72)/3
            }
        }
        return CGSize(width: collectionWidth, height: collectionWidth+130.0)
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "WDInternetSpeedupCell", for: indexPath) as! WDInternetSpeedupCell
        let server = serviceArray![indexPath.item] as! SpeedUpServerEntity
        cell.speedupServer = server
        if speedUpServer == server.key {
            self.indexPath = indexPath
            if NetworkUtils.shared.isCftStarted {
                cell.connectStatus = connectStatus // 正在连接
            } else {
                cell.connectStatus = nil
            }
        }
        cell.deleteInternetServerCallBack = { [self] serverStr in
            if speedUpServer == serverStr {
                Defaults[\.internetSpeedupServer] = ""
                speedUpServer = ""
            }
            configureResourceData()
            refreshInterfaceView()
            if serviceArray!.count == 0 {
                // 二维码被删除完，没有可用二维码的话，需要重新判断是否开启cft
                NetworkUtils.shared.stopProxyServer()
                NetworkUtils.shared.updateNetworkConnection(nil)
            }
        }
        cell.useInternetServiceCallBack = { [self] serverStr in
            updateSpeedupService(serverStr)
            collectionView.reloadData()
        }
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
    }
    
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        // 确保我们正在处理footer视图
        guard kind == UICollectionView.elementKindSectionFooter else {
            fatalError("Unexpected element kind")
        }
        
        // 获取footer视图
        let footer = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: WDInternetSpeedupFooterView.reuseIdentifier, for: indexPath) as! WDInternetSpeedupFooterView
        footer.addServiceBtnBeClick = {
            let qrcodeScanVC = WDQrCodeScanVC()
            self.navigationController?.pushViewController(qrcodeScanVC, animated: true)
        }
        return footer
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForFooterInSection section: Int) -> CGSize {
        // 返回footer视图的尺寸
        return CGSize(width: collectionView.bounds.width, height: UIDevice.isIpad() ? 120 : 80)
    }
    
    func isAppRunningInSplitView() -> Bool {
        if #available(iOS 13.0, *) {
            guard let scene = UIApplication.shared.connectedScenes.first as? UIWindowScene else {
                return false
            }
            if let sizeRestrictions = scene.sizeRestrictions {
                let minimumSize = sizeRestrictions.minimumSize
                let maximumSize = sizeRestrictions.maximumSize
                
                // 如果最小尺寸和最大尺寸不相等，那么应用可能在分屏模式下运行
                return minimumSize != maximumSize
            }
        }
        return false
    }
}

class WDInternetSpeedupCell: UICollectionViewCell {
    private var containerView: UIView!
    private var qrCodeImageView: UIImageView!
    private var codeTitleLabel: UILabel!
    private var dateTitleLabel: UILabel!
    private var statusContainer: UIView!
    private var statusImageView: UIImageView!
    private var statusTitleLabel: UILabel!
    private var deleteButton: UIButton!
    private var serviceUseButton: UIButton!
    open var deleteInternetServerCallBack:((_ serverStr: String) -> ())?
    open var useInternetServiceCallBack:((_ serverStr: String) -> ())?
    
    private var _speedupServer: SpeedUpServerEntity?
    var speedupServer: SpeedUpServerEntity? {
        get {
            return _speedupServer
        }
        set {
            _speedupServer = newValue
            codeTitleLabel.text = _speedupServer?.title
            let dateStr = convertTimestampToDateString(timestamp: TimeInterval(_speedupServer!.createTime))
            dateTitleLabel.text = String.init(format: "%@ %@", "添加于".localized, dateStr)
            let formatServerContent = "wdcft://\(_speedupServer?.title ?? ""):\(_speedupServer?.key ?? "")"
            if Defaults[key: DefaultsKeys.IS_DARK_MODE] {
                qrCodeImageView.image = QRCodeGenerator.generateQRCodeImage(withLink: formatServerContent, sizeLength: 100*2, colorRed: 1.0, green: 1.0, blue: 1.0)
            } else {
                qrCodeImageView.image = QRCodeGenerator.generateQRCodeImage(withLink: formatServerContent, sizeLength: 100*2, colorRed: 0.0, green: 0.0, blue: 0.0)
            }
        }
    }
    
    private var _connectStatus: Int?  // 0:Invalid; 1:Active; 2:Connecting
    var connectStatus: Int? {
        get {
            return _connectStatus
        }
        set {
            _connectStatus = newValue
            statusContainer.isHidden = false
            if _connectStatus == 0 {
                self.statusImageView.image = UIImage(named: "service_status_invalid")
                self.statusTitleLabel.textColor = UIColor(hex: 0xE33733)
                self.statusTitleLabel.text = "链接无效".localized
            } else if _connectStatus == 1 {
                self.statusImageView.image = UIImage(named: "service_status_active")
                self.statusTitleLabel.textColor = UIColor(hex: 0x24B144)
                self.statusTitleLabel.text = "已连接".localized
            } else if _connectStatus == 2 {
                self.statusImageView.image = UIImage(named: "service_status_connecting")
                self.statusTitleLabel.textColor = dynamicBorderColor
                self.statusTitleLabel.text = "正在连接".localized
            }
            self.serviceUseButton.isSelected = true
            self.serviceUseButton.isUserInteractionEnabled = false
            self.containerView.layer.borderColor = UIColor(hex: 0xE33733).cgColor
        }
    }
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        self.backgroundColor = .clear

        initialize()
    }

    required init?(coder aDecoder: NSCoder) {
        super.init(coder: aDecoder)

        fatalError("Interface Builder is not supported!")
    }

    override func awakeFromNib() {
        super.awakeFromNib()

        fatalError("Interface Builder is not supported!")
    }

    func initialize() {
        self.backgroundColor = .clear
        contentView.backgroundColor = .clear
        let containerView = UIView()
        containerView.layer.cornerRadius = 16.0
        containerView.layer.borderWidth = 1.5
        containerView.layer.borderColor = UIColor.clear.cgColor
        containerView.layer.masksToBounds = true
        containerView.backgroundColor = dynamicBackgroundColor1
        contentView.addSubview(containerView)
        self.containerView = containerView
        containerView.snp.makeConstraints { make in
            make.edges.equalTo(contentView).inset(UIEdgeInsets.zero)
        }
        
        let topView = UIView()
        topView.backgroundColor = .clear
        containerView.addSubview(topView)
        topView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.equalToSuperview().offset(20)
            make.right.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }
        
        let deleteButton = UIButton(type: .custom)
        deleteButton.backgroundColor = .clear
        deleteButton.addTarget(self, action: #selector(deleteSpeedupService), for: .touchUpInside)
        deleteButton.setImage(UIImage.init(named: "close_24"), for: .normal)
        topView.addSubview(deleteButton)
        deleteButton.snp.makeConstraints { make in
            make.right.equalToSuperview().inset(10)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
        
        let codeTitleLabel = UILabel()
        codeTitleLabel.backgroundColor = .clear
        codeTitleLabel.textAlignment = .left
        codeTitleLabel.font = .boldSystemFont(ofSize: 28)
        codeTitleLabel.numberOfLines = 1
        codeTitleLabel.text = "X7S2ND"
        topView.addSubview(codeTitleLabel)
        self.codeTitleLabel = codeTitleLabel
        codeTitleLabel.textColor = dynamicTitleColor
        codeTitleLabel.snp.makeConstraints { make in
            make.left.top.equalToSuperview()
            make.right.equalTo(deleteButton.snp.left).offset(-10)
            make.height.equalTo(34)
        }
        
        let dateTitleLabel = UILabel()
        dateTitleLabel.backgroundColor = UIColor.clear
        dateTitleLabel.textAlignment = NSTextAlignment.left
        dateTitleLabel.font = UIFont.systemFont(ofSize: 12)
        dateTitleLabel.numberOfLines = 1
        dateTitleLabel.text = "Added on 03/08/2024"
        topView.addSubview(dateTitleLabel)
        self.dateTitleLabel = dateTitleLabel
        dateTitleLabel.textColor = dynamicTitleColor3
        dateTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(codeTitleLabel.snp.bottom)
            make.left.right.equalTo(codeTitleLabel)
            make.height.equalTo(16)
        }

        let qrCodeImage = UIImageView()
        qrCodeImage.backgroundColor = UIColor.clear
        containerView.addSubview(qrCodeImage)
        self.qrCodeImageView = qrCodeImage
        qrCodeImage.snp.makeConstraints { make in
            make.top.equalTo(topView.snp.bottom).offset(10)
            make.left.right.equalTo(topView)
            make.height.equalTo(qrCodeImage.snp.width)
        }
        
        let statusContainer = UIView()
        statusContainer.backgroundColor = .clear
        statusContainer.isHidden = true
        containerView.addSubview(statusContainer)
        self.statusContainer = statusContainer
        statusContainer.snp.makeConstraints { make in
            make.top.equalTo(qrCodeImage.snp.bottom)
            make.left.right.equalTo(qrCodeImage)
            make.height.equalTo(16)
        }
        
        let statusImageView = UIImageView()
        statusImageView.backgroundColor = .clear
        statusContainer.addSubview(statusImageView)
        self.statusImageView = statusImageView
        statusImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(10)
            make.top.bottom.equalToSuperview()
            make.width.equalTo(16)
        }
        
        let statusLabel = UILabel()
        statusLabel.backgroundColor = UIColor.clear
        statusLabel.textAlignment = NSTextAlignment.left
        statusLabel.font = UIFont.systemFont(ofSize: 14)
        statusLabel.numberOfLines = 1
        containerView.addSubview(statusLabel)
        self.statusTitleLabel = statusLabel
        statusLabel.snp.makeConstraints { make in
            make.left.equalTo(statusImageView.snp.right).offset(8)
            make.top.bottom.equalTo(statusImageView)
            make.right.equalToSuperview()
        }
        
        let useButton = UIButton(type: .custom)
        useButton.layer.cornerRadius = 20.0
        useButton.layer.masksToBounds = true
        useButton.layer.borderWidth = 1.0
        useButton.layer.borderColor = dynamicBorderColor.cgColor
        useButton.setTitle("使用此二维码".localized, for: .normal)
        useButton.setTitle("已使用该二维码".localized, for: .selected)
        useButton.setBackgroundImage(UIImage.creatImageWithColor(color: dynamicBackgroundColor4), for: .normal)
        useButton.setBackgroundImage(UIImage.creatImageWithColor(color: dynamicBtnSelectedBGColor), for: .selected)
        useButton.setBackgroundImage(UIImage.creatImageWithColor(color: dynamicBtnSelectedBGColor), for: .highlighted)
        useButton.setTitleColor(dynamicTitleColor , for: .normal)
        useButton.addTarget(self, action: #selector(userCurrentService), for: .touchUpInside)
        containerView.addSubview(useButton)
        useButton.isSelected = false
        useButton.isUserInteractionEnabled = true
        self.serviceUseButton = useButton
        useButton.snp.makeConstraints { make in
            make.top.equalTo(qrCodeImage.snp.bottom).offset(24)
            make.left.right.equalTo(qrCodeImage)
            make.height.equalTo(40)
        }
    }
    
    @objc func userCurrentService(_ sender: UIButton) {
        if !sender.isSelected {
            if let serverStr = _speedupServer?.key {
                useInternetServiceCallBack?(serverStr)
            }
        }
    }
    
    @objc func deleteSpeedupService() {
        DispatchQueue.main.async {
            let alertController = UIAlertController(title: "", message: "您确定要删除此二维码吗？".localized, preferredStyle: .alert)
            alertController.addAction(UIAlertAction(title: "确定".localized, style: .default, handler: { [self] _ in
                if WDBookApiServerSDK.shared.deleteSpeedUpServer(key: _speedupServer?.key ?? "") {
                    deleteInternetServerCallBack?(_speedupServer?.key ?? "")
                }
            }))
            alertController.addAction(UIAlertAction(title: "取消".localized, style: .cancel, handler: nil))
            if let viewController = UIApplication.shared.keyWindow?.rootViewController {
                viewController.present(alertController, animated: true, completion: nil)
            }
        }
    }
    
    func convertTimestampToDateString(timestamp: TimeInterval, dateFormat: String = "MM/dd/yyyy") -> String {
        let date = Date(timeIntervalSince1970: timestamp)
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = dateFormat
        dateFormatter.timeZone = TimeZone.current // 使用当前时区
        let dateStr = dateFormatter.string(from: date)
        return dateStr
    }

    override func prepareForReuse() {
        super.prepareForReuse()

        self.qrCodeImageView.image = nil
        self.codeTitleLabel.text = nil
        self.dateTitleLabel.text = nil
        self.statusTitleLabel?.text = nil
        self.statusContainer.isHidden = true
        self.serviceUseButton.isSelected = false
        self.serviceUseButton.isUserInteractionEnabled = true
        self.containerView.layer.borderColor = UIColor.clear.cgColor
    }
}

class WDInternetSpeedupFooterView: UICollectionReusableView {
    static let reuseIdentifier = "WDInternetSpeedupFooterView"
    // 添加footer视图的内容
    private var addServiceButton = UIButton(type: .custom)
    open var addServiceBtnBeClick:(() -> ())?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        addServiceButton.backgroundColor = dynamicBackgroundColor1
        addServiceButton.layer.cornerRadius = 22.0
        addServiceButton.layer.masksToBounds = true
        addServiceButton.layer.borderWidth = 1.0
        addServiceButton.layer.borderColor = dynamicBorderColor.cgColor
        addServiceButton.addTarget(self, action: #selector(addSpeedupService), for: .touchUpInside)
        addServiceButton.setTitle("添加二维码".localized, for: .normal)
        addServiceButton.setTitleColor(dynamicTextColor4, for: .normal)
        addSubview(addServiceButton)
        
        addServiceButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.height.equalTo(44)
            if UIDevice.isIpad() {
                make.centerX.equalToSuperview()
                make.width.equalToSuperview().multipliedBy(0.7)
            } else {
                make.left.equalToSuperview().offset(20)
                make.right.equalToSuperview().inset(20)
            }
        }
    }
    
    @objc func addSpeedupService() {
        self.addServiceBtnBeClick?()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
}


