//
//  DeleteAccountAppliedV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/11/25.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct DeleteAccountAppliedV: View {
    @EnvironmentObject var appState:AppState
    
    var body: some View {
        BackNavigation(title: "注销账号".localized){
            VStack(alignment: .center, spacing: 0) {
                Text(verbatim:"您的注销申请已收到，我们将会在三个工作日回复您！如需帮助，请联系**************。".localized)
                    .font(Font.regular(size:16))
                    .foregroundColor(Color(dynamicTextColor18))
                    .frame(maxWidth:.infinity)
                    .padding(EdgeInsets(top: 50, leading: 25, bottom: 25, trailing: 25))
                Spacer()
            }.background(Color(dynamicBackgroundColor3))
        }.environment(\.locale, appState.locale)
        
    }
}

struct DeleteAccountAppliedV_Previews: PreviewProvider {
    static var previews: some View {
        DeleteAccountAppliedV()
    }
}
