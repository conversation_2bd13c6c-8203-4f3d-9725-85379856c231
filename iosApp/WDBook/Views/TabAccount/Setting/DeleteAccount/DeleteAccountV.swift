//
//  DeleteAccountV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/11/25.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct DeleteAccountV: View {
    @EnvironmentObject var appState:AppState
    
    @State var isDisable = true //默认不可注销。
    @State var isPresentAlert = false
    @State var isPresentAppliedV = false
    
    var body: some View {
        BackNavigation(title: "注销账号".localized){
            VStack(alignment: .center, spacing: 0) {
                ScrollView {
                    VStack(alignment: .center, spacing: 0){
                        
                        VStack(alignment: .center, spacing: 11) {
                            Image("icon_!").frame(width: 40,height: 40)
                            Text("申请注销帐号须知".localized).font(Font.medium(size: 18)).bold()
                                .foregroundColor(Color(dynamicTextColor18))
                                .frame(maxWidth:.infinity,alignment:.center).frame(height:25)
                        }.padding(.top,24)
                            .padding(.bottom,19)
                        
                        VStack(spacing:0) {
                            Text("您提交的注销申请生效前，系统将自动进行以下验证，以保证您的帐号、财产安全：".localized)
                                .frame(maxWidth:.infinity,alignment: .leading)
                            Text("1、微读书城财产结清".localized).font(Font.regular(size: 16)).bold().padding(.top,18)
                                .frame(maxWidth:.infinity,alignment: .leading)
                            Text("帐号余额为0，没有电子书，没有订单待支付或结算。".localized).padding(.top,6)
                                .frame(maxWidth:.infinity,alignment: .leading)
                            Text("2、如有疑问".localized).font(Font.regular(size: 16)).bold().padding(.top,18)
                                .frame(maxWidth:.infinity,alignment: .leading)
                            Text(verbatim:"请发送邮件到**************。".localized).padding(.top,6)
                                .frame(maxWidth:.infinity,alignment: .leading)
                            
                            Group{
                                Text("注销帐号是".localized) + Text("不可恢复".localized).foregroundColor(Color(hex: 0xff342a)) + Text("的操作（即使您使用相同手机号码再次注册并使用App），您应自行备份帐号相关的信息和数据。一旦您的帐号被删除：".localized)
                            }.frame(maxWidth:.infinity,alignment: .leading)
                                .padding(.top,10)
                            
                            Text("1）您将无法登录、使用本帐号。您的朋友将无法通过本帐号和您一起读经。".localized)
                                .frame(maxWidth:.infinity,alignment: .leading)
                                .padding(.top,10)
                            Group{
                                Text("2）您帐号的个人资料、数据和历史信息（包括昵称、头像、读经记录、".localized) + Text("笔记、高亮".localized).foregroundColor(Color(hex: 0xff342a)) + Text("等）都将".localized) + Text("无法找回".localized).foregroundColor(Color(hex: 0xff342a)) + Text("。")
                            }
                            .frame(maxWidth:.infinity,alignment: .leading)
                            .padding(.top,10)
                            Group{
                                Text("3）您在微读书城".localized) + Text("已购买的电子书".localized).foregroundColor(Color(hex: 0xff342a))  + Text("将".localized) + Text("无法使用和找回".localized).foregroundColor(Color(hex: 0xff342a)) + Text("。您理解并同意，微读圣经无法协助您重新恢复前述服务。".localized)
                            }
                            .frame(maxWidth:.infinity,alignment: .leading)
                            .padding(.top,10)
                        }.padding(20)
                            .foregroundColor(Color(dynamicTextColor19))
                            .font(Font.regular())
                            .background(Color(dynamicTextColor27))
                        
                        
                        Spacer()
                    }
                }.background(Color(dynamicBackgroundColor3))
                Button {
                    isPresentAlert = true
                } label: {
                    Text("注销我的帐号".localized).font(Font.regular(size: 14))
                        .disabled(isDisable)
                        .foregroundColor(.white)
                        .frame(height:44).frame(maxWidth:.infinity)
                        .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor).cornerRadius(22)
                }.disabled(isDisable)
                    .padding(.horizontal, 32)
                    .padding(.bottom,43)
            }.background(Color(dynamicBackgroundColor3))
            
            NavigationLink(destination: DeleteAccountAppliedV().environmentObject(appState),isActive: $isPresentAppliedV) {
                EmptyView()
            }
        }.environment(\.locale, appState.locale)
            .alert(isPresented: $isPresentAlert) {
                Alert(title: Text("确定要申请注销帐号吗？".localized),
                      message: Text("注销帐号成功后将不可恢复".localized),
                      primaryButton: .cancel(Text("取消".localized)) {
                    
                }, secondaryButton: .destructive(Text("申请注销".localized)) {
                    isPresentAppliedV = true
                    HUDManager.showLoadingBlockHUD(text: "")
                    WDBookUserSDK.shared.deleteUserAccount { result in
                        HUDManager.hideLoadingHUD()
                        switch result{
                        case .success(let s):
                            debugPrint(s)
                            isPresentAlert = false
                            break
                        case .failure(let e):
                            Toaster.showToast(message: "出错了，请稍后再试".localized)
                            break
                        }
                    }
                })
            }
            .onAppear{
                WDBookUserSDK.shared.getUserAccountStatus { result in
                    switch result{
                    case .success(let s):
                        if let state = s as? Int,state == 1{
                            debugPrint("已经申请注销")
                            isDisable = true
                        }else{
                            isDisable = false
                        }
                        debugPrint(s)
                        break
                    case .failure(let e):
                        break
                    }
                }
            }
        
    }
}

#if DEBUG
struct DeleteAccountV_Previews: PreviewProvider {
    static var previews: some View {
        DeleteAccountV().environmentObject(AppState.shared)
    }
}
#endif
