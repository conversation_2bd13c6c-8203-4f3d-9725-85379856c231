//
//  CheckIfBindingAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/15.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

enum LoginType{
    case email
    case mobile
}

struct CheckIfBindingAlert: View {
    @State var bindingType:LoginType
    @State var username:String
    var cancelHandler:()->()
    var okHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text(bindingType == .email ? "换绑邮箱".localized : "换绑手机号".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                Text(bindingType == .email ? "当前绑定邮箱为 %@ 是否换绑？".localizedFormat(username) : "当前绑定手机号为 %@ 是否换绑？".localizedFormat(username))
                    .frame(maxWidth:.infinity,alignment:.center)
                    .multilineTextAlignment(.center)
                    .padding(.bottom,20)
                
                HStack {
                    Button(action: {
                        cancelHandler()
                    }) {
                        Text("取消".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(mainColor)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(RoundedRectangle(cornerRadius: 20)
                            .strokeBorder(mainColor, lineWidth: 1))
                    .cornerRadius(20)
                    
                    Button(action: {
                        okHandler()
                    }) {
                        Text("换绑".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(Color.white)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(mainColor)
                    .cornerRadius(20)
                }
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal, UIDevice.alertAdjustHorizontalPadding)
                .frame(width: UIDevice.alertAdjustFrameWidth)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
    }
}

#if DEBUG
struct CheckIfBindingAlert_Previews: PreviewProvider {
    static var previews: some View {
        CheckIfBindingAlert(bindingType: LoginType.email, username:"13533333",cancelHandler: {
            
        }, okHandler: {
            
        })
    }
}
#endif
