//
//  AccountAndSecurityV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/11.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import NavigationRouter

struct AccountAndSecurityV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    
    @State private var showGreeting = true
    @State var presentComment: Bool = false
    @State var detail:String = ""

    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
            ZStack(alignment: .top, content: {
                content
                    .navigationBarHidden(true)
                    .padding(.top, (45 + safeAreaInsets.top))
                
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text("账号与安全".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height:45)
                
                    .frame(maxWidth:.infinity)
                    .padding(.horizontal, 20)
                    .padding(.top, safeAreaInsets.top)
                    .background(Color(dynamicBackgroundColor1))
                    .modifier(BottomLineViewModifier(isShowBottomLine: true))
            })
            .edgesIgnoringSafeArea(.top)
    }
    
    var content: some View {
        VStack(alignment: .center, spacing: 0) {
            ScrollView {
                VStack(alignment: .center, spacing: 8){
                    
                    VStack(spacing:0) {
                        SettingBindingRow(title: "邮箱".localized, detail:$appState.userInfo.email)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                if !appState.userInfo.email.isBlank{
                                    RoutableManager.navigate(toPath: RouterName.checkpwdWithPageActionType.withParam(SecutiryVerifyV.PageActionType.actionEmailModify.rawValue))
                                } else {
                                    RoutableManager.showBindingEmailV(returnView: AccountAndSecurityV.self)
                                }
                            }
                        SettingBindingRow(title: "手机号".localized, detail:$appState.userInfo.mobileForSecurity)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                if !appState.userInfo.mobile.isBlank{
                                    RoutableManager.navigate(toPath: RouterName.checkpwdWithPageActionType.withParam(SecutiryVerifyV.PageActionType.actionMobileModify.rawValue))
                                }else{
                                    RoutableManager.navigate(toPath: RouterName.bindingMobile.rawValue)
                                }
                            }
                    }
                    
                    VStack(spacing:0) {
                        let actionType = AppState.shared.userInfo.email.isBlank ? SecutiryVerifyV.PageActionType.actionMobileModifyPwd.rawValue : SecutiryVerifyV.PageActionType.actionEmailModifyPwd.rawValue
                        RoutedLink(to:RouterName.checkpwdWithPageActionType.withParam(actionType) ) {
                            SettingRow(title: "修改密码".localized)
                        }

                        if WDBookSessionSDK.shared.isLogin{
                            RoutedLink(toRoute: .deleteAccount) {
                                SettingSubTitleRow(title: "注销账号".localized,subTitle: "提交申请，删除所有数据，永久注销".localized)
                            }
                        }
                            
                    }

                    Spacer()
                }
            }.background(Color(dynamicBackgroundColor3))
            
        }
    }
}

struct SettingBindingRow: View {
    @State var title:String
    @Binding var detail:String

    init(title:String,detail:Binding<String> = .constant("")) {
        _title = State(initialValue: title)
        _detail = detail
    }
    
    var body: some View {
        HStack {
            Text(LocalizedStringKey(title))
                .font(Font.regular(size: 16))
                .foregroundColor(Color(dynamicTitleColor2))
            Spacer()
            
            Text(detail.isBlank ? "未绑定".localized : detail)
                .font(Font.regular(size: 15))
                .foregroundColor(Color(detail.isBlank ? dynamicTextBlueColor : dynamicTextColor19))
            
            Image("arrow_right").frame(width: 24, height: 24, alignment: .center)
                .foregroundColor(Color(dynamicTitleColor))
        }.frame(minWidth: 0, maxWidth: .infinity)
        .frame(height: 64)
        .padding(.leading, 16)
        .padding(.trailing,8)
        .background(Color(dynamicBackgroundColor1))
        .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 16), alignment: .bottom)
    }
}

#if DEBUG
struct AccountAndSecurityV_Previews: PreviewProvider {
    static var previews: some View {
        AccountAndSecurityV().environmentObject(AppState.shared)
    }
}
#endif
