//
//  SecutiryVerifyV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/11.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI

struct SecutiryVerifyV: View {
    enum PageActionType: Int {
        case actionEmailModify = 0
        case actionMobileModify
        case actionEmailModifyPwd
        case actionMobileModifyPwd
    }

    @Environment(\.safeAreaInsets3) private var safeAreaInsets

    @State var pageActionType: PageActionType
    @State var verifyCode = ""
    @State var verifyCodeType = VerifyType.none
    @State var timer: Timer?
    @State var remainSecond: Int = 0
    @State var token = ""
    @State var isShowResetPwdV = false
    @State var isShowSentVerifyCode = false
    
    @State private var textHeight: CGFloat = 0

    var hasRemainSeconds: Bool {
        remainSecond > 0
    }

    var isDisable: Bool {
        verifyCode.isBlank
    }
    
    func startTimer() {
        stopTimer()
        remainSecond = 60
        timer = Timer(timeInterval: 1, repeats: true, block: { _ in
            remainSecond -= 1
            if remainSecond <= 0 {
                remainSecond = 0
                stopTimer()
            }
        })
        RunLoop.main.add(timer!, forMode: .default)
    }
    
    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    var body: some View {
        BackNavigation(title: "安全验证".localized) {
            VStack(alignment: .center, spacing: 0) {
                VStack(spacing: 0) {
                    VStack {
                        titleTextView
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .background(Color.clear)
                        .multilineTextAlignment(.leading)
                        .lineLimit(nil)
                        
                        Spacer().frame(height: 16)
                        HStack(spacing: 0) {
                            TextField("验证码".localized, text: $verifyCode) { _ in

                            } onCommit: {}.keyboardType(.numberPad) // .modifier(ClearButtonMode(text: $verifyCode))
                                .padding(.leading, 16).padding(.trailing, 8)
                                .onChange(of: verifyCode) { _ in
                                    verifyCodeType = .none
                                }

                            verifyCodeType.borderColor().frame(width: 1)

                            Button {
                                verifyCodeOnTap()
                            } label: {
                                Text(hasRemainSeconds ? ("\(remainSecond)" + "s后重新获取".localized) : "获取验证码".localized)
                                    .foregroundColor(hasRemainSeconds ? Color(hex: 0xBCBCBC) : mainColor).font(Font.regular(size: 16))
                            }.frame(width: 120).disabled(hasRemainSeconds)
                        }.frame(height: 46)
                            .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(verifyCodeType.borderColor(), lineWidth: 1)
                            )
                        switch verifyCodeType {
                        case let .failure(msg):
                            Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                        default:
                            EmptyView()
                        }
                    }
    
                    Spacer().frame(height: 24)
                    Button(action: {
                        nextOnTap()
                    }) {
                        Text("下一步".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .foregroundColor(Color.white)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height: 44)
                        .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                        .cornerRadius(22)
                        .disabled(isDisable)
                    
                    Spacer().frame(height: 16)
                    footerTipView
                    .frame(maxWidth: .infinity, alignment: .center)
                    .background(Color.clear)
                    .multilineTextAlignment(.leading)
                    .lineLimit(nil)
                }
                Spacer()
                NavigationLink(
                    destination: ForgetPwdV(token: token, pwdModifyType: .pwdModifyTypeReset),
                    isActive: $isShowResetPwdV,
                    label: {
                        EmptyView()
                    }
                )
            }.frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(24)
                .background(Color(dynamicBackgroundColor3))
        }.overlay(
            Group {
                if isShowSentVerifyCode {
                    SendVerifyCodeToEmailAlert(email: AppState.shared.userInfo.email) {
                        isShowSentVerifyCode = false
                    }
                } else {
                    EmptyView()
                }
            },
            alignment: .center
        )
    }
    
    // 组合 Text 视图，支持多语言和加粗
    @ViewBuilder
    private var titleTextView: some View {
        switch pageActionType {
        case .actionEmailModify, .actionEmailModifyPwd:
            (
                Text("为验证".localized)
                + Text(" \(AppState.shared.userInfo.email) ").font(Font.semibold())
                + Text("账号，请点击获取并输入验证码：".localized)
            ).font(Font.regular()) // 统一设置字体大小
            .foregroundColor(Color(dynamicTextColor31))
        case .actionMobileModify, .actionMobileModifyPwd:
            (
                Text("为验证".localized)
                + Text(" \(AppState.shared.userInfo.mobile) ").font(Font.semibold())
                + Text("账号，请点击获取并输入验证码：".localized)
            ).font(Font.regular()) // 统一设置字体大小
            .foregroundColor(Color(dynamicTextColor31))
        }
    }
    
    // 使用 @ViewBuilder 来构建视图，不再需要 AnyView
    // 【核心修改】这里是之前报错的地方，必须用 HStack
    // 使用 HStack 组合，间距为0，模拟拼接效果
    @ViewBuilder
    private var footerTipView: some View {
        switch pageActionType {
        case .actionEmailModify:
            // 使用 EmptyView 表示空视图，比 Text("") 更高效
            EmptyView()
        case .actionMobileModify:
            (
                Text("获取验证码失败？可尝试".localized)
                + Text(" 绑定邮箱 ").font(Font.semibold())
                + Text("修改密码".localized)
            ).font(Font.regular()) // 统一设置字体大小
            .foregroundColor(Color(dynamicTextColor31))
            
        case .actionEmailModifyPwd:
            AttributedTextView(
                attributedText: .constant(getAttributedString(.actionEmailModifyPwd)),
                height: $textHeight,
                linkTextAttributes: [.foregroundColor: UIColor(hex:0x0D6EFD)],
                onLinkTapped: { url in
                    Log.d("Link tapped: \(url.absoluteString)")
                    pageActionType = .actionMobileModifyPwd
                }
            ).frame(height:textHeight + 4)
        case .actionMobileModifyPwd:
            AttributedTextView(
                attributedText: .constant(getAttributedString(.actionMobileModifyPwd)),
                height: $textHeight,
                linkTextAttributes: [.foregroundColor: UIColor(hex:0x0D6EFD)],
                onLinkTapped: { url in
                    Log.d("Link tapped: \(url.absoluteString)")
                    pageActionType = .actionEmailModifyPwd
                }
            ).frame(height:textHeight + 4)
        }
    }
    
    private func getAttributedString(_ pageAction: PageActionType) -> NSAttributedString {
        var fullString = ""
        var tappablePart = ""
        if pageAction == .actionEmailModifyPwd {
            fullString = "获取验证码失败？可尝试切换 ".localized + "手机验证".localized
            tappablePart = "手机验证".localized
        } else {
            fullString = "获取验证码失败？可尝试切换 ".localized + "邮箱验证".localized
            tappablePart = "邮箱验证".localized
        }

        let attributedString = NSMutableAttributedString(string: fullString)
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        paragraphStyle.lineSpacing = 8
        
        // 1. 设置整体的基础样式
        attributedString.addAttributes([
            .font: UIFont.regular(size: 14),
            .foregroundColor: dynamicTextColor31,
            .paragraphStyle: paragraphStyle
        ], range: NSRange(location: 0, length: attributedString.length))
        
        // 2. 找到需要特殊处理的部分的范围
        if let range = fullString.range(of: tappablePart) {
            let nsRange = NSRange(range, in: fullString)
            
            // 3. 为这个范围添加特殊样式和链接
            attributedString.addAttributes([
                .font: UIFont.regular(size: 14),
                .foregroundColor: UIColor(hex: 0x0D6EFD), // 可点击部分用蓝色提示
                .link: URL(string: "action://verificationTypeChange")! // 添加链接
            ], range: nsRange)
        }
        
        return attributedString
    }

    func verifyCodeOnTap() {
        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        
        let email = pageActionType == .actionEmailModify || pageActionType == .actionEmailModifyPwd ? AppState.shared.userInfo.email : ""
        let phone = pageActionType == .actionMobileModify || pageActionType == .actionMobileModifyPwd ? AppState.shared.userInfo.mobile : ""
        
        WDBookSessionSDK.shared.sendVerifyCode(email: email, mobile: phone, codeType: CodeType.reset, appKeySignInfo: sign) { result in
            verifyCodeType = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case .success(_):
                startTimer()
                if email.isBlank {
                    Toaster.showToast(message: "验证码已发送至：%@".localizedFormat(phone))
                } else {
                    isShowSentVerifyCode = true
                }
            case let .failure(error):
                debugPrint(error)
                if error.isUserNotExist() {
                    verifyCodeType = .failure(ErrorInfo.usernotexists.message)
                } else if error.errno == ErrorInfo.smsexceedlimit.code {
                    Toaster.showToast(message: ErrorInfo.smsexceedlimit.message) // 验证码发送次数过多，请稍后再试
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }
    
    func nextOnTap() {
        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        let email = pageActionType == .actionEmailModify || pageActionType == .actionEmailModifyPwd ? AppState.shared.userInfo.email : ""
        let phone = pageActionType == .actionMobileModify || pageActionType == .actionMobileModifyPwd ? AppState.shared.userInfo.mobile : ""
        WDBookSessionSDK.shared.checkVerifyCode(email: email, mobile: phone, verificationCode: verifyCode, appKeySignInfo: sign) { result in
            verifyCodeType = .none
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(r):
                switch pageActionType {
                case .actionEmailModify:
                    RoutableManager.showBindingEmailV(returnView: AccountAndSecurityV.self)
                case .actionMobileModify:
                    RoutableManager.navigate(toPath: RouterName.bindingMobile.rawValue)
                case .actionEmailModifyPwd, .actionMobileModifyPwd:
                    if let t = r {
                        token = t
                        isShowResetPwdV = true
                    }
                }
            case let .failure(error):
                if error.isUserNotExist() {
                    verifyCodeType = .failure(ErrorInfo.usernotexists.message)
                } else if error.errno == ErrorInfo.invalidverificationcode.code {
                    Toaster.showToast(message: "验证码输入错误或已过期".localized)
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }
}

#if DEBUG
    struct SecutiryVerifyV_Previews: PreviewProvider {
        static var previews: some View {
            SecutiryVerifyV(pageActionType: .actionEmailModify)
                
        }
    }
#endif
