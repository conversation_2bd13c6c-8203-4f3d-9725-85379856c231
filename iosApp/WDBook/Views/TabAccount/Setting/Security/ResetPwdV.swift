//
//  ResetPwdV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/17.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct ResetPwdV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @State var oldPwd = ""
    
    @State var pwd = ""
    @State var pwdVerify = VerifyType.none
    @State var isShowPlanPWD = false
    
    @State var repwd = ""
    @State var repwdVerify = VerifyType.none
    @State var isShowPlanRePWD = false
    
    var isDisable:Bool{
        pwd.isBlank || repwd.isBlank
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text("设置密码".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height:45)
            
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
    
    var content: some View {
        VStack(alignment: .center, spacing: 0) {

            VStack(spacing:0) {
                VStack {
                    Text("密码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth:.infinity,alignment:.leading)
                    ZStack {
                        if isShowPlanPWD{
                            TextField("密码不小于7位".localized, text: $pwd)
                                //.modifier(ClearButtonMode(text: $pwd))
                        }else{
                            SecureField("密码不小于7位".localized, text: $pwd)
                                .disableAutocorrection(true)
                                .autocapitalization(.none)
                                //.modifier(ClearButtonMode(text: $pwd))
                        }
                    }.onChange(of: pwd) { v in
                        pwdVerify = .none
                    }.padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .frame(height:46)
                        .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(pwdVerify.borderColor(), lineWidth: 1)
                            )
                        .overlay(Button(action: {
                            isShowPlanPWD.toggle()
                        }, label: {
                            Image(isShowPlanPWD ? "icon_visible_highlight" : "icon_visible")
                    }).padding(.trailing,8),alignment: .trailing)
                    switch pwdVerify{
                    case .failure(let msg):
                        Text(msg).foregroundColor(Color(hex:0xFF342A)).font(Font.regular()).frame(maxWidth:.infinity,alignment: .leading)
                    default:
                        EmptyView()
                    }
                }
                
                Spacer().frame(height:24)
                VStack {
                    Text("重复密码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth:.infinity,alignment:.leading)
                    ZStack {
                        if isShowPlanRePWD{
                            TextField("再次输入新密码".localized, text: $repwd)
                                //.modifier(ClearButtonMode(text: $repwd))
                        }else{
                            SecureField("再次输入新密码".localized, text: $repwd)
                                .disableAutocorrection(true)
                                .autocapitalization(.none)
                                //.modifier(ClearButtonMode(text: $repwd))
                        }
                    }.onChange(of: repwd) { v in
                        repwdVerify = .none
                    }.padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .frame(height:46)
                        .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(repwdVerify.borderColor(), lineWidth: 1)
                            )
                        .overlay(Button(action: {
                            isShowPlanRePWD.toggle()
                        }, label: {
                            Image(isShowPlanRePWD ? "icon_visible_highlight" : "icon_visible")
                    }).padding(.trailing,8),alignment: .trailing)
                    
                    switch repwdVerify{
                    case .failure(let msg):
                        Text(msg).foregroundColor(Color(hex:0xFF342A)).font(Font.regular()).frame(maxWidth:.infinity,alignment: .leading)
                    default:
                        EmptyView()
                    }
                }
                
                
                Spacer().frame(height:24)
                Button(action: {
                    pwdVerify = .none
                    repwdVerify = .none
                    
                    if pwd.count < 7{
                        pwdVerify = .failure("密码长度不能少于7位".localized)
                        return
                    }
                    
                    if pwd.count > 30{
                        pwdVerify = .failure("密码太长".localized)
                        return
                    }
                    
                    if repwd.count < 7{
                        repwdVerify = .failure("密码长度不能少于7位".localized)
                        return
                    }
                    
                    if repwd.count > 30{
                        repwdVerify = .failure("密码太长".localized)
                        return
                    }
                    
                    guard pwd == repwd else{
                        pwdVerify = .failure("两次密码不一致".localized)
                        repwdVerify = .failure("两次密码不一致".localized)
                        return
                    }
                    
                    HUDManager.showLoadingBlockHUD(text: "")
                    let sign = CryptoKitUtils.encrypt(string:"\(oldPwd)\n\(pwd)") ?? ""
                    WDBookUserSDK.shared.updatePassword(pwdSign:sign) { result in
                        pwdVerify = .none
                        repwdVerify = .none
                        HUDManager.hideLoadingHUD()
                        switch result{
                        case .success(let r):
                            
                            Toaster.showToast(message: "密码已重置".localized) { b in
                                WDBookDataSyncManager.shared.signOut(msg: nil) {
                                    AppState.shared.cleanup()
                                    RoutableManager.popToRoot()
                                    AppState.shared.isShowLoginV = true
                                }
                            }
                            break
                        case .failure(let error):
                            if error.isUserNotExist(){
                                Toaster.showToast(message: ErrorInfo.usernotexists.message)
                            }else if error.errno == ErrorInfo.invalidverificationcode.code{
                                Toaster.showToast(message: "验证码输入错误或已过期".localized)
                            }else if !error.msg.isEmpty{
                                Toaster.showToast(message: error.msg)
                            }else{
                                Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                            }
                            break
                        }
                    }

                }) {
                    Text("重置密码".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:44)
                    .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                    .cornerRadius(22)
                    .disabled(isDisable)
            }
            
            Spacer()
        }.frame(maxWidth: .infinity,maxHeight:.infinity)
        .padding(24)
        .background(Color(dynamicBackgroundColor3))
    }
}

struct ResetPwdV_Previews: PreviewProvider {
    static var previews: some View {
        ResetPwdV()
    }
}
