//
//  DeviceManager.swift
//  WDBook
//
//  Created by QK on 2022/1/24.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct DeviceManagerV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @State var isChecked = false
    @State var isLogoutWhenBack = true //退出界面时候，退出登录
    @State var suggestProductList: SuggestProductList = SuggestProductList()
    @State var userDeviceList: [UserDeviceEntity] = []
    @State var isFirstLoad = true
    @State var limitDeviceCount:Int = 0
    
    func backAndLogout(){
        if isLogoutWhenBack{
            WDBookDataSyncManager.shared.signOut(msg: nil) {
                self.presentationMode.wrappedValue.dismiss()
                AppState.shared.isInDeviceMangerPage = false
                AppState.shared.hideLoginRegisterV(isClearCache: true)
            }
        }else{
            self.presentationMode.wrappedValue.dismiss()
            AppState.shared.isInDeviceMangerPage = false
        }
    }
    
    var content: some View{
        VStack(alignment: .center, spacing: 0) {
            Spacer().frame(height:9)
            HStack {
                VStack(alignment: .leading){
                    Text(WDBookSessionSDK.shared.deviceName)
                        .font(Font.regular(size: 16))
                        .foregroundColor(Color(dynamicTitleColor2))
                    
                    Text(Int64(Date().timeIntervalSince1970).yyyymmddhhmm)
                        .font(Font.regular(size: 14))
                        .foregroundColor(.secondary)
                }
                Spacer()
                Text("本机")
                    .font(Font.regular(size: 15))
                    .foregroundColor(.secondary)
                
            }.frame(minWidth: 0, maxWidth: .infinity)
                .frame(height: 64).padding(.horizontal, 18)
                .background(Color(dynamicBackgroundColor1))
            
            Text("提示：同一微读账号最多可同时在%lld台设备上登录微读书城，您的账号登录设备已满，请解绑任意设备后使用微读书城。".localizedFormat(limitDeviceCount))
                .font(Font.regular(size: 12))
                .foregroundColor(Color(dynamicTextColor24))
                .padding(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
            
            ScrollView {
                LazyVStack(spacing: 0) {
                    if self.userDeviceList.count > 0{
                        ForEach(self.userDeviceList, id: \.deviceId) { item in
                            DeviceRow(item: item){
                                AppState.shared.isUnbind = true
                                for i in 0 ..< userDeviceList.count {
                                    if userDeviceList[i].deviceId == item.deviceId {
                                        userDeviceList.remove(at: i)
                                        break
                                    }
                                }
                                refresh()
                                checkDeviceState()
                            }.overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 16), alignment: .bottom)
                        }
                        
                        Spacer()
                    }else if !AppState.shared.isUnbind{
                        Spacer().frame(height:25)
                        ActivityIndicator()
                    }
                }
            }.background(Color(dynamicBackgroundColor3))
            
            Spacer().frame(height:10)
            Button(action: {
                WDBookSessionSDK.shared.setUserTokenString(tokenString: AppState.shared.tokenString)
                presentationMode.wrappedValue.dismiss()
                WDBookDataSyncManager.shared.loginComplete()
                AppState.shared.hideLoginRegisterV(isSuccess: true)
                DispatchQueue.main.asyncAfter(deadline: DispatchTime.now() + 10){
                    AppState.shared.isShowDeviceFullAlert = true
                }
            }) {
                Text("进入微读书城".localized)
                    .font(Font.medium(size: 16))
                    .foregroundColor(Color.white)
                    .frame(minWidth: 0, maxWidth: .infinity)
            }
            .disabled(!isChecked)
            .padding(12)
            .background(self.isChecked ? Color(UIColor.primaryColor1) : Color(UIColor.primaryColor1.alpha(0.25)))
            //            .frame(height: 44)
            .cornerRadius(22)
            .padding(EdgeInsets(top: 0, leading: 32, bottom: 0, trailing: 32))
            Spacer().frame(height:50)
        }
        .background(Color(dynamicBackgroundColor3))
        .onAppear{
            if isFirstLoad {
                setDeviceMaxNum()
                refresh()
                AppState.shared.isShowDeviceFullAlert = false
                AppState.shared.isUnbind = false
            }
        }
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        backAndLogout()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Text("退出登录".localized)
                        .foregroundColor(Color(dynamicBackgroundColor1))
                        .font(Font.semibold(size: 14))
                        .opacity(0)
                    
                    Spacer()
                    Text("设备管理".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0) //占位
                    Button {
                        backAndLogout()
                    } label: {
                        Text("退出登录".localized)
                            .foregroundColor(Color(dynamicTitleColor2))
                            .font(Font.regular(size: 14))
                            .contentShape(Rectangle())
                    }
                    
                }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
    
    func refresh() {
        WDBookSessionSDK.shared.setUserTokenString(tokenString: AppState.shared.tokenString)
        WDBookUserSDK.shared.getUserDeviceList() { result in
            switch result {
            case .success(let list):
                AppState.shared.isInDeviceMangerPage = true
                self.userDeviceList = list ?? []
                for i in 0 ..< userDeviceList.count {
                    if userDeviceList[i].deviceId == WDBookSessionSDK.shared.deviceId{
                        userDeviceList.remove(at: i)
                        return
                    }
                }
                isFirstLoad = false
            case .failure(let error):
                isFirstLoad = false
                print(error)
            }
        }
    }
    
    func setDeviceMaxNum() {
        WDBookAppSDK.shared.getDeviceMaxNum() { result in
            switch result {
            case .success(let data):
                limitDeviceCount = Int(data?.deviceLimit ?? 0)
            case .failure(let error):
                print(error)
            }
        }
    }
    
    func checkDeviceState(){
        WDBookSessionSDK.shared.setUserTokenString(tokenString: AppState.shared.tokenString)
        WDBookUserSDK.shared.checkAndAddDevice(deviceId: WDBookSessionSDK.shared.deviceId) { result in
            switch result {
            case .success(let result):
                if (result != 1312){
                    self.isChecked = true
                }
                break
            case .failure(let error):
                print(error)
            }
        }
    }
    
    func deleteUserDevice(deviceId:String){
        WDBookUserSDK.shared.deleteUserDevice(deviceId: deviceId ) { result in
            switch result {
            case .success(_):
                break
            case .failure(let error):
                print(error)
            }
        }
    }
}

struct DeviceRow: View {
    @State var item:UserDeviceEntity
    var unbindAction:(()->())?
    init(item:UserDeviceEntity, unbindAction:(()->())? = nil) {
        self.unbindAction = unbindAction
        self.item = item
    }
    var body: some View {
        HStack {
            VStack(alignment: .leading){
                Text(item.deviceName ?? "")
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(dynamicTitleColor2))
                
                Text((Int64((item.lastUpdateTime ?? 0))/1000).yyyymmddhhmm)
                    .font(Font.regular(size: 14))
                    .foregroundColor(.secondary)
            }
            Spacer()
            
            Button {
                WDBookSessionSDK.shared.setUserTokenString(tokenString: AppState.shared.tokenString)
                WDBookUserSDK.shared.deleteUserDevice(deviceId: item.deviceId ?? "") { result in
                    switch result {
                    case .success(_):
                        Toaster.showToast(message: "解绑成功".localized)
                        self.unbindAction?()
                        break
                    case .failure(let error):
                        print(error)
                    }
                }
            } label: {
                ZStack(alignment: .center){
                    Capsule().strokeBorder(Color(dynamicTextColor25), lineWidth: 1)
                    Text("解绑".localized)
                        .font(Font.regular(size: 12))
                        .foregroundColor(Color(dynamicTextColor25))
                        .frame(width: 61, height: 24, alignment: .center)
                }
                .frame(width: 61, height: 24, alignment: .center)
                .contentShape(Rectangle())
            }
        }.frame(maxWidth: .infinity)
            .frame(height: 64).padding(.horizontal, 18)
            .background(Color(dynamicBackgroundColor1))
    }
}
