//
//  TopupView.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/10/10.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SwiftyStoreKit
import shared
import DeviceKit

struct TopupView: View {
    @EnvironmentObject var appState:AppState
    @Binding var isPresent:Bool

    private var columns:[GridItem] {
        return [GridItem(.adaptive(minimum: 90))]
    }
    
    var topupV:some View{
        VStack(alignment: .center, spacing: 10) {
            Spacer()
            Text("选择充值金额".localized).frame(height:24).font(Font.medium(size: 16)).foregroundColor(Color(dynamicTitleColor2))

            Spacer()
            LazyVGrid(columns: columns, alignment: .center, spacing: TabShelfV.span) {
                ForEach(appState.iapProductList, id:\.productId) { item in
                    InAppProductV(item: item) {
                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5, execute: {
                            InAppPurchaseManager.shared.createOrder(product: item)
                        })
                        self.isPresent = false
                    }
                }
            }
            .frame(height:150)
            .overlay(appState.iapProductList.count == 0 ? AnyView(ActivityIndicator()) : AnyView(EmptyView()),alignment: .center)

            Spacer()
            Button(action: {
                self.isPresent = false
            }) {
                Text("取消".localized).font(Font.medium(size: 18)).foregroundColor(Color(dynamicTitleColor2))
            }.frame(width:120,height:24)

            Spacer()
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .bottom)
        .padding()
        .background(Color(dynamicBackgroundColor1))
        .onAppear {
            self.appState.syncIapPurchaseProducts()
        }
    }
    
    var body: some View {
        ZStack(alignment:.bottom) {
            Button(action: {
                self.isPresent = false
            }) {
                Text("").frame(maxWidth: .infinity,maxHeight: .infinity)
            }.background(Color(dynamicAlphaBackgroundColor1))
            
            topupV
                .frame(height: 335, alignment: .bottom)
                .background(Color(dynamicBackgroundColor1))
                .cornerRadius(6)
                .padding(8)
        }.frame(maxWidth: .infinity,maxHeight: .infinity, alignment: .bottom)
    }
    
}

extension InAppPurchaseProductEntity: Identifiable {}

struct InAppProductV: View {
    var productItem:InAppPurchaseProductEntity
    var rechargeAction:(()->())
    
    init(item:InAppPurchaseProductEntity, action:@escaping (()->())) {
        productItem = item
        rechargeAction = action
    }
    
    var body: some View {
        Button(action: {
            // 创建订单
            rechargeAction()
        }) {
            Text(String(format: "%.2f", productItem.price)).font(Font.medium(size: 16))
                .foregroundColor(Color(dynamicTitleColor2))
                .frame(width:90,height:60)
                .cornerRadius(2)
                .border(Color(dynamicBorderColor4),width: 1.0)
                .background(Color(dynamicBackgroundColor6))
        }
    }
}

#if DEBUG
struct TopupView_Previews: PreviewProvider {
    @State static var isShow = true
    static var previews: some View {
        Group{
            TopupView(isPresent: $isShow)
            TopupView(isPresent: $isShow).environment(\.colorScheme, .dark)
        }
    }
}
#endif
