//
//  CheckPrivacyAgreementAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/4/28.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct CheckPrivacyAgreementAlert: View {
    var cancelHandler:()->()
    var okHandler:()->()
    var userAgreementHandler:()->()
    var privacyPolicyHandler:()->()
    
    @State private var textHeight: CGFloat = 0
    @State private var linkTapped: String?
    
    var alertAdjustCorners: UIRectCorner {
        if UIDevice.current.userInterfaceIdiom == .pad {
            return  [.topLeft, .topRight, .bottomLeft, .bottomRight]
        } else {
            return  [.topLeft, .topRight]
        }
    }
    
    var textAttributedStr:NSAttributedString{
        let attributedString = NSMutableAttributedString(string: "请先阅读并同意微读书城的 用户协议 & 隐私条款".localized)
        let range1 = (attributedString.string as NSString).range(of: "用户协议".localized)
        let range2 = (attributedString.string as NSString).range(of: "隐私条款".localized)
        
        attributedString.addAttribute(.link, value: "https://www.example.com/user_agreement", range: range1)
        attributedString.addAttribute(.link, value: "https://www.example.com/privacy_policy", range: range2)
        attributedString.addAttribute(.font, value: UIFont.regular(size: 16), range: NSRange(location: 0, length: attributedString.length))
        attributedString.addAttribute(.foregroundColor, value: dynamicTitleColor10, range: NSRange(location: 0, length: attributedString.length))
        
        let paragraphStyle = NSMutableParagraphStyle()
        paragraphStyle.alignment = .center
        paragraphStyle.lineSpacing = 8
        attributedString.addAttribute(.paragraphStyle, value: paragraphStyle, range: NSRange(location: 0, length: attributedString.length))
        
        return attributedString
    }
    
    var body: some View {
        VStack{
            if UIDevice.current.userInterfaceIdiom == .phone {
                Spacer()
            }
            VStack(spacing:0) {
                Text("用户协议和隐私政策".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                AttributedTextView(
                    attributedText: .constant(textAttributedStr),
                    height: $textHeight,
                    linkTextAttributes: [.foregroundColor: UIColor(hex:0x4D95F7)],
                    onLinkTapped: { url in
                        // Handle link tap here
                        print("Link tapped: \(url.absoluteString)")
                        if url.absoluteString == "https://www.example.com/user_agreement"{
                            userAgreementHandler()
                        }else if url.absoluteString == "https://www.example.com/privacy_policy"{
                            privacyPolicyHandler()
                        }
                    }
                ).frame(height:textHeight + 4)
                    .padding(.bottom,20)
                
                HStack {
                    Button(action: {
                        cancelHandler()
                    }) {
                        Text("不同意".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(mainColor)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(RoundedRectangle(cornerRadius: 20)
                            .strokeBorder(mainColor, lineWidth: 1))
                    .cornerRadius(20)
                    
                    Button(action: {
                        okHandler()
                    }) {
                        Text("同意并继续".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(Color.white)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(mainColor)
                    .cornerRadius(20)
                }
            }.padding(24).background(Color(dynamicTextColor27))
                .frame(width: UIDevice.alertAdjustFrameWidth)
                .cornerRadius(radius: 10,corners:UIDevice.alertAdjustCorners)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
            .edgesIgnoringSafeArea(.top)

    }
}

#if DEBUG
struct CheckProtocolAgreementAlert_Previews: PreviewProvider {
    static var previews: some View {
        CheckPrivacyAgreementAlert(cancelHandler: {
            
        }, okHandler: {
            
        }, userAgreementHandler:{}
          ,privacyPolicyHandler:{})
    }
}
#endif
