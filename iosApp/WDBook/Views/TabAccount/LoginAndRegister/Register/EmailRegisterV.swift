//
//  EmailRegisterV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON>hou on 2023/4/28.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Combine
import NavigationRouter
import shared
import SwiftUI

struct EmailRegisterV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets2) private var safeAreaInsets
    @StateObject var phoneRegisterVM = PhoneRegisterVM()
    @State var isFirstLoad = true

    @State var email = ""
    @State var emailVerified = VerifyType.none
    @State var emailTFFocused = false
    @State var canEmailVerifiedTouch = false

    @State var verifyCode = ""
    @State var verifyCodeVerified = VerifyType.none
    @State var timer: Timer?
    @State var remainSecond: Int = 0

    @State var isChecked = false

    @State var isShowSentVerifyCode = false

    @State var isShowPhoneRegisterV = false

    @State var isHideOtherLoginBtns = false

    @State var isPresentAgreement = false
    @State var urlAgreement: String = ""
    @State var isPresentPrivacy = false
    @State var urlPrivacy: String = ""

    @State var isShowPrivacyAgreementAlert = false

    @State var isShowSetUserInfoV = false
    @State var token: String = ""

    var isDisable: Bool {
        email.isBlank || verifyCode.isBlank
    }

    var hasRemainSeconds: Bool {
        remainSecond > 0
    }

    func startTimer() {
        stopTimer()
        remainSecond = 60
        timer = Timer(timeInterval: 1, repeats: true, block: { _ in
            remainSecond -= 1
            if remainSecond <= 0 {
                remainSecond = 0
                stopTimer()
            }
        })
        RunLoop.main.add(timer!, forMode: .default)
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    func verifyCodeOnTap() {
        if email.count > 30 {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        guard email =~ RegularExp.EMAIL else {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.sendVerifyCode(email: email, mobile: "", codeType: CodeType.register0, appKeySignInfo: sign) { result in
            emailVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case let .success(r):
                debugPrint(r)
                startTimer()
                isShowSentVerifyCode = true
            case let .failure(error):
                debugPrint(error)
                if error.errno == ErrorInfo.userexists.code {
                    emailVerified = .failure(ErrorInfo.userexists.message)
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    func nextOnTap() {
        UIApplication.dismissKeyboard()
        guard isChecked else {
            isShowPrivacyAgreementAlert = true
            return
        }

        guard NetReachability.isReachability() else {
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }

        // 下一步界面
        if email.count > 30 {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        guard email =~ RegularExp.EMAIL else {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.checkVerifyCode(email: email, mobile: "", verificationCode: verifyCode, appKeySignInfo: sign) { result in
            emailVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case let .success(r):
                if let t = r {
                    token = t
                    isShowSetUserInfoV = true
                }
            case let .failure(error):
                if error.errno == ErrorInfo.userexists.code {
                    emailVerified = .failure(ErrorInfo.userexists.message)
                } else if error.errno == ErrorInfo.invalidverificationcode.code {
                    verifyCodeVerified = .failure("验证码输入错误或已过期".localized)
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    func tapUserAgreement() {
        HUDManager.showLoadingBlockHUD(text: "")
        WDBookAppSDK.shared.getBookAgreementURL { result in
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(url):
                if let u = url,!u.isEmpty {
                    self.urlAgreement = u
                    self.isPresentAgreement = true
                }
            case .failure:
                break
            }
        }
    }

    func tapPrivacyPolicy() {
        HUDManager.showLoadingBlockHUD(text: "")
        WDBookAppSDK.shared.getBookPrivacyURL { result in
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(url):
                if let u = url,!u.isEmpty {
                    self.urlPrivacy = u
                    self.isPresentPrivacy = true
                }
            case .failure:
                break
            }
        }
    }

    func btnBack(left: Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
    }) {
        HStack {
            !left ? AnyView(Spacer()) : AnyView(EmptyView())
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) // ios14无效
            left ? AnyView(Spacer()) : AnyView(EmptyView())
        }.frame(width: 40, height: 45)
    }
    }

    var body: some View {
        NavigationView {
            ZStack(alignment: .top, content: {
                VStack {
                    ScrollView {
                        content
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }.frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color(dynamicBackgroundColor3))
                        .navigationBarHidden(true)
                        .padding(.top, 45 + safeAreaInsets.top)

                    Spacer()
                    if !isHideOtherLoginBtns {
                        VStack(spacing: 20) {
                            Button(action: {
                                isShowPhoneRegisterV = true
                            }) {
                                HStack {
                                    Image("icon_phone")
                                    Text("使用手机号注册".localized).foregroundColor(Color(dynamicTextColor35))
                                        .font(Font.regular(size: 16))
                                        .foregroundColor(Color(UIColor.gray))
                                }.frame(maxWidth: .infinity, alignment: .center)
                            }
                            .frame(height: 44)
                            .background(Color(dynamicTextColor27))
                            .cornerRadius(22)
                            .overlay(
                                RoundedRectangle(cornerRadius: 22)
                                    .strokeBorder(Color(dynamicSpanLineColor6), lineWidth: 1)
                            )

                        }.foregroundColor(Color(dynamicTextColor7)).padding(24)
                    }
                }.background(Color(dynamicBackgroundColor3))

                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text("使用邮箱注册".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height: 45)

                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 20)
                    .padding(.top, safeAreaInsets.top)
                    .background(Color(dynamicBackgroundColor1))
                    .modifier(BottomLineViewModifier(isShowBottomLine: true))
            })
            .navigationBarHidden(true)
            .edgesIgnoringSafeArea(.top)
        }.overlay(
            Group {
                if isShowSentVerifyCode {
                    VStack {
                        VStack(spacing: 0) {
                            Text("验证码已发送至邮箱".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                                .padding(.bottom, 16)

                            Text("为验证您的电子邮箱，我们向 %@ 发送了一个验证码，请查收。".localizedFormat(email))
                                .foregroundColor(Color(dynamicTitleColor10)).font(Font.regular(size: 16))
                                .lineSpacing(10).frame(maxWidth: .infinity, alignment: .center)
                                .multilineTextAlignment(.center)
                                .padding(.bottom, 24)

                            Button(action: {
                                isShowSentVerifyCode = false
                            }) {
                                Text("确定".localized).font(Font.medium(size: 16))
                                    .frame(maxWidth: .infinity, maxHeight: .infinity)
                                    .foregroundColor(Color.white)
                            }.frame(minWidth: 0, maxWidth: .infinity)
                                .frame(height: 40)
                                .background(mainColor)
                                .cornerRadius(20)
                        }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                            .padding(.horizontal, 35)
                    }.frame(maxWidth: .infinity, maxHeight: .infinity)
                        .background(Color(UIColor.black.alpha(0.25)))

                } else {
                    EmptyView()
                }
            },
            alignment: .center
        )
        .overlay(
            Group {
                if isShowPrivacyAgreementAlert {
                    CheckPrivacyAgreementAlert {
                        isShowPrivacyAgreementAlert = false
                    } okHandler: {
                        isChecked = true
                        isShowPrivacyAgreementAlert = false
                        nextOnTap()
                    } userAgreementHandler: {
                        tapUserAgreement()
                    } privacyPolicyHandler: {
                        tapPrivacyPolicy()
                    }

                } else {
                    EmptyView()
                }
            },
            alignment: .center
        )
    }

    var content: some View {
        VStack(alignment: .center, spacing: 0) {
            VStack(spacing: 0) {
                VStack {
                    Text("邮箱地址".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        TextField("请输入邮箱地址".localized, text: $email) { focused in
                            emailTFFocused = focused
                            if !focused {
                                emailVerified = Validator.email(email)
                                switch emailVerified {
                                case .none:
                                    canEmailVerifiedTouch = true
                                case let .failure(string):
                                    canEmailVerifiedTouch = false
                                case .success:
                                    canEmailVerifiedTouch = true
                                }
                            } else {
                                emailVerified = .none
                            }
                        } onCommit: {
                            debugPrint(email)
                        }.autocorrectionDisabled(true)
                            .keyboardType(.emailAddress)
                            .modifier(ClearButtonMode(text: $email, focused: $emailTFFocused))
                            .onChange(of: email) { _ in
                                emailVerified = .none
                            }

                        switch emailVerified {
                        case .success:
                            EmptyView()
//                            Image("icon_right_green").frame(width:24,height:24).opacity(0)
                        case let .failure(msg):
                            Image("icon_exclamation").frame(width: 24, height: 24)
                        case .none:
                            EmptyView()
//                            Image("icon_right_green").frame(width:24,height:24).opacity(0)
                        }

                    }.frame(height: 46)
                        .padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(emailVerified.borderColor(), lineWidth: 1)
                        )
                    switch emailVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)
                VStack {
                    Text("验证码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        TextField("验证码".localized, text: $verifyCode) { _ in

                        } onCommit: {}.keyboardType(.numberPad) // .modifier(ClearButtonMode(text: $verifyCode))
                            .padding(.leading, 16).padding(.trailing, 8)
                            .onChange(of: verifyCode) { _ in
                                verifyCodeVerified = .none
                            }

                        verifyCodeVerified.borderColor().frame(width: 1)

                        Button {
                            verifyCodeOnTap()
                        } label: {
                            Text(hasRemainSeconds ? ("\(remainSecond)" + "s后重新获取".localized) : "获取验证码".localized)
                                .foregroundColor(hasRemainSeconds || !canEmailVerifiedTouch ? Color(hex: 0xBCBCBC) : mainColor).font(Font.regular(size: 16))
                        }.frame(width: 120).disabled(hasRemainSeconds || !canEmailVerifiedTouch)
                    }.frame(height: 46)
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(verifyCodeVerified.borderColor(), lineWidth: 1)
                        )
                    switch verifyCodeVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)

                HStack(spacing: 4) {
                    CheckBox20(isChecked: self.$isChecked).frame(width: 20, height: 20)

                    Text("已阅读并同意".localized)

                    Text("用户协议".localized).foregroundColor(Color(hex: 0x4D95F7))
                        .contentShape(Rectangle()).onTapGesture {
                            tapUserAgreement()
                        }
                    NavigationLink(destination: LightWebview(url: urlAgreement, title: "微读书城用户协议".localized), isActive: $isPresentAgreement) {
                        EmptyView()
                    }

                    Text("&")

                    Text("隐私条款".localized).foregroundColor(Color(hex: 0x4D95F7))
                        .contentShape(Rectangle()).onTapGesture {
                            tapPrivacyPolicy()
                        }
                    NavigationLink(destination: LightWebview(url: urlPrivacy, title: "微读书城隐私政策".localized), isActive: $isPresentPrivacy) {
                        EmptyView()
                    }
                }.font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor5))
                    .frame(maxWidth: .infinity, alignment: .leading)

                Spacer().frame(height: 24)
                Button(action: {
                    nextOnTap()
                }) {
                    Text("下一步".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height: 44)
                    .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                    .cornerRadius(22)
                    .disabled(isDisable)
            }

            NavigationLink(
                destination: PhoneRegisterV(vm: phoneRegisterVM),
                isActive: $isShowPhoneRegisterV,
                label: {
                    EmptyView()
                }
            )
            NavigationLink(
                destination: SetUserInfoOnRegisterV(loginType: .email, email: email, token: token),
                isActive: $isShowSetUserInfoV,
                label: {
                    EmptyView()
                }
            )
        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(24)
            .background(Color(dynamicBackgroundColor3))
            .contentShape(Rectangle()).onTapGesture {
                UIApplication.dismissKeyboard()
            }
            .onReceive(Publishers.keyboardHeight) { keyboardHeight in
                if keyboardHeight > 0 {
                    isHideOtherLoginBtns = true
                } else {
                    isHideOtherLoginBtns = false
                }
            }.onAppear {
                if isFirstLoad {
                    isChecked = !isTestFlight1()
                    isFirstLoad = false
                }
            }
    }
}

#if DEBUG
    struct EmailRegisterV_Previews: PreviewProvider {
        static var previews: some View {
            EmailRegisterV()
        }
    }
#endif
