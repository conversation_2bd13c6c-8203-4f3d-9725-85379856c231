//
//  SetUserInfoOnRegisterV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/5/2.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct SetUserInfoOnRegisterV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    
    @State var loginType = LoginType.email
    @State var email = ""
    @State var countryCode = ""
    @State var phone = ""
    @State var token = ""
    
    @State var nickname = ""
    @State var nicknameVerified = VerifyType.none
    @State var nicknameTFFocused = false
    
    @State var pwd = ""
    @State var pwdVerify = VerifyType.none
    @State var isShowPlanPWD = false
    
    @State var repwd = ""
    @State var repwdVerify = VerifyType.none
    @State var isShowPlanRePWD = false
    
    var isDisable:Bool{
        nickname.isBlank || pwd.isBlank || repwd.isBlank
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text("设置密码".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height:45)
            
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
    
    var content: some View {
        VStack(alignment: .center, spacing: 0) {

            VStack(spacing:0) {
                VStack {
                    Text("名字".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth:.infinity,alignment:.leading)
                    HStack(spacing:0) {
                        TextField("请输入名字，30字符以内".localized, text: $nickname) { focused in
//                            nicknameTFFocused = focused
//                            if !focused{
//                                nicknameVerified = Validator.nickname(nickname)
//                            }else{
//                                nicknameVerified = .none
//                            }
                        } onCommit: {
                            
                        }.autocorrectionDisabled(true)
                            .modifier(ClearButtonMode(text: $nickname,focused: $nicknameTFFocused))
                            .onChange(of: nickname) { v in
                                nicknameVerified = .none
                                if nickname.count > 30{
                                    nickname = String(nickname.prefix(30))
                                    Toaster.showToast(message: "您最多只能输入30个字符".localized)
                                }
                            }
                        
                        switch nicknameVerified{
                        case .success:
                            EmptyView()
//                            Image("icon_right_green").frame(width:24,height:24).opacity(0)
                        case .failure(let msg):
                            Image("icon_exclamation").frame(width:24,height:24)
                        case .none:
                            EmptyView()
//                            Image("icon_right_green").frame(width:24,height:24).opacity(0)
                        }
                        
                    }.frame(height:46)
                    .padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(nicknameVerified.borderColor(), lineWidth: 1)
                        )
                    switch nicknameVerified{
                    case .failure(let msg):
                        Text(msg).foregroundColor(Color(hex:0xFF342A)).font(Font.regular()).frame(maxWidth:.infinity,alignment: .leading)
                    default:
                        EmptyView()
                    }
                }
                
                Spacer().frame(height:24)
                
                VStack {
                    Text("密码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth:.infinity,alignment:.leading)
                    ZStack {
                        if isShowPlanPWD{
                            TextField("密码不小于7位".localized, text: $pwd)
                                //.modifier(ClearButtonMode(text: $pwd))
                        }else{
//                            SecureField("密码不小于7位".localized, text: $pwd)
//                                .disableAutocorrection(true)
//                                .autocapitalization(.none)
//                                //.modifier(ClearButtonMode(text: $pwd))
                            
                            CustomSecureField(placeHolder:"密码不小于7位".localized, text: $pwd, onEditingChanged: { isEditing in
                                if isEditing {
                                    pwdVerify = .none
                                }else{
                                    pwdVerify = Validator.pwd(pwd)
                                }
                            })
                        }
                    }.onChange(of: pwd) { v in
                        pwdVerify = .none
                    }.padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .frame(height:46)
                        .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(pwdVerify.borderColor(), lineWidth: 1)
                            )
                        .overlay(Button(action: {
                            isShowPlanPWD.toggle()
                        }, label: {
                            Image(isShowPlanPWD ? "icon_visible_highlight" : "icon_visible")
                    }).padding(.trailing,8),alignment: .trailing)
                    switch pwdVerify{
                    case .failure(let msg):
                        Text(msg).foregroundColor(Color(hex:0xFF342A)).font(Font.regular()).frame(maxWidth:.infinity,alignment: .leading)
                    default:
                        EmptyView()
                    }
                }
                
                Spacer().frame(height:24)
                VStack {
                    Text("重复密码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth:.infinity,alignment:.leading)
                    ZStack {
                        if isShowPlanRePWD{
                            TextField("再次输入新密码".localized, text: $repwd)
                                //.modifier(ClearButtonMode(text: $repwd))
                        }else{
//                            SecureField("再次输入新密码".localized, text: $repwd)
//                                .disableAutocorrection(true)
//                                .autocapitalization(.none)
//                                //.modifier(ClearButtonMode(text: $repwd))
                            
                            CustomSecureField(placeHolder:"再次输入新密码".localized, text: $repwd, onEditingChanged: { isEditing in
//                                if isEditing {
                                repwdVerify = .none
//                                }else{
//                                    pwdVerify = Validator.pwd(pwd)
//                                }
                            })
                        }
                    }.onChange(of: pwd) { v in
                        repwdVerify = .none
                    }.padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .frame(height:46)
                        .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(repwdVerify.borderColor(), lineWidth: 1)
                            )
                        .overlay(Button(action: {
                            isShowPlanRePWD.toggle()
                        }, label: {
                            Image(isShowPlanRePWD ? "icon_visible_highlight" : "icon_visible")
                    }).padding(.trailing,8),alignment: .trailing)
                    
                    switch repwdVerify{
                    case .failure(let msg):
                        Text(msg).foregroundColor(Color(hex:0xFF342A)).font(Font.regular()).frame(maxWidth:.infinity,alignment: .leading)
                    default:
                        EmptyView()
                    }
                }
                
                
                Spacer().frame(height:24)
                Button(action: {
                    nicknameVerified = .none
                    pwdVerify = .none
                    repwdVerify = .none
                    
                    if nickname.count > 30{
                        nicknameVerified = .failure("用户名太长".localized)
                        return
                    }
                    
                    if pwd.count < 7{
                        pwdVerify = .failure("密码长度不能少于7位".localized)
                        return
                    }
                    
                    if pwd.count > 30{
                        pwdVerify = .failure("密码太长".localized)
                        return
                    }
                    
                    guard pwd == repwd else{
                        repwdVerify = .failure("两次密码不一致".localized)
                        return
                    }
                    
                    HUDManager.showLoadingBlockHUD(text: "")
                    
                    let sign = CryptoKitUtils.encrypt(string: "\(Constants().APP_KEY_IOS)\n1\n\(pwd)\n\(nickname)") ?? ""
                    WDBookSessionSDK.shared.registration(signInfo: sign, verificationCodeToken: token) { result in
                        nicknameVerified = .none
                        pwdVerify = .none
                        repwdVerify = .none
                        HUDManager.hideLoadingHUD()
                        switch result{
                        case .success(let r):
                            if loginType == .email{
                                LoginService().login(username: email, password: pwd) {
//                                    Toaster.showToast(message: "注册成功".localized) { b in
                                        AppState.shared.isShowRegisterV = false
//                                    }
                                } fails: { error in
                                    Toaster.showToast(message: "注册成功".localized) { b in
                                        AppState.shared.isShowRegisterV = false
                                    }
                                }
                            }else{
                                LoginService().login(username: countryCode + phone,countryCode: countryCode,phone: phone, password: pwd) {
//                                    Toaster.showToast(message: "注册成功".localized) { b in
                                        AppState.shared.isShowRegisterV = false
//                                    }
                                    
                                } fails: { error in
                                    Toaster.showToast(message: "注册成功".localized) { b in
                                        AppState.shared.isShowRegisterV = false
                                    }
                                }
                            }
                            
                            break
                        case .failure(let error):
                            if error.errno == ErrorInfo.invalidverificationcode.code{
                                Toaster.showToast(message: "验证码输入错误或已过期".localized)
                            }else if !error.msg.isEmpty{
                                Toaster.showToast(message: error.msg)
                            }else{
                                Toaster.showToast(message: "注册失败".localized)
                            }
                            break
                        }
                    }

                }) {
                    Text("立即注册".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity)
                        .frame(height:44)
                        .foregroundColor(Color.white)
                        .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                        .cornerRadius(22)
                }.disabled(isDisable)
                
                Text("微读帐号可以登录微读书城及微读圣经".localized).font(Font.regular()).foregroundColor(Color(hex:0xBCBCBC)).padding(.top,16)
            }
            
            Spacer()
        }.frame(maxWidth: .infinity,maxHeight:.infinity)
        .padding(24)
        .background(Color(dynamicBackgroundColor3))
    }
}

#if DEBUG
struct SetUserInfoOnRegisterV_Previews: PreviewProvider {
    static var previews: some View {
        SetUserInfoOnRegisterV()
    }
}
#endif
