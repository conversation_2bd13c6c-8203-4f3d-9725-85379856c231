//
//  PhoneRegisterV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/5/2.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Combine
import NavigationRouter
import shared
import SwiftUI

class PhoneRegisterVM: ObservableObject {
    @Published var countryCode = "+86"
    @Published var phone = ""
    @Published var canPhoneVerifiedTouch = false

    @Published var timer: Timer?
    @Published var remainSecond: Int = 0

    var hasRemainSeconds: Bool {
        remainSecond > 0
    }

    func startTimer() {
        stopTimer()
        remainSecond = 60
        timer = Timer(timeInterval: 1, repeats: true, block: { [weak self] _ in
            guard let self = self else { return }
            self.remainSecond -= 1
            if self.remainSecond <= 0 {
                self.remainSecond = 0
                self.stopTimer()
            }
        })
        RunLoop.main.add(timer!, forMode: .default)
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }
}

struct PhoneRegisterV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @ObservedObject var vm: PhoneRegisterVM

    @State var isFirstLoad = true
    @State var phoneVerified = VerifyType.none
    @State var phoneTFFocused = false
    @State var isShowCountryCodeV = false

    @State var verifyCode = ""
    @State var verifyCodeVerified = VerifyType.none

    @State var isChecked = false
    @State var isPresentAgreement = false
    @State var urlAgreement: String = ""
    @State var isPresentPrivacy = false
    @State var urlPrivacy: String = ""

    @State var isShowPrivacyAgreementAlert = false

    @State var isShowSetUserInfoV = false
    @State var token: String = ""

    var isDisable: Bool {
        vm.phone.isBlank || verifyCode.isBlank
    }

    func verifyCodeOnTap() {
        if vm.phone.count > 30 {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        guard RegularExp.isValidPhone(phone: vm.countryCode + vm.phone) else {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""

        WDBookSessionSDK.shared.sendVerifyCode(email: "", mobile: vm.countryCode + vm.phone, codeType: CodeType.register0, appKeySignInfo: sign) { result in
            phoneVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case let .success(r):
                debugPrint(r)
                vm.startTimer()
                Toaster.showToast(message: "验证码已发送至：%@".localizedFormat(vm.countryCode + vm.phone))
            case let .failure(error):
                debugPrint(error)
                if error.errno == ErrorInfo.userexists.code {
                    phoneVerified = .failure(ErrorInfo.userexists.message)
                } else if error.errno == ErrorInfo.smsexceedlimit.code {
                    Toaster.showToast(message: ErrorInfo.smsexceedlimit.message) // 验证码发送次数过多，请稍后再试
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    func nextOnTap() {
        UIApplication.dismissKeyboard()
        guard isChecked else {
            isShowPrivacyAgreementAlert = true
            return
        }

        guard NetReachability.isReachability() else {
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            return
        }

        if vm.phone.count > 30 {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        guard RegularExp.isValidPhone(phone: vm.countryCode + vm.phone) else {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.checkVerifyCode(email: "", mobile: vm.countryCode + vm.phone, verificationCode: verifyCode, appKeySignInfo: sign) { result in
            phoneVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(r):
                if let t = r {
                    token = t
                    isShowSetUserInfoV = true
                }
            case let .failure(error):
                if error.errno == ErrorInfo.userexists.code {
                    phoneVerified = .failure(ErrorInfo.userexists.message)
                } else if error.errno == ErrorInfo.invalidverificationcode.code {
                    verifyCodeVerified = .failure("验证码输入错误或已过期".localized)
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    func tapUserAgreement() {
        HUDManager.showLoadingBlockHUD(text: "")
        WDBookAppSDK.shared.getBookAgreementURL { result in
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(url):
                if let u = url,!u.isEmpty {
                    self.urlAgreement = u
                    self.isPresentAgreement = true
                }
            case .failure:
                break
            }
        }
    }

    func tapPrivacyPolicy() {
        HUDManager.showLoadingBlockHUD(text: "")
        WDBookAppSDK.shared.getBookPrivacyURL { result in
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(url):
                if let u = url,!u.isEmpty {
                    self.urlPrivacy = u
                    self.isPresentPrivacy = true
                }
            case .failure:
                break
            }
        }
    }

    func btnBack(left: Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
    }) {
        HStack {
            !left ? AnyView(Spacer()) : AnyView(EmptyView())
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) // ios14无效
            left ? AnyView(Spacer()) : AnyView(EmptyView())
        }.frame(width: 40, height: 45)
    }
    }

    var body: some View {
        ZStack(alignment: .top, content: {
            ScrollView {
                content
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            }.frame(maxWidth: .infinity, maxHeight: .infinity)
                .background(Color(dynamicBackgroundColor3))
                .navigationBarHidden(true)
                .padding(.top, 45 + safeAreaInsets.top)

            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
                Text("使用手机号注册".localized)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18))
                Spacer()
                btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
            }).frame(height: 45)

                .frame(maxWidth: .infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top).overlay(
            Group {
                if isShowPrivacyAgreementAlert {
                    CheckPrivacyAgreementAlert {
                        isShowPrivacyAgreementAlert = false
                    } okHandler: {
                        isChecked = true
                        isShowPrivacyAgreementAlert = false
                        nextOnTap()
                    } userAgreementHandler: {
                        tapUserAgreement()
                    } privacyPolicyHandler: {
                        tapPrivacyPolicy()
                    }

                } else {
                    EmptyView()
                }
            },
            alignment: .center
        )
    }

    var content: some View {
        VStack(alignment: .center, spacing: 0) {
            VStack(spacing: 0) {
                VStack {
                    Text("手机号".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        Button {
                            isShowCountryCodeV = true
                            //                        RoutableManager.navigate(toRoute: .countryCode)
                            //                        RoutableManager.push(CountryCodeV(countryCodeSelected:{countryCode in
                            //                            self.countryCode = countryCode
                            //                        }),animated: true)
                        } label: {
                            Text(vm.countryCode).foregroundColor(mainColor)
                        }.frame(width: 61)

                        phoneVerified.borderColor().frame(width: 1)

                        TextField("请输入手机号".localized, text: $vm.phone) { focused in
                            phoneTFFocused = focused
                            if !focused {
                                phoneVerified = Validator.phone(vm.countryCode + vm.phone)
                                switch phoneVerified {
                                case .none:
                                    vm.canPhoneVerifiedTouch = true
                                case let .failure(string):
                                    vm.canPhoneVerifiedTouch = false
                                case .success:
                                    vm.canPhoneVerifiedTouch = true
                                }
                            } else {
                                phoneVerified = .none
                            }
                        } onCommit: {}.modifier(ClearButtonMode(text: $vm.phone, focused: $phoneTFFocused))
                            .keyboardType(.phonePad)
                            .onChange(of: vm.phone) { _ in
                                phoneVerified = .none
                            }
                            .padding(.leading, 8).padding(.trailing, 16)

                    }.frame(height: 46)
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(phoneVerified.borderColor(), lineWidth: 1)
                        )
                    switch phoneVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)
                VStack {
                    Text("验证码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        TextField("验证码".localized, text: $verifyCode) { _ in
//                            if !focused{
//
//                            }else{
//                                verifyCodeVerified = .none
//                            }
                        } onCommit: {}.keyboardType(.numberPad) // .modifier(ClearButtonMode(text: $verifyCode))
                            .padding(.leading, 16).padding(.trailing, 8)
                            .onChange(of: verifyCode) { _ in
                                verifyCodeVerified = .none
                            }

                        verifyCodeVerified.borderColor().frame(width: 1)

                        Button {
                            verifyCodeOnTap()
                        } label: {
                            Text(vm.hasRemainSeconds ? ("\(vm.remainSecond)" + "s后重新获取".localized) : "获取验证码".localized)
                                .foregroundColor(vm.hasRemainSeconds || !vm.canPhoneVerifiedTouch ? Color(hex: 0xBCBCBC) : mainColor).font(Font.regular(size: 16))
                        }.frame(width: 120).disabled(vm.hasRemainSeconds || !vm.canPhoneVerifiedTouch)
                    }.frame(height: 46)
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(verifyCodeVerified.borderColor(), lineWidth: 1)
                        )
                    switch verifyCodeVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)

                HStack(spacing: 4) {
                    CheckBox20(isChecked: self.$isChecked).frame(width: 20, height: 20)

                    Text("已阅读并同意".localized)

                    Text("用户协议".localized).foregroundColor(Color(hex: 0x4D95F7))
                        .contentShape(Rectangle()).onTapGesture {
                            tapUserAgreement()
                        }
                    NavigationLink(destination: LightWebview(url: urlAgreement, title: "微读书城用户协议".localized), isActive: $isPresentAgreement) {
                        EmptyView()
                    }

                    Text("&")

                    Text("隐私条款".localized).foregroundColor(Color(hex: 0x4D95F7))
                        .contentShape(Rectangle()).onTapGesture {
                            tapPrivacyPolicy()
                        }
                    NavigationLink(destination: LightWebview(url: urlPrivacy, title: "微读书城隐私政策".localized), isActive: $isPresentPrivacy) {
                        EmptyView()
                    }
                }.font(Font.regular(size: 16)).foregroundColor(Color(dynamicTitleColor5))
                    .frame(maxWidth: .infinity, alignment: .leading)

                Spacer().frame(height: 24)
                Button(action: {
                    nextOnTap()
                }) {
                    Text("下一步".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height: 44)
                    .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                    .cornerRadius(22)
                    .disabled(isDisable)
            }

            Spacer()
            NavigationLink(
                destination: CountryCodeV(countryCodeSelected: { countryCode in
                    self.vm.countryCode = countryCode
                }),
                isActive: $isShowCountryCodeV,
                label: {
                    EmptyView()
                }
            )
            NavigationLink(
                destination: SetUserInfoOnRegisterV(loginType: .mobile, countryCode: vm.countryCode, phone: vm.phone, token: token),
                isActive: $isShowSetUserInfoV,
                label: {
                    EmptyView()
                }
            )

        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(24)
            .background(Color(dynamicBackgroundColor3))
            .contentShape(Rectangle()).onTapGesture {
                UIApplication.dismissKeyboard()
            }
            .onAppear {
                if isFirstLoad {
                    isChecked = !isTestFlight1()
                    isFirstLoad = false
                }
            }
    }
}

struct PhoneRegisterV_Previews: PreviewProvider {
    static var previews: some View {
        PhoneRegisterV(vm: PhoneRegisterVM())
    }
}
