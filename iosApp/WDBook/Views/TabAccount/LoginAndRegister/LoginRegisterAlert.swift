//
//  LoginRegisterAlert.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/6/1.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct LoginRegisterAlert: View {
//    @Binding var isShowingCustomView: Bool // 绑定自定义view的显示和隐藏状态
    @State var isShowUpdateBibleApp = false
    
    // FIXME: 是否需要通过kmm层来获取url
    func tapAuthLogin() {
        let authLoginCheckUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginCheckUrl())
        let authLoginUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginUrl())
        // 有新版授权登录，有老板提示升级。
        if UIApplication.shared.canOpenURL(authLoginCheckUrl!) {
            
            LoginManager.shared.hideLoginRegisterV()
            UIApplication.shared.open(authLoginUrl!, options: [:]) { success in
                if success {
                    Log.d("10以后可以跳转url")
                } else {
                    Log.d("10以后不能完成跳转")
                }
            }
        } else if UIApplication.shared.canOpenURL(authLoginUrl!) {
            isShowUpdateBibleApp = true
        } else {
            LoginManager.shared.hideLoginRegisterV()
           debugPrint("")
        }
    }
    
    func isInstalledWdbibleApp()->Bool{
        let authLoginCheckUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginCheckUrl())
        let authLoginUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginUrl())
        // 有新版授权登录，有老板提示升级。
        if UIApplication.shared.canOpenURL(authLoginCheckUrl!) {
            return true //安装
        } else if UIApplication.shared.canOpenURL(authLoginUrl!) {
            return true //安装了旧版
        }
        return false
    }
    
    var body: some View {
//        VStack {
//            Spacer()
            VStack(alignment: .center) {
                Text("您还没有登录".localized).font(Font.regular(size:24).bold())
                Spacer().frame(height: 8)
                Text("登录后可以同步阅读记录、高亮笔记、阅读进度等数据".localized).font(Font.regular(size:18))
                Spacer().frame(height:16)
                
                Text("没有微读帐号，请注册".localized)
                .font(Font.medium(size: 16))
                .foregroundColor(Color(UIColor.primaryColor1))
                .frame(maxWidth: .infinity,alignment: .center)
            .frame(height: 44)
            .background(Color(dynamicBtnBGColor3))
            .cornerRadius(22)
            .overlay(
                RoundedRectangle(cornerRadius: 22)
                    .strokeBorder(Color(UIColor.primaryColor1), lineWidth: 1)
            ).contentShape(Rectangle())
            .onTapGesture {
                LoginManager.shared.hideLoginRegisterV()
                RoutableManager.navigate(toRoute: .register)
            }
                

                Spacer().frame(height:16)
                
                Text("已有微读帐号，请登录".localized).font(Font.medium(size: 16))
                    .frame(maxWidth: .infinity,maxHeight: .infinity)
                    .foregroundColor(Color.white)
                    .frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:44)
                        .background(Color(UIColor.primaryColor1))
                        .cornerRadius(22)
                        .contentShape(Rectangle())
                        .onTapGesture {
                            LoginManager.shared.hideLoginRegisterV()
                            RoutableManager.navigate(toRoute: .login)
                        }
                
                if isInstalledWdbibleApp(){
                    Spacer().frame(height:16)
                    
                    Button(action: {
                        tapAuthLogin()
                    }) {
                        HStack {
                            Image("icon_wdbible")
                            Text("微读圣经授权登录".localized).foregroundColor(Color(dynamicTextColor35))
                                .font(Font.regular(size: 16))
                                .foregroundColor(Color(UIColor.gray))
                        }.frame(maxWidth: .infinity,alignment: .center)
                    }
                    .frame(height: 44)
                    .background(Color(dynamicTextColor27))
                    .cornerRadius(22)
                    .overlay(
                        RoundedRectangle(cornerRadius: 22)
                            .strokeBorder(Color(hex: 0xDD5B56), lineWidth: 1)
                    )
                }

            }.padding(24)
            .frame(maxWidth: .infinity, alignment: .center)
            .background(Color(dynamicBackgroundColor1))
            .cornerRadius(radius: 10,corners:UIDevice.alertAdjustCorners)
            
//        }.frame(maxWidth:.infinity,maxHeight:.infinity)
//        .background(Color(UIColor.black.alpha(0.25)))
        .alert(isPresented: $isShowUpdateBibleApp) {
            Alert(title: Text("请使用最新版[微读圣经]APP进行授权登录".localized),
                  //                        message: Text(message),
                  primaryButton: .default(Text("去升级".localized)) {
                UIApplication.shared.open(URL(string: "https://apps.apple.com/cn/app/wei-du-sheng-jing/id654898456")!, options: [:]) { _ in
                }
            }, secondaryButton: .cancel(Text("取消授权".localized)) {
                isShowUpdateBibleApp = false
            })
        }
    }


}

#if DEBUG
struct LoginRegisterAlert_Previews: PreviewProvider {
    static var previews: some View {
        LoginRegisterAlert()
    }
}
#endif
