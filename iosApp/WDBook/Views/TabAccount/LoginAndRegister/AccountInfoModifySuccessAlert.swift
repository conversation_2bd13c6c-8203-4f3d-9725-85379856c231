//
//  AccountInfoModifySuccessAlert.swift
//  WDBook
//
//  Created by 杜文泽 on 2025/8/17.
//  Copyright © 2025 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct AccountInfoModifySuccessAlert: View {
    @State var title:String
    @State var content:String
    var okHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text(title).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                Text(content)
                    .foregroundColor(Color(dynamicTitleColor10)).font(Font.regular(size: 16))
                    .lineSpacing(10).frame(maxWidth:.infinity,alignment:.center)
                    .multilineTextAlignment(.center)
                    .padding(.bottom,24)
                
                But<PERSON>(action: {
                    okHandler()
                }) {
                    Text("好的".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:40)
                    .background(mainColor)
                    .cornerRadius(20)
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal, UIDevice.alertAdjustHorizontalPadding)
                .frame(width: UIDevice.alertAdjustFrameWidth)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
    }
}

#if DEBUG
struct AccountInfoModifySuccessAlert_Previews: PreviewProvider {
    static var previews: some View {
        AccountInfoModifySuccessAlert(title: "绑定成功", content: "您的账号已成功绑定，请继续使用。", okHandler: {})
    }
}
#endif

