//
//  SendVerifyCodeToEmailAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/4/28.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct SendVerifyCodeToEmailAlert: View {
    @State var email:String
    var okHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text("验证码已发送至邮箱".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                Text("为验证您的电子邮箱，我们向 %@ 发送了一个验证码，请查收。".localizedFormat(email))
                    .foregroundColor(Color(dynamicTitleColor10)).font(Font.regular(size: 16))
                    .lineSpacing(10).frame(maxWidth:.infinity,alignment:.center)
                    .multilineTextAlignment(.center)
                    .padding(.bottom,24)
                
                <PERSON><PERSON>(action: {
                    okHandler()
                }) {
                    Text("确定".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height:40)
                    .background(mainColor)
                    .cornerRadius(20)
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal, UIDevice.alertAdjustHorizontalPadding)
                .frame(width: UIDevice.alertAdjustFrameWidth)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
    }
}

#if DEBUG
struct SendVerifyCodeToEmailAlert_Previews: PreviewProvider {
    static var previews: some View {
        SendVerifyCodeToEmailAlert(email: "<EMAIL>", okHandler:{
            
        })
    }
}
#endif
