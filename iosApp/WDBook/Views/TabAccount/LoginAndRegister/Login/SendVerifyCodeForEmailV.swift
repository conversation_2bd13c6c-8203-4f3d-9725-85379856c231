//
//  SendVerifyCodeForEmailV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/4/21.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Combine
import NavigationRouter
import shared
import SwiftUI

enum VerifyType {
    case none
    case failure(String)
    case success

    func borderColor() -> Color {
        switch self {
        case .success:
            return Color(dynamicSpanLineColor6)
        case let .failure(msg):
            return Color(hex: 0xFF342A)
        case .none:
            return Color(dynamicSpanLineColor6)
        }
    }
}

struct SendVerifyCodeForEmailV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @State var email = ""
    @State var emailVerified = VerifyType.none
    @State var emailTFFocused = false

    @State var verifyCode = ""
    @State var verifyCodeVerified = VerifyType.none

    @State var token = ""

    @State var timer: Timer?
    @State var remainSecond: Int = 0
    @State var isShowSentVerifyCode = false

    @State var isShowResetPwdV = false

    var hasRemainSeconds: Bool {
        remainSecond > 0
    }

    func startTimer() {
        stopTimer()
        remainSecond = 60
        timer = Timer(timeInterval: 1, repeats: true, block: { _ in
            remainSecond -= 1
            if remainSecond <= 0 {
                remainSecond = 0
                stopTimer()
            }
        })
        RunLoop.main.add(timer!, forMode: .default)
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    var isDisable: Bool {
        email.isBlank || verifyCode.isBlank
    }

    func verifyCodeOnTap() {
        if email.count > 30 {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        guard email =~ RegularExp.EMAIL else {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.sendVerifyCode(email: email, mobile: "", codeType: CodeType.reset, appKeySignInfo: sign) { result in
            emailVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case let .success(r):
                debugPrint(r)
                startTimer()
                isShowSentVerifyCode = true
            case let .failure(error):
                debugPrint(error)
                if error.isUserNotExist() {
//                                Toaster.showToast(message: ErrorInfo.usernotexists.message)
                    emailVerified = .failure(ErrorInfo.usernotexists.message)
//                                emailVerified = .failure("帐号信息有误，请检查后重试")
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    func nextOnTap() {
        if email.count > 30 {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        guard email =~ RegularExp.EMAIL else {
            emailVerified = .failure("邮箱格式有误".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.checkVerifyCode(email: email, mobile: "", verificationCode: verifyCode, appKeySignInfo: sign) { result in
            emailVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case let .success(r):
                if let t = r {
                    token = t
                    isShowResetPwdV = true
                }
            case let .failure(error):
                if error.isUserNotExist() {
                    emailVerified = .failure(ErrorInfo.usernotexists.message)
//                                emailVerified = .failure("帐号信息有误，请检查后重试")
                } else if error.errno == ErrorInfo.invalidverificationcode.code {
                    verifyCodeVerified = .failure("验证码输入错误或已过期".localized)
                } else {
                    if !error.msg.isBlank {
                        Toaster.showToast(message: error.msg)
                    } else {
                        Toaster.showToast(message: "操作失败")
                    }
                }
            }
        }
    }

    func btnBack(left: Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
    }) {
        HStack {
            !left ? AnyView(Spacer()) : AnyView(EmptyView())
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) // ios14无效
            left ? AnyView(Spacer()) : AnyView(EmptyView())
        }.frame(width: 40, height: 45)
    }
    }

    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, 45 + safeAreaInsets.top)

            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
                Text("重置密码".localized)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18))
                Spacer()
                btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
            }).frame(height: 45)

                .frame(maxWidth: .infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .overlay(
            Group {
                if isShowSentVerifyCode {
                    SendVerifyCodeToEmailAlert(email: email) {
                        isShowSentVerifyCode = false
                    }
                } else {
                    EmptyView()
                }
            },
            alignment: .center
        )
    }

    var content: some View {
        VStack(alignment: .center, spacing: 0) {
            VStack(spacing: 0) {
                VStack {
                    Text("邮箱地址".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        TextField("请输入邮箱地址".localized, text: $email) { focused in
                            emailTFFocused = focused
                            if !focused {
                                emailVerified = Validator.email(email)
                            } else {
                                emailVerified = .none
                            }
                        } onCommit: {}.keyboardType(.emailAddress)
                            .modifier(ClearButtonMode(text: $email, focused: $emailTFFocused))
                            .onChange(of: email) { _ in
                                emailVerified = .none
                            }
                            .padding(.trailing, 16)

                        switch emailVerified {
                        case .success:
                            EmptyView()
//                            Image("icon_right_green").frame(width:24,height:24).opacity(0)
                        case let .failure(msg):
                            Image("icon_exclamation").frame(width: 24, height: 24)
                        case .none:
                            EmptyView()
//                            Image("icon_right_green").frame(width:24,height:24).opacity(0)
                        }

                    }.frame(height: 46)
                        .padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(emailVerified.borderColor(), lineWidth: 1)
                        )
                    switch emailVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)
                VStack {
                    Text("验证码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        TextField("验证码".localized, text: $verifyCode) { _ in

                        } onCommit: {} // .modifier(ClearButtonMode(text: $verifyCode))
                            .padding(.leading, 16).padding(.trailing, 8)

                        verifyCodeVerified.borderColor().frame(width: 1)

                        Button {
                            verifyCodeOnTap()
                        } label: {
                            Text(hasRemainSeconds ? ("\(remainSecond)" + "s后重新获取".localized) : "获取验证码".localized).foregroundColor(hasRemainSeconds ? Color(hex: 0xBCBCBC) : mainColor).font(Font.regular(size: 16))
                        }.frame(width: 120).disabled(hasRemainSeconds)
                    }.frame(height: 46)
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(verifyCodeVerified.borderColor(), lineWidth: 1)
                        )
                    switch verifyCodeVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)
                Button(action: {
                    nextOnTap()
                }) {
                    Text("下一步".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height: 44)
                    .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                    .cornerRadius(22)
                    .disabled(isDisable)
            }

            Spacer()
            NavigationLink(
                destination: ForgetPwdV(token: token, pwdModifyType: .pwdModifyTypeForget),
                isActive: $isShowResetPwdV,
                label: {
                    EmptyView()
                }
            )
        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(24)
            .background(Color(dynamicBackgroundColor3))
            .onAppear {
                if !email.isEmpty {
                    emailVerified = Validator.email(email)
                }
            }
    }
}

#if DEBUG
    struct SendVerifyCodeForEmailV_Previews: PreviewProvider {
        static var previews: some View {
            SendVerifyCodeForEmailV()
        }
    }
#endif
