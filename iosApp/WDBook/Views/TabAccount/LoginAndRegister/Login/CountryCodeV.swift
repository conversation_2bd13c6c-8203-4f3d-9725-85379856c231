//
//  CountryCodeV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/4/18.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct CountryCodeV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @State var commonCountries:[CountryCode] = []
    @State var allCountries:[CountryCode] = []
    
    var countryCodeSelected:(String)->()
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
        ZSta<PERSON>(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text("选择区号".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height:45)
            
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
    }
    
    var content: some View {
        ScrollView {
            LazyVStack(alignment:.leading,spacing:0,pinnedViews:[.sectionHeaders]) {
                
                Section(header: HStack(content: {
                    Text("常用国家/地区".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor22))
                    Spacer()
                }).frame(height:28).frame(width:.infinity).padding(.horizontal,24).background(Color(dynamicBackgroundColor3))) {
                    ForEach(commonCountries,id: \.id) { item in
                        HStack {
                            Text(item.localeName).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTextColor19)).padding(.leading,24)
                            Spacer()
                            Text(item.dial_code).font(Font.regular(size: 16)).foregroundColor(mainColor).padding(.trailing,24)
                        }.frame(width:.infinity).frame(height:50)
                            .background(Color(dynamicSpanLineColor6).frame(height:0.5).frame(maxWidth:.infinity).padding(.leading,18),alignment: .bottom)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                presentationMode.wrappedValue.dismiss()
                                countryCodeSelected(item.dial_code)
                            }
                    }.frame(width:.infinity)
                    .background(Color(dynamicTextColor27))
                }
                
                Section(header: HStack(content: {
                    Text("全部地区".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor22))
                    Spacer()
                }).frame(height:28).frame(width:.infinity).padding(.horizontal,24).background(Color(dynamicBackgroundColor3))) {
                    ForEach(allCountries,id: \.code) { item in
                        HStack {
                            Text(item.localeName).font(Font.regular(size: 16)).foregroundColor(Color(dynamicTextColor19)).padding(.leading,24)
                            Spacer()
                            Text(item.dial_code).font(Font.regular(size: 16)).foregroundColor(mainColor).padding(.trailing,24)
                        }.frame(width:.infinity).frame(height:50)
                            .background(Color(dynamicSpanLineColor6).frame(height:0.5).frame(maxWidth:.infinity).padding(.leading,18),alignment: .bottom)
                            .contentShape(Rectangle())
                            .onTapGesture {
                                presentationMode.wrappedValue.dismiss()
                                countryCodeSelected(item.dial_code)
                            }
                    }.frame(width:.infinity)
                    .background(Color(dynamicTextColor27))
                }
                
            }.frame(maxWidth: .infinity,maxHeight:.infinity)
                .background(Color(dynamicBackgroundColor3))
        }.onAppear{
            loadCountryCodeList()
        }
    }
    
    func loadCountryCodeList(){
        var list = Resources.loadCountryCodeList()
        
        let locale = LocaleManager.locale
        
//        var countryArray:[String] = []
////        if #available(iOS 16, *) {
////            countryArray = Locale.Currency.isoCurrencies.map($0)
////        } else {
//            countryArray = Locale.commonISOCurrencyCodes
////        }
        
//        let commonDialCodes = ["+86","+852","+853","+886","+1","+81","+1"]
        let commonCountryCodes = ["CN","HK","MO","TW","US","JP","CA"]
        commonCountries = []
        allCountries = []
        for var country in list {
            country.localeName = locale.localizedString(forRegionCode: country.code) ?? country.name
            if commonCountryCodes.contains(country.code){
                var commonCountry = country.clone()
                commonCountry.id = "c-" + commonCountry.code
                commonCountries.append(commonCountry)
            }
        }
        
        allCountries = list.sorted(by: { (obj1, obj2) -> Bool in
            return obj1.name.localizedStandardCompare(obj2.name) == .orderedAscending
        })
        
//
//        for key in dicSort.keys {
//            var array = dicSortkey
//            array = array!.sorted(by: { (obj1, obj2) -> Bool in
//                return obj1.name!.localizedStandardCompare(obj2.name!) == ComparisonResult.orderedAscending
//            })
//            dicSortkey = array
//        }
    }
}

struct CountryCodeV_Previews: PreviewProvider {
    static var previews: some View {
        CountryCodeV(countryCodeSelected: { str in
            
        })
    }
}
