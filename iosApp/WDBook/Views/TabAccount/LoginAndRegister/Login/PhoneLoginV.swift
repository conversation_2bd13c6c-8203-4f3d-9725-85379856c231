//
//  PhoneLoginV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2023/4/11.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import Combine
import NavigationRouter
import shared
import SwiftUI
import SwiftyUserDefaults

struct PhoneLoginV: View {
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @Binding var isShowPhoneLoginV: Bool

    @State var countryCode = Defaults[key: DefaultsKeys.LAST_LOGIN_COUNTRY_CODE] ?? "+86"
    @State var phone: String = Defaults[key: DefaultsKeys.LAST_LOGIN_PHONE] ?? ""
    @State var phoneVerified = VerifyType.none
    @State var phoneTFFocused = false
    @State var isShowCountryCodeV = false

    @State var pwd = ""
    @State var pwdVerify = VerifyType.none
    @State var isShowPlanPWD = false

    @State var isShowFogetPwdV = false

    @State var isHideOtherLoginBtns = false
    @State var isShowUpdateBibleApp = false

    var isDisable: Bool {
        phone.isBlank || pwd.isBlank
    }

    // FIXME: 是否需要通过kmm层来获取url
    func tapAuthLogin() {
        let authLoginCheckUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginCheckUrl())
        let authLoginUrl = URL(string: WDBookSessionSDK.shared.getAuthLoginUrl())

        // 有新版授权登录，有老板提示升级。
        if UIApplication.shared.canOpenURL(authLoginCheckUrl!) {
            UIApplication.shared.open(authLoginUrl!, options: [:]) { success in
                if success {
                    Log.d("10以后可以跳转url")
                } else {
                    Log.d("10以后不能完成跳转")
                }
            }
        } else {
            isShowUpdateBibleApp = true
            debugPrint("")
        }
    }

    func btnBack(left: Bool = true) -> some View {
        Button(action: {
            withAnimation {
                self.isShowPhoneLoginV = false
            }
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                    .aspectRatio(contentMode: .fit)
                    .foregroundColor(Color(btnTintColor)) // ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width: 40, height: 45)
        }
    }

    var body: some View {
        NavigationView {
            ZStack(alignment: .top, content: {
                VStack {
                    ScrollView {
                        content
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                    }.frame(maxWidth: .infinity, maxHeight: .infinity)
                        .navigationBarHidden(true)
                        .padding(.top, 45 + safeAreaInsets.top)

                    Spacer()
                    if !isHideOtherLoginBtns {
                        VStack(spacing: 20) {
                            Button(action: {
                                withAnimation {
                                    self.isShowPhoneLoginV = false
                                }
                            }) {
                                HStack {
                                    Image("icon_email")
                                    Text("使用邮箱登录".localized).foregroundColor(Color(dynamicTextColor35))
                                        .font(Font.regular(size: 16))
                                        .foregroundColor(Color(UIColor.gray))
                                }.frame(maxWidth: .infinity, alignment: .center)
                            }
                            .frame(height: 44)
                            .background(Color(dynamicTextColor27))
                            .cornerRadius(22)
                            .overlay(
                                RoundedRectangle(cornerRadius: 22)
                                    .strokeBorder(Color(dynamicSpanLineColor6), lineWidth: 1)
                            )

                            if isInstalledWdbibleApp() {
                                Button(action: {
                                    tapAuthLogin()
                                }) {
                                    HStack {
                                        Image("icon_wdbible")
                                        Text("微读圣经授权登录".localized).foregroundColor(Color(dynamicTextColor35))
                                            .font(Font.regular(size: 16))
                                            .foregroundColor(Color(UIColor.gray))
                                    }.frame(maxWidth: .infinity, alignment: .center)
                                }
                                .frame(height: 44)
                                .background(Color(dynamicTextColor27))
                                .cornerRadius(22)
                                .overlay(
                                    RoundedRectangle(cornerRadius: 22)
                                        .strokeBorder(Color(dynamicSpanLineColor6), lineWidth: 1)
                                )
                            }

                        }.foregroundColor(Color(dynamicTextColor7)).padding(24)
                    }
                }.background(Color(dynamicBackgroundColor3))

                HStack(alignment: .center, spacing: 0, content: {
                    btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                    Spacer()
                    Text("使用手机号登录".localized)
                        .foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.semibold(size: 18))
                    Spacer()
                    btnBack(left: false).padding(EdgeInsets(top: 0, leading: 20, bottom: 0, trailing: 0)).opacity(0)
                }).frame(height: 45)

                    .frame(maxWidth: .infinity)
                    .padding(.horizontal, 20)
                    .padding(.top, safeAreaInsets.top)
                    .background(Color(dynamicBackgroundColor1))
                    .modifier(BottomLineViewModifier(isShowBottomLine: true))
            })
            .navigationBarHidden(true)
            .edgesIgnoringSafeArea(.top)
        }
    }

    var content: some View {
        VStack(alignment: .center, spacing: 0) {
            VStack(spacing: 0) {
                VStack {
                    Text("手机号".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    HStack(spacing: 0) {
                        Button {
                            isShowCountryCodeV = true
                            //                        RoutableManager.navigate(toRoute: .countryCode)
                            //                        RoutableManager.push(CountryCodeV(countryCodeSelected:{countryCode in
                            //                            self.countryCode = countryCode
                            //                        }),animated: true)
                        } label: {
                            Text(countryCode).foregroundColor(mainColor)
                        }.frame(width: 61)

                        phoneVerified.borderColor().frame(width: 1)

                        TextField("请输入手机号".localized, text: $phone) { focused in
                            phoneTFFocused = focused
                            if !focused {
                                phoneVerified = Validator.phone(countryCode + phone)
                            } else {
                                phoneVerified = .none
                            }
                        } onCommit: {}.modifier(ClearButtonMode(text: $phone, focused: $phoneTFFocused))
                            .keyboardType(.phonePad)
                            .onChange(of: phone) { _ in
                                phoneVerified = .none
                            }
                            .padding(.leading, 8).padding(.trailing, 16)

                    }.frame(height: 46)
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(phoneVerified.borderColor(), lineWidth: 1)
                        )
                    switch phoneVerified {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)
                VStack {
                    Text("密码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                        .frame(maxWidth: .infinity, alignment: .leading)
                    ZStack {
                        if isShowPlanPWD {
                            TextField("请输入密码".localized, text: $pwd)
                            // .modifier(ClearButtonMode(text: $pwd))
                        } else {
                            SecureField("请输入密码".localized, text: $pwd)
                                .disableAutocorrection(true)
                                .autocapitalization(.none)
                            // .modifier(ClearButtonMode(text: $pwd))
                        }
                    }.onChange(of: pwd) { _ in
                        pwdVerify = .none
                    }.padding(EdgeInsets(top: 0, leading: 16, bottom: 0, trailing: 16))
                        .frame(height: 46)
                        .background(Color(dynamicTextColor27))
                        .cornerRadius(12)
                        .overlay(
                            RoundedRectangle(cornerRadius: 12)
                                .strokeBorder(pwdVerify.borderColor(), lineWidth: 1)
                        )
                        .overlay(Button(action: {
                            isShowPlanPWD.toggle()
                        }, label: {
                            Image(isShowPlanPWD ? "icon_visible_highlight" : "icon_visible")
                        }).padding(.trailing, 8), alignment: .trailing)

                    switch pwdVerify {
                    case let .failure(msg):
                        Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                    default:
                        EmptyView()
                    }
                }

                Spacer().frame(height: 24)
                HStack {
                    Spacer()
                    Text("忘记密码？".localized).font(Font.regular(size: 16)).foregroundColor(Color(hex: 0x006FFF))
                        .contentShape(Rectangle())
                        .onTapGesture {
                            UIApplication.dismissKeyboard()
                            isShowFogetPwdV = true
                        }
                }

                Spacer().frame(height: 24)
                Button(action: {
                    phoneVerified = .none
                    pwdVerify = .none
                    if phone.count > 30 {
                        phoneVerified = .failure("请输入正确手机号".localized)
                        return
                    }

                    guard RegularExp.isValidPhone(phone: countryCode + phone) else {
                        phoneVerified = .failure("请输入正确手机号".localized)
                        return
                    }

                    if pwd.count < 1 {
                        pwdVerify = .failure("请填写密码".localized)
                        return
                    }

                    LoginService().login(username: countryCode + phone, countryCode: countryCode, phone: phone, password: pwd) {
                        phoneVerified = .none
                        pwdVerify = .none

                    } fails: { error in
                        phoneVerified = .none
                        pwdVerify = .none

                        if error.isUserNotExist() {
//                            Toaster.showToast(message: ErrorInfo.usernotexists.message)
                            //                                    Toaster.showToast(message: "帐号或密码不正确")
                            phoneVerified = .failure(ErrorInfo.usernotexists.message)
                        } else if error.errno == ErrorInfo.passwordnotmatch.code {
                            //                                    Toaster.showToast(message: ErrorInfo.passwordnotmatch.message)
//                            Toaster.showToast(message: "帐号或密码不正确")
                            pwdVerify = .failure(ErrorInfo.passwordnotmatch.message)
                        } else if !error.msg.isEmpty {
                            Toaster.showToast(message: error.msg)
                        } else {
                            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                        }
                    }
                }) {
                    Text("登录".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity, maxHeight: .infinity)
                        .foregroundColor(Color.white)
                }.frame(minWidth: 0, maxWidth: .infinity)
                    .frame(height: 44)
                    .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                    .cornerRadius(22)
                    .disabled(isDisable)
            }

            NavigationLink(
                destination: CountryCodeV(countryCodeSelected: { countryCode in
                    self.countryCode = countryCode
                }),
                isActive: $isShowCountryCodeV,
                label: {
                    EmptyView()
                }
            )
            NavigationLink(
                destination: SendVerifyCodeForPhoneV(countryCode: countryCode, phone: phone),
                isActive: $isShowFogetPwdV,
                label: {
                    EmptyView()
                }
            )
        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .padding(24)
            .background(Color(dynamicBackgroundColor3))
            .contentShape(Rectangle()).onTapGesture {
                UIApplication.dismissKeyboard()
            }.onReceive(Publishers.keyboardOnHeight) { keyboardHeight in
                if keyboardHeight > 0 {
                    isHideOtherLoginBtns = true
                } else {
                    isHideOtherLoginBtns = false
                }
            }.alert(isPresented: $isShowUpdateBibleApp) {
                Alert(title: Text("请使用最新版[微读圣经]APP进行授权登录".localized),
                      //                        message: Text(message),
                      primaryButton: .default(Text("去升级".localized)) {
                          UIApplication.shared.open(URL(string: "https://apps.apple.com/cn/app/wei-du-sheng-jing/id654898456")!, options: [:]) { _ in
                          }
                      }, secondaryButton: .cancel(Text("取消授权".localized)) {
                          isShowUpdateBibleApp = false
                      })
            }
    }
}

#if DEBUG
    struct PhoneLoginV_Previews: PreviewProvider {
        static var previews: some View {
            PhoneLoginV(isShowPhoneLoginV: .constant(true))
        }
    }
#endif
