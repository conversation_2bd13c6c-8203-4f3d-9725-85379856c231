//
//  SendVerifyCodeForPhoneV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/4/25.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import shared
import SwiftUI

struct SendVerifyCodeForPhoneV: View {
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @State var countryCode = "+86"
    @State var isShowCountryCodeV = false

    @State var phone = ""
    @State var phoneVerified = VerifyType.none
    @State var phoneTFFocused = false
    @State var isShowPlanPWD = false

    @State var verifyCode = ""
    @State var verifyCodeVerified = VerifyType.none
    @State var timer: Timer?
    @State var remainSecond: Int = 0

    @State var token = ""
    @State var isShowResetPwdV = false

    var hasRemainSeconds: Bool {
        remainSecond > 0
    }

    func startTimer() {
        stopTimer()
        remainSecond = 60
        timer = Timer(timeInterval: 1, repeats: true, block: { _ in
            remainSecond -= 1
            if remainSecond <= 0 {
                remainSecond = 0
                stopTimer()
            }
        })
        RunLoop.main.add(timer!, forMode: .default)
    }

    func stopTimer() {
        timer?.invalidate()
        timer = nil
    }

    var isDisable: Bool {
        phone.isBlank || verifyCode.isBlank
    }

    func verifyCodeOnTap() {
        if phone.count > 30 {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        guard RegularExp.isValidPhone(phone: countryCode + phone) else {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.sendVerifyCode(email: "", mobile: countryCode + phone, codeType: CodeType.reset, appKeySignInfo: sign) { result in
            phoneVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            UIApplication.dismissKeyboard()
            switch result {
            case let .success(r):
                debugPrint(r)
                startTimer()
                Toaster.showToast(message: "验证码已发送至：%@".localizedFormat(countryCode + phone))
            case let .failure(error):
                debugPrint(error)
                if error.isUserNotExist() {
                    phoneVerified = .failure(ErrorInfo.usernotexists.message)
                } else if error.errno == ErrorInfo.smsexceedlimit.code {
                    Toaster.showToast(message: ErrorInfo.smsexceedlimit.message) // 验证码发送次数过多，请稍后再试
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    func nextOnTap() {
        if phone.count > 30 {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        guard RegularExp.isValidPhone(phone: countryCode + phone) else {
            phoneVerified = .failure("请输入正确手机号".localized)
            return
        }

        HUDManager.showLoadingBlockHUD(text: "")
        let sign = CryptoKitUtils.encrypt(string: Constants().APP_KEY_IOS) ?? ""
        WDBookSessionSDK.shared.checkVerifyCode(email: "", mobile: countryCode + phone, verificationCode: verifyCode, appKeySignInfo: sign) { result in
            phoneVerified = .none
            verifyCodeVerified = .none
            HUDManager.hideLoadingHUD()
            switch result {
            case let .success(r):
                if let t = r {
                    token = t
                    isShowResetPwdV = true
                }
            case let .failure(error):
                if error.isUserNotExist() {
                    phoneVerified = .failure(ErrorInfo.usernotexists.message)
                } else if error.errno == ErrorInfo.invalidverificationcode.code {
                    Toaster.showToast(message: "验证码输入错误或已过期".localized)
                } else if !error.msg.isEmpty {
                    Toaster.showToast(message: error.msg)
                } else {
                    Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                }
            }
        }
    }

    var body: some View {
        BackNavigation(title: "重置密码".localized) {
            VStack(alignment: .center, spacing: 0) {
                VStack(spacing: 0) {
                    VStack {
                        Text("手机号".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                            .frame(maxWidth: .infinity, alignment: .leading)
                        HStack(spacing: 0) {
                            Button {
                                isShowCountryCodeV = true
                                //                        RoutableManager.navigate(toRoute: .countryCode)
                                //                        RoutableManager.push(CountryCodeV(countryCodeSelected:{countryCode in
                                //                            self.countryCode = countryCode
                                //                        }),animated: true)
                            } label: {
                                Text(countryCode).foregroundColor(mainColor)
                            }.frame(width: 61)

                            phoneVerified.borderColor().frame(width: 1)

                            TextField("请输入手机号".localized, text: $phone) { focused in
                                phoneTFFocused = focused
                                if !focused {
                                    phoneVerified = Validator.phone(countryCode + phone)
                                } else {
                                    phoneVerified = .none
                                }
                            } onCommit: {}.modifier(ClearButtonMode(text: $phone, focused: $phoneTFFocused))
                                .onChange(of: phone) { _ in
                                    phoneVerified = .none
                                }
                                .padding(.leading, 8).padding(.trailing, 16)

                        }.frame(height: 46)
                            .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(phoneVerified.borderColor(), lineWidth: 1)
                            )
                        switch phoneVerified {
                        case let .failure(msg):
                            Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                        default:
                            EmptyView()
                        }
                    }

                    Spacer().frame(height: 24)
                    VStack {
                        Text("验证码".localized).font(Font.regular()).foregroundColor(Color(dynamicTextColor31))
                            .frame(maxWidth: .infinity, alignment: .leading)
                        HStack(spacing: 0) {
                            TextField("验证码".localized, text: $verifyCode) { _ in

                            } onCommit: {} // .modifier(ClearButtonMode(text: $verifyCode))
                                .padding(.leading, 16).padding(.trailing, 8)

                            verifyCodeVerified.borderColor().frame(width: 1)

                            Button {
                                verifyCodeOnTap()
                            } label: {
                                Text(hasRemainSeconds ? ("\(remainSecond)" + "s后重新获取".localized) : "获取验证码".localized).foregroundColor(hasRemainSeconds ? Color(hex: 0xBCBCBC) : mainColor).font(Font.regular(size: 16))
                            }.frame(width: 120).disabled(hasRemainSeconds)
                        }.frame(height: 46)
                            .background(Color(dynamicTextColor27))
                            .cornerRadius(12)
                            .overlay(
                                RoundedRectangle(cornerRadius: 12)
                                    .strokeBorder(verifyCodeVerified.borderColor(), lineWidth: 1)
                            )
                        switch verifyCodeVerified {
                        case let .failure(msg):
                            Text(msg).foregroundColor(Color(hex: 0xFF342A)).font(Font.regular()).frame(maxWidth: .infinity, alignment: .leading)
                        default:
                            EmptyView()
                        }
                    }

                    Spacer().frame(height: 24)
                    Button(action: {
                        nextOnTap()
                    }) {
                        Text("下一步".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity, maxHeight: .infinity)
                            .foregroundColor(Color.white)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height: 44)
                        .background(isDisable ? Color(mainUIColor.alpha(0.5)) : mainColor)
                        .cornerRadius(22)
                        .disabled(isDisable)
                }

                Spacer()
                NavigationLink(
                    destination: CountryCodeV(countryCodeSelected: { countryCode in
                        self.countryCode = countryCode
                    }),
                    isActive: $isShowCountryCodeV,
                    label: {
                        EmptyView()
                    }
                )
                NavigationLink(
                    destination: ForgetPwdV(token: token, pwdModifyType: .pwdModifyTypeForget),
                    isActive: $isShowResetPwdV,
                    label: {
                        EmptyView()
                    }
                )
            }.frame(maxWidth: .infinity, maxHeight: .infinity)
                .padding(24)
                .background(Color(dynamicBackgroundColor3))
                .onAppear {
                    if !phone.isEmpty {
                        phoneVerified = Validator.phone(countryCode + phone)
                    }
                }
        }
    }
}

#if DEBUG
    struct SendVerifyCodeForPhoneV_Previews: PreviewProvider {
        static var previews: some View {
            SendVerifyCodeForPhoneV()
        }
    }
#endif
