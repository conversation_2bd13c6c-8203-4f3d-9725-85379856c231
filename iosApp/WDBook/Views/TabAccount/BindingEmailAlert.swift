//
//  BindingEmailAlert.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2023/6/8.
//  Copyright © 2023 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct BindingEmailAlert: View {
    var cancelHandler:()->()
    var okHandler:()->()
    
    var body: some View {
        VStack{
            VStack(spacing:0) {
                Text("请先绑定邮箱".localized).font(Font.regular(size: 18)).bold().foregroundColor(Color(dynamicTextColor30))
                    .padding(.bottom,16)
                
                Text("收据需要发送到您的邮箱，请绑定邮箱后重新获取".localized)
                    .foregroundColor(Color(dynamicTitleColor10)).font(Font.regular(size: 16))
                    .lineSpacing(10).frame(maxWidth:.infinity,alignment:.center)
                    .multilineTextAlignment(.center)
                    .padding(.bottom,24)
                
                HStack {
                    Button(action: {
                        cancelHandler()
                    }) {
                        Text("稍后再说".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(mainColor)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(RoundedRectangle(cornerRadius: 20)
                            .strokeBorder(mainColor, lineWidth: 1))
                    .cornerRadius(20)
                    
                    Button(action: {
                        okHandler()
                    }) {
                        Text("现在绑定".localized).font(Font.medium(size: 16))
                            .frame(maxWidth: .infinity,maxHeight: .infinity)
                            .foregroundColor(Color.white)
                    }.frame(minWidth: 0, maxWidth: .infinity)
                        .frame(height:40)
                        .background(mainColor)
                    .cornerRadius(20)
                }
            }.padding(24).background(Color(dynamicTextColor27)).cornerRadius(10)
                .padding(.horizontal, UIDevice.alertAdjustHorizontalPadding)
                .frame(width: UIDevice.alertAdjustFrameWidth)
            
        }.frame(maxWidth:.infinity,maxHeight:.infinity)
            .background(Color(UIColor.black.alpha(0.25)))
    }
}

#if DEBUG
struct BindingEmailAlert_Previews: PreviewProvider {
    static var previews: some View {
        BindingEmailAlert(cancelHandler: {}, okHandler: {})
    }
}
#endif
