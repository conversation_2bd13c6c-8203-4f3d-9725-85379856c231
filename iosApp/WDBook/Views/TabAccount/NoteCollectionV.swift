//
//  NoteCollectionV.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/7/28.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import DeviceKit
import SDWebImageSwiftUI

struct NoteCollectionV: View {
    @State private var bookNoteList: [BookNoteCountEntity] = []
    @State private var headerRefreshing: Bool = false
    @State private var isFirstLoad: Bool = true
    @State private var needRefresh = false
    @State private var selectItem: BookNoteCountEntity?
    
    static let itemDesignWidth:CGFloat = 92.0
    static let itemDesignHeight:CGFloat = 132.0
    
    static let itemMiniWidth = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                               Device.iPodTouch7,
                                                               Device.simulator(.iPodTouch7),
                                                               Device.simulator(.iPhoneSE)]) ? 82 : itemDesignWidth)
    static let itemMinHeight = itemDesignHeight * itemMiniWidth / itemDesignWidth
    static let span = CGFloat(Device.current.isOneOf([Device.iPhoneSE,
                                                      Device.iPodTouch7,
                                                      Device.simulator(.iPodTouch7),
                                                      Device.simulator(.iPhoneSE)]) ? 12 : 24)
    private var columns:[GridItem]{
        return [GridItem(.adaptive(minimum: TabShelfV.itemMiniWidth))]
    }
    
    private func loadData() {
        bookNoteList.removeAll()
        var list = WDBookUserSDK.shared.getBookNoteCountList(offset: 0)
        while list.count > 0  {
            bookNoteList.append(contentsOf: list)
            list = WDBookUserSDK.shared.getBookNoteCountList(offset: Int64(bookNoteList.count))
        }
        headerRefreshing = false
    }
    
    private func dataRefresh() {
        WDBookSyncSDK.shared.syncNoteData { [self] result in
            switch result {
            case .success:
                loadData()
            case .failure:
                Toaster.showToast(message: "刷新失败，请重新尝试！".localized)
            }
        }
    }
    
    var body: some View {
        BackNavigation(title: "笔记".localized,isHideBackButton:false,isHideBottomLine:true) {
            VStack {
                if self.bookNoteList.count > 0 {
                    ScrollView {
                        WDRefreshHeader(refreshing: $headerRefreshing, action: {
                            self.dataRefresh()
                        })

                        LazyVGrid(columns: columns, alignment: .center, spacing: NoteCollectionV.span) {
                            ForEach(bookNoteList, id:\.resourceId) { item in
                                NoteCollectionCell(bookNoteCountEntity: item)
                                    .onTapGesture {
                                        self.selectItem = item
                                        RoutableManager.navigate(toPath: RouterName.notesBooksWithId.withParam(self.selectItem?.resourceId ?? ""))
                                    }
                            }
                        }
                        .padding(.horizontal, NoteCollectionV.span)
                        .padding(.vertical, 12)
                    }.enableRefresh()
                    .frame(maxWidth: .infinity,maxHeight: .infinity)
                    .background(Color(dynamicBackgroundColor4))
                } else {
                    VStack(alignment: .center, spacing: 0, content: {
                        Image("error_empty").frame(width: 167, height: 110)
                        Spacer().frame(height:54)
                        Text("您还没有做任何笔记".localized).font(Font.medium(size: 16)).foregroundColor(Color(dynamicTitleColor2))
                    })
                }
            }.padding(.top, 12)
            .frame(maxWidth:.infinity,maxHeight: .infinity)
            .navigationBarTitle("笔记".localized,displayMode: .inline)
            .onAppear {
                if isFirstLoad {
                    loadData()
                    isFirstLoad = false
                }else if needRefresh{
                    loadData()
                    needRefresh = false
                }
            }
            .onReceive(NotificationCenter.default.publisher(for: NoteEditerVC.noteChangedNotification), perform: { noti in
                needRefresh = true
            })
            .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteDeletedNotification), perform: { noti in
                needRefresh = true
            })
            .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteResumeNotification), perform: { noti in
                needRefresh = true
            })
        }
    }
}

extension BookNoteCountEntity: Identifiable {}

struct NoteCollectionCell: View {
    var bookNoteCountEntity: BookNoteCountEntity
    
    var body: some View {
        VStack(spacing: 0.0) {
            ImageManager.getWebImage(url: URL(string: ImageManager.getImageUrl(self.bookNoteCountEntity.cover)))
                .resizable()
                .placeholder{
                    Image("cover_92*132").aspectRatio(contentMode: .fit).frame(width: NoteCollectionV.itemMiniWidth, height: NoteCollectionV.itemMinHeight, alignment: .center)
                }
                .transition(.fade(duration: 0.5)) // Fade Transition with duration
                .cornerRadius(4)
                .frame(width: NoteCollectionV.itemMiniWidth, height: NoteCollectionV.itemMinHeight, alignment: .center)

            Text(self.bookNoteCountEntity.name)
                .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                .font(Font.regular(size: 12))
                .foregroundColor(Color(dynamicTitleColor2))
                .frame(maxWidth: .infinity, maxHeight: .infinity, alignment: .topLeading)

            HStack(spacing:2.0) {
                Image("mine_note_countmark").frame(width: 20, height: 20, alignment: .center)
                Text("\(self.bookNoteCountEntity.count)条")
                    .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(dynamicTitleColor2))
                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
            }
        }
        .frame(width: NoteCollectionV.itemMiniWidth, height: NoteCollectionV.itemMinHeight + 66)
    }
}

#if DEBUG
struct NoteCollectionV_Previews: PreviewProvider {
    static var previews: some View {
        NoteCollectionV()
    }
}
#endif
