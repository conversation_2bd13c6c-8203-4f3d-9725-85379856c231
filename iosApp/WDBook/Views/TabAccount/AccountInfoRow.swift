//
//  AccountInfoRow.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/5/19.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SDWeb<PERSON>mageSwiftUI
import shared
import NavigationRouter

struct UserAvatar: View{
    @EnvironmentObject var appState:AppState
    @State var usePlaceholderCache = false
    @State var placeHolderImage:Image?
    
    var placeHolder:Image{
        if usePlaceholderCache{
            return placeHolderImage != nil ? placeHolderImage!.resizable() : Image("header").resizable()
        }else{
            return Image("header").resizable()
        }
    }
    
    var body: some View{
        ImageManager.getWebImage(url: URL(string:appState.userInfo.headerImageUrl))
            .onSuccess { image, data, cacheType in
                if usePlaceholderCache{
                    placeHolderImage =  Image(uiImage: image)
                }
            }
            .resizable()
            .placeholder(
                placeHolder
            )
            .transition(.fade(duration: 0.5)) // Fade Transition with duration
            .scaledToFill()
            .clipShape(Circle())
    }
}

struct AccountInfoRow: View {
    @EnvironmentObject var appState:AppState
    var body: some View {
        if WDBookSessionSDK.shared.isLogin {
            HStack(alignment: .center,spacing: 16) {
                UserAvatar().frame(width: 90, height: 90, alignment: .center)

                Text(appState.userInfo.nickname ?? "")
                    .font(.medium(size: 18))
                    .frame(maxWidth:.infinity,alignment:.leading)
                    .foregroundColor(Color(dynamicTitleColor2))
                
                RoutedLink(toRoute: .editUserInfo) {
                    Text("编辑资料")
                        .font(.regular())
                        .foregroundColor(Color(dynamicTitleColor2))
                }
                
            }
            .frame(maxWidth: .infinity, alignment: .center)
            .padding(16)
            .background(Color(dynamicBackgroundColor1))
        }else{
            VStack(alignment: .center) {
                Text("您还没有登录".localized).font(Font.regular(size:18).bold())
                Spacer().frame(height: 8)
                Text("登录后可以同步阅读记录、高亮笔记、阅读进度等数据".localized).font(Font.regular()) //TODO:白天 4E4E4E,黑夜BCBCBC
                Spacer().frame(height:16)
                
                RoutedLink(toRoute: .register) {
                    Text("没有微读帐号，请注册".localized)
                        .font(Font.medium(size: 16))
                        .foregroundColor(Color(UIColor.primaryColor1))
                        .frame(maxWidth: .infinity,alignment: .center)
                    .frame(height: 44)
                    .background(Color(dynamicBtnBGColor3))
                    .cornerRadius(22)
                    .overlay(
                        RoundedRectangle(cornerRadius: 22)
                            .strokeBorder(Color(UIColor.primaryColor1), lineWidth: 1)
                    )
                }

                Spacer().frame(height:16)
                
                RoutedLink(toRoute: .login) {
                    Text("已有微读帐号，请登录".localized).font(Font.medium(size: 16))
                        .frame(maxWidth: .infinity,maxHeight: .infinity)
                        .foregroundColor(Color.white)
                        .frame(minWidth: 0, maxWidth: .infinity)
                            .frame(height:44)
                            .background(Color(UIColor.primaryColor1))
                            .cornerRadius(22)
                }
            }.padding(24)
            .frame(maxWidth: .infinity, alignment: .center)
            .background(Color(dynamicBackgroundColor1))
        }
        
    }
}

#if DEBUG
struct AccountInfoRow_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            AccountInfoRow().environmentObject(AppState.shared)
            
            AccountInfoRow().environmentObject(AppState.shared).environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (2nd generation)")
        }
    }
}
#endif
