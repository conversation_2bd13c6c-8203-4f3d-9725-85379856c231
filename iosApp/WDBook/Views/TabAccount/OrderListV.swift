//
//  OrderListV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/22.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import GRDB

struct OrderListV: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets3) private var safeAreaInsets
    @State var isShowBindingEmailAlert = false
    @State var isShowOrderReceiptAlert = false
    
    @State var orders: [OrderEntity] = []
    init(orders: [OrderEntity]? = nil) {
        if let res = orders {
            _orders = State(initialValue: res)
        }
    }
    
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    
    var minCountOnFirstLoad = 50
    @State var noMore: Bool = false {
        didSet {
            if !noMore && orders.count < minCountOnFirstLoad {
                loadMore()
            }
        }
    }
    
    @State var needRefresh = true
    @State var isFirstLoad = true
    
    func refresh() {
        WDBookUserSDK.shared.getOrderEntityList(lastUpdateTime: 0) { result in
            switch result {
            case .success(let list):
                self.orders = list ?? []
                
                self.headerRefreshing = false
                    
                let hasNext = orders.count > 0
                self.noMore = !hasNext
   
                isFirstLoad = false
            case .failure(let error):
                self.headerRefreshing = false
                isFirstLoad = false
                print(error)
            }
        }
    }
    
    func loadMore() {
        guard headerRefreshing == false else {
            return
        }
        
        let lastUpdateTime = Int64(orders.map { $0.lastUpdateTime }.min() ?? 0)
        WDBookUserSDK.shared.getOrderEntityList(lastUpdateTime: lastUpdateTime) { result in
            switch result {
            case .success(let list):
                self.orders.append(contentsOf: list ?? [])
                
                self.footerRefreshing = false
                
                let hasNext = list?.count > 0
                self.noMore = !hasNext
                
                isFirstLoad = false
            case .failure(let error):
                self.footerRefreshing = false
                isFirstLoad = false
                print(error)
            }
        }
    }
    
    func tapgetOrderReceipt(orderId:String){
        if appState.userInfo.email.isBlank{
            isShowBindingEmailAlert = true
            
        }else{
            HUDManager.showLoadingBlockHUD(text: "")
            WDBookUserSDK.shared.getOrderReceipt(orderId: orderId) { result in
                HUDManager.hideLoadingHUD()
                switch result{
                case .success(let r):
                    isShowOrderReceiptAlert = true
                case .failure(let e):
                    break
                }
            }
        }
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        presentationMode.wrappedValue.dismiss()
        }) {
            HStack {
                !left ? AnyView(Spacer()) : AnyView(EmptyView())
                Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor)) //ios14无效
                left ? AnyView(Spacer()) : AnyView(EmptyView())
            }.frame(width:40,height: 45)
        }
    }
    
    var body: some View{
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .overlay(
                Text("购买记录".localized)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18)), alignment: .center)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .overlay(
            Group {
                if isShowBindingEmailAlert {
                    BindingEmailAlert(cancelHandler: {
                        isShowBindingEmailAlert = false
                    }, okHandler: {
                        isShowBindingEmailAlert = false
                        RoutableManager.showBindingEmailV(returnView: OrderListV.self)
                    })
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
        .overlay(
            Group {
                if isShowOrderReceiptAlert {
                    OrderReceiptAlert(email: appState.userInfo.email) {
                        isShowOrderReceiptAlert = false
                    }
                }else{
                    EmptyView()
                }
            }
            , alignment: .center)
    }
    
    var content: some View {
        ScrollView {
            if self.orders.count > 0 {
                WDRefreshHeader(refreshing: $headerRefreshing, action: {
                    self.refresh()
                })
            }
            
            LazyVStack(spacing:8){
                Color(dynamicBackgroundColor3).frame(height:0.1)
                ForEach(self.orders, id: \.orderId) { order in
                    OrderItem(order: order, tapGetOrderReceipt: {
                        tapgetOrderReceipt(orderId: order.orderId)
                    })
                        .contentShape(Rectangle())
                        .onTapGesture {
                            RoutableManager.push(OrderDetailsV(order: order))
                        }
                }
            }.background(Color(dynamicBackgroundColor3))
            .padding(.vertical,8)
            
            
            if self.orders.count > 0 {
                if !noMore {
                    if self.orders.count < minCountOnFirstLoad {
                        SimpleRefreshingView().padding()
                    } else {
                        WDRefreshFooter(refreshing: $footerRefreshing, noMore: $noMore) {
                            self.loadMore()
                        }
                    }
                } else {
                    Spacer().frame(height: 12).listRowInsets(EdgeInsets())
                }
            }
        }
        .enableRefresh()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(dynamicBackgroundColor4))
        .overlay(Group {
            if isFirstLoad {
                ActivityIndicator()
            } else if self.orders.count == 0 {
                Text("当前暂无购买记录".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
            } else {
                EmptyView()
            }
        }).onAppear {
            if needRefresh {
                self.refresh()
            }
            needRefresh = false
        }
    }
}

func getCurrencySymbol(currency: String?) -> String {
    switch currency {
    case "USD":
        return "$"
    case "HKD":
        return "HK$"
    case "RMB":
        return "￥"
    default:
        return currency ?? ""
    }
}

struct OrderItem: View {
    var order: OrderEntity
    var tapGetOrderReceipt:()->()
    var body: some View {
        VStack(spacing: 0) {
            Text("订单时间：%@".localizedFormat(order.lastUpdateTime.yyyymmddhhmm))
                .lineLimit(1)
                .font(Font.regular(size: 16).bold())
                .foregroundColor(Color(dynamicTextColor19))
                .frame(maxWidth: .infinity, alignment: .leading)
                .padding(.horizontal,16)
                .frame(height: 48)
            Color(dynamicSpanLineColor6).frame(maxWidth:.infinity).frame(height:0.5)
            
            if order.type == 1{
                OrderGiftItem(o: order)
            }else{
                if order.products?.count > 1{
                    HStack(spacing:0){
                        HStack(spacing:12){
                            ForEach((order.products ?? []).prefix(4),id:\.productId) { product in
                                ProductCoverView(purchased: 0, cover: product.cover,width: 56,height: 74)
                            }
                        }.frame(maxWidth:.infinity,maxHeight: .infinity,alignment: .leading).padding(.leading,24)
                        
                        HStack(spacing:0) {
                            Text("共%lld件".localizedFormat((order.products ?? []).count))
                                .font(Font.regular()).foregroundColor(Color(dynamicTitleColor8))
                            Image("arrow_right_20").frame(width: 20,height: 20)
                        }.frame(width:90).frame(maxHeight:.infinity)

                    }.frame(maxWidth:.infinity).frame(height:106)
                }else if order.products?.count == 1{
                    let p = order.products!.first!
                    OrderProductItem(p: p)
                }
            }
            
        
            HStack {
                Text("获取收据".localized).font(Font.regular(size: 14))
                    .foregroundColor(Color(dynamicTextColor19))
                    .padding(EdgeInsets(top: 6, leading: 13.5, bottom: 6, trailing: 13.5))
                    .background(RoundedRectangle(cornerRadius: 16).strokeBorder(Color(dynamicBorderColor6), lineWidth: 1))
                    .contentShape(Rectangle())
                    .onTapGesture {
                        tapGetOrderReceipt()
                    }
                Spacer()
                Text("实付款：".localized + getCurrencySymbol(currency: order.currency) + order.totalAmount.fractionDigitsDot2)
                    .font(Font.regular(size: 16).bold())
                    .foregroundColor(Color(dynamicTextColor19))
            }.padding(EdgeInsets(top: 14, leading: 16, bottom: 14, trailing: 16))
            
        }
        .frame(maxWidth: .infinity)
        .background(Color(dynamicTextColor27))
    }
}

struct OrderProductItem: View{
    var p: OrderProductEntity
    var isSimpleNum:Bool = false
    
    var body:some View{
        HStack(spacing:10) {
            ProductCoverView(purchased: 0, cover: p.cover,width: 64,height: 85)
            VStack(spacing:6){
                Text(p.title ?? "")
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(dynamicTextColor19))
                    .frame(maxWidth: .infinity, alignment: .leading)
                Text(p.authorNames)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(hex:0x999999))
                    .frame(maxWidth: .infinity, alignment: .leading)
                Text(p.publisherName)
                    .font(Font.regular(size: 12))
                    .foregroundColor(Color(hex:0x999999))
                    .frame(maxWidth: .infinity, alignment: .leading)
                Spacer()
            }.frame(maxWidth: .infinity)
            
            VStack(spacing:6){
                Text("$\(p.price.fractionDigitsDot2)")
                    .font(Font.regular(size: 14))
                    .foregroundColor(Color(dynamicTextColor19))
                Text(isSimpleNum ? "x1" : "共%lld件".localizedFormat(1))
                    .font(Font.regular(size: 14))
                    .foregroundColor(Color(hex:0x999999))
                Spacer()
            }
        }.frame(height:85)
        .padding(EdgeInsets(top: 12, leading: 24, bottom: 12, trailing: 24))
        .background(Color(dynamicTextColor27))
    }
}

struct OrderGiftItem: View{
    var o: OrderEntity
    var isSimpleNum:Bool = false
    
    var body:some View{
        HStack(spacing:10) {
            ProductCoverView(purchased: 0, cover:o.giftCardVo?.cover ?? "" ,width: 64,height: 40)
            VStack(spacing:6){
                Text("微读书城礼品卡".localized)
                    .font(Font.regular(size: 16))
                    .foregroundColor(Color(dynamicTextColor19))
                    .frame(maxWidth: .infinity, alignment: .leading)
                Spacer()
            }.frame(maxWidth: .infinity)
            
            VStack(spacing:6){
                Text("$\((o.giftCardVo?.amount ?? 0).fractionDigitsDot2)")
                    .font(Font.regular(size: 14))
                    .foregroundColor(Color(dynamicTextColor19))
                Text(isSimpleNum ? "x\(o.giftCardVo?.count ?? 0)" : "共%lld件".localizedFormat((o.giftCardVo?.count ?? 0)))
                    .font(Font.regular(size: 14))
                    .foregroundColor(Color(hex:0x999999))
                Spacer()
            }
        }.frame(height:40)
        .padding(EdgeInsets(top: 12, leading: 24, bottom: 12, trailing: 24))
        .background(Color(dynamicTextColor27))
    }
}

#if DEBUG
struct OrderListV_Previews: PreviewProvider {
    static var testOrderList:[OrderEntity]{
        let jsonString = loadJsonString(from: test_json_OrderList)
        let list = OrderEntity().testModels(jsonString: jsonString) as! [OrderEntity]
        return list
    }
    
    static var previews: some View {
        OrderListV(orders: testOrderList).environmentObject(AppState.shared)
    }
}
#endif
