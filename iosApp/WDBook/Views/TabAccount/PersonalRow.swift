//
//  PersonalRow.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/5/21.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct PersonalRow: View {
    @EnvironmentObject var appState: AppState
    let imageName: String
    let titleStringKey: String
    @Binding var descStringKey: String
    @Binding var hasDot: Bool

    init(imageName: String, titleStringKey: String, descStringKey: Binding<String> = .constant(""), hasDot: Binding<Bool> = .constant(false)) {
        self.imageName = imageName
        self.titleStringKey = titleStringKey
        _descStringKey = descStringKey
        _hasDot = hasDot
    }

    var body: some View {
        HStack {
            Image(imageName).frame(width: 24, height: 24, alignment: .center)
                .hasRedDot(hasDot: $hasDot)
            Text(LocalizedStringKey(titleStringKey))
                .font(Font.regular(size: 16))
                .foregroundColor(Color(dynamicTitleColor2))
            Spacer()
            Text(LocalizedStringKey(descStringKey))
                .font(Font.regular(size: 14))
                .foregroundColor(Color(dynamicTextColor4))
                .opacity(WDBookSessionSDK.shared.isLogin ? 1 : 0)
            Image("arrow_right").frame(width: 20, height: 20, alignment: .center)
        }.frame(minWidth: 0, maxWidth: .infinity)
            .frame(height: 64).padding(.horizontal, 24)
            .background(Color(dynamicBackgroundColor1))
    }
}

#if DEBUG
    struct PersonalRow_Previews: PreviewProvider {
        static var previews: some View {
            Group {
                PersonalRow(imageName: "icon_book_purchased", titleStringKey: "已购书籍", descStringKey: .constant("3本")).environmentObject(AppState.shared)
                PersonalRow(imageName: "icon_account", titleStringKey: "账户", descStringKey: .constant("￥12.00")).environmentObject(AppState.shared).environment(\.colorScheme, .dark)
                PersonalRow(imageName: "icon_feedback", titleStringKey: "帮助与反馈".localized, descStringKey: .constant(""), hasDot: .constant(true))
            }
        }
    }
#endif
