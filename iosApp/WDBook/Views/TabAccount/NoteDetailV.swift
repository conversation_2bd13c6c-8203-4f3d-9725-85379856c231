//
//  NoteDetailV.swift
//  WDBook
//
//  Created by 杜文泽 on 2021/8/23.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared
import SnapKit

struct NoteDetailV: View {
    @EnvironmentObject var dataObserable: NoteListObservable
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.viewController) private var holder
    @StateObject var selectedNote:Note = Note()
    @State var noteDataId: String
    @State var showingNoteEditV: Bool = false
    @State var resourceId:String = ""
    @State var isFirstLoad = true
    @State var needPopCollectionV = false
    @State var needPopListV = false
    
    init(noteDataId:String){
        _noteDataId = State(initialValue: noteDataId)
    }
    
    private func openBook() {
        let resourceDownloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: selectedNote.noteEntity!.resourceId)
        if resourceDownloadInfo?.downloadStatus == .complete {
            RoutableManager.pushBookReader(resourceId: selectedNote.noteEntity!.resourceId,chapterPath: selectedNote.chapterPath,contentOffset: selectedNote.range.location)
        }else{
            guard NetReachability.isReachability() else{
                Toaster.showToast(message: "无网络连接，请稍后再试".localized)
                return
            }
            
            guard WDBookSessionSDK.shared.isLogin else{
                AppState.shared.showLoginRegisterVAndPopToStore()
                return
            }
            
            AppState.shared.alert.bookNoteDownloadAlert(title: "提示".localized, msg: "请先下载本书，才可进入阅读模式".localized) { [self] in
                Log.d("开始下载")
                if(AppState.shared.shelfItems.filter( {$0.resourceId == selectedNote.noteEntity?.resourceId} ).first == nil){
                    AppState.shared.shelfItems.append(getShelfBookEntity(resourceId: selectedNote.noteEntity?.resourceId ?? ""))
                }
                holder?.present(builder: {
                    if let item = AppState.shared.shelfItems.filter( {$0.resourceId == selectedNote.noteEntity?.resourceId} ).first{
                        BookDownloadingView(bookItem: item) {
                            let info = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: selectedNote.noteEntity!.resourceId)
                            if info?.downloadStatus == .complete {
                                RoutableManager.pushBookReader(resourceId: selectedNote.noteEntity!.resourceId,chapterPath: selectedNote.chapterPath,contentOffset: selectedNote.range.location)
                            }
                        }
                    }
                    
                })
            } cancelHandler: {
                Log.d("不下载，取消")
            }
        }
    }
    
    var body: some View {
        BackNavigation(title: "笔记详情".localized,isHideBackButton:false,isHideBottomLine:true) {
            ZStack{
                VStack {
                    GeometryReader { geometry in
                        ScrollView {
                            VStack(alignment: .leading, spacing: 16) {
                                HStack(spacing: 12) {
                                    Color(selectedNote.style.showColor)
                                        .frame(width: 10, height: 10, alignment: .center)
                                        .cornerRadius(5.0)
                                    
                                    Text("\(Int64(selectedNote.createTime.timeIntervalSince1970).yyyymmddhhmm)")
                                        .font(.regular(size: 12))
                                        .foregroundColor(Color(UIColor(hex: 0x969696)))
                                        .frame(alignment: .center)
                                    
                                    Spacer()
                                    
                                    Button {
                                        AppState.shared.alert.deleteNoteAlert(title: "删除笔记".localized, msg: "该笔记删除后，可在笔记回收站找回".localized) { [self] in
                                            DispatchQueue.main.async {
                                                WDBookUserSDK.shared.removeNote(dataId: selectedNote.id)
                                                if let bookNoteCounterEntity = WDBookUserSDK.shared.getBookNoteCountEntity(resourceId: selectedNote.noteEntity!.resourceId), bookNoteCounterEntity.count > 0 {
                                                    self.presentationMode.wrappedValue.dismiss()
                                                } else {
                                                    RoutableManager.popToView(NoteCollectionV.self)
                                                }
                                                NotificationCenter.default.post(name: NoteEditerVC.noteChangedNotification, object: selectedNote)
                                            }
                                        } cancelHandler: {
                                            Log.d("取消")
                                        }
                                    } label: {
                                        Image("mine_note_delete")
                                            .foregroundColor(Color.gray)
                                            .frame(width: 24.0, height: 24.0, alignment: .center)
                                    }.buttonStyle(BorderlessButtonStyle())
                                }.frame(alignment: .bottomLeading)
                                
                                LTRLabel(text: selectedNote.summary,textColor: dynamicTextColor15,font: UIFont.regular(size: 16),lineSpacing: 5,horizontalPadding:16)
                                    .fixedSize(horizontal: false, vertical: true)
                                    .font(.regular(size: 16))
                                    .foregroundColor(Color(dynamicTextColor15))
                                    .lineSpacing(5)
                                    .onTapGesture(perform: openBook)
                                
                                Divider()
                                    .background(Color(dynamicSpanLineColor5))
                                    .scaleEffect(CGSize(width: 1, height: 1))
                                
                                LTRLabel(text: selectedNote.noteText,textColor: dynamicTitleColor4,font: UIFont.systemFont(ofSize: 16),bgColor: dynamicBackgroundColor4,lineSpacing: 5,horizontalPadding:16)
                                    .foregroundColor(Color(dynamicTitleColor4))
                                    .fixedSize(horizontal: false, vertical: true)
                                    .frame(maxWidth:.infinity,maxHeight: .infinity,alignment: .topLeading)
                                    .background(Color(dynamicBackgroundColor4))
                                    .font(.system(size: 16))
                                    .lineSpacing(5)
                                    .frame(width: geometry.size.width)
                                    .onTapGesture {
                                        self.showingNoteEditV = true
                                    }
                            }.frame(minHeight: geometry.size.height, alignment: .topLeading)
                        }
                    }
                    
                    Button {
                        var noteIndex = dataObserable.bookNoteList.firstIndex(where: {$0.id == noteDataId}) ?? 0
                        if noteIndex >= dataObserable.bookNoteList.count-1 {
                            noteIndex = 0
                        } else {
                            noteIndex += 1
                        }
                        
                        let nextNote = dataObserable.bookNoteList[noteIndex]
                        selectedNote.changeNoteEntity(entity: nextNote.noteEntity!)
                        noteDataId = selectedNote.id
                        
                    } label: {
                        Text("下一条".localized)
                            .frame(minWidth: 0, maxWidth: .infinity)
                            .font(.system(size: 18))
                            .padding()
                            .foregroundColor(Color(dynamicTitleColor4))
                            .background(RoundedCorners(bgColor: Color(dynamicBtnBGColor4), tl: 50, tr: 50, bl: 50, br: 50))
                            .overlay(
                                RoundedRectangle(cornerRadius: 25)
                                    .stroke(Color(dynamicBorderColor2), lineWidth: 1)
                            )
                    }
                    
                }
            }
            .padding(EdgeInsets(top: 16, leading: 16, bottom: 6, trailing: 16))
            .background(Color(dynamicBackgroundColor4))
            .sheet(isPresented: $showingNoteEditV) {
                NoteEditerVCUIKit(note: selectedNote, needPostNoti: true)
            }.onReceive(NotificationCenter.default.publisher(for: WDReaderView.readBookWillHideNotification), perform: { noti in
                if needPopCollectionV{
                    RoutableManager.popToView(NoteCollectionV.self)
                }else if needPopListV{
                    self.presentationMode.wrappedValue.dismiss()
                }else{
                    self.presentationMode.wrappedValue.dismiss()
                }
            }).onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteDeletedNotification), perform: { noti in
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    if let existNote = dataObserable.bookNoteList.filter({$0.noteEntity?.dataId == noteDataId}).first{
                        selectedNote.changeNoteEntity(entity: existNote.noteEntity!)
                        noteDataId = selectedNote.id
                    }else{
                        if let bookNoteCounterEntity = WDBookUserSDK.shared.getBookNoteCountEntity(resourceId: selectedNote.noteEntity!.resourceId), bookNoteCounterEntity.count > 0 {
                            needPopListV = true
                        } else {
                            needPopCollectionV = true
                        }
                    }
                }
            }).onAppear {
                if let existNote = dataObserable.bookNoteList.filter({$0.noteEntity?.dataId == noteDataId}).first{
                    selectedNote.changeNoteEntity(entity: existNote.noteEntity!)
                    noteDataId = selectedNote.id
                }
            }
        }
    }
}

