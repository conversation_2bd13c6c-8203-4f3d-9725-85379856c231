//
//  CouponCellV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/14.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct CouponsCellV : View {
    @State var canToUse = false
    @State var canSelected = false
    @StateObject var coupon: Coupon
    var takeAction: ((Int64)->())?
    var selectedAction:((Int64)->())?
    var style: StrokeStyle{
        var style = StrokeStyle(lineWidth: 1)
        style.dash = [4, 2]
        return style
    }
    
    func statusImage(coupon: Coupon) -> some View{
        let name:String
        switch coupon.status{
        case .unReceived:
            name = ""
        case .unused:
            name = "coupon_draw"
        case .used:
            name = "coupon_uesd"
        case .expired:
            name = "coupon_expired"
        }
        return Image(name)
    }
    
    //领取优惠券
    func getCoupon() {
        HUDManager.showLoadingBlockHUD(text: "")
        WDBookUserSDK.shared.getUserCoupon(couponId: coupon.couponId) {
            result in
            HUDManager.hideLoadingHUD()
            switch result{
            case .success(let cid):
                coupon.status = .unused
                takeAction?(coupon.couponId)
                break
            case .failure(let error):
                Log.d("\(error)")
                Toaster.showToast(message: error.msg)
                break
            }
        }
    }
    
    var body: some View{
        HStack(spacing:0) {
            HStack(spacing:0){
                Text("$").font(Font.regular(size: 16))
                Text("\(Int(coupon.couponAmount))").lineLimit(1).font(Font.regular(size: 30))
            }.frame(maxWidth:.infinity,alignment: .leading)
                .foregroundColor((coupon.status == .used || coupon.status == .expired) ? Color(hex: 0xBCBCBC) : mainColor)
                .padding(.leading,24)
                .frame(width:96)
            
            VStack(alignment:.leading) {
                Text(coupon.couponName)
                    .font(Font.regular())
                    .lineLimit(1).foregroundColor((coupon.status == .used || coupon.status == .expired) ? Color(hex: 0xBCBCBC) : Color(dynamicTextColor26))
                Spacer()
                Text("\(coupon.availableStartTime.yyyymmddDot)-\(coupon.availableEndTime.yyyymmddDot)")
                    .font(Font.regular(size: 12))
                    .lineLimit(1).foregroundColor(Color(hex: 0xBCBCBC))
            }.frame(maxWidth:.infinity,alignment:.leading)
                .padding(.trailing,8)
                .padding(.vertical,18)
            
            
            VStack(spacing:0){
                if coupon.status == .unReceived{
                    Button {
                        getCoupon()
                    } label: {
                        Text("领取".localized)
                            .font(Font.regular(size: 12))
                            .foregroundColor(mainColor)
                            .frame(width: 50, height: 20)
                            .cornerRadius(10)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10, style: .continuous)
                                    .stroke(mainColor, lineWidth: 1)
                                
                            )
                            .frame(maxWidth:.infinity,maxHeight:.infinity)
                    }
                }else if coupon.status == .unused && canSelected{
                    Button{
                        selectedAction?(coupon.couponId)
                        //                        coupon.isSelected.toggle()
                    } label: {
                        Image(coupon.isSelected ? "checkbox24_check" : "checkbox24_uncheck").frame(width: 24, height: 24)
                            .frame(maxWidth:.infinity,maxHeight:.infinity)
                    }
                }else if coupon.status == .unused && canToUse{
                    Button {
                        
                        if coupon.productIds.count == 1{
                            RoutableManager.navigate(toPath: RouterName.productDetail.withParam(coupon.productIds.first!))
                        }else{
                            RoutableManager.popToRoot()
                            AppState.shared.changeTab(tabName: .store)
                        }
                        
                    } label: {
                        Text("去使用".localized)
                            .font(Font.regular(size: 12))
                            .foregroundColor(mainColor)
                            .frame(width: 50, height: 20)
                            .cornerRadius(10)
                            .overlay(
                                RoundedRectangle(cornerRadius: 10, style: .continuous)
                                    .stroke(mainColor, lineWidth: 1)
                                
                            )
                            .frame(maxWidth:.infinity,maxHeight:.infinity)
                    }
                }else{
                    statusImage(coupon: coupon)
                }
            }.frame(width:80)
                .frame(maxHeight:.infinity)
            
        }.frame(maxWidth:.infinity)
            .frame(height:95)
            .background(GeometryReader { geometry in
                let itemWidth = geometry.size.width
                let itemHeight = geometry.size.height
                
                ZStack {
                    CouponBGShape().fill(Color(dynamicTextColor27))
                    
                    Path {
                        path in
                        path.move(to: CGPoint(x: itemWidth - 80, y: 5 + 3))
                        path.addLine(to: CGPoint(x: itemWidth - 80, y: itemHeight - 5 - 3))
                    }.stroke(Color(dynamicSpanLineColor9), style: style)
                }.frame(maxWidth:.infinity,maxHeight:.infinity)
                    .shadow(color: Color(UIColor.black.alpha(0.1)), radius: 4, x: 0, y: 0)
            })
            .padding(.horizontal,16)
    }
}

struct CouponBGShape:Shape{
    func path(in rect: CGRect) -> Path {
        let width = rect.width
        let height = rect.height
        
        return Path { path in
            path.move(to: CGPoint(x: 0,y: 0))
            path.addLine(to: CGPoint(x: width - 75,y: 0))
            
            path.addArc(center: CGPoint(x: width - 80, y: 0), radius: 5, startAngle: .degrees(-180), endAngle: .degrees(0), clockwise: true)
            path.addLine(to: CGPoint(x: width - 85,y: 0))
            path.addLine(to: CGPoint(x: width,y: 0))
            path.addLine(to: CGPoint(x: width,y: height))
            path.addLine(to: CGPoint(x: width - 85,y: height))
            path.addArc(center: CGPoint(x: width - 80, y: height), radius: 5, startAngle: .degrees(0), endAngle: .degrees(180), clockwise: true)
            path.addLine(to: CGPoint(x: width - 75,y: height))
            path.addLine(to: CGPoint(x: 0,y: height))
            path.addLine(to: CGPoint(x: 0,y: 0))
        }
    }
}

#if DEBUG
struct CouponCellV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            
            CouponsCellV(canToUse: false, canSelected: true, coupon: test_coupons.first!)
                .previewDevice("iPhone 13 Pro Max")
            
            CouponsCellV(canToUse: false, canSelected: true, coupon: test_coupons.first!)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
            CouponsCellV(canToUse: true, canSelected: true, coupon: test_coupons[2])
                .previewDevice("iPhone 13 Pro Max")
            
            CouponsCellV(canToUse: true, canSelected: true, coupon: test_coupons[2])
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
            
            CouponListV(status: .unused, mv:CouponListMV(status: .unused, test_coupons))
            
            CouponListV(status: .unused, mv:CouponListMV(status: .unused, test_coupons))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
        }
    }
}
#endif
