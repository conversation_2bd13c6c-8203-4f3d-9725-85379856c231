//
//  CouponDetailsV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/30.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

struct CouponDetailsV : View {
    @State var coupon: Coupon

            var
    body: some View{
            BackNavigation(title:"券详情".localized) {
                VStack(spacing:0) {
                    CouponsCellV(canToUse: true, coupon: coupon)
                    .padding(.vertical, 16)
                    VStack(spacing:0){
                        Text("使用说明".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor8))
                                .frame(height:20).frame(maxWidth:.infinity, alignment: .leading)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)

                        Text("coupon_description".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor8))
                                .frame(maxWidth:.infinity, alignment: .leading).multilineTextAlignment(.leading).padding(.horizontal, 16)
                        .padding(.bottom, 25)
                    }
                    Spacer()
                }.background(Color(dynamicBackgroundColor4))
            }
    }
}

#if DEBUG
struct CouponDetailsV_Previews: PreviewProvider {
    static var previews: some View {
        Group{
            CouponDetailsV(coupon: test_coupons.first!)
                .previewDevice("iPhone 13 Pro Max")
            
            CouponDetailsV(coupon: test_coupons.first!)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
        }
        
    }
}
#endif
