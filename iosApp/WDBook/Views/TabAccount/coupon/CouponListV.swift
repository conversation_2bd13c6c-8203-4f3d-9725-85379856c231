//
//  CouponListV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/6/10.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import NavigationRouter
import shared

class CouponListMV: ObservableObject {
    @Published var status:CouponStatus = .unused
    @Published var items: [Coupon] = []

    init(status:CouponStatus,_ items:[Coupon]? = nil) {
        self.status = status
        if let list = items{
            self.items = list
        }
    }
    
//    func refresh(success:()->(),failure:()->()){
//        WDBookUserSDK.shared.getCouponList(status: status, minId: 0) { [weak self] result in
//            switch result{
//            case .success(let arr):
//                if let entities = arr,entities.count > 0{
//                    self?.items.removeAll()
//                    self?.items.append(contentsOf: entities.map{Coupon(entity: $0)})
//                }
//            case .failure(let err):
//                break
//            }
//        }
//    }
}

struct CouponListV: View {
    @EnvironmentObject var appState:AppState
    @StateObject var mv:CouponListMV
    @State var isFirstLoad = true
    @State var needRefresh = false
    @State var status:CouponStatus
    @State var canToUse = true
    
    @State var minid:Int64 = 0
    @State var noMore = false
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    
    init(status:CouponStatus,mv:CouponListMV? = nil) {
        _status = State(initialValue: status)
        if mv != nil{
            _mv = StateObject(wrappedValue: mv!)
        }else{
            _mv = StateObject(wrappedValue: CouponListMV(status: status))
        }
    }
    
    func refresh(){
        WDBookUserSDK.shared.getCouponList(status: status, minId: 0) { result in
            switch result{
            case .success(let arr):
                if let entities = arr,entities.count > 0{
                    mv.items.removeAll()
                    mv.items.append(contentsOf: entities.map{Coupon(entity: $0)})
                    minid = Int64(entities.last?.couponId ?? 0)
                    noMore = false
                }else{
                    mv.items.removeAll()
                    noMore = true
                    minid = 0
                }
            case .failure(let err):
                break
            }
            headerRefreshing = false
            isFirstLoad = false
        }
    }
    
    func loadMore() {
        if noMore {
            footerRefreshing = false
            return
        }
        
        WDBookUserSDK.shared.getCouponList(status: status, minId: minid) { result in
            switch result{
            case .success(let arr):
                if let entities = arr,entities.count > 0{
                    for e in entities{
                        if !mv.items.contains(where: {$0.couponId == e.couponId}){
                            mv.items.append(Coupon(entity: e))
                        }
                    }
                    minid = Int64(mv.items.last?.couponId ?? 0)
                    noMore = false
                }else{
                    noMore = true
                }
            case .failure(let err):
                break
            }
            footerRefreshing = false
        }
    }
    
    
    var body: some View {
            ScrollView{
                WDRefreshHeader(refreshing: $headerRefreshing, action: {
                    refresh()
                }).padding(.bottom, 15)
                
                VStack(spacing:12) {
                    ForEach(mv.items, id:\.couponId) { item in
                        CouponsCellV(canToUse:canToUse, coupon: item)
                            .onTapGesture {
                                RoutableManager.showCouponDetailsV(coupon: item)
                            }
                        
                    }
                }.frame(maxWidth:.infinity,maxHeight: .infinity)
                
                if mv.items.count > 0{
                    if !noMore {
                        WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                            self.loadMore()
                        }
                    } else {
                        Spacer().frame(height:12)
                    }
                }
            }
            .enableRefresh()
            .frame(maxWidth: .infinity,maxHeight:.infinity)
            .overlay(Group {
                if self.isFirstLoad{
                    ActivityIndicator()
                }else if self.mv.items.count == 0 {
                    VStack(spacing:15) {
                        Text(status == .unused ? "暂无可使用优惠券".localized : status == .used ? "暂无已使用优惠券".localized : "暂无已过期优惠券".localized).font(Font.regular(size: 18)).foregroundColor(Color(dynamicTitleColor7))
//                        Text("通知的消息会显示在这里".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor4))
                    }.padding(.bottom, UIScreen.main.bounds.height * 0.25)
                } else {
                    EmptyView()
                }
            }).onAppear {
                if isFirstLoad || needRefresh{
                    refresh()
                }
                needRefresh = false
            }
            .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
                needRefresh = true
            })
    }
}

#if DEBUG
struct CouponListV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            CouponListV(status: .unused, mv:CouponListMV(status: .unused, test_coupons))
            
            CouponListV(status: .unReceived, mv:CouponListMV(status: .unused, test_coupons))
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
        }
    }
}
#endif
