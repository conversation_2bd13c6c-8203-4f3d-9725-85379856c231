//
//  CouponCenterV.swift
//  WDBook
//
//  Created by ka<PERSON> <PERSON><PERSON> on 2022/6/10.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

class CouponCenterMV: ObservableObject {
    @Published var items: [[Coupon]] = [[]]
    //    @Published var type:NotificationMsgType
    
    init(_ items:[[Coupon]]? = nil) {
        if let list = items{
            self.items = list
        }
    }
}

struct CouponCenterV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @State var currentPage = 0
    var statusArr:[CouponStatus] = [.unused,.used,.expired]
    var btnTitles = ["可使用".localized,"已使用".localized,"已过期".localized]
    
    var body: some View {
        BackNavigation(title:"优惠券".localized) {
            //必须用ZStack且title bar在PagerViewWithSwiftUI上层。
            ZStack(alignment:.top) {
                //                        //UIKit版本
                //                        TabViewUIKit(tabTitles: btnTitles, selectedIndex: $currentPage)
                ////                            .frame(height: 48).frame(maxWidth:.infinity)
                
                PagerViewWithSwiftUI(pageCount: statusArr.count, currentIndex: $currentPage) {
                    ForEach(statusArr,id:\.self) { (status) in
                        CouponListV(status: status)
                            .frame( maxWidth: .infinity,maxHeight: .infinity, alignment: .center)
                            .padding(.vertical,12)
                            .background(Color(dynamicBackgroundColor16))
                    }
                    
                }
                .frame(maxWidth: .infinity,maxHeight:.infinity)
                .background(Color(dynamicBackgroundColor16))
                .padding(.top,48)
                
                HStack(spacing:0) {
                    ForEach(0 ..< btnTitles.count) { (i) in
                        Button {
                            currentPage = i
                        } label: {
                            Text(btnTitles[i])
                                .foregroundColor(currentPage == i ? mainColor : Color(dynamicTextColor19))
                                .font(Font.regular())
                                .padding(.bottom,3)
                                .background(GeometryReader { geometry in
                                    if currentPage == i{
                                        mainColor.frame(width:geometry.size.width * 0.618,height:2).cornerRadius(1)
                                            .frame(maxWidth:.infinity,maxHeight: .infinity,alignment: .bottom)
                                    }else{
                                        Color.clear
                                    }
                                })
                                .frame(maxWidth:.infinity)
                                .frame(height: 48)
                                .contentShape(Rectangle())
                        }
                    }
                }.font(Font.regular())
                    .frame(height: 48)
                    .frame(maxWidth:.infinity)
                    .background(Color(dynamicTextColor27))
                
            }.frame(maxWidth:.infinity,maxHeight: .infinity)
            .clipped()
        }
    }
}

import UIKit
import SnapKit
struct TabViewUIKit: UIViewRepresentable {
    @State var tabTitles: [String]
    @Binding var selectedIndex:Int
    
    func makeCoordinator() -> Coordinator {
        Coordinator(owner: self)
    }
    
    class Coordinator: NSObject {
        
        private var owner:TabViewUIKit?
        var btns:[UIButton] = []
        var lines:[UIView] = []
        
        init(owner:TabViewUIKit?) {
            self.owner = owner
        }
        
        @objc func tapBtn(btn:UIButton){
            //            for i in btns.indices{
            //                btns[i].isSelected = false
            //            }
            //            btns[btn.tag].isSelected = true
            owner?.selectedIndex = btn.tag
        }
        
        func updateState(index:Int){
            for i in btns.indices{
                btns[i].isSelected = false
                //                lines[i].isHidden = true
            }
            btns[index].isSelected = true
            //            lines[index].isHidden = false
        }
    }
    
    func makeUIView(context: Context) -> UIView {
        let contentV = UIView()
        var btns:[UIButton] = []
        var lines:[UIView] = []
        for i in 0 ..< tabTitles.count{
            let btn = UIButton()
            btn.tag = i
            btn.titleLabel?.font = UIFont.regular()
            btn.setTitle(tabTitles[i], for: .normal)
            btn.setTitleColor(mainUIColor, for: .selected)
            btn.setTitleColor(dynamicTextColor19, for: .normal)
            btn.addTarget(context.coordinator, action: #selector(Coordinator.tapBtn), for: .touchUpInside)
            contentV.addSubview(btn)
            btn.snp.makeConstraints { make in
                make.leading.equalTo(CGFloat(i) * UIScreen.main.bounds.width / CGFloat(tabTitles.count))
                make.top.equalTo(0)
                make.bottom.equalTo(0)
                make.width.equalTo(UIScreen.main.bounds.width / CGFloat(tabTitles.count))
            }
            
            //            let line = UIView()
            //            line.tag = i
            //            line.backgroundColor = mainUIColor
            ////            contentV.addSubview(line)
            //            contentV.insertSubview(line, at: 0)
            //            lines.append(line)
            //            line.snp.makeConstraints { make in
            //                make.bottom.equalTo(-10)
            //                make.height.equalTo(2)
            //                make.width.equalTo(24)
            //                make.centerX.equalTo(btn.snp.centerX)
            //            }
            btns.append(btn)
        }
        context.coordinator.btns = btns
        context.coordinator.lines = lines
        contentV.snp.makeConstraints { make in
            make.width.equalTo(UIScreen.main.bounds.width)
            make.height.equalTo(48)
        }
        contentV.backgroundColor = dynamicTextColor27
        context.coordinator.updateState(index: 0)
        return contentV
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        context.coordinator.updateState(index:selectedIndex)
    }
    
}

#if DEBUG
struct CouponCenterV_Previews: PreviewProvider {
    static var test_coupons_center:[[Coupon]]{
        [first,second,third]
    }
    static var first:[Coupon]{
        test_coupons.filter{$0.status == .unused}
    }
    static var second:[Coupon]{
        test_coupons.filter{$0.status == .used}
    }
    static var third:[Coupon]{
        test_coupons.filter{$0.status == .expired}
    }
    
    static var previews: some View {
        Group {
            CouponCenterV().environmentObject(AppState.shared)
            
            CouponCenterV().environmentObject(AppState.shared)
                .environment(\.colorScheme, .dark)
                .previewDevice("iPhone SE (3nd generation)")
            
        }
    }
}
#endif
