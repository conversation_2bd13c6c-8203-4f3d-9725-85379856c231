//
//  OrderDeductedProductsV.swift
//  WDBook
//
//  Created by <PERSON> on 2020/9/25.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import SwiftUI
import shared

struct OrderDeductedProductsV: View {
    var products:[OrderEntity.OrderDeductedProduct]
    
    var body: some View {
        BackNavigation(title: "已购商品详情".localized) {
            ScrollView {
                VStack(spacing:24) {
                    ForEach(self.products, id:\.productId) { product in
                        HStack(content: {
                            Text(product.title)
                            Spacer()
                            Text("- $\(product.price.fractionDigits2)")
                        }).foregroundColor(Color(dynamicTitleColor2))
                        .font(Font.regular())
                    }
                }.frame(maxWidth: .infinity,maxHeight:.infinity)
                .padding(EdgeInsets(top: 24, leading: 32, bottom: 24, trailing: 32))
                
            }.frame(maxWidth: .infinity,maxHeight:.infinity)
            .background(Color(dynamicBackgroundColor1))
            .navigationBarTitle("已购商品详情".localized, displayMode: .inline)
        }
    }
}

//#if DEBUG
//struct OrderDeductedProductsV_Previews: PreviewProvider {
//    static var previews: some View {
//        Group {
//            NavigationView{
//                OrderDeductedProductsV(products: test_OrderList[0].deductedProducts).environmentObject(AppState.shared)
//            }
//            NavigationView{
//                OrderDeductedProductsV(products: test_OrderList[0].deductedProducts).environmentObject(AppState.shared)
//                    .previewDevice("iPhone SE (2nd generation)")
//                    .environment(\.colorScheme, .dark)
//            }
//        }
//    }
//}
//#endif
