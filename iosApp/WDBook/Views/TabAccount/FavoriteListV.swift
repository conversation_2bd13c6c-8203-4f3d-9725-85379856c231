//
//  FavoriteListV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/2/21.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI

import shared

extension FavoriteListV{
    static let addFavoriteNotification = Notification.Name(rawValue: "FavoriteListV.addFavoriteNotification")
    static let removeFavoriteNotification = Notification.Name(rawValue: "FavoriteListV.removeFavoriteNotification")
}

struct FavoriteListV: View {
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @StateObject var dataObservable = ProductEntityListObservable()
    
    @State var noMore = false
    @State var headerRefreshing: Bool = false
    @State var footerRefreshing: Bool = false
    
    @State var needRefresh = true
    @State var isFirstLoad = true
    @State var isEdit = false
    @State var isAllSelected:Bool = false
    @State var selectedCount:Int = 0
    @State var jumpProductId:Int64 = 0
    @State var disableSwap = false
    
    func resetSelectedCount(){
        selectedCount = dataObservable.products.reduce(0){$0 + ($1.isSelected ? 1 : 0)}
    }
    
    func resetAllSelectedState(){
        dataObservable.products.forEach{product in
            product.isSelected = false
        }
    }
    
    func refresh(complete:(()->())? = nil) {
        if !NetReachability.isReachability(){
            Toaster.showToast(message: "无网络连接，请稍后再试".localized)
            HUDManager.hideLoadingHUD()
            complete?()
        }

        WDBookUserSDK.shared.getFavoriteBookList(lastUpdateTime:0) { result in
            switch result{
            case .success(let list):
                dataObservable.products.removeAll()
                if let productList = list,productList.count > 0{
                    dataObservable.products.append(contentsOf: productList.map{ProductEntityWarpper($0)})
                    noMore = false
                }else{
                    noMore = true
                }
            case .failure(let error):
                break
            }
            
            headerRefreshing = false
            isFirstLoad = false
            
            HUDManager.hideLoadingHUD()
            if dataObservable.products.count == 0{
                isAllSelected = false
                resetSelectedCount()
                isEdit = false
            }
            complete?()
        }
    }
    
    func loadMore() {
        if noMore {
            footerRefreshing = false
            return
        }
        let minLastUpdateTime = Int64(dataObservable.products.min{Int64($0.entity.lastUpdateTime ?? 0) < Int64($1.entity.lastUpdateTime ?? 0)}?.entity.lastUpdateTime ?? 0)
        WDBookUserSDK.shared.getFavoriteBookList(lastUpdateTime:minLastUpdateTime) { result in
            switch result{
            case .success(let list):
                if let productList = list,productList.count > 0{
                    dataObservable.products.append(contentsOf: productList.map{ProductEntityWarpper($0)})
                    noMore = false
                }else{
                    noMore = true
                }
            case .failure(let err):
                break
            }
            footerRefreshing = false
        }
    }
    
    var body : some View{
        ZStack(alignment: .top, content: {
            mainContent
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 20, content: {
                if isEdit{
                    Button(action: {
                        isAllSelected = !isAllSelected
                        dataObservable.products.forEach{product in
                            product.isSelected = isAllSelected
                        }
                        resetSelectedCount()
                    }) {
                        Text(isAllSelected ? "取消全选".localized : "全选".localized)
                            .foregroundColor(Color(btnTintColor))
                            .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                    }
                }else{
                    Button(action: {
                        presentationMode.wrappedValue.dismiss()
                    }) {
                        Image("back_ui")
                            .aspectRatio(contentMode: .fit)
                            .frame(width: 24, height: 24, alignment: .center)
                            .foregroundColor(Color(btnTintColor))
                            .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                    }
                }
                
                Spacer()
                
                if dataObservable.products.count > 0{
                    Button(action: {
                        withAnimation {
                            isEdit.toggle()
                            if isEdit{
                                isAllSelected = false
                                resetAllSelectedState()
                                resetSelectedCount()
                                NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                            }else{
                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.35) {
                                    isAllSelected = false
                                    resetAllSelectedState()
                                    resetSelectedCount()
                                }
                            }
                        }
                    }) {
                        Text(isEdit ? "取消".localized : "编辑".localized)
                            .foregroundColor(Color(btnTintColor))
                            .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                    }
                }
                
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
                .overlay(Text("我的收藏".localized)
                            .foregroundColor(Color(dynamicTitleColor2))
                            .font(Font.semibold(size: 18)).frame(height:45), alignment: .bottom)
            
        }).navigationBarHidden(true)
            .ignoresSafeArea()
    }
    
    var mainContent : some View{
        VStack {
            GeometryReader { proxy in
                ScrollView{
                    WDRefreshHeader(refreshing: $headerRefreshing, action: {
                        refresh()
                    })
                    GeometryReader { topProxy in
                        let offset = topProxy.frame(in: .named("scroll")).minY
                        Color(UIColor.black.alpha(0.01)).frame(height:0.01).preference(key: ViewOffsetKey.self, value: offset)//.id(topID)
                    }
                    LazyVStack(spacing:0) {
                        Spacer().frame(height:24)
                        ForEach(dataObservable.products, id:\.id) {product in
                            ProductListCollectCell(width:proxy.size.width,isEdit: $isEdit,warpper:product)
                                .contentShape(Rectangle())
                                .onSwipe(disable:$isEdit, trailing: [
                                    Slot(
                                        title: {
                                            Text("取消收藏".localized).embedInAnyView()
                                        },
                                        action: {
                                            HUDManager.showLoadingBlockHUD(text: "")
                                            WDBookUserSDK.shared.removeFavoriteBook(productId: product.id) { result in
                                                switch result{
                                                case .success(let r):
                                                    if let index = dataObservable.products.firstIndex{$0.id == product.id}{
                                                        dataObservable.products.remove(at: index)
                                                    }
                                                    AppState.shared.refreshFavoriteListCount()
                                                case .failure(let error):
                                                    break
                                                }
                                                HUDManager.hideLoadingHUD()
                                            }
                                        },
                                        style: .init(background: Color(UIColor(hex: 0xFF342A))),
                                        alertTyle: .NONE
                                    )
                                ])
                                .onTapGesture {
                                    if isEdit{
                                        product.isSelected.toggle()
                                        for product in dataObservable.products{
                                            if !product.isSelected{
                                                isAllSelected = false
                                                resetSelectedCount()
                                                return
                                            }
                                        }
                                        isAllSelected = true
                                        resetSelectedCount()
                                    }else{
                                        NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                                        jumpProductId = product.entity.productId as! Int64
                                        RoutableManager.navigate(toPath: RouterName.productDetail.withParam(jumpProductId))
                                    }
                                }
                        }
                    }
                    if dataObservable.products.count > 0{
                        if !noMore {
                            WDRefreshFooter(refreshing: $footerRefreshing, noMore:$noMore){
                                self.loadMore()
                            }
                        } else {
                            Spacer().frame(height:12)
                        }
                    }
                }
                .enableRefresh()
                .frame(maxWidth: .infinity,maxHeight:.infinity)
                .overlay(Group {
                    if self.isFirstLoad{
                        ActivityIndicator()
                    }else if dataObservable.products.count == 0 {
                        Text("暂无收藏记录".localized).font(Font.regular()).foregroundColor(Color(dynamicTitleColor2))
                    } else {
                        EmptyView()
                    }
                })
                .onPreferenceChange(ViewOffsetKey.self) { value in
                    NotificationCenter.default.post(name: SlidableModifier.hideSlotControlNotification, object: nil)
                }

                .onAppear {
                    if needRefresh{
                        self.refresh()
                    }
                    needRefresh = false
                    
                    AppState.shared.refreshFavoriteListCount()
                }
                .onReceive(NotificationCenter.default.publisher(for: Noti_Payment_Complete), perform: { (obj) in
//                    self.needRefresh = true
                    self.refresh()
                }).onReceive(NotificationCenter.default.publisher(for: FavoriteListV.addFavoriteNotification, object: nil)) { obj in
//                    self.needRefresh = true
                    self.refresh()
                }.onReceive(NotificationCenter.default.publisher(for: FavoriteListV.removeFavoriteNotification, object: nil)) { obj in
//                    self.needRefresh = true
                    self.refresh()
                }
            }
            
            if isEdit{
                Button {
                    let toDeleteProductIds = dataObservable.products.filter{$0.isSelected == true}
                    if toDeleteProductIds.count > 0{
//                        toDeleteProductIds.forEach { product in
//                            if let index = dataObservable.products.firstIndex{$0.entity.productId as! Int64 == product.id}{
//                                dataObservable.products.remove(at: index)
//                            }
//                            WDBookStoreSDK.shared.removeFavoriteBook(productId: product.entity.productId as! Int64) { result in
//
//                            }
//                        }
//                        isEdit = false
//                        resetAllSelectedState()
//                        DispatchQueue.main.asyncAfter(deadline: .now() + 1) {
//                            refresh()
//                        }
                        HUDManager.showLoadingBlockHUD(text: "")
                        let group = DispatchGroup()
                        toDeleteProductIds.forEach { product in
                            group.enter()
                            DispatchQueue.main.async(group: group, execute: DispatchWorkItem(block: {
                                WDBookUserSDK.shared.removeFavoriteBook(productId: product.entity.productId as! Int64) { result in
                                    group.leave()
                                }
                            }))
                        }
                        
                        group.notify(queue: DispatchQueue.main) {
                            refresh {
                                resetAllSelectedState()
                                resetSelectedCount()
                                isEdit = false
                            }
                            AppState.shared.refreshFavoriteListCount()
                        }
                    }
                    
                } label: {
                    if safeAreaInsets.bottom > 0{
                        Text("取消收藏".localized).font(Font.regular()).frame(maxWidth:.infinity).frame(height:44,alignment: .bottom).foregroundColor(Color(UIColor(hex:selectedCount > 0 ? 0xFF8A00 : 0xFFE2BF)))
                            .padding(.bottom, safeAreaInsets.bottom)
                    }else{
                        Text("取消收藏".localized).font(Font.regular()).frame(maxWidth:.infinity).frame(height:44).foregroundColor(Color(UIColor(hex:selectedCount > 0 ? 0xFF8A00 : 0xFFE2BF)))
                            .padding(.bottom, safeAreaInsets.bottom)
                    }
                    
                }.overlay(Color(dynamicLineOnDayColor).frame(height:0.5),alignment: .top).disabled(selectedCount == 0)
            }
        }
        .background(Color(dynamicBackgroundColor12))
    }
}

struct ProductListCollectCell: View {
    var width:CGFloat
    @Binding var isEdit:Bool
    @ObservedObject var warpper: ProductEntityWarpper
    var product:FavoriteBookEntity{
        return warpper.entity
    }
    
    var body: some View {
        HStack(spacing: 0) {
            VStack(spacing: 0) {
                Spacer().frame(height: 12)
                HStack(spacing: 0) {
                    ProductCoverView(purchased: Int(product.isPurchased ?? 0), cover: product.cover ?? "")
                    Spacer().frame(width:16)
                    VStack(alignment:.leading, spacing: 8) {
                        Text(product.title ?? "")
                            .lineLimit(2).fixedSize(horizontal: false, vertical: true)
                            .font(Font.regular(size: 16))
                            .foregroundColor(Color(dynamicTitleColor2))
                            .frame(maxWidth: .infinity,alignment: .leading)
                            .multilineTextAlignment(.leading)
                        Text(product.author ?? "")
                            .lineLimit(1).fixedSize(horizontal: false, vertical: true)
                            .font(Font.regular(size: 12))
                            .foregroundColor(Color(dynamicTitleColor2))
                            .frame(maxWidth: .infinity,alignment: .leading)
                        
                        VStack(alignment:.leading, spacing: 0) {
                            
                            
                            if product.hasActivity(){
                                HStack {
                                    Text("活动价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(product.currencyText)\(product.activityAmount.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                    + Text(" (约￥%@)".localizedFormat(product.activityPriceCNY.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                    + Text(product.activityDiscount >= 1 || product.activityDiscount == 0 ? "": " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.activityDiscount * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                                }.lineLimit(1)
                                    .font(Font.regular(size: 12))
                                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                            }else{
                                HStack {
                                    Text("现价：".localized).foregroundColor(Color(dynamicTitleColor2))
                                    + Text("\(product.currencyText)\(product.price.fractionDigits2)").foregroundColor(Color(UIColor.red))
                                    + Text(" (约￥%@)".localizedFormat(product.priceCNY2.fractionDigits2)).foregroundColor(Color(UIColor.red))
                                    + Text(product.discount2 >= 1 || product.discount == 0 ? "" : " (%@折)".localizedFormat(Formatter.fractionDigits2.string(from: NSNumber.init(value: product.discount2 * 10))!)).foregroundColor(Color(dynamicTitleColor2))
                                }.lineLimit(1)
                                    .font(Font.regular(size: 12))
                                    .frame(maxWidth: .infinity,maxHeight: .infinity,alignment: .leading)
                            }
                            
                            Spacer().frame(height:2)
                            Group {
                                Text("原价：".localized) + Text(product.currencyText + String(product.originalPrice0)).strikethrough()
                            }.lineLimit(1)
                        }
                        .font(Font.regular(size: 12))
                        .foregroundColor(Color(dynamicTitleColor2))
                        .frame(maxWidth: .infinity,alignment: .leading).opacity(isTestFlight1() ? 0 : 1)
                    }
                }
                Spacer().frame(height: 12)
            }
            .padding(.top,0)
            .frame(height: 110 + 24)
            .frame(width:isEdit ? width - 66 : width - 24*2)
            .padding(.leading,isEdit ? 66 : 24)
            .padding(.trailing,isEdit ? 0 : 24)
        }.frame(width:width)
            .background(Image(warpper.isSelected ? "checkbox2_check" : "checkbox2_uncheck").frame(maxHeight:.infinity).padding(.leading, 18).padding(.trailing, 6).offset(x: isEdit ? 0 : -42, y: 0), alignment: .leading)
    }
}

class ProductEntityListObservable:ObservableObject{
    @Published var products:[ProductEntityWarpper] = []
}

class ProductEntityWarpper:ObservableObject{
    @Published var entity:FavoriteBookEntity
    @Published var isSelected:Bool
    init(_ entity:FavoriteBookEntity) {
        self.entity = entity
        self.isSelected = false
    }
}

extension ProductEntityWarpper:Identifiable{
    public var id: Int64{
        return entity.productId as! Int64
    }
}

extension FavoriteBookEntity{

    var currencyText:String{
        if currency == "USD"{
            return "$"
        }
        return currency ?? "$"
    }

    var price:Double{
        return (currentPrice ?? 0) as! Double
    }

    var priceCNY2:Double{
        return (priceCNY ?? 0) as! Double
    }

    var discount2:Double{
        return (discount ?? 1) as! Double
    }

    var originalPrice0:Double{
        return (originalPrice ?? 0) as! Double
    }
    
    func hasActivity() -> Bool{
        activitiesList?.count > 0
    }
    
    func hasActivity(activityId:Int64) -> Bool{
        (activitiesList as? [ActivityEntity] ?? []).contains{$0.activityId == activityId}
    }
    
    //活动价
    var activityAmount:Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId as? Int64 ?? 0}.first?.amount ?? 0.0)
    }
    
    var activityDiscount: Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId as? Int64 ?? 0}.first?.discount ?? 0.0)
    }
    
    var activityPriceCNY: Double{
        Double((activitiesList as? [ActivityEntity] ?? []).filter{$0.productId == productId as? Int64 ?? 0}.first?.amountCNY ?? 0.0)
    }
}
