//
//  TabAccountV.swift
//  WDBook
//
//  Created by 杜文泽 on 2020/5/19.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import NavigationRouter
import SwiftUI

struct TabAccountV: View {
    @EnvironmentObject var appState: AppState
    @Environment(\.safeAreaInsets2) private var safeAreaInsets
    @State var isNavigationHide: Bool = false
    @State var isFirstLoad = true

    init() {
//        Log.d("初始化")
    }

    var content: some View {
        ScrollView(.vertical, showsIndicators: false) {
            VStack(alignment: .center, spacing: 8) {
                AccountInfoRow()

                VStack(alignment: .center, spacing: 0) {
                    RoutedLink(toRoute: .notesBooks) {
                        PersonalRow(imageName: "icon_book_note", titleStringKey: "笔记", descStringKey: $appState.noteCountStr)
                    }
                }

                VStack(alignment: .center, spacing: 0) {
                    RoutedLink(toRoute: .purchasedBooks) {
                        PersonalRow(imageName: "icon_book_purchased", titleStringKey: isTestFlight1() ? "我的书籍" : "已购书籍", descStringKey: self.$appState.accountTab.purchasedListCount)
                            .overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
                    }

                    RoutedLink(toRoute: .favorites) {
                        PersonalRow(imageName: "icon_collection", titleStringKey: "收藏", descStringKey: self.$appState.favoriteListCount)
                            .overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
                    }

                    if !isTestFlight1() {
                        RoutedLink(toRoute: .balance) {
                            PersonalRow(imageName: "icon_account", titleStringKey: "账户", descStringKey: self.$appState.walletBalanceStr)
                                .overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
                        }

                        RoutedLink(toRoute: .couponsCenter) {
                            PersonalRow(imageName: "icon_coupon", titleStringKey: "优惠券", descStringKey: $appState.couponCount)
                        }
                    }
                }

                VStack(alignment: .center, spacing: 0) {
                    RoutedLink(toRoute: .setting) {
                        PersonalRow(imageName: "icon_setting", titleStringKey: "应用设置", descStringKey: .constant("")).overlay(Color(dynamicSpanLineColor3).frame(height: 0.5).padding(.leading, 16), alignment: .bottom)
                    }

                    RoutedLink(toRoute: .help) {
                        PersonalRow(imageName: "icon_feedback", titleStringKey: "帮助与反馈", descStringKey: .constant(""), hasDot: self.$appState.hasNewFeedback)
                    }
                }

                Spacer()

                VStack {
                    Text(String("V\(Bundle.main.infoDictionary!["CFBundleShortVersionString"]!)")).font(Font.regular())
                    Spacer().frame(height: 8)
                    Text("充值或购买出现问题请联系我们".localized).font(Font.regular())
                    Text("<EMAIL>").font(Font.regular())
                    Spacer().frame(height: 8)
                }.foregroundColor(Color(dynamicTitleColor2))

            }.frame(maxWidth: .infinity)

        }.frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(dynamicBackgroundColor3))
            .environment(\.locale, appState.locale)
            .onAppear {
                appState.refreshNoteCountStr()
                appState.refreshPurchasedList()
                appState.refreshFavoriteListCount()
                appState.refreshFeedbackStatus()
                appState.refreshNotificationStatus()
                appState.refreshCouponCount()
            }
            .onReceive(NotificationCenter.default.publisher(for: NoteEditerVC.noteChangedNotification), perform: { _ in
                appState.refreshNoteCountStr()
            })
            .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteDeletedNotification), perform: { _ in
                appState.refreshNoteCountStr()
            })
            .onReceive(NotificationCenter.default.publisher(for: NoteBookmarkNotifications.noteResumeNotification), perform: { _ in
                appState.refreshNoteCountStr()
            })
//            .onReceive(NotificationCenter.default.publisher(for: AuthLoginV.loginSuccessNotification), perform: { (obj) in
//                appState.refreshNoteCountStr()
//            })
    }

    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, 45 + safeAreaInsets.top)

            // Use ConfigurableNavBar
            ConfigurableNavBar(title: "我的".localized) {
                // Right content: Notification Bell
                RoutedLink(toRoute: .messages) {
                    Image("icon_notification_bell")
                        .frame(width: 24, height: 24, alignment: .center)
//                        .redDot(num: $appState.notificationStatusAllCount)
                        .hasRedDot(hasDot: $appState.hasNewNotification)
//                        .padding(EdgeInsets(top: 10, leading: 16, bottom: 10, trailing: 16))
                        .contentShape(Rectangle())
                }
            }

        }).frame(maxWidth: .infinity, maxHeight: .infinity)
            .navigationBarHidden(true)
            .edgesIgnoringSafeArea(.top)
    }
}

#if DEBUG
    struct HomePersonalView_Previews: PreviewProvider {
        static var state: AppState {
            let s = AppState.shared
            s.notificationStatusAllCount = 3
            return s
        }

        static var previews: some View {
            Group {
                NavigationView {
                    TabAccountV().environmentObject(state)
                        .environment(\.locale, Locale(identifier: "zh-Hant"))
                }
                NavigationView {
                    TabAccountV().environmentObject(state).environment(\.colorScheme, .dark)
                        .environment(\.locale, Locale(identifier: "zh-Hans"))
                }
            }
        }
    }
#endif
