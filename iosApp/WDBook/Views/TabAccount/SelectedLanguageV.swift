//
//  SelectedLanguageV.swift
//  WDBook
//
//  Created by <PERSON><PERSON> <PERSON><PERSON> on 2022/6/15.
//  Copyright © 2022 WeDevote Bible. All rights reserved.
//

import SwiftUI
import SwiftyUserDefaults

struct SelectedLanguageV: View {
    @EnvironmentObject var appState:AppState
    @Environment(\.safeAreaInsets) private var safeAreaInsets
    @Environment(\.presentationMode) var presentationMode: Binding<PresentationMode>
    
    func refresh(){
        LocaleManager.checkCurrentLanguage()
    }
    
    func btnBack(left:Bool = true) -> some View { Button(action: {
        self.presentationMode.wrappedValue.dismiss()
    }) {
        HStack {
            !left ? AnyView(Spacer()) : AnyView(EmptyView())
            Image("back_ui")
                .aspectRatio(contentMode: .fit)
                .foregroundColor(Color(btnTintColor))
            left ? AnyView(Spacer()) : AnyView(EmptyView())
        }.frame(width:40,height: 45)
    }
    }
    
    var content:some View{
        VStack(alignment: .center, spacing: 0) {
            HStack {
                Text("simplified_chinese")
                    .frame(maxWidth:.infinity,alignment: .leading)
                    .padding(.leading,16)
                if appState.selectedLanguage == "zh-Hans"{
                    Image("right_blue")
                        .frame(width: 24, height: 24)
                        .padding(.horizontal,12)
                }
                
            }.frame(height:65)
                .background(Color(dynamicBackgroundColor1))
                .overlay(Color(dynamicSpanLineColor3).frame(height:0.5).padding(.leading, 16), alignment: .bottom)
                .contentShape(Rectangle())
                .onTapGesture {
                    Defaults[key:DefaultsKeys.SELECTED_LANGUAGE] = "zh-Hans"
                    refresh()
                }
            
            HStack {
                Text("traditional_chinese")
                    .frame(maxWidth:.infinity,alignment: .leading)
                    .padding(.leading,16)
                if appState.selectedLanguage == "zh-Hant"{
                    Image("right_blue")
                        .frame(width: 24, height: 24)
                        .padding(.horizontal,12)
                }
            }.frame(height:65)
                .background(Color(dynamicBackgroundColor1))
                .contentShape(Rectangle())
                .onTapGesture {
                    Defaults[key:DefaultsKeys.SELECTED_LANGUAGE] = "zh-Hant"
                    refresh()
                }
            
            Spacer()
        }.background(Color(dynamicBackgroundColor3))
            .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification), perform: { noti in
                refresh()
            })
    }
    
    var body: some View {
        ZStack(alignment: .top, content: {
            content
                .navigationBarHidden(true)
                .padding(.top, (45 + safeAreaInsets.top))
            
            HStack(alignment: .center, spacing: 0, content: {
                btnBack().padding(EdgeInsets(top: 0, leading: 0, bottom: 0, trailing: 20))
                Spacer()
            }).frame(height:45)
                .frame(maxWidth:.infinity)
                .padding(.horizontal, 20)
                .overlay(Text("语言").frame(maxHeight:.infinity)
                    .foregroundColor(Color(dynamicTitleColor2))
                    .font(Font.semibold(size: 18)),alignment:.center)
                .padding(.top, safeAreaInsets.top)
                .background(Color(dynamicBackgroundColor1))
                .modifier(BottomLineViewModifier(isShowBottomLine: true))
                
        })
        .navigationBarHidden(true)
        .edgesIgnoringSafeArea(.top)
        .environment(\.locale, appState.locale)
    }
}

struct SelectedLanguageV_Previews: PreviewProvider {
    static var previews: some View {
        Group {
            SelectedLanguageV().environmentObject(AppState.shared)
                .previewDevice("iPhone 13 Pro Max")
                .environment(\.locale, Locale(identifier: "zh-Hant"))
            
            SelectedLanguageV().environmentObject(AppState.shared)
                .previewDevice("iPhone SE (3nd generation)")
                .environment(\.colorScheme, .dark)
                .environment(\.locale, Locale(identifier: "zh-Hans"))
        }
    }
}
