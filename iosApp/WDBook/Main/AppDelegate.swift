//
//  AppDelegate.swift
//  WDBook
//
//  Created by <PERSON> on 5/18/20.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import UIKit
import SwiftyStoreKit
import DeviceKit
import NavigationRouter
import AudioToolbox
import RealReachability
import Sentry
import Clarity

@UIApplicationMain
class AppDelegate: UIResponder, UIApplicationDelegate {
    
    
    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
//        HTTPDNSCURLManager.initConfig() //TODO: 拦截测试
        LogUtils.shared.startLogging()

        RealReachability.sharedInstance().startNotifier()
#if !CAN_PREVIEW
        NetworkUtils.shared.updateCFTNetworkConnectionAndRefreshUI()
#endif

        RoutableManager.shared.config()
        LocaleManager.checkCurrentLanguage()
        ImageManager.config()
        
        InAppPurchaseManager.shared.setupIAP() // 苹果内购订单检查

        //MARK: 推送
        UNUserNotificationCenter.current().delegate = self
        
        AnalysisUtils.initialize()
        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_APP_OPEN, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_TIMESTAMP, String(Int64(Date().timeIntervalSince1970))], isFlush: true)

        DispatchQueue.main.asyncAfter(deadline: .now() + 3, execute: {
            AppDownloadManager.shared.resetConfig()
        })

        // 使用 SentryUtils 进行安全的 Sentry 初始化
        SentryUtils.safeSentryInitialization(dsn: "https://<EMAIL>/9")
        
        ClaritySDK.initialize(config: ClarityConfig(projectId: "l4fvgvms2e"))
        
        return true
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }

    func applicationWillTerminate(_ application: UIApplication) {
        if AppState.shared.isInDeviceMangerPage{
            let semaphore = DispatchSemaphore(value: 0)
            WDBookDataSyncManager.shared.signOut(msg: nil) {
                AppState.shared.isInDeviceMangerPage = false
                AppState.shared.changeTab(tabName: .store)
                semaphore.signal()
            }
            semaphore.wait(timeout: .distantFuture)
        }
    }
    
    //MARK: 推送
    func application(_ application: UIApplication, didRegisterForRemoteNotificationsWithDeviceToken deviceToken: Data) {
        debugPrint("Get Push token: \(deviceToken.hexString)")
        WDBookUserSDK.shared.postDeviceToken(deviceToken: deviceToken.hexString,granted: AppState.shared.isAcceptedNotification ? 1:0) { result in
            debugPrint(result)
        }
//        UIApplication.shared.applicationIconBadgeNumber = 1
    }
    
    func application(_ application: UIApplication, didFailToRegisterForRemoteNotificationsWithError error: Error) {
        debugPrint("didFailToRegisterForRemoteNotificationsWithError: \(error)")
        WDBookUserSDK.shared.postDeviceToken(deviceToken: "",granted: AppState.shared.isAcceptedNotification ? 1:0) { result in
            debugPrint(result)
        }
    }
    
    func postTestLocalNotification(){
//        {"aps":{"alert":"This is some fancy message.","badge":1"sound": "default"}}
        let json = """
{
    "aps" : {
       "alert" : {
          "title" : "标题",
          "subtitle" : "子标题",
          "body" : "body"
       },
       "badge" : 1,
       "sound":"default"
    },
    "url" : "https://wdbook.com/dp/42441924222977"
 }
"""
        
//    wdbook://mine/messages/48670154358785?type=2
        let data = json.data(using: .utf8)
        let userInfo = try! JSONSerialization.jsonObject(with: data!, options: []) as! [AnyHashable : Any]
        
        let content = UNMutableNotificationContent()
        content.title = "标题"
        content.subtitle = "子标题"
        content.body = "body"
        content.badge = 1
        content.sound = UNNotificationSound.default
        content.userInfo = userInfo

        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 5, repeats: false)
        let request = UNNotificationRequest(identifier: "Notification", content: content, trigger: trigger)
        UNUserNotificationCenter.current().add(request) { err in
            err != nil ? print("添加本地通知错误", err!.localizedDescription) : print("添加本地通知成功")
        }
    }
}


extension AppDelegate:UNUserNotificationCenterDelegate{
    
    //应用在前台收到通知
    func userNotificationCenter(_ center: UNUserNotificationCenter, willPresent notification: UNNotification, withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void) {

           // Retrieve the meeting details.
        let userInfo = notification.request.content.userInfo
        debugPrint("userNotificationCenter.willPresent:\(userInfo)")
        
        //本方法
        DispatchQueue.main.asyncAfter(deadline: .now() + 1){
            clearBadge()
        }
        
        if let aps = userInfo["aps"] as? [String: Any],
            let alert = aps["alert"] as? [String: Any],
            let title = alert["title"] as? String,
            let subtitle = alert["subtitle"] as? String,
            let urlStr = userInfo["url"] as? String, let url = URL(stringRobustness: urlStr.trim()){
            AudioServicesPlaySystemSound(1315)
            NotificationSystemNoticeSwiftUIV.show(title: title, desc: subtitle, on: UIWindow.keyWindow!) {
                URLManager.shared.parseWDBookUrl(url: url,duration: 1)
            }
        }
        
        completionHandler(.badge)
    }
    
    //点击通知进入应用
    func userNotificationCenter(_ center: UNUserNotificationCenter, didReceive response: UNNotificationResponse, withCompletionHandler completionHandler: @escaping () -> Void) {
        // Get the meeting ID from the original notification.
        let userInfo = response.notification.request.content.userInfo
        debugPrint("userNotificationCenter.didReceive:\(userInfo)")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 1){
            clearBadge()
        }

        if let urlStr = userInfo["url"] as? String, let url = URL(stringRobustness: urlStr.trim()){
            URLManager.shared.parseWDBookUrl(url: url,duration: 1)
        }
        completionHandler()
    }
}

//对Data类型进行扩展
extension Data {
    var hexString: String {
         let hexString = map { String(format: "%02.2hhx", $0) }.joined()
         return hexString
     }
}

func clearBadge() {
    UIApplication.shared.applicationIconBadgeNumber = 0 //清除数字，和未读通知。
}
