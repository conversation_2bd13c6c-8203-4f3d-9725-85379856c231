//
//  SceneDelegate.swift
//  WDBook
//
//  Created by <PERSON> on 5/18/20.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import UIKit
import SwiftUI
import SwiftyUserDefaults
import wdebug


class SceneDelegate: UIResponder, UIWindowSceneDelegate {
    @Environment(\.presentationMode) var presentation
    var window: UIWindow?
    var activeTime:Int64 = 0
    private var isAppInitialized = false // 一个标志来标识应用是否是全新启动进来

    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
        // If using a storyboard, the `window` property will automatically be initialized and attached to the scene.
        // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).

        // Create the SwiftUI view that provides the window contents.
        let appState = AppState.shared
        let contentView = ContentView().environmentObject(AppState.shared)
            .environment(\.locale, AppState.shared.locale)
        
        // Use a UIHostingController as window root view controller.
        if let windowScene = scene as? UIWindowScene {
            let window = UIWindow(windowScene: windowScene)
            let navigationController: UINavigationController = UINavigationController(rootViewController: UIHostingController(rootView: contentView))
            navigationController.navigationBar.isHidden = true
            window.rootViewController = navigationController
//            window.windowLevel = .statusBar + 100
            self.window = window
            window.makeKeyAndVisible()
        }
        AppUtils.resetAppUpgradeAlertInfo()
        guard let _ = (scene as? UIWindowScene) else { return }
        self.scene(scene, openURLContexts: connectionOptions.urlContexts) // 1
        
        self.isAppInitialized = true
        // WDebug.shared().isOn = true 可视化日志开关
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        debugPrint("状态:sceneDidDisconnect")
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not neccessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        debugPrint("状态:sceneWillEnterForeground")
        WDebug.log(withTag: "CFT", message: "sceneWillEnterForeground: app将要切入前台")
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
        AppUtils.showForceUpgradeAlertIfNeed()
        clearBadge()
        LocaleManager.checkCurrentLanguage()
        enterReadBrightness()
        checkDarkMode()
        AppState.shared.refreshDeviceToken()
        if !isAppInitialized {
            DispatchQueue.main.async {
                NetworkUtils.shared.stopProxyServer()
                NetworkUtils.shared.updateNetworkConnection { isConnect in
                    WDBookDataSyncManager.shared.syncDataFromServer()
                }
            }
        }
        self.isAppInitialized = false
    }
    
    func sceneDidBecomeActive(_ scene: UIScene) {
        debugPrint("状态:sceneDidBecomeActive")
        activeTime = Int64(Date().timeIntervalSince1970)
    }

    func sceneWillResignActive(_ scene: UIScene) {
        debugPrint("状态:sceneWillResignActive")
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_APP_USE_DURATION, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_DURATION, String(Int64(Date().timeIntervalSince1970) - activeTime)], isFlush: true)
        LocaleManager.checkCurrentLanguage()
        exitReadeBrigtness()
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        debugPrint("状态:sceneDidEnterBackground")
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
        var backgroundTask: UIBackgroundTaskIdentifier = UIBackgroundTaskIdentifier.invalid
        backgroundTask = UIApplication.shared.beginBackgroundTask(withName: "keep alive") {
            if AppState.shared.isInDeviceMangerPage{
                let semaphore = DispatchSemaphore(value: 0)
                WDBookDataSyncManager.shared.signOut(msg: nil) {
                    AppState.shared.isInDeviceMangerPage = false
                    AppState.shared.changeTab(tabName: .store)
                    semaphore.signal()
                }
                semaphore.wait(timeout: .distantFuture)
            }
            UIApplication.shared.endBackgroundTask(backgroundTask)
        }

    }

    
    func scene(_ scene: UIScene, openURLContexts URLContexts: Set<UIOpenURLContext>) {
        if let url = URLContexts.first?.url{
            URLManager.shared.parseWDBookUrl(url: url,duration: 1)
        }
    }
}

