//
//  AppState.swift
//  WDBook
//
//  Created by <PERSON> on 2020/8/12.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import DTCoreText
import Foundation
import NavigationRouter
import shared
import SwiftUI
import SwiftyUserDefaults

class AppState: ObservableObject {
    static var shared = AppState()
    var geometry: GeometryProxy?

    var tabSelect: TabSelect = .init()
    @Published var locale: Locale = .init(identifier: "zh-Hant")
    @Published var enableDownloadProgress = true // 显示下载与下载进度。默认不显示

    @Published var isShowLoginRegisterV: Bool = false {
        didSet {
            if isShowLoginRegisterV {
                LoginManager.shared.showLoginRegisterV()
            } else {
                LoginManager.shared.hideLoginRegisterV()
            }
        }
    }

    @Published var isShowLogoutV: Bool = false

    @Published var isShowTickoutV: Bool = false
    @Published var tickoutMsg: String = ""

    @Published var isShowRegisterV: Bool = false
    @Published var isShowLoginV: Bool = false
    @Published var returnTabAfterLogin: TabName?
    var loginCompleteHandler: (() -> Void)?

    @Published var isContentViewLoaded: Bool = false

    // 购买后待添加到书架的书籍
    @Published var preAddShelfProductDetails: ProductDetailEntity = .init()

    // 购买支付结果弹框
    @Published var isShowPaymentResultV: Bool = false
    @Published var paymentResultType: PaymentResultVType = .rechargeFailed

    @Published var categoryList: [StoreCategoryEntity] = .init()
    @Published var allCategories: [StoreCategoryEntity] = .init() // 包含全部类别的数据
    @Published var iapProductList: [InAppPurchaseProductEntity] = []

    // MARK: 设备管理

    @Published var isUnbind = true
    @Published var tokenString: String = ""
    @Published var isShowDeviceFullAlert = true
    @Published var isInDeviceMangerPage = false

    // MARK: 阅读器

    @Published var isShowReader = false
    @Published var readerFilePath: String = ""
    @Published var code: String = ""
    @Published var isShowingReader = false
    @Published var readerUserInfo: [AnyHashable: Any] = .init() // 储存阅读器携带信息

    // MARK: 子页面数据

    @Published var shelfTab = ShelfTab()
    @Published var storeTab = StoreTab()
    @Published var accountTab = AccountTab()
    @Published var alert = Alert()

    @Published var storeContainers: [WidgetContainerCombineEntity] = .init()
    @Published var cacheStoreContainers: [WidgetContainerCombineEntity] = .init() // 后台更新后的缓存
    @Published var isStoreVonDisappear = false // 和newStoreContainers一起使用。storev在最上层刷新；不在的话保存缓存，显示时候再刷新。
    @Published var suggestProductListDic: [Int64: SuggestProductList] = [:] // [containerId:SuggestProductList]
    @Published var discountProductsResultDic = SuggestProductList()

    @Published var isShelfFirstLoad = true
    @Published var shelfCombineItems: [DownloadResourceObservable] = [] // 书架中第一层的条目（单本和文件夹）
    @Published var shelfItems: [DownloadResourceObservable] = [] // 书架中所有层的条目合计（只单本）
    @Published var selectedShelfItems: [DownloadResourceObservable] = []
    @Published var archiveCount: Int32 = 0
    @Published var purchasedListResources: [DownloadResourceObservable] = []
    @Published var walletBalance: Double = 0.0 {
        didSet {
//            walletBalanceUS$Str = "US$ \(Formatter.fractionDigits2.string(from: NSNumber.init(value: walletBalance))!)"
//            walletBalance$Str = "$ \(Formatter.fractionDigits2.string(from: NSNumber.init(value: walletBalance))!)"
            walletBalanceStr = "\(Formatter.fractionDigits2.string(from: NSNumber(value: walletBalance))!)"
        }
    }

//    @Published var walletBalanceUS$Str:String = "US$ \(Formatter.fractionDigits2.string(from: NSNumber.init(value: 0.0))!)"
//    @Published var walletBalance$Str:String = "$ \(Formatter.fractionDigits2.string(from: NSNumber.init(value: 0.0))!)"
    @Published var walletBalanceStr: String = "\(Formatter.fractionDigits2.string(from: NSNumber(value: 0.0))!)"

    @Published var userInfo: UserInfo = .init() {
        didSet {
            AnalysisUtils.updateUserID()
        }
    }

    @Published var purchasedCount: Int = 0
    @Published var favoriteListCount: String = "" // "%lld本".localizedFormat(0)

    // 通知
    @Published var isAcceptedNotification: Bool = true
    @Published var allowNotification = false
    @Published var notificationStatusPlatformCount: Int = 0
    @Published var notificationStatusDiscountCount: Int = 0
    @Published var notificationStatusAllCount: Int = 0 {
        didSet {
            hasNewNotification = notificationStatusAllCount > 0
        }
    }

    @Published var hasNewNotification: Bool = false {
        didSet {
            hasNewFeedbackOrNotification = hasNewFeedback || hasNewNotification
        }
    }

    // 反馈
    @Published var hasNewFeedback: Bool = false {
        didSet {
            hasNewFeedbackOrNotification = hasNewFeedback || hasNewNotification
        }
    }

    // 通知+反馈
    @Published var hasNewFeedbackOrNotification: Bool = false

    var Orign_IsIdleTimerDisabled: Bool = false
    var openBookAction: (() -> Void)?

    var lastReadTime: Int64 = 0
    var duration: Int64 = 0
    var isSendComplete: Bool = false
    var isFirst: Bool = true

    // MARK: 设置

    @Published var showPaperBookPageNum: Bool = Defaults[key: DefaultsKeys.SHOW_PAPER_BOOK_PAGE_NUM] {
        didSet {
            Defaults[key: DefaultsKeys.SHOW_PAPER_BOOK_PAGE_NUM] = showPaperBookPageNum
            Log.d("显示纸书页码:\(showPaperBookPageNum)")
        }
    }

    let isHide4GDownloadControl = true // 隐藏4G下载的提示控制
    @Published var downloadOnlyOnWifi: Bool = Defaults[key: DefaultsKeys.DOWNLOAD_ONLY_ON_WIFI] {
        didSet {
            Defaults[key: DefaultsKeys.DOWNLOAD_ONLY_ON_WIFI] = downloadOnlyOnWifi
            NotificationCenter.default.post(name: Notification.Name.ALLOW_CELLULAR_DOWNLOAD_CHANGED, object: nil)
            Log.d("仅在wifi下载:\(downloadOnlyOnWifi)")
        }
    }

    @Published var notAutoLockScreenOnRead: Bool = Defaults[key: DefaultsKeys.NOT_AUTO_LOCK_SCREEN_ON_READ] {
        didSet {
            Defaults[key: DefaultsKeys.NOT_AUTO_LOCK_SCREEN_ON_READ] = notAutoLockScreenOnRead
            Log.d("阅读期间不自动锁屏:\(notAutoLockScreenOnRead)")
        }
    }

    @Published var acceptDiscout: Bool = Defaults[key: DefaultsKeys.ACCEPT_DISCOUNT] {
        didSet {
            Defaults[key: DefaultsKeys.ACCEPT_DISCOUNT] = acceptDiscout
            Log.d("接收优惠促销提醒:\(acceptDiscout)")
            resetNotificationStatus()
        }
    }

    @Published var selectedLanguage: String = Defaults[key: DefaultsKeys.SELECTED_LANGUAGE] ?? "" {
        didSet {
            if selectedLanguage == "zh-Hans" {
                selectedLanguageText = "简体中文"
            } else {
                selectedLanguageText = "繁体中文"
            }
            locale = Locale(identifier: selectedLanguage)
        }
    }

    @Published var selectedLanguageText: String = ""

    private init() {
        DispatchQueue.main.asyncAfter(deadline: .now() + 3) { [weak self] in
            if let self = self {
                self.Orign_IsIdleTimerDisabled = UIApplication.shared.isIdleTimerDisabled
            }
        }
        NotificationCenter.default.addObserver(self, selector: #selector(readerBookWillShow(noti:)), name: WDReaderView.readBookWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(readerBookWillHide(noti:)), name: WDReaderView.readBookWillHideNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(readerBookDidPageChanged(noti:)), name: WDReaderView.readBookDidPageChangedNotification, object: nil)
        if WDBookSessionSDK.shared.isLogin {
            refreshUserInfo(forceRefresh: false)
        }
    }

    // MARK: 阅读器

    @objc func readerBookWillShow(noti: Notification) {
        DispatchQueue.main.async {
            self.isShowingReader = true
        }

        if notAutoLockScreenOnRead {
            UIApplication.shared.isIdleTimerDisabled = notAutoLockScreenOnRead
        }
        duration = 0
        lastReadTime = 0
        savePageReadDuration()
        
        // Start reading session tracking
        ReadingSessionTracker.shared.startSession()
    }

    @objc func readerBookWillHide(noti: Notification) {
        DispatchQueue.main.async {
            self.isShowingReader = false
        }

        if let resourceId = noti.userInfo?["resourceId"] as? String,!resourceId.isEmpty {
            savePageReadDuration()

            WDBookUserSDK.shared.updateBookLastVisitTime(resourceId: resourceId)
            reloadShelfData()
            accountTab.refreshPurchasedListCount()
            refreshFavoriteListCount()

            UIApplication.shared.isIdleTimerDisabled = Orign_IsIdleTimerDisabled
            if duration >= 3 {
                AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_BOOK_READ_DURATION, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_RESOURCE_ID, resourceId, SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_DURATION, String(duration)])
                isFirst = true
                isSendComplete = false
            }

            WDBookDataSyncManager.shared.syncShelfRelateAndDownload(resourceId: resourceId, progress: noti.userInfo?["progress"] as? Int ?? 0)
            
            // End reading session tracking
            ReadingSessionTracker.shared.endSession()
        }
    }

    func savePageReadDuration() {
        if lastReadTime == 0 {
            lastReadTime = Int64(Date().timeIntervalSince1970)
            return
        }
        var pageReadDuration = Int64(Date().timeIntervalSince1970) - lastReadTime
        if pageReadDuration > 3 * 60 {
            pageReadDuration = 3 * 60
        }
        duration = duration + pageReadDuration
        lastReadTime = Int64(Date().timeIntervalSince1970)
    }

    @objc func readerBookDidPageChanged(noti: Notification) {
        if let userInfo = noti.userInfo, let resourceId = userInfo["resourceId"] as? String,!resourceId.isEmpty {
            if let exixtProgressEntity = WDBookUserSDK.shared.getFileReadProgressEntity(resourceId: resourceId) {
                exixtProgressEntity.pagePath = userInfo["chapterPath"] as? String ?? ""
                exixtProgressEntity.firstWordOffset = Int32(userInfo["contentOffset"] as! Int)
                exixtProgressEntity.progress = Int32(userInfo["progress"] as! Int)
                exixtProgressEntity.summary = userInfo["summary"] as? String ?? ""
                WDBookUserSDK.shared.saveReadProgress(entity: exixtProgressEntity)
                WDBookUserSDK.shared.updateBookLastVisitTime(resourceId: resourceId)
            } else {
                let bookProgress = BookProgress(
                    resourceId: resourceId,
                    resourceTypeId: WDBookUserSDK.shared.getResourceTypeId(resourceId: resourceId) ?? "",
                    pagePath: userInfo["chapterPath"] as? String ?? "",
                    firstWordOffset: Int32(userInfo["contentOffset"] as! Int),
                    progress: Int32(userInfo["progress"] as! Int),
                    summary: userInfo["summary"] as? String ?? "",
                    uploadStatus: 0,
                    versionCode: 0,
                    syncKey: 0,
                    lastUpdateTime: Int64(Date().timeIntervalSince1970 * 1000)
                )
                let readProgressEntity = ReadProgressEntity(bookProgress: bookProgress)
                WDBookUserSDK.shared.saveReadProgress(entity: readProgressEntity)
                WDBookUserSDK.shared.updateBookLastVisitTime(resourceId: resourceId)
            }
            if Int32(userInfo["progress"] as! Int) >= 99 {
                if !isFirst && !isSendComplete {
                    AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_BOOK_READ_COMPLETED, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_RESOURCE_ID, resourceId])
                }
                isSendComplete = true
            }
            isFirst = false
        }
        savePageReadDuration()
    }

    func cleanup() {
        // 清空微件
        storeTab = StoreTab()

        userInfo = UserInfo()

        shelfCombineItems = []
        shelfItems = []
        isShelfFirstLoad = true

        hasNewFeedback = false
        hasNewNotification = false
        purchasedListResources = []

        noteCountStr = "0条"
        accountTab.purchasedListCount = "0本"
        favoriteListCount = "0本"
        walletBalance = 0.0
        couponCount = "0"

        AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_USER_LOGOUT)
        NotificationCenter.default.post(name: AuthLoginV.logoutSuccessNotification, object: nil)
    }

    // MARK: 登录和用户信息

    func showLoginRegisterV(returnTabAfterLogin: TabName? = nil, loginComplete: (() -> Void)? = nil) {
        WDBookDataSyncManager.shared.deleteUserDevice(deviceId: WDBookSessionSDK.shared.deviceId)
        NotificationCenter.default.post(name: ContentView.Noti_IsLogOut, object: true)

        self.returnTabAfterLogin = returnTabAfterLogin
        loginCompleteHandler = loginComplete
        isShowLoginRegisterV = true
    }

    func showLoginRegisterVAndPopToStore() {
        WDBookDataSyncManager.shared.deleteUserDevice(deviceId: WDBookSessionSDK.shared.deviceId)
        NotificationCenter.default.post(name: ContentView.Noti_IsLogOut, object: true)
        returnTabAfterLogin = nil
        isShowLoginRegisterV = true
        NotificationCenter.default.post(name: TabChangeNotification, object: TabName.store)
    }

    func hideLoginRegisterV(isSuccess: Bool = false, isClearCache: Bool = true) {
        if isSuccess {
            if let tab = returnTabAfterLogin {
                changeTab(tabName: tab)
            }
            if let complete = loginCompleteHandler {
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                    complete()
                }
            }
        }

        if isClearCache {
            returnTabAfterLogin = nil
            loginCompleteHandler = nil
        }
        isShowLoginV = false
        isShowRegisterV = false

        isShowLoginRegisterV = false
    }

    func refreshUserInfo(forceRefresh: Bool) {
        WDBookUserSDK.shared.getUserInfo(forceRefresh: forceRefresh) { [weak self] result in
            switch result {
            case let .success(userInfo):
                if userInfo != nil {
                    self?.userInfo = UserInfo(entity: userInfo ?? UserInfoEntity())
                    self?.refreshWalletBalance()
                }

            case let .failure(error):
                print(error)
            }
        }
    }

    func syncIapPurchaseProducts(complete: (() -> Void)? = nil) {
        WDBookSyncSDK.shared.syncInAppPurchaseProducts { [weak self] _ in
            self?.refreshIapPurchaseProducts(complete: complete)
        }
    }

    func refreshIapPurchaseProducts(complete: (() -> Void)? = nil) {
        let purchaseProcuts = WDBookAppSDK.shared.getInAppPurchaseProductList()
        iapProductList.removeAll()
        if purchaseProcuts.count > 0 {
            iapProductList.append(contentsOf: purchaseProcuts.sorted { $0.price < $1.price })
        }
        complete?()
    }

    // MARK: 商城，微件

    func syncAndRefreshWidgets(complete: (() -> Void)? = nil) {
        let startT = CFAbsoluteTimeGetCurrent()
        WDBookSyncSDK.shared.syncDataOfWidgetsRelated { result in
            WDBookAppSDK.shared.getNewWidgetCombineDataList(homeType: HomeType.storehome) { [weak self] result in
                switch result {
                case let .success(list):
                    debugPrint("性能测试：syncAndRefreshWidgets -- WDBookAppSDK.shared.syncDataOfWidgetsRelated：数据：\(String(describing: list))")
                    let containers = list ?? []
                    for c in containers {
                        if let productList = self?.suggestProductListDic[c.containerId] {
                            c.suggestProductList = productList
                        }
                    }
                    if self?.isStoreVonDisappear ?? false {
                        self?.cacheStoreContainers = containers
                    } else {
                        self?.storeContainers = containers
                    }

                case let .failure(error):
                    if let sdkException = error as? SDKException {
                        print(sdkException)
                    } else {
                        print(error)
                    }
                }
                debugPrint("性能测试：syncAndRefreshWidgets -- WDBookAppSDK.shared.syncDataOfWidgetsRelated：\(CFAbsoluteTimeGetCurrent() - startT)")
                complete?()
            }
        }
    }

    func refreshWidgets(complete: (() -> Void)? = nil) {
        let startT = CFAbsoluteTimeGetCurrent()
        WDBookAppSDK.shared.getNewWidgetCombineDataList(homeType: HomeType.storehome) { [weak self] result in
            switch result {
            case let .success(list):
                debugPrint("性能测试：refreshWidgets -- WDBookAppSDK.shared.getNewWidgetCombineDataList：数据：\(String(describing: list))")
                if self?.isStoreVonDisappear ?? false {
                    self?.cacheStoreContainers = list ?? []
                } else {
                    self?.storeContainers = list ?? []
                }
            case let .failure(error):
                if let sdkException = error as? SDKException {
                    print(sdkException)
                } else {
                    print(error)
                }
            }
            debugPrint("性能测试：refreshWidgets -- WDBookAppSDK.shared.getNewWidgetCombineDataList：\(CFAbsoluteTimeGetCurrent() - startT)")
            complete?()
        }
    }

    // 修复首次启动app，没有网络权限的时候，主页空白问题。每次点击tab或重新开启网络权限
    func trySyncRefreshWidgets() {
        if storeContainers.isEmpty && cacheStoreContainers.isEmpty {
            syncAndRefreshWidgets()
        }
    }

    func refreshCategoryList() {
        let startT = CFAbsoluteTimeGetCurrent()
        let categoryList = WDBookStoreSDK.shared.getLocalBookCategoryList()?
            .filter { $0.parentCategoryId == -1 && $0.productCount > 0 } ?? []
        categoryList.forEach { $0.subCategories = WDBookStoreSDK.shared.getSubCategoryEntityList(parentCategoryId: $0.categoryId) ?? [] }
        self.categoryList = categoryList

        // 全部类别
        self.categoryList.forEach { entity in
            let allCate = StoreCategoryEntity()
            allCate.categoryId = 0
            allCate.categoryName = "全部".localized
            entity.subCategories = [allCate] + entity.subCategories
        }

        let allCate = StoreCategoryEntity()
        allCate.categoryId = 0
        allCate.categoryName = "全部书籍".localized
        allCate.productCount = categoryList.reduce(0) { r, entity in
            r + entity.productCount
        }

        allCategories.removeAll()
        allCategories.append(contentsOf: [allCate] + self.categoryList)
        debugPrint("性能测试：refreshCategoryList：\(CFAbsoluteTimeGetCurrent() - startT) ：\(allCategories)")
    }

    // MARK: 书架

    func refreshShelfList() {
        // FIXME: 分页功能需要完善
        if !WDBookSessionSDK.shared.isLogin {
            return
        }
        let shelfCombineItems = WDBookUserSDK.shared.getShelfEntityList(offset: Int64(self.shelfCombineItems.count), limit: 20)
        if shelfCombineItems.count > 0 {
            for shelfCombineItem in shelfCombineItems {
                let combineDownloadObservable = DownloadResourceObservable(item: shelfCombineItem)
                if shelfCombineItem.dataType == ShelfDataType.archive {
                    archiveCount = archiveCount + 1
                    let shelfItemsInOneArchive = WDBookUserSDK.shared.getBookListByClientArchiveId(clientArchiveId: shelfCombineItem.archiveEntity?.clientArchiveId ?? "", offset: 0, limit: 1_000_000)
                    shelfItems.append(contentsOf: shelfItemsInOneArchive.map { DownloadResourceObservable(item: $0) })
                } else {
                    shelfItems.append(combineDownloadObservable)
                }
                if containsShelfObject(shelfCombineItems: selectedShelfItems, shelfItem: combineDownloadObservable) {
                    combineDownloadObservable.isSeleted = true
                }
                self.shelfCombineItems.append(combineDownloadObservable)
            }
            refreshShelfList()
        }
        isShelfFirstLoad = false
    }

    func reloadShelfData() {
        archiveCount = 0
        selectedShelfItems.removeAll()
        for i in 0 ..< shelfCombineItems.count {
            if shelfCombineItems[i].isSeleted {
                selectedShelfItems.append(shelfCombineItems[i])
            }
        }
        shelfCombineItems.removeAll()
        shelfItems.removeAll()
        refreshShelfList()
    }

    func containsShelfObject(shelfCombineItems: [DownloadResourceObservable], shelfItem: DownloadResourceObservable) -> Bool {
        for i in 0 ..< shelfCombineItems.count {
            if shelfCombineItems[i].resourceId == shelfItem.resourceId {
                return true
            }
        }
        return false
    }

    func refreshPurchasedList() {
        if !WDBookSessionSDK.shared.isLogin {
            return
        }
        let purchasedEntityList = WDBookUserSDK.shared.getPurchasedResourceEntityList(position: purchasedListResources.count, limit: 50)

        if purchasedEntityList.count > 0 {
            for entity in purchasedEntityList {
                purchasedListResources.append(DownloadResourceObservable(item: entity))
            }
            refreshPurchasedList()
        }
        accountTab.refreshPurchasedListCount()
    }

    func hasBuyBook(resourceId: String) -> Bool {
        purchasedListResources.first(where: { $0.resourceId == resourceId }) != nil
    }

    func refreshWalletBalance() {
        if WDBookSessionSDK.shared.isLogin {
            if let walletBalanceEntity = WDBookUserSDK.shared.getWalletBalance(), !walletBalanceEntity.userId.isEmpty, walletBalanceEntity.userId == self.userInfo.userId {
                walletBalance = Double(walletBalanceEntity.balance)
            }
        }
    }

    func checkBookUpdate(resourceId: String, openBookAction: @escaping () -> Void) {
        let resourceDownloadInfo = WDBookDownloadSDK.shared.getResourceDownloadInfo(resourceId: resourceId)
        if resourceDownloadInfo?.downloadStatus == .update {
            Log.d("文件资源发生更新")
            self.openBookAction = Optional(openBookAction)
            AppState.shared.alert.showBookUpdateAlert(title: "温馨提示".localized, msg: "当前书籍有升级版本".localized) { [weak self] in
                guard let self = self else { return }
                Log.d("开始更新")
                DispatchQueue.main.async {
                    self.upateBookFile(fileId: resourceDownloadInfo?.fileId ?? "", md5: resourceDownloadInfo?.downloadInfo?.md5 ?? "")
                    Toaster.showToast(message: "正在更新...".localized)
                }

            } cancelHandler: {
                Log.d("不更新，直接打开")
                openBookAction()
            }
        } else {
            openBookAction()
        }
    }

    func upateBookFile(fileId: String, md5: String) {
        WDBookDownloadSDK.shared.getFileDownloadUrl(fileId: fileId) { result in
            switch result {
            case let .success(fileDownloadEntity):
                if fileDownloadEntity != nil {
                    WDBookUserSDK.shared.getEncryptionKey(fileId: fileId) { result in
                        switch result {
                        case .success:
                            Log.d("开始下载了")
                            AppDownloadManager.shared.start(key: fileId, url: fileDownloadEntity!.downloadUrl, destinationFilePath: PathManager.zipPathRelative(fileId), md5: md5)
                        case .failure:
                            Log.d("获取加密key失败")
                        }
                    }
                } else {
                    HUDManager.hideLoadingHUD()
                    Toaster.showToast(message: "当前书籍更新失败".localized) // TODO: 使用alert
                    NotificationCenter.default.removeObserver(self, name: Noti_Download_Complete, object: nil)
                    self.openBookAction?()
                }
            case .failure:
                HUDManager.hideLoadingHUD()
                Toaster.showToast(message: "当前书籍更新失败".localized) // TODO: 使用alert
                NotificationCenter.default.removeObserver(self, name: Noti_Download_Complete, object: nil)
                self.openBookAction?()
            }
        }
    }

    @objc func bookUpdateComplete() {
        HUDManager.hideLoadingHUD()
        NotificationCenter.default.removeObserver(self, name: Noti_Download_Complete, object: nil)
        // openBook
        openBookAction?()
    }

    func changeTab(tabName: TabName) {
        NotificationCenter.default.post(name: TabChangeNotification, object: tabName)
    }

    func refreshFavoriteListCount() {
        if WDBookSessionSDK.shared.isLogin {
            WDBookUserSDK.shared.getFavoriteCount(completion: { [weak self] result in
                switch result {
                case let .success(c):
                    if let count = c as? Int {
                        self?.favoriteListCount = "%lld本".localizedFormat(count)
                    }
                case .failure:
                    break
                }
            })
        }
    }

    func refreshFeedbackStatus() {
        if WDBookSessionSDK.shared.isLogin {
            WDBookUserSDK.shared.getFeedbackStatus { [weak self] result in
                switch result {
                case let .success(entity):
                    if let e = entity {
                        self?.hasNewFeedback = e.feedbackStatus == 1
                    }
                case let .failure(error):
                    Log.d("refresh feedback failed with error \(error)")
                }
            }
        }
    }

    // 通知相关
    func refreshNotificationStatus() {
        if WDBookSessionSDK.shared.isLogin {
            WDBookUserSDK.shared.getNotificationStatus { [unowned self] result in
                switch result {
                case let .success(entitites):
                    if let list = entitites, list.count > 0 {
                        for entity in list {
                            if entity.type == NotificationMsgType.platform.rawValue {
                                notificationStatusPlatformCount = Int(entity.unreadCount)
                            }
                            if entity.type == NotificationMsgType.discount.rawValue {
                                notificationStatusDiscountCount = Int(entity.unreadCount)
                            }
                        }

                        resetNotificationStatus()
                    } else {
                        makeReadAllNotifications()
                    }
                    debugPrint("notificationStatusPlatformCount: \(notificationStatusPlatformCount)")
                    debugPrint("notificationStatusDiscountCount: \(notificationStatusDiscountCount)")
                    debugPrint("notificationStatusAllCount: \(notificationStatusAllCount)")
                case let .failure(error):
                    Log.d("refresh notification status failed with error \(error)")
                }
            }
        }
    }

    func makeReadAllNotifications() {
        notificationStatusPlatformCount = 0
        notificationStatusDiscountCount = 0
        notificationStatusAllCount = 0
    }

    func makeReadOneNotification(type: NotificationMsgType) {
        if type == .platform {
            notificationStatusPlatformCount -= 1
        }
        if type == .discount {
            notificationStatusDiscountCount -= 1
        }
        resetNotificationStatus()
    }

    func resetNotificationStatus() {
        if acceptDiscout {
            notificationStatusAllCount = notificationStatusPlatformCount + notificationStatusDiscountCount
        } else {
            notificationStatusAllCount = notificationStatusPlatformCount
        }
    }

    // 通知横幅。写在storeTab上main.asyncAfte无效
    @Published var isShowNotificationBanner = false
    @Published var notificationDetailsOnBanner: [NotificationDetails] = .init()
    @Published var notificationDetailsOnAlert: [NotificationDetails] = .init()

    // 只当加载的时候
    func checkNotificationRemindWhenReload(method: NotificationShowMethod) {
        isShowNotificationBanner = false
        // （7天内，且前20条内，未读的）前两条。且比记录时间大。
        var results = [NotificationDetails]()
        if let allNotifications = WDBookUserSDK.shared.loadALlNotificationListFromDB(offset: 0, limit: 20) {
//            let days7Notifications = allNotifications.filter { (Int64($0.lastUpdateTime) > (Int64(Date().timeIntervalSince1970) - Int64(3600 * 24 * 7))) && $0.read == 0 && $0.showMethod == method.rawValue}
            let days7Notifications = allNotifications.filter {
                $0.read == 0
                    && $0.showMethod == method.rawValue
                    && Int64($0.lastUpdateTime) > UserDefaults.getLastDialogTime(userId: userInfo.userId ?? "", method: method)
            }
            .prefix(1).map { $0 } // 改为弹出1条。
            if days7Notifications.count > 0 {
                UserDefaults.setLastDialogTime(userId: userInfo.userId ?? "", method: method)
                let group = DispatchGroup()

                for one in days7Notifications {
                    group.enter()
                    WDBookUserSDK.shared.getNotificationDetail(id: one.id, type: Int(one.type)) { result in
                        switch result {
                        case let .success(entity):
                            if let detailsEntity = entity {
                                results.append(NotificationDetails(entity: detailsEntity))
                            }

                        case let .failure(error):
                            Log.d("get notification detail failed with error \(error)")
                        }
                        group.leave()
                    }
                }

                group.notify(queue: DispatchQueue.main) { [unowned self] in
                    if results.count > 0 {
                        if method == .banner {
                            showNotificationBanner(notificationDetails: results)
                        } else if method == .dialog {
                            showNotificationAlert(notificationDetails: results)
                        }
                    }
                }
            }
        }
    }

    private func showNotificationBanner(notificationDetails: [NotificationDetails]) {
        notificationDetailsOnBanner = notificationDetails
        isShowNotificationBanner = true
    }

    // 设置横幅已读
    func makeReadNotificationBanner(readId: Int64) {
        // 取消横幅时候属于首次标记已读。其他情况属于二次标记。
        WDBookUserSDK.shared.makeLocalNotificationReadById(id: readId)
        if let i = notificationDetailsOnBanner.firstIndex(where: { $0.id == readId }) {
            notificationDetailsOnBanner.remove(at: i)

            if notificationDetailsOnBanner.count == 0 {
                isShowNotificationBanner = false
            } else {
                isShowNotificationBanner = false
                DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [weak self] in
                    self?.isShowNotificationBanner = true
                }
            }
        }
    }

    // 设置所有横幅已读
    func makeReadAllNotificationBanners() {
        notificationDetailsOnBanner.removeAll()
        isShowNotificationBanner = false
    }

    private func showNotificationAlert(notificationDetails: [NotificationDetails]) {
        notificationDetailsOnAlert = notificationDetails
        showNotificationOneAlert()
    }

    private func showNotificationOneAlert() {
        weak var weakself = self

        DispatchQueue.main.asyncAfter(deadline: .now() + 1) { [unowned self] in
            if let noti = notificationDetailsOnAlert.first {
                // 打开alert就标记已读。
                if let entity = WDBookUserSDK.shared.getNotificationMessage(id: noti.id), entity.read == 0 {
                    WDBookUserSDK.shared.makeLocalNotificationReadById(id: noti.id)
                    AppState.shared.makeReadOneNotification(type: noti.type)
//                    NotificationCenter.default.post(name: NotificationDetailsV.readeNotification, object:noti.id)
                }

                NotificationAlertV.show(title: noti.title,
                                        // htmlstring除去格式
                                        detail: DTHTMLAttributedStringBuilder(html: noti.content.data(using: .utf8)!, options: [:], documentAttributes: nil).generatedAttributedString().string,
                                        on: UIApplication.shared.windows[0],
                                        onOK: {
                                            RoutableManager.push(NotificationDetailsV(messageId: noti.id, type: noti.type, mv: NotificationDetailsMV(noti)))
                                            weakself?.makeReadLastAlert()
                                        }, onCancel: {
                                            weakself?.makeReadLastAlert()
                                        })
            }
        }
    }

    private func makeReadLastAlert() {
        WDBookUserSDK.shared.makeLocalNotificationReadById(id: notificationDetailsOnAlert.first?.id ?? 0)
        if notificationDetailsOnAlert.count > 0 { notificationDetailsOnAlert.removeFirst() }

        DispatchQueue.main.asyncAfter(deadline: .now() + 1.5) { [unowned self] in
            if notificationDetailsOnAlert.count > 0 {
                showNotificationOneAlert()
            }
        }
    }

    // 系统推送
    func refreshDeviceToken() {
        // 请求通知权限
        //    //iOS8以下
        //    [application registerForRemoteNotificationTypes:UIRemoteNotificationTypeBadge | UIRemoteNotificationTypeAlert | UIRemoteNotificationTypeSound];
        //    //iOS8 - iOS10
        //    [application registerUserNotificationSettings:[UIUserNotificationSettings settingsForTypes:UIUserNotificationTypeAlert | UIUserNotificationTypeSound | UIUserNotificationTypeBadge categories:nil]];
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { [weak self] accepted, _ in
            DispatchQueue.main.async {
                self?.isAcceptedNotification = accepted
                // 向APNs请求token
                UIApplication.shared.registerForRemoteNotifications()

                //           guard accepted else {
                //               debugPrint("用户不允许消息通知。")
                //               return
                //           }
                //            debugPrint("用户允许消息通知。")
                //            DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                //                self?.postTestLocalNotification()
                //            }
            }
        }
    }

    func refreshSystemNotificationStatus() {
        let current = UNUserNotificationCenter.current()
        current.getNotificationSettings(completionHandler: { [weak self] permission in
            self?.allowNotification = (permission.authorizationStatus == .authorized || permission.authorizationStatus == .provisional) // 开启普通通知，ephemeral状态也包括在authorized内了
            switch permission.authorizationStatus {
            case .authorized:
                debugPrint("User granted permission for notification")
            case .denied:
                debugPrint("User denied notification permission")
            case .notDetermined:
                debugPrint("Notification permission haven't been asked yet")
            case .provisional:
                // @available(iOS 12.0, *)
                debugPrint("The application is authorized to post non-interruptive user notifications.")
            case .ephemeral:
                // @available(iOS 14.0, *)
                debugPrint("The application is temporarily authorized to post notifications. Only available to app clips.")
            @unknown default:
                debugPrint("Unknow Status")
            }
        })
    }

    @Published var couponCount: String = "0"
    func refreshCouponCount() {
        guard WDBookSessionSDK.shared.isLogin else {
            return
        }
        WDBookUserSDK.shared.getCouponCount(status: .unused) { [weak self] result in
            switch result {
            case let .success(e):
                if let entity = e {
                    self?.couponCount = String(Int(entity.count))
                }
            case let .failure(error):
                Log.d("get coupon count failed with error \(error)")
            }
        }
    }

    // 笔记条数
    @Published var noteCountStr = "%lld条".localizedFormat(0)
    func refreshNoteCountStr() {
        if WDBookSessionSDK.shared.isLogin {
            let count = WDBookUserSDK.shared.getAllNoteCount()
            noteCountStr = "%lld条".localizedFormat(count)
        }
    }
}

extension AppState {
    struct ShelfTab {}

    struct StoreTab {
        var storeContainers: [WidgetContainerCombineEntity] = .init()
        var isSearchPresent = false
    }

    struct AccountTab {
        var selected: Int? = 0
        var purchasedListCount: String = "%lld本".localizedFormat(0)

        mutating func refreshPurchasedListCount() {
            if WDBookSessionSDK.shared.isLogin {
                purchasedListCount = "%lld本".localizedFormat(WDBookUserSDK.shared.getPurchasedCount())
            }
        }
    }

    func popToRootOnlyForSwiftUI() {
        storeTab.isSearchPresent = false
        accountTab.selected = nil
        isShowLoginV = false
    }
}

enum TabName: Int {
    case home = 0
    case bookshelf
    case store
    case account

    var title: String {
        switch self {
        case .home:
            return "首页".localized
        case .bookshelf:
            return "书架".localized
        case .store:
            return "书城".localized
        case .account:
            return "我的".localized
        }
    }
}

class TabSelect: ObservableObject, TabSelectable {
    init() {
        if WDBookSessionSDK.shared.isLogin {
            selection = Tab.bookshelf
        } else {
            selection = Tab.store
        }
        _selection.register(self)
    }

    func shouldSelect(_ tab: TabName) -> Bool {
        Log.i("点击:\(tab)")
        AppState.shared.trySyncRefreshWidgets()
        if WDBookSessionSDK.shared.isLogin {
            WDBookDataSyncManager.shared.syncDataWithHomeType(tabType: tab)
            _ = InAppPurchaseManager.shared.checkForUnfilledOrders()
        }
        switch tab {
        case .bookshelf:
            if !WDBookSessionSDK.shared.isLogin {
                AppState.shared.showLoginRegisterV(returnTabAfterLogin: tab)
                return false
            }
            return true
        case .home, .store, .account:
            return true
        }
    }

    @TabSelection var selection = Tab.store {
        willSet {
            objectWillChange.send()
        }
    }
}
