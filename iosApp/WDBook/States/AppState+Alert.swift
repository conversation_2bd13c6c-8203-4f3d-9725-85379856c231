//
//  AppState+Alert.swift
//  WDBook
//
//  Created by <PERSON> on 2021/2/1.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation

enum AlertType {
    case defaultAlertType
    case formatVersionAlertType
    case onlyOkBtnAlertType
    case bookUpdateAlertType
    case bookUpdateFailureAlertType
    case bookNoteDownloadAlertType
    case bookNoteDeleteAlertType
    case appUpgradeAlertType
    case notificationType
    case networkErrorType
}

extension AppState{
    struct Alert{
        //MARK: 工具方法
        //MARK: Alert。目前只有下载的4G网络判断Alert
        var isShowAlert:Bool = false
        var alertTitle:String = ""
        var alertMsg:String = ""
        var alertOKHandler:(()->())?
        var alertCancelHandler:(()->())?

        var alertType = AlertType.defaultAlertType
        
        mutating func showCanCellularDownloadAlert(okHandler:(()->())? = nil){
            showAlert(title: "", msg: "目前处在2G/3G/4G网络下，下载会消耗数据流量，是否继续？".localized, okHandler: okHandler, cancelHandler: nil)
        }
        
        mutating func showFormatVersionAlert(){
            alertType = AlertType.formatVersionAlertType
            showAlert()
        }
        
        mutating func showFileErrorAlert(okHandler:(()->())? = nil){
            showOnlyOKBtnAlert(title: "提示".localized, msg: "出错了，请联系：<EMAIL>".localized, okHandler: okHandler)
        }
        
        mutating func showDeviceFullAlert(okHandler:(()->())? = nil){
            if AppState.shared.isShowDeviceFullAlert && WDBookSessionSDK.shared.isLogin{
                showOnlyOKBtnAlert(title: "", msg: "您的账号在多台设备登录，当前设备已退出登录，若非本人操作，请及时更改密码".localized, okHandler: okHandler)
                AppState.shared.isShowDeviceFullAlert = false
            }
        }

        mutating func showOnlyOKBtnAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil){
            alertType = AlertType.onlyOkBtnAlertType
            showAlert(title: title, msg: msg, okHandler: okHandler)
        }
        
        mutating func showBookUpdateAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil,cancelHandler:(()->())? = nil){
            alertType = AlertType.bookUpdateAlertType
            showAlert(title: title, msg: msg, okHandler: okHandler, cancelHandler: cancelHandler)
        }

        mutating func bookUpdateFailureAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil,cancelHandler:(()->())? = nil){
            alertType = AlertType.bookUpdateFailureAlertType
            showAlert(title: title, msg: msg, okHandler: okHandler, cancelHandler: cancelHandler)
        }
        
        mutating func bookNoteDownloadAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil,cancelHandler:(()->())? = nil){
            alertType = AlertType.bookNoteDownloadAlertType
            showAlert(title: title, msg: msg, okHandler: okHandler, cancelHandler: cancelHandler)
        }
        
        mutating func deleteNoteAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil,cancelHandler:(()->())? = nil){
            alertType = AlertType.bookNoteDeleteAlertType
            showAlert(title: title, msg: msg, okHandler: okHandler, cancelHandler: cancelHandler)
        }

        mutating func appUpgradeAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil,cancelHandler:(()->())? = nil) {
            alertType = AlertType.appUpgradeAlertType
            showAlert(title: title, msg: msg, okHandler: okHandler, cancelHandler: cancelHandler)
        }

        mutating func notificationAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil,cancelHandler:(()->())? = nil){
            alertType = AlertType.notificationType
            showAlert(title: title, msg: msg) {
                //TODO: 去通知详情
            } cancelHandler: {
                cancelHandler?()
            }
        }
        
        mutating func showNetworkErrorAlert(){
            alertType = .networkErrorType
            isShowAlert = true
        }

        private mutating func showAlert(title:String = "", msg:String = "",okHandler:(()->())? = nil, cancelHandler:(()->())? = nil) {
            alertTitle = title
            alertMsg = msg
            alertOKHandler = okHandler
            alertCancelHandler = cancelHandler
            isShowAlert = true
        }
        
        mutating func tapCancelAlert(){
            alertTitle = ""
            alertMsg = ""
            isShowAlert = false
            alertType = AlertType.defaultAlertType
            alertCancelHandler?()
            alertCancelHandler = nil
            alertOKHandler = nil
        }
        
        mutating func tapOKAlert(){
            alertTitle = ""
            alertMsg = ""
            isShowAlert = false
            alertType = AlertType.defaultAlertType
            alertOKHandler?()
            alertCancelHandler = nil
            alertOKHandler = nil
        }
    }
}
