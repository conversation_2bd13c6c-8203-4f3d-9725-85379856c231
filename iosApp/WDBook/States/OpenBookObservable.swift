//
//  OpenBookObservable.swift
//  WDBook
//
//  Created by <PERSON> on 2020/10/12.
//  Copyright © 2020 WeDevote Bible. All rights reserved.
//

import Foundation
import UIKit
import Combine
import WDCrypt

class OpenBookObservable:ObservableObject{
    @Published var isShowReader = false{
        didSet{
            Log.d("---------------\(isShowReader)------------------")
        }
    }
    @Published var resourceId:String = ""
    @Published var fileId:String = ""
    @Published var readerFilePath:String = ""
    @Published var code:String = ""
    @Published var chapterPath:String?
    @Published var contentOffset:Int?
    
    func openBook(path:String,code:String,resourceId:String,fileId:String) -> Bool{
//        guard !AppState.shared.isShowingReader else {
//            return
//        }
        self.resourceId = resourceId
        self.fileId = fileId
        readerFilePath = path
        self.code = code
        let userId:String = AppState.shared.userInfo.userId ?? ""
        Log.d("打开书籍:\(path),密码code:\(code),userid:\(userId)")
        
        if let user = WDBookSessionSDK.shared.getEncryptionKeyUser(),WDCryptor.initBook(path, code: code, user: user) == 0 , WDCryptor.keyAvailable(path: path) {
            var userInfo = [AnyHashable : Any]()
            userInfo["fileId"] = fileId
            userInfo["resourceId"] = resourceId
            AppState.shared.readerUserInfo = userInfo
            
            if let readProgress =  WDBookUserSDK.shared.getFileReadProgressEntity(resourceId: resourceId){
                chapterPath = readProgress.pagePath
                contentOffset = Int(readProgress.firstWordOffset)
            }else{
                chapterPath = nil
                contentOffset = nil
            }
            AnalysisUtils.logEvent(eventString: SHARED_CONSTANTS_ANALYSIS.LOG_V1_BOOK_OPEN, params: [SHARED_CONSTANTS_ANALYSIS.LOG_V1_PARAM_RESOURCE_ID, resourceId])
            return true
        }
        return false
    }
    
    private func openBookAndShowReader(path:String,code:String,resourceId:String,fileId:String){
        if openBook(path: path, code: code, resourceId: resourceId, fileId: fileId) {
            isShowReader = true
        }else{
            AppState.shared.alert.showFileErrorAlert()
        }
    }
}

extension WDCryptor{
    static func keyAvailable(path:String) -> Bool {
        if let keyCheck = getBookText(path, pagePath: "META-INF/encryption"),
           !keyCheck.isEmpty,
           keyCheck == "resurrection"{
            return true
        }
        return false
    }
}
