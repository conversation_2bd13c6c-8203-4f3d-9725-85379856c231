//
//  WDBookSDK.swift
//  WDBook
//
//  Created by <PERSON> on 2021/4/23.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import shared

enum SDKError: Error {
    case error(KotlinException)
}

func getAppMode() -> AppMode {
#if DEBUG
    return .debug
#else
    guard let appStoreReceiptURL = Bundle.main.appStoreReceiptURL else {
        return .testflight
    }
    let isSandboxReceipt = appStoreReceiptURL.lastPathComponent == "sandboxReceipt"
    return isSandboxReceipt ? .testflight : .appstore
#endif
}

/**
 * iOS端的业务逻辑实现
 * 处理App升级通知等业务逻辑
 */
fileprivate class iOSBusinessPatch: BusinessPatch {
    
    /**
     * App需要升级通知
     * @param isForce true 表示强制升级，false 表示可选升级
     * @param upgradeUrl 备用的下载地址，可能为空
     */
    func onAppNeedUpdateNotification(isForce: Bool, upgradeUrl: String?) {
        DispatchQueue.main.async {
            AppUtils.handleUpgrade(isForce: isForce, upgradeUrl: upgradeUrl ?? "")
        }
    }
}

class WDBookSDK {
    static let shared = WDBookSDK()
    var sdk:IosSDK

    private init() {
        sdk = IosSDK(env: ENV, isDebug: true, appMode: getAppMode(), businessPatch: iOSBusinessPatch())
    }

    func getSessionBl() -> IosSessionBl {
        sdk.sessionBl
    }

    func getUserBl() -> IosUserBl {
        sdk.userBl
    }

    func getAppBl() -> IosAppBl {
        sdk.appBl
    }

    func getSyncBl() -> IosSyncBl {
        sdk.syncBl
    }

    func getDownloadBl() -> IosDownloadBl {
        sdk.downloadBl
    }
    
    func getStoreBl() -> IosStoreBl {
        sdk.storeBl
    }
    
    func getPaymentBl() -> IosPaymentBl {
        sdk.paymentBl
    }

    func getApiServerBl() -> IosApiServerBl {
        sdk.apiServerBl
    }
}


extension SDKError{
    var errno:Int{
        switch self{
        case .error(let kotExp):
            if let exp = kotExp as? ApiException{
                return Int(exp.code)
            }
            break
        }
        return -1
    }
    
    var msg:String{
        switch self{
        case .error(let kotExp):
            if let exp = kotExp as? ApiException{
                return exp.message ?? ""
            }
            break
        }
        return ""
    }
    
    var code:Int{
        switch self{
        case .error(let kotExp):
            let nserror = kotExp.asError()
            return getCode(msg: nserror.localizedDescription)
        }
    }
    
    private func getCode(msg:String) -> Int {
        
        var strArr = msg.split(separator: " ")
        strArr = strArr.filter({$0.contains("=")})
        
        for str in strArr{
            let pair = str.split(separator: "=")
            if pair.first == "Code",
                let value = pair.last,
                let code = Int(value){
                return code
            }
        }
        
        return 0
    }
    
    func isUserNotExist() -> Bool{
        [Int(ErrorInfo.usernotexists.code),Int(ErrorInfo.usernotexists2.code)].contains(errno)
    }
    
    func isPwdInvalid() -> Bool{
        [Int(ErrorInfo.passwordnotmatch.code),Int(ErrorInfo.passwordnotmatch2.code)].contains(errno)
    }
}
