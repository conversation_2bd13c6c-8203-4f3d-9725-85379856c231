//
//  ResponseCallback.swift
//  WDBook
//
//  Created by <PERSON> on 2021/6/1.
//  Copyright © 2021 WeDevote Bible. All rights reserved.
//

import Foundation

import shared

func getResponseCallback<T>(completion: @escaping (Result<T?, SDKError>) -> Void) -> (T?, KotlinException?) -> Void {
    func responseHandler(result: T?, error: KotlinException?) {
        if let error = error {
            if let sdkException = error as? SDKException {
                handleSDKException(sdkException)
            }
            if let apiException = error as? ApiException {
                handleApiException(apiException)
            }
            completion(.failure(.error(error)))
            return
        }
        completion(.success(result))
    }
    return responseHandler
}
