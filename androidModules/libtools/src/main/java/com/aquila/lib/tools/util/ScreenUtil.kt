package com.aquila.lib.tools.util

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.ContextWrapper
import android.content.res.Configuration
import android.os.Build
import android.util.DisplayMetrics
import android.view.View
import android.view.Window
import android.view.WindowManager
import androidx.appcompat.app.AppCompatActivity
import androidx.appcompat.view.ContextThemeWrapper
import com.aquila.lib.tools.ToolsLibAPP

/***
 * @date 创建时间 2018/8/13 12:22 PM
 * <AUTHOR> W.<PERSON>
 * @description 处理屏幕相关的参数
 */
object ScreenUtil {
    private var screenWidth: Int = 0
    private var screenHeight: Int = 0
    @JvmStatic
    fun getScreenHeight(): Int {
        if (screenHeight <= 0) {
            initScreenSize()
        }
        return screenHeight
    }

    @JvmStatic
    fun getScreenWidth(): Int {
        if (screenWidth <= 0) {
            initScreenSize()
        }
        return screenWidth
    }

    /*实时获取屏幕宽度，考虑到屏幕可能要横屏，所以宽度会有变化*/
    fun getScreenWidthLiveTime(): Int {
        val manager = ToolsLibAPP.get().getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outMetrics = DisplayMetrics()
        manager.defaultDisplay.getMetrics(outMetrics)
        return outMetrics.widthPixels
    }

    private fun initScreenSize() {
        val manager = ToolsLibAPP.get().getSystemService(Context.WINDOW_SERVICE) as WindowManager
        val outMetrics = DisplayMetrics()
        manager.defaultDisplay.getMetrics(outMetrics)
        screenWidth = outMetrics.widthPixels
        screenHeight = outMetrics.heightPixels
    }

    // 如果是沉浸式的，全屏前就没有状态栏
    @SuppressLint("RestrictedApi")
    fun hideStatusBar(context: Context?) {
        getWindow(context)!!.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
    }

    @SuppressLint("NewApi")
    fun hideSystemUI(context: Context?) {
        var uiOptions = (
            View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                or View.SYSTEM_UI_FLAG_FULLSCREEN
                or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
            )
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.KITKAT) {
            uiOptions = uiOptions or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
        }
//        SYSTEM_UI = getWindow(context)?.getDecorView()?.getSystemUiVisibility()
        getWindow(context)!!.decorView.systemUiVisibility = uiOptions
    }

    fun getWindow(context: Context?): Window? {
        return if (getAppCompActivity(context) != null) {
            getAppCompActivity(context)?.window
        } else {
            scanForActivity(context)?.window
        }
    }

    fun getAppCompActivity(context: Context?): AppCompatActivity? {
        if (context == null) return null
        if (context is AppCompatActivity) {
            return context
        } else if (context is ContextThemeWrapper) {
            return getAppCompActivity(context.baseContext)
        }
        return null
    }

    fun scanForActivity(context: Context?): Activity? {
        if (context == null) return null
        if (context is Activity) {
            return context
        } else if (context is ContextWrapper) {
            return scanForActivity(context.baseContext)
        }
        return null
    }

    @JvmStatic
    fun isTablet(context: Context): Boolean {
        return (context.resources.configuration.screenLayout
                and Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE
    }
}
