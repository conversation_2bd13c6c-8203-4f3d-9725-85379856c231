package com.wedevote.wdbook.ui

import android.content.Intent
import android.content.res.Configuration
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.widget.Button
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.RelativeLayout
import android.widget.TextView
import com.aquila.lib.dialog.CommAlertDialog
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.ThemeStyle
import com.wedevote.wdbook.constants.ThemeStyle.*
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.event.OnChangeAccountSuccess
import com.wedevote.wdbook.tools.event.RestartAllActivityEvent
import com.wedevote.wdbook.tools.upgrade.APPUpgradeManager
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.ThemeUtils
import com.wedevote.wdbook.ui.account.DeviceManagerActivity
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.home.HomeMainActivity
import com.wedevote.wdbook.ui.home.HomeTab
import com.wedevote.wdbook.ui.read.lib.css.CSSParser
import com.wedevote.wdbook.ui.service.SyncDataService
import com.wedevote.wdbook.ui.user.DeleteAccountActivity
import com.wedevote.wdbook.ui.user.LanguageSelectActivity
import com.wedevote.wdbook.ui.user.notification.NotificationSettingActivity
import com.wedevote.wdbook.ui.widgets.SlideSwitch
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File
import java.util.*

/***
 * @date 创建时间 2020/5/14 15:19
 * <AUTHOR> W.YuLong
 * @description 设置页面
 */
class SettingActivity : RootActivity(), View.OnClickListener {
    private lateinit var notLockScreenSwitch: SlideSwitch
    private lateinit var wifiDownloadSwitch: SlideSwitch
    private lateinit var versionTextView: TextView
    private lateinit var changeThemeSwitch: SlideSwitch
    private lateinit var showPageNumberSwitch: SlideSwitch
    private lateinit var aboutLayout: GroupImageTextLayout
    private lateinit var faithAnnouncementLayout: GroupImageTextLayout
    private lateinit var securityLayout: LinearLayout
    private lateinit var clearCacheLayout: GroupImageTextLayout
    private lateinit var checkUpdateLayout: GroupImageTextLayout
    private lateinit var logoutButton: Button
    private lateinit var languageLayout: GroupImageTextLayout
    private lateinit var selectLanguageTextView: TextView

    private lateinit var fitSystemThemeLayout: LinearLayout
    private lateinit var lightThemeLayout: LinearLayout
    private lateinit var nightThemeLayout: LinearLayout

    private lateinit var fitSystemCheckImageView: ImageView
    private lateinit var lightCheckImageView: ImageView
    private lateinit var nightCheckImageView: ImageView

    private lateinit var notificationSettingLayout: GroupImageTextLayout
    private lateinit var deviceManagerLayout: GroupImageTextLayout

    private lateinit var speedUpLayout: LinearLayout

    private lateinit var deleteAccountLayout: RelativeLayout

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_setting_layout)
        initViewFromXML()

        notLockScreenSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_IsLockScreen, false)
        wifiDownloadSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_IsWifiDownload, true)
        showPageNumberSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_isShowPage, false)

        initUI()
        setViewListeners()
    }

    private fun initUI() {
        versionTextView.text = "V${APPUtil.getVersionName()}"
        if (SDKSingleton.sessionBl.isLogin()) {
            logoutButton.visibility = View.VISIBLE
            securityLayout.visibility = View.VISIBLE
        } else {
            logoutButton.visibility = View.GONE
            securityLayout.visibility = View.GONE
        }

        if (APPUtil.currentLocale == Locale.SIMPLIFIED_CHINESE) {
            selectLanguageTextView.text = "简体中文"
        } else {
            selectLanguageTextView.text = "繁體中文"
        }
        initThemeUI()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun OnChangeAccountSuccessEvent(event: OnChangeAccountSuccess) {
        if (event.isResetPassword) {
            finish()
        }
    }

    override fun onResume() {
        super.onResume()
        Handler().postDelayed({
            if (APPConfig.isChangeThemeClicked) {
                APPConfig.isChangeThemeClicked = false
                ThemeUtils.mainActivity?.onTabSelect(HomeTab.MINE)
            }
        }, 1000)
        initUI()
    }

    private fun setViewListeners() {
        logoutButton.setOnClickListener(this)
        checkUpdateLayout.setOnClickListener(this)
        clearCacheLayout.setOnClickListener(this)
        aboutLayout.setOnClickListener(this)
        deleteAccountLayout.setOnClickListener(this)
        faithAnnouncementLayout.setOnClickListener(this)
        securityLayout.setOnClickListener(this)
        notLockScreenSwitch.setSlideListener { slideSwitch, isOpen ->
            SPSingleton.get().putBoolean(SPKeyDefine.SP_IsLockScreen, isOpen)
        }
        wifiDownloadSwitch.setSlideListener { slideSwitch, isOpen ->
            SPSingleton.get().putBoolean(SPKeyDefine.SP_IsWifiDownload, isOpen)
        }
        showPageNumberSwitch.setSlideListener { slideSwitch, isOpen ->
            SPSingleton.get().putBoolean(SPKeyDefine.SP_isShowPage, isOpen)
        }

        fitSystemThemeLayout.setOnClickListener(this)
        lightThemeLayout.setOnClickListener(this)
        nightThemeLayout.setOnClickListener(this)
        notificationSettingLayout.setOnClickListener(this)
        deviceManagerLayout.setOnClickListener(this)
        languageLayout.setOnClickListener(this)
        speedUpLayout.setOnClickListener(this)
    }

    private fun initViewFromXML() {
        notLockScreenSwitch = findViewById(R.id.setting_not_lock_screen_Switch)
        wifiDownloadSwitch = findViewById(R.id.setting_wifi_download_Switch)
        versionTextView = findViewById(R.id.setting_app_version_TextView)
        changeThemeSwitch = findViewById(R.id.setting_change_theme_Switch)
        showPageNumberSwitch = findViewById(R.id.setting_show_page_Switch)
        aboutLayout = findViewById(R.id.setting_about_layout)
        faithAnnouncementLayout = findViewById(R.id.setting_faith_announcement_Layout)
        securityLayout = findViewById(R.id.setting_account_security_Layout)
        clearCacheLayout = findViewById(R.id.setting_clear_cache_Layout)
        checkUpdateLayout = findViewById(R.id.setting_check_upgrade_layout)
        languageLayout = findViewById(R.id.setting_language_setting_Layout)
        selectLanguageTextView = findViewById(R.id.setting_select_language_TexView)

        logoutButton = findViewById(R.id.setting_logout_Button)

        fitSystemThemeLayout = findViewById(R.id.setting_theme_fit_sysytem_container_layout)
        lightThemeLayout = findViewById(R.id.setting_theme_light_container_layout)
        nightThemeLayout = findViewById(R.id.setting_theme_night_container_layout)

        fitSystemCheckImageView = findViewById(R.id.setting_theme_fit_system_check_ImageView)
        lightCheckImageView = findViewById(R.id.setting_theme_light_check_ImageView)
        nightCheckImageView = findViewById(R.id.setting_theme_night_check_ImageView)

        notificationSettingLayout = findViewById(R.id.setting_notification_setting_layout)
        deviceManagerLayout = findViewById(R.id.setting_device_manager_layout)
        deleteAccountLayout = findViewById(R.id.setting_delete_account_Layout)

        versionTextView.text = "V${APPUtil.getVersionName()}"
        notLockScreenSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_IsLockScreen, false)
        wifiDownloadSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_IsWifiDownload, true)
        showPageNumberSwitch.state = SPSingleton.get().getBoolean(SPKeyDefine.SP_isShowPage, false)

        speedUpLayout = findViewById(R.id.setting_internet_speed_up_Layout)

        deleteAccountLayout.visibility = View.GONE
    }

    override fun onClick(v: View?) {
        when (v) {
            aboutLayout -> {
                startActivity(Intent(this, AboutActivity::class.java))
            }

            deleteAccountLayout -> {
                if (!SDKSingleton.sessionBl.isLogin()) {
                    SSOLoginActivity.checkAndGotoLogin(
                        this,
                        callBack = object : OnLoginResultCallBack {
                            override fun onLoginResult(isSuccess: Boolean) {
                                if (isSuccess) {
                                    startActivity(
                                        Intent(
                                            this@SettingActivity,
                                            DeleteAccountActivity::class.java
                                        )
                                    )
                                }
                            }
                        }
                    )
                    return
                } else {
                    startActivity(Intent(this@SettingActivity, DeleteAccountActivity::class.java))
                }
            }

            deleteAccountLayout -> {
                if (!SDKSingleton.sessionBl.isLogin()) {
                    SSOLoginActivity.checkAndGotoLogin(
                        this,
                        callBack = object : OnLoginResultCallBack {
                            override fun onLoginResult(isSuccess: Boolean) {
                                if (isSuccess) {
                                    startActivity(
                                        Intent(
                                            this@SettingActivity,
                                            DeleteAccountActivity::class.java
                                        )
                                    )
                                }
                            }
                        }
                    )
                    return
                } else {
                    startActivity(Intent(this@SettingActivity, DeleteAccountActivity::class.java))
                }

            }

            clearCacheLayout -> {
                clearCacheFile()
            }

            languageLayout -> {
                startActivity(Intent(this, LanguageSelectActivity::class.java))
            }

            logoutButton -> {
                CommAlertDialog.with(this)
                    .setMessage(R.string.confirm_logout_question)
                    .setStartText(R.string.label_cancel).setEndText(R.string.confirm_logout)
                    .setOnViewClickListener { d, v, tag ->
                        if (tag == CommAlertDialog.TAG_CLICK_END) {
                            AnalyticsUtils.logEvent(AnalyticsConstants.LOG_V1_USER_LOGOUT)
                            SDKSingleton.sessionBl.cleanUserToken()
                            val intent = Intent(this@SettingActivity, SyncDataService::class.java)
                            intent.putExtra("cancelSync", true)
                            startService(intent)

                            SPSingleton.get().putString(SPKeyDefine.SP_LoginUserId, "")

                            EventBus.getDefault().post(LogoutEvent())
                            HomeMainActivity.gotoHomeActivity(this@SettingActivity)
                            AnalyticsUtils.updateAnalyticsUserID()
                            finish()
                        }
                    }.create().show()
            }

            checkUpdateLayout -> {
                APPUpgradeManager(this).checkUpgrade(true, true)
            }

            fitSystemThemeLayout -> {
                changeAppTheme(FIT_SYSTEM)
            }

            nightThemeLayout -> {
                changeAppTheme(DARK)
            }

            lightThemeLayout -> {
                changeAppTheme(LIGHT)
            }

            notificationSettingLayout -> {
                startActivity(Intent(this, NotificationSettingActivity::class.java))
            }

            deviceManagerLayout -> {
                startActivity(Intent(this, DeviceManagerActivity::class.java))
            }

            securityLayout -> {
                startActivity(Intent(this, AccountSecurityActivity::class.java))
            }

            speedUpLayout -> {
                startActivity(Intent(this, InternetSpeedUpActivity::class.java))
            }
        }
    }

    private fun changeAppTheme(theme: ThemeStyle) {
        if (SDKSingleton.appBl.currentTheme == theme) {
            return
        }
        APPConfig.isChangeThemeClicked = true
        CSSParser.releaseInstance()
        ThemeUtils.setCurrentTheme(this, theme, true)
        initThemeUI()
    }

    fun initThemeUI() {
        when (SDKSingleton.appBl.currentTheme) {
            LIGHT -> {
                lightCheckImageView.isSelected = true
                nightCheckImageView.isSelected = false
                fitSystemCheckImageView.isSelected = false
            }

            DARK -> {
                lightCheckImageView.isSelected = false
                nightCheckImageView.isSelected = true
                fitSystemCheckImageView.isSelected = false
            }

            FIT_SYSTEM -> {
                lightCheckImageView.isSelected = false
                nightCheckImageView.isSelected = false
                fitSystemCheckImageView.isSelected = true
            }
        }
    }

    fun clearCacheFile() {
        val dirFile = File(DataPathUtil.getPictureCachePath())
        val listFile = dirFile.listFiles()
        if (!listFile.isNullOrEmpty()) {
            Arrays.sort(
                listFile,
                object : Comparator<File> {
                    override fun compare(o1: File?, o2: File?): Int {
                        return if (o1!!.lastModified() > o2!!.lastModified()) -1 else 1
                    }
                }
            )
            for (i in listFile.indices) {
                listFile[i].delete()
            }
        }
    }
}
