package com.wedevote.wdbook.ui

import android.content.Intent
import android.graphics.Paint
import android.os.Bundle
import android.widget.TextView
import com.aquila.lib.log.KLog
import com.aquila.lib.widget.group.GroupImageTextLayout
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.WrapCoroutineHelper
import com.wedevote.wdbook.tools.test.LabPageActivity
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.findString

/***
 * @date 创建时间 2020/5/14 15:53
 * <AUTHOR> <PERSON><PERSON><PERSON>
 * @description 关于我们的页面
 */
class AboutActivity : RootActivity() {
    private lateinit var versionTextView: TextView
    private lateinit var protocolTextView: TextView
    private lateinit var copyrightTextView: TextView
    private lateinit var privacyTextView: TextView

    private lateinit var logoImageTextLayout: GroupImageTextLayout
    private var versionClickCount = 0
    private var logoClickCount = 0

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_about_layout)

        versionTextView = findViewById(R.id.about_version_TextView)
        protocolTextView = findViewById(R.id.about_protocol_TextView)
        privacyTextView = findViewById(R.id.about_privacy_TextView)
        copyrightTextView = findViewById(R.id.about_copyright_TextView)

        versionTextView.text = "V%s".format(APPUtil.getVersionName())
        versionTextView.setOnClickListener {
            checkVersionClick()
        }

        logoImageTextLayout = findViewById(R.id.about_logo_Layout)
        logoImageTextLayout.setOnClickListener {
            checkLogoClick()
        }

        protocolTextView.paintFlags = protocolTextView.paintFlags or Paint.UNDERLINE_TEXT_FLAG
        privacyTextView.paintFlags = privacyTextView.paintFlags or Paint.UNDERLINE_TEXT_FLAG

        protocolTextView.setOnClickListener {
            WrapCoroutineHelper.getBookAgreementURL {
                CommWebViewActivity.gotoWebView(
                    this,
                    it,
                    titleName = findString(R.string.title_agreement)
                )
            }
        }

        privacyTextView.setOnClickListener {
            WrapCoroutineHelper.getBookPrivacyURL {
                CommWebViewActivity.gotoWebView(
                    this,
                    it,
                    titleName = findString(R.string.title_privacy)
                )
            }
        }
    }

    private fun checkVersionClick() {
        if (versionClickCount < VERSION_CLICK_THRESHOLD) {
            versionClickCount++
            KLog.d(TAG, "版本号被点击了 $versionClickCount 次")
        }
    }

    private fun checkLogoClick() {
        if (versionClickCount == VERSION_CLICK_THRESHOLD && logoClickCount < LOGO_CLICK_THRESHOLD) {
            logoClickCount++
            KLog.d(TAG, "Logo 图片被点击了 $logoClickCount 次")
            if (logoClickCount == LOGO_CLICK_THRESHOLD) {
                KLog.d(
                    TAG,
                    "版本号的点击次数（ $versionClickCount 次 ）和 Logo 图片的点击次数（ $logoClickCount 次 ）满足条件，重置次数为 0"
                )
                versionClickCount = 0
                logoClickCount = 0
                KLog.d(TAG, "满足点击次数条件，打开 Lab Page")
                startActivity(Intent(this, LabPageActivity::class.java))
            }
        }
    }

    companion object {
        private const val TAG = "AboutActivity"
        private const val VERSION_CLICK_THRESHOLD = 7
        private const val LOGO_CLICK_THRESHOLD = 3
    }
}
