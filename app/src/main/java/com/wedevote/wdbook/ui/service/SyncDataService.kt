package com.wedevote.wdbook.ui.service

import android.app.Service
import android.content.Intent
import android.os.IBinder
import com.aquila.lib.log.KLog
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.entity.SyncResult
import com.wedevote.wdbook.entity.SyncType
import com.wedevote.wdbook.tools.event.OnSyncAllFinish
import com.wedevote.wdbook.tools.event.OnSyncBookmarkFinish
import com.wedevote.wdbook.tools.event.OnSyncNoteFinish
import com.wedevote.wdbook.tools.event.OnSyncNotificationMessageFinish
import com.wedevote.wdbook.tools.event.OnSyncPurchasedDataFinish
import com.wedevote.wdbook.tools.event.OnSyncShelfArchiveFinish
import com.wedevote.wdbook.tools.event.OnSyncShelfItemFinish
import com.wedevote.wdbook.tools.event.OnSyncStoreCategoryFinish
import com.wedevote.wdbook.tools.event.OnSyncWidgetContainerFinish
import com.wedevote.wdbook.tools.event.OnSyncWidgetDetailFinish
import com.wedevote.wdbook.tools.util.APPUtil
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.EventBus

/***
 * @date 创建时间 2020/10/1 16:54
 * <AUTHOR> W.YuLong
 * @description
 */
class SyncDataService : Service() {
    companion object {
        var isInSyncProgress = false

        /*是否做了取消任务*/
        var isTaskCanceled = false

        fun handleItemSyncResult(item: SyncResult) {
            when (item.syncType) {
                SyncType.ShelfBook -> {
                    EventBus.getDefault().post(OnSyncShelfItemFinish())
                }

                SyncType.ShelfArchive -> {
                    EventBus.getDefault().post(OnSyncShelfArchiveFinish())
                }

                SyncType.PurchasedData -> {
                    EventBus.getDefault().post(OnSyncPurchasedDataFinish())
                }

                SyncType.WidgetContainer -> {
                    EventBus.getDefault().post(OnSyncWidgetContainerFinish())
                }

                SyncType.WidgetDetail -> {
                    EventBus.getDefault().post(OnSyncWidgetDetailFinish())
                }

                SyncType.BookCategory -> {
                    EventBus.getDefault().post(OnSyncStoreCategoryFinish())
                }

                SyncType.Bookmark -> {
                    EventBus.getDefault().post(OnSyncBookmarkFinish())
                }

                SyncType.Note -> {
                    EventBus.getDefault().post(OnSyncNoteFinish())
                }

                SyncType.NotificationMessages -> {
                    EventBus.getDefault().post(OnSyncNotificationMessageFinish(item.count))
                }

                else -> {}
            }
        }

        fun handleItemSyncResult(resultList: List<SyncResult>) {
            for (item in resultList) {
                handleItemSyncResult(item)
            }
            EventBus.getDefault().post(OnSyncAllFinish())
        }

    }

    override fun onBind(intent: Intent?): IBinder? {
        return null
    }

    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        isTaskCanceled = intent?.getBooleanExtra("cancelSync", false) ?: false
        if (isTaskCanceled) {
            stopSelf()
            KLog.d("Stop服务")
            return super.onStartCommand(intent, flags, startId)
        }
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            isInSyncProgress = true

            SDKSingleton.apiServerBl.syncHostAPIServer()

            val result = mutableListOf<SyncResult>()
            result.addAll(SDKSingleton.syncBl.syncAppData())
            if (SDKSingleton.sessionBl.isLogin()) {
                result.addAll(SDKSingleton.syncBl.syncUserData())

                result.add(APPUtil.syncNotification())
            }
            isInSyncProgress = false
            handleItemSyncResult(result)
            stopSelf()
        }
        return super.onStartCommand(intent, flags, startId)
    }
}
