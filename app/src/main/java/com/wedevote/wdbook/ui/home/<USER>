package com.wedevote.wdbook.ui.home

import android.annotation.SuppressLint
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.os.Handler
import android.view.View
import android.view.animation.AnimationUtils
import android.widget.FrameLayout
import com.aquila.lib.base.BaseFragment
import com.aquila.lib.tools.singleton.SPSingleton
import com.aquila.lib.tools.util.ToastUtil
import com.gyf.immersionbar.ImmersionBar
import com.gyf.immersionbar.NotchUtils
import com.sunchen.netbus.NetStatusBus
import com.sunchen.netbus.annotation.NetSubscribe
import com.sunchen.netbus.type.Mode
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.ExceptionHandler
import com.wedevote.wdbook.base.RootActivity
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_DURATION
import com.wedevote.wdbook.constants.AnalyticsConstants.LOG_V1_PARAM_TIMESTAMP
import com.wedevote.wdbook.constants.DownloadStatus
import com.wedevote.wdbook.tools.download.DownloaderEngine
import com.wedevote.wdbook.tools.download.OnDownloadServiceConnectedListener
import com.wedevote.wdbook.tools.event.AutoDownloadEvent
import com.wedevote.wdbook.tools.event.LogoutEvent
import com.wedevote.wdbook.tools.event.OnLoginEvent
import com.wedevote.wdbook.tools.event.OnSyncNotificationMessageFinish
import com.wedevote.wdbook.tools.upgrade.APPUpgradeManager
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.CftUtils
import com.wedevote.wdbook.tools.util.DataPathUtil
import com.wedevote.wdbook.tools.util.FilePathUtil
import com.wedevote.wdbook.tools.util.IntentConstants
import com.wedevote.wdbook.tools.util.NetWorkUtils
import com.wedevote.wdbook.tools.util.SPKeyDefine
import com.wedevote.wdbook.tools.util.ThemeUtils
import com.wedevote.wdbook.ui.account.OnLoginResultCallBack
import com.wedevote.wdbook.ui.account.SSOLoginActivity
import com.wedevote.wdbook.ui.dialogs.NotificationActivityDialog
import com.wedevote.wdbook.ui.shelf.HomeBookShelfFragment
import com.wedevote.wdbook.ui.user.HomeMineFragment
import com.wedevote.wdbook.ui.user.OnNewNotificationListener
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode
import java.io.File

/*APP的首页*/
class HomeMainActivity : RootActivity(), HomeBottomNavigationLayout.OnTabSelectListener, OnHomeTabListener {
    lateinit var containerLayout: FrameLayout
    lateinit var navigationLayout: HomeBottomNavigationLayout
    private val fragmentMap = HashMap<Int, BaseFragment>(8)
    private var lastClickTime = 0L
    private val waitTime = 1000L
    private var startTime = 0L

    companion object {
        var firstOpen = true
        var currentTab = HomeTab.EMPTY

        fun gotoHomeActivity(context: Context, homeTab: HomeTab = HomeTab.STORE) {
            val intent = Intent(context, HomeMainActivity::class.java)
            if (context !is Activity) {
                intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK)
            }
            intent.putExtra(IntentConstants.EXTRA_HomeTab, homeTab.tab)
            context.startActivity(intent)
            if (context is Activity && context !is HomeMainActivity) {
                context.finish()
            }
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_home_main_layout)
        ThemeUtils.mainActivity = this
        navigationLayout = findViewById(R.id.home_main_bottom_navigation_layout)
        containerLayout = findViewById(R.id.home_main_container_Layout)
        initFragments()
        initNotchParameter()
        setSwipeBackEnable(false)
        navigationLayout.onTabSelectListener = this
        if (!firstOpen) {
            return
        }
        firstOpen = false

        val homeTab = intent.getIntExtra(IntentConstants.EXTRA_HomeTab, -1)
        if (homeTab == -1) {
            if (SDKSingleton.sessionBl.isLogin()) {
                onTabSelect(HomeTab.SHELF)
            } else {
                onTabSelect(HomeTab.STORE)
            }
        } else {
            onTabSelect(HomeTab.contentOf(homeTab))
        }

        CftUtils.reportCFTSuccessFailedCount()

        AnalyticsUtils.updateAnalyticsUserID()
        logEvent(AnalyticsConstants.LOG_V1_APP_OPEN, LOG_V1_PARAM_TIMESTAMP, startTime.toString())
        // 清理掉APK的文件夹文件
        FilePathUtil.deleteFolderFile(File(DataPathUtil.getAPkDirPath()), false)

        containerLayout.post {
            doOnNotificationEvent(OnSyncNotificationMessageFinish(1))
        }
        downloadEngine = DownloaderEngine(this)
        downloadEngine.setServiceConnectedListener(serviceConnectedListener)

        downloadPurchasedBook()
    }

    private var position = 0
    private var limitSize = 20
    private var needDownloadAfterConnect = false

    lateinit var downloadEngine: DownloaderEngine
    private val serviceConnectedListener = object : OnDownloadServiceConnectedListener {
        override fun onServiceConnected() {
            if (needDownloadAfterConnect) {
                downloadBook()
                needDownloadAfterConnect = false
            }
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLoginEvent(event: OnLoginEvent) {
        downloadPurchasedBook()
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doAutoDownloadEvent(event: AutoDownloadEvent) {
        downloadPurchasedBook()
    }

    override fun onStart() {
        super.onStart()
        NetStatusBus.getInstance().register(this);
    }

    @NetSubscribe(mode = Mode.WIFI_CONNECT)
    fun wifiChange() {
        try {
            downloadPurchasedBook()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @NetSubscribe(mode = Mode.MOBILE_CONNECT)
    fun netChange() {
        try {
            downloadPurchasedBook()
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    /*
    * 批量下载已购书籍
    * */
    private fun downloadPurchasedBook() {
        if (APPConfig.isFastClick()){
            return
        }
        if (!SDKSingleton.sessionBl.isLogin()){
            return
        }
        if (!NetWorkUtils.isNetworkAvailable()) {
            return
        }
        position = 0
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.syncBl.syncPurchasedData()
            if (downloadEngine.isServiceConnected) {
                downloadBook()
            } else {
                needDownloadAfterConnect = true
            }
        }
    }

    private fun downloadBook() {
        val dataList = SDKSingleton.dbWrapBl.getPurchasedResourceEntityList(position, limitSize)
        if (dataList.isNullOrEmpty()) {
            SPSingleton.get().putBoolean(SPKeyDefine.SP_NeedCleanXMLData, false)
            return
        }
        var isFirstInstall = SPSingleton.get().getBoolean(SPKeyDefine.SP_NeedCleanXMLData, true)
        dataList.forEach {
            it.resourceDownloadInfo.fileId?.let { it1 ->
                if (isFirstInstall) {
                    SDKSingleton.dbWrapBl.saveBookXmlData(it1, "")
                }
            }
            if (!(it.resourceDownloadInfo.downloadStatus == DownloadStatus.COMPLETE || it.resourceDownloadInfo.downloadStatus == DownloadStatus.UPDATE) ||
                !File(it.resourceDownloadInfo.getActualFilePath()).exists()
            ) {
                it.resourceDownloadInfo.fileId?.let { it1 ->
                    Handler().postDelayed({
                        downloadEngine.readyDownloadOnlyByFileId(
                            it1,
                        )
                    }, ((position * 30 + 1000).toLong()))
                }
            }
        }
        position += dataList.size
        downloadBook()
    }

    @SuppressLint("MissingSuperCall")
    override fun onSaveInstanceState(outState: Bundle) {
        super.onSaveInstanceState(outState)
        outState.putInt("Tab", currentTab.tab)
    }

    /*这两个方法是解决在其他页面切换了主题之后，返回首页没有记住之前选择的tab问题*/
    override fun onRestoreInstanceState(savedInstanceState: Bundle) {
        super.onRestoreInstanceState(savedInstanceState)
        savedInstanceState.let {
            currentTab = HomeTab.STORE
            val tab = it.getInt("Tab")
            onTabSelect(HomeTab.contentOf(tab))
        }
    }

    override fun onResume() {
        startTime = System.currentTimeMillis() / 1000
        ThemeUtils.mainActivity = this
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.appBl.checkErrorCodeWithEmptyData()
        }
        super.onResume()
    }

    override fun onStop() {
        SPSingleton.get().putBoolean(SPKeyDefine.SP_IsCellularDownload, false)
        super.onStop()
    }

    override fun onNewIntent(intent: Intent) {
        super.onNewIntent(intent)
        val homeTab = intent.getIntExtra(IntentConstants.EXTRA_HomeTab, HomeTab.STORE.tab)!!
        when (homeTab) {
            HomeTab.STORE.tab -> {
                onTabSelect(HomeTab.STORE)
                if (!SDKSingleton.sessionBl.isLogin() && !SPSingleton.get().getString(SPKeyDefine.SP_LoginUserId, "").isNullOrEmpty()) {
                    SSOLoginActivity.checkAndGotoLogin(
                        this,
                        callBack = object : OnLoginResultCallBack {
                            override fun onLoginResult(isSuccess: Boolean) {
                                if (isSuccess) {
                                    onTabSelect(currentTab)
                                }
                            }
                        },
                    )
                }
            }
            HomeTab.MINE.tab -> {
                onTabSelect(HomeTab.MINE)
            }
            else -> {
                SSOLoginActivity.checkAndGotoLogin(
                    this,
                    callBack = object : OnLoginResultCallBack {
                        override fun onLoginResult(isSuccess: Boolean) {
                            if (isSuccess) {
                                onTabSelect(HomeTab.contentOf(homeTab))
                            }
                        }
                    },
                )
            }
        }
    }

    override fun onDestroy() {
        NetStatusBus.getInstance().unregister(this)
        firstOpen = true
        APPUpgradeManager.hasShownUpdateDialog = false
        APPUpgradeManager.isApkDownloadDialogShowing = false
        super.onDestroy()
    }

    private fun initNotchParameter() {
        var notchHeight = SPSingleton.get().getInt(SPKeyDefine.SP_NotchHeight, -1)
        if (notchHeight < 0) {
            if (NotchUtils.hasNotchScreen(this)) {
                notchHeight = NotchUtils.getNotchHeight(this)
                val statusBarHeight = ImmersionBar.getStatusBarHeight(this)
                if (notchHeight < statusBarHeight) {
                    notchHeight = statusBarHeight
                }
            } else {
                notchHeight = 0
            }
        }
        SPSingleton.get().putInt(SPKeyDefine.SP_NotchHeight, notchHeight)
    }

    private fun initFragments() {
        val ft = supportFragmentManager.beginTransaction()
        if (fragmentMap.size > 0) {
            for (entry in fragmentMap) {
                ft.detach(entry.value)
            }
        }
        fragmentMap.clear()
        fragmentMap.put(
            HomeTab.SHELF.tab,
            HomeBookShelfFragment(this).apply {
                onShelfItemEditListener = object : OnShelfItemEditListener {
                    override fun onShelfEditMode(isEdit: Boolean) {
                        if (isEdit) {
                            navigationLayout.visibility = View.GONE
                            navigationLayout.startAnimation(
                                AnimationUtils.loadAnimation(
                                    this@HomeMainActivity,
                                    com.aquila.lib.dialog.R.anim.translate_dialog_move_down,
                                ),
                            )
                        } else {
                            navigationLayout.visibility = View.VISIBLE
                            navigationLayout.startAnimation(
                                AnimationUtils.loadAnimation(
                                    this@HomeMainActivity,
                                    com.aquila.lib.dialog.R.anim.translate_dialog_move_up,
                                ),
                            )
                        }
                    }
                }
            },
        )
        fragmentMap.put(HomeTab.STORE.tab, HomeStoreFragment())
        fragmentMap.put(
            HomeTab.MINE.tab,
            HomeMineFragment().also {
                it.onNewNotificationListener = onNewNotificationListener
            },
        )

        containerLayout.removeAllViews()
        for (entry in fragmentMap) {
            ft.add(R.id.home_main_container_Layout, entry.value)
        }
        ft.commitAllowingStateLoss()
    }

    override fun onChangeHomeTab(tab: Int) {
        onTabSelect(HomeTab.contentOf(tab))
    }

    override fun onTabSelect(tab: HomeTab) {
        when (tab) {
            HomeTab.SHELF -> {
                if (!SDKSingleton.sessionBl.isLogin()) {
                    SSOLoginActivity.checkAndGotoLogin(
                        this,
                        callBack = object : OnLoginResultCallBack {
                            override fun onLoginResult(isSuccess: Boolean) {
                                if (isSuccess) {
                                    onTabSelect(tab)
                                }
                            }
                        },
                    )
                    return
                }
            }
            HomeTab.EMPTY -> {
            }
            HomeTab.MINE -> {
            }
            HomeTab.STORE -> {
            }
        }

        val ft = supportFragmentManager.beginTransaction()
        for (entry in fragmentMap) {
            if (entry.key == tab.tab) {
                ft.show(entry.value)
            } else {
                ft.hide(entry.value)
            }
        }
        ft.commitAllowingStateLoss()
        currentTab = tab
        navigationLayout.setCurrentTabUI(tab)
    }

    val onNewNotificationListener = object : OnNewNotificationListener {
        override fun onNewUnReadMessage(haveUnread: Boolean) {
            navigationLayout.setDotViewShowState(haveUnread)
        }
    }

    var notificationDialog: NotificationActivityDialog? = null

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnNotificationEvent(event: OnSyncNotificationMessageFinish) {
        try {
            val notificationEntity = SDKSingleton.userBl.getLatestNotificationEntityExceptType(0)
            if (notificationEntity != null && notificationEntity.showMethod == 1 &&
                APPUtil.formatNotifyMsgId(notificationEntity) != SPSingleton.get().getString(SPKeyDefine.SP_ClosedDialogMessageId, "")
            ) {
                MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
                    val notificationDetail = SDKSingleton.userBl.getNotificationDetail(notificationEntity.id, notificationEntity.type)
                    if (notificationDialog == null) {
                        notificationDialog = NotificationActivityDialog(this@HomeMainActivity)
                    }
                    if (!notificationDialog!!.isShowing) {
                        notificationDialog!!.show()
                        notificationDialog!!.initUIData(notificationEntity, notificationDetail)
                    }
                }
            }
        } catch (e: Exception) {
            e.printStackTrace()
        }
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    fun doOnLogoutEvent(logoutEvent: LogoutEvent) {
        if (logoutEvent.isTokenExpire) {//token过期导致退出登录
            var intent = Intent(this, HomeMainActivity::class.java)
            intent.flags = Intent.FLAG_ACTIVITY_CLEAR_TASK or Intent.FLAG_ACTIVITY_NEW_TASK
            startActivity(intent)
        } else {
            initFragments()
            onTabSelect(HomeTab.STORE)
        }
        MainScope().launch(ExceptionHandler.coroutineExceptionHandler) {
            SDKSingleton.userBl.deleteUserDevice(SDKSingleton.appBl.deviceId)
            SDKSingleton.sessionBl.logout()
        }
    }

    override fun onBackPressed() {
        if (currentTab == HomeTab.SHELF &&
            (fragmentMap[HomeTab.SHELF.tab] as HomeBookShelfFragment).onBackPress()
        ) {
            return
        }

        if (System.currentTimeMillis() - lastClickTime < waitTime) {
            logEvent(
                AnalyticsConstants.LOG_V1_APP_USE_DURATION,
                LOG_V1_PARAM_DURATION,
                (System.currentTimeMillis() / 1000 - startTime).toString(),
            )
            super.onBackPressed()
            android.os.Process.killProcess(android.os.Process.myPid())
            return
        }
        ToastUtil.showToastShort(R.string.click_again_exit)
        lastClickTime = System.currentTimeMillis()
    }
}
