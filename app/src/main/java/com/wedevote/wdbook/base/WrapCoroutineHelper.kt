package com.wedevote.wdbook.base

import kotlinx.coroutines.MainScope
import kotlinx.coroutines.launch

object Wrap<PERSON><PERSON>utineHelper {

    fun getBookAgreementURL(callBack: (url: String) -> Unit) {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            callBack("https://wdbook.com/agreement")
        }) {
            callBack(SDKSingleton.appBl.getBookAgreementURL())
        }
    }

    fun getBookPrivacyURL(callBack: (url: String) -> Unit) {
        MainScope().launch(ExceptionHandler.getCoroutineExceptionHandler {
            callBack("https://wdbook.com/privacy")
        }) {
            callBack(SDKSingleton.appBl.getBookPrivacyURL())
        }
    }
}