package com.wedevote.wdbook.base

import android.content.Intent
import android.os.Bundle
import android.view.MotionEvent
import android.view.inputmethod.InputMethodManager
import androidx.annotation.ColorInt
import com.aquila.lib.base.BaseActivity
import com.gyf.immersionbar.ImmersionBar
import com.wedevote.wdbook.R
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.tools.event.RestartAllActivityEvent
import com.wedevote.wdbook.tools.util.APPUtil
import com.wedevote.wdbook.tools.util.AnalyticsUtils.logEvent
import com.wedevote.wdbook.tools.util.CftUtils
import com.wedevote.wdbook.tools.util.WindowInsetsUtils
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import org.greenrobot.eventbus.EventBus
import org.greenrobot.eventbus.Subscribe
import org.greenrobot.eventbus.ThreadMode

/***
 * @date 创建时间 2020/6/17 15:03
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
open class RootActivity : BaseActivity(), CoroutineScope by CoroutineScope(Dispatchers.Main) {

    companion object {
        private var isAppActive = false
        private var firstRun = true
    }

    open lateinit var immersionBar: ImmersionBar
    private var startTime = -1L
    
    protected var needTopPadding = true
    protected var needBottomPadding = true
    protected var transparentStatusBar = false
    protected var transparentNavigationBar = false

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        APPUtil.setLanguage(this)
        if (!interceptSetTheme()) {
            setTheme(APPConfig.getCurrentThemeStyle())
        }
        setSwipeBackEnable(false)
        
        if (APPUtil.isAboveAndroid15()) {
            WindowInsetsUtils.setEdgeToEdgeDisplay(this)
        }
        
        immersionBar = ImmersionBar.with(this).autoDarkModeEnable(true).fitsSystemWindows(false)
            .autoNavigationBarDarkModeEnable(true)
        if (!interceptSetStatusBar()) {
            if (APPUtil.isAboveAndroid15()) {
                updateStatusBar()
            } else {
                // Android 15 以下版本保持原有逻辑
                if (APPConfig.isCurrentThemeLight()) {
                    immersionBar.statusBarDarkFont(false).statusBarColor(R.color.white)
                        .navigationBarColor(R.color.white).navigationBarDarkIcon(false)
                } else {
                    immersionBar.statusBarDarkFont(true).statusBarColor(R.color.color_dark_1E1E1E)
                        .navigationBarColor(R.color.color_dark_1E1E1E).navigationBarDarkIcon(true)
                }
                immersionBar.init()
            }
        }
        
        if (startTime < 0) {
            startTime = System.currentTimeMillis() / 1000
        }
        EventBus.getDefault().register(this)
    }
    
    /**
     * 更新状态栏设置（仅在 Android 15 及以上版本使用）
     */
    protected open fun updateStatusBar() {
        val isLightTheme = APPConfig.isCurrentThemeLight()
        
        WindowInsetsUtils.setStatusBarColor(
            this,
            getStatusBarColor(),
            needTopPadding,
            needBottomPadding,
            transparentStatusBar,
            transparentNavigationBar
        )
        
        WindowInsetsUtils.setSystemBarsColor(
            this,
            android.graphics.Color.TRANSPARENT,
            android.graphics.Color.TRANSPARENT,
            isLightStatusBar = isLightTheme,
            isLightNavigationBar = isLightTheme
        )
    }
    
    /**
     * 获取状态栏颜色
     * 子类可以重写此方法返回不同的颜色
     * 仅在 Android 15 及以上版本使用
     */
    @ColorInt
    protected open fun getStatusBarColor(): Int {
        return if (APPConfig.isCurrentThemeLight()) {
            getColor(R.color.white)
        } else {
            getColor(R.color.color_dark_1E1E1E)
        }
    }
    
    /**
     * 更新是否需要顶部内边距（仅在 Android 15 及以上版本有效）
     */
    protected fun updateNeedTopPadding(need: Boolean) {
        if (APPUtil.isAboveAndroid15()) {
            needTopPadding = need
            if (!interceptSetStatusBar()) {
                updateStatusBar()
            }
        }
    }
    
    /**
     * 更新是否需要底部内边距（仅在 Android 15 及以上版本有效）
     */
    protected fun updateNeedBottomPadding(need: Boolean) {
        if (APPUtil.isAboveAndroid15()) {
            needBottomPadding = need
            if (!interceptSetStatusBar()) {
                updateStatusBar()
            }
        }
    }
    
    /**
     * 更新状态栏是否透明（仅在 Android 15 及以上版本有效）
     */
    protected fun updateTransparentStatusBar(transparent: Boolean) {
        if (APPUtil.isAboveAndroid15()) {
            transparentStatusBar = transparent
            if (!interceptSetStatusBar()) {
                updateStatusBar()
            }
        }
    }
    
    /**
     * 更新导航栏是否透明（仅在 Android 15 及以上版本有效）
     */
    protected fun updateTransparentNavigationBar(transparent: Boolean) {
        if (APPUtil.isAboveAndroid15()) {
            transparentNavigationBar = transparent
            if (!interceptSetStatusBar()) {
                updateStatusBar()
            }
        }
    }

    open fun interceptSetTheme(): Boolean {
        return false
    }

    open fun interceptSetStatusBar(): Boolean {
        return false
    }

    @Subscribe(threadMode = ThreadMode.MAIN)
    open fun doChangeThemeEvent(event: RestartAllActivityEvent) {
        if (event.isLanguageSetting) {
            // 切换语言
            intent.addFlags(Intent.FLAG_ACTIVITY_NO_ANIMATION)
            finish()
            overridePendingTransition(0, 0)
            startActivity(intent)
        } else {
            recreate()
        }
    }

    override fun onStart() {
        if (!isAppActive) {
            // app 从后台唤醒，进入前台
            isAppActive = true
            startTime = System.currentTimeMillis() / 1000
            logEvent(AnalyticsConstants.LOG_V1_APP_ENTER_FOREGROUND)

            if (!firstRun) {
                CftUtils.backgroundToForegroundCFTLogic(APP.get())
            }

            firstRun = false
        }
        APPUtil.setLanguage(this)
        super.onStart()
    }

    override fun onStop() {
        if (!APPUtil.isAppOnForeground()) {
            // app 进入后台
            isAppActive = false // 记录当前已经进入后台
            logEvent(
                AnalyticsConstants.LOG_V1_APP_USE_DURATION,
                AnalyticsConstants.LOG_V1_PARAM_DURATION,
                (System.currentTimeMillis() / 1000 - startTime).toString(),
            )
        }
        super.onStop()
    }

    override fun onDestroy() {
        super.onDestroy()
        EventBus.getDefault().unregister(this)
    }

    override fun onTouchEvent(event: MotionEvent?): Boolean {
        if (null != this.currentFocus) {
            /**
             * 点击空白位置 隐藏软键盘
             */
            val mInputMethodManager: InputMethodManager =
                (getSystemService(INPUT_METHOD_SERVICE) as InputMethodManager)
            return mInputMethodManager.hideSoftInputFromWindow(this.currentFocus!!.windowToken, 0)
        }
        return super.onTouchEvent(event)
    }
}
