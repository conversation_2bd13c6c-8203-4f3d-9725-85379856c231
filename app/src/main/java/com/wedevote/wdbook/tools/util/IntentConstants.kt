package com.wedevote.wdbook.tools.util

/***
 * @date 创建时间 2020/5/13 17:22
 * <AUTHOR> W<PERSON>
 * @description
 */
object IntentConstants {
    const val EXTRA_url = "url"
    const val EXTRA_NotificationId = "NotificationId"
    const val EXTRA_NotificationType = "NotificationType"

    //    const val EXTRA_BookDataBean = "BookDataBean"
    const val EXTRA_ShelfBookBean = "ShelfBookBean"
    const val EXTRA_feedbackId = "feedbackId"

    //    const val EXTRA_WidgetDetailBean = "WidgetDetailBean"
    const val EXTRA_CategoryId = "CategoryId"
    const val EXTRA_TypeKey = "TypeKey"
    const val EXTRA_LastPublishId = "LastPublishId"
    const val EXTRA_CategoryName = "CategoryName"
    const val EXTRA_ProductId = "ProductId"
    const val EXTRA_CouponEntity = "CouponEntity"

    const val EXTRA_BookContents = "BookContents"

    const val EXTRA_BookFilePath = "BookFilePath"
    const val EXTRA_fileId = "BookId"
    const val EXTRA_BookKey = "BookKey"

    const val EXTRA_ResourceId = "ResourceId"
    const val EXTRA_PathIndex = "PathIndex"
    const val EXTRA_StartWord = "StartWord"
    const val EXTRA_EndWord = "EndWord"
    const val EXTRA_OrderListItemBean = "OrderListItemBean"
    const val EXTRA_OrderId = "OrderId"
    const val EXTRA_PayAmount = "PayAmount"
    const val EXTRA_NoteEntity = "NoteEntity"
    const val EXTRA_FromSearch = "EXTRA_FromSearch"
    const val EXTRA_NoteCountEntity = "NoteCountEntity"
    const val EXTRA_HomeTab = "HomeTab"
    const val EXTRA_WidgetDetailEntityJson = "WidgetDetailEntity"
    const val EXTRA_Keywords = "Keywords"
    const val EXTRA_jumpStep = "jumpStep"
    const val INTENT_RESULT_COUNTRY_CODE = 0X205
    const val Extra_countryCode = "CountryCode"
    const val EXTRA_account = "account"
    const val EXTRA_fromEmail = "fromEmail"
    const val EXTRA_Account_Security_Type = "AccountSecurityType"
    const val EXTRA_Old_Password = "OldPassword"
    const val EXTRA_AuthorId = "AuthorId"
    const val EXTRA_AuthorName = "AuthorName"
    const val EXTRA_IsPublisher = "IsPublisher"
    const val EXTRA_qrCode = "qrCode"
}
