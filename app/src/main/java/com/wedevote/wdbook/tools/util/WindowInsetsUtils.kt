package com.wedevote.wdbook.tools.util

import android.app.Activity
import android.graphics.Color
import android.os.Build
import android.view.Gravity
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.annotation.ColorInt
import androidx.core.graphics.Insets
import androidx.core.view.*

object WindowInsetsUtils {
    
    private const val STATUS_BAR_VIEW_TAG = "status_bar_bg"
    private const val NAVIGATION_BAR_VIEW_TAG = "navigation_bar_bg"
    
    /**
     * 设置边到边显示模式
     */
    fun setEdgeToEdgeDisplay(activity: Activity) {
        if (APPUtil.isAboveAndroid15()) {
            WindowCompat.setDecorFitsSystemWindows(activity.window, false)
        } else {
            // 旧版本的处理
            @Suppress("DEPRECATION")
            activity.window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_LAYOUT_STABLE or
                    View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION or
                    View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
        }
    }
    
    /**
     * 设置状态栏颜色（适配 Android 15）
     * @param activity Activity
     * @param color 状态栏颜色
     * @param needTopPadding 是否需要顶部内边距
     * @param needBottomPadding 是否需要底部内边距
     * @param transparentStatusBar 状态栏是否透明
     * @param transparentNavigationBar 导航栏是否透明
     */
    fun setStatusBarColor(
        activity: Activity,
        @ColorInt color: Int,
        needTopPadding: Boolean = true,
        needBottomPadding: Boolean = true,
        transparentStatusBar: Boolean = false,
        transparentNavigationBar: Boolean = false
    ) {
        val window = activity.window
        val decorView = window.decorView as ViewGroup
        
        WindowCompat.setDecorFitsSystemWindows(window, false)
        
        window.statusBarColor = Color.TRANSPARENT
        window.navigationBarColor = Color.TRANSPARENT
        
        ViewCompat.setOnApplyWindowInsetsListener(decorView) { view, windowInsets ->
            val insets = windowInsets.getInsets(
                WindowInsetsCompat.Type.systemBars() or 
                WindowInsetsCompat.Type.displayCutout()
            )
            
            updateStatusBarBackground(
                view as ViewGroup, 
                insets, 
                if (transparentStatusBar) Color.TRANSPARENT else color
            )
            
            if (needBottomPadding) {
                updateNavigationBarBackground(
                    view, 
                    insets, 
                    if (transparentNavigationBar) Color.TRANSPARENT else color
                )
            }
            
            val contentView = activity.findViewById<View>(android.R.id.content)
            contentView?.updatePadding(
                left = insets.left,
                top = if (needTopPadding) insets.top else 0,
                right = insets.right,
                bottom = if (needBottomPadding) insets.bottom else 0
            )
            
            WindowInsetsCompat.CONSUMED
        }
        
        ViewCompat.requestApplyInsets(decorView)
    }
    
    /**
     * 更新状态栏背景视图
     */
    private fun updateStatusBarBackground(
        decorView: ViewGroup,
        insets: Insets,
        @ColorInt color: Int
    ) {
        var statusBarBg = decorView.findViewWithTag<View>(STATUS_BAR_VIEW_TAG)
        if (statusBarBg == null) {
            statusBarBg = View(decorView.context).apply {
                tag = STATUS_BAR_VIEW_TAG
                z = Float.MAX_VALUE
            }
            val params = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                insets.top
            ).apply {
                gravity = Gravity.TOP
            }
            statusBarBg.layoutParams = params
            decorView.addView(statusBarBg, 0)
        } else {
            val params = statusBarBg.layoutParams
            params.height = insets.top
            statusBarBg.layoutParams = params
        }
        statusBarBg.setBackgroundColor(color)
    }
    
    /**
     * 更新导航栏背景视图
     */
    private fun updateNavigationBarBackground(
        decorView: ViewGroup,
        insets: Insets,
        @ColorInt color: Int
    ) {
        var navBarBg = decorView.findViewWithTag<View>(NAVIGATION_BAR_VIEW_TAG)
        if (navBarBg == null) {
            navBarBg = View(decorView.context).apply {
                tag = NAVIGATION_BAR_VIEW_TAG
                z = Float.MAX_VALUE
            }
            val params = FrameLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                insets.bottom
            ).apply {
                gravity = Gravity.BOTTOM
            }
            navBarBg.layoutParams = params
            decorView.addView(navBarBg)
        } else {
            val params = navBarBg.layoutParams
            params.height = insets.bottom
            navBarBg.layoutParams = params
        }
        navBarBg.setBackgroundColor(color)
    }
    
    /**
     * 应用窗口插入监听器到视图
     * @param view 需要应用的视图
     * @param applyTop 是否应用顶部内边距
     * @param applyBottom 是否应用底部内边距
     * @param applyLeft 是否应用左侧内边距
     * @param applyRight 是否应用右侧内边距
     * @param additionalTop 额外的顶部内边距
     * @param additionalBottom 额外的底部内边距
     */
    fun applyWindowInsetsListener(
        view: View,
        applyTop: Boolean = true,
        applyBottom: Boolean = true,
        applyLeft: Boolean = true,
        applyRight: Boolean = true,
        additionalTop: Int = 0,
        additionalBottom: Int = 0
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, windowInsets ->
            val insets = windowInsets.getInsets(
                WindowInsetsCompat.Type.systemBars() or 
                WindowInsetsCompat.Type.displayCutout()
            )
            
            v.updatePadding(
                top = if (applyTop) insets.top + additionalTop else v.paddingTop,
                bottom = if (applyBottom) insets.bottom + additionalBottom else v.paddingBottom,
                left = if (applyLeft) insets.left else v.paddingLeft,
                right = if (applyRight) insets.right else v.paddingRight
            )
            
            windowInsets
        }
    }
    
    /**
     * 应用窗口插入监听器到视图（使用 margin）
     */
    fun applyWindowInsetsMargin(
        view: View,
        applyTop: Boolean = true,
        applyBottom: Boolean = true,
        applyLeft: Boolean = true,
        applyRight: Boolean = true
    ) {
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, windowInsets ->
            val insets = windowInsets.getInsets(
                WindowInsetsCompat.Type.systemBars() or 
                WindowInsetsCompat.Type.displayCutout()
            )
            
            val params = v.layoutParams as? ViewGroup.MarginLayoutParams
            params?.let {
                it.topMargin = if (applyTop) insets.top else it.topMargin
                it.bottomMargin = if (applyBottom) insets.bottom else it.bottomMargin
                it.leftMargin = if (applyLeft) insets.left else it.leftMargin
                it.rightMargin = if (applyRight) insets.right else it.rightMargin
                v.layoutParams = it
            }
            
            windowInsets
        }
    }
    
    /**
     * 获取状态栏高度
     */
    fun getStatusBarHeight(activity: Activity): Int {
        return if (APPUtil.isAboveAndroid15()) {
            val windowInsets = activity.window.decorView.rootWindowInsets
            windowInsets?.getInsets(WindowInsetsCompat.Type.statusBars())?.top ?: 0
        } else {
            APPUtil.getStatusBarHeight(activity)
        }
    }
    
    /**
     * 获取导航栏高度
     */
    fun getNavigationBarHeight(activity: Activity): Int {
        return if (APPUtil.isAboveAndroid15()) {
            val windowInsets = activity.window.decorView.rootWindowInsets
            windowInsets?.getInsets(WindowInsetsCompat.Type.navigationBars())?.bottom ?: 0
        } else {
            val resourceId = activity.resources.getIdentifier("navigation_bar_height", "dimen", "android")
            if (resourceId > 0) {
                activity.resources.getDimensionPixelSize(resourceId)
            } else {
                0
            }
        }
    }
    
    /**
     * 检查是否有显示切口（刘海屏）
     */
    fun hasDisplayCutout(activity: Activity): Boolean {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val displayCutout = activity.window.decorView.rootWindowInsets?.displayCutout
            displayCutout != null
        } else {
            false
        }
    }
    
    /**
     * 获取显示切口的安全区域
     */
    fun getDisplayCutoutSafeInsets(activity: Activity): Insets {
        return if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.P) {
            val windowInsets = activity.window.decorView.rootWindowInsets
            val displayCutout = windowInsets?.displayCutout
            if (displayCutout != null) {
                Insets.of(
                    displayCutout.safeInsetLeft,
                    displayCutout.safeInsetTop,
                    displayCutout.safeInsetRight,
                    displayCutout.safeInsetBottom
                )
            } else {
                Insets.NONE
            }
        } else {
            Insets.NONE
        }
    }
    
    /**
     * 设置系统栏颜色（兼容边到边模式）
     * 注意：在 Android 15 边到边模式下，直接设置 window 的 statusBarColor 和 navigationBarColor 
     * 可能不会生效，因为系统栏是透明的。需要配合 setStatusBarColor 方法使用。
     */
    fun setSystemBarsColor(
        activity: Activity,
        statusBarColor: Int,
        navigationBarColor: Int,
        isLightStatusBar: Boolean,
        isLightNavigationBar: Boolean
    ) {
        val decorView = activity.window.decorView as? ViewGroup
        decorView?.let {
            val statusBarBg = it.findViewWithTag<View>(STATUS_BAR_VIEW_TAG)
            statusBarBg?.setBackgroundColor(statusBarColor)
            
            val navBarBg = it.findViewWithTag<View>(NAVIGATION_BAR_VIEW_TAG)
            navBarBg?.setBackgroundColor(navigationBarColor)
        }
        
        activity.window.statusBarColor = statusBarColor
        activity.window.navigationBarColor = navigationBarColor
        
        val insetsController = WindowCompat.getInsetsController(activity.window, activity.window.decorView)
        insetsController?.isAppearanceLightStatusBars = isLightStatusBar
        insetsController?.isAppearanceLightNavigationBars = isLightNavigationBar
    }
} 