package com.wedevote.wdbook.tools.util

import android.annotation.SuppressLint
import android.app.Activity
import android.app.ActivityManager
import android.content.Context
import android.content.pm.ApplicationInfo
import android.content.pm.PackageInfo
import android.content.pm.PackageManager
import android.content.res.Resources
import android.net.Uri
import android.os.Build
import android.text.Html
import android.text.InputFilter
import android.text.Spanned
import android.widget.EditText
import com.aquila.lib.tools.singleton.SPSingleton
import com.wedevote.wdbook.R
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.LanguageMode
import com.wedevote.wdbook.entity.SyncResult
import com.wedevote.wdbook.entity.notification.NotificationEntity
import com.wedevote.wdbook.ui.dialogs.CommLoadingDialog
import com.wedevote.wdbook.ui.dialogs.RegisterTipDialog
import java.net.URL
import java.util.*

/***
 * @date 创建时间 2020/5/6 10:11
 * <AUTHOR> <PERSON><PERSON>
 * @description
 */
object APPUtil {

    private val dialogMap = HashMap<String, CommLoadingDialog>()

    fun showLoadingDialog(context: Context, text: String? = null): CommLoadingDialog {
        var dialog = dialogMap[context.javaClass.name]
        if (dialog == null) {
            dialog = CommLoadingDialog(context)
            dialogMap[context.javaClass.name] = dialog
        }
        if (!dialog.isShowing) {
            dialog.show()
            dialog.setOnDismissListener {
                dialogMap.remove(context.javaClass.name)
            }
        }
        if (text != null) {
            dialog.setDialogText(text)
        }
        return dialog
    }

    @SuppressLint("QueryPermissionsNeeded")
    fun isAppInstalled(context: Context, packageName: String): Boolean {
        return try {
            context.packageManager.getPackageInfo(packageName, 0)
            true
        } catch (e: PackageManager.NameNotFoundException) {
            false
        } catch (e: Exception) {
            e.printStackTrace()
            SDKSingleton.loggerBl.handleThrowable(e, getFunctionInfo())
            false
        }
    }

    fun formatNotifyMsgId(entity: NotificationEntity): String {
        return "${entity.id}_${entity.publishedId}"
    }

    fun dismissLoadingDialog(context: Context) {
        val dialog = dialogMap[context.javaClass.name]
        if (dialog != null && dialog.isShowing) {
            dialog.dismiss()
            dialogMap.remove(context.javaClass.name)
        }
    }

    fun showTipDialog(context: Context, title: String, content: String) {
        val tipDialog = RegisterTipDialog(context)
        tipDialog.show()
        tipDialog.setTitleText(title)
        tipDialog.setContentText(content)
    }

    fun getStatusBarHeight(context: Context): Int {
        var result = dp2px(25)
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        if (result <= 0) {
            result = dp2px(25)
        }
        return result
    }

    fun checkEmailAccount(str: String): Boolean {
        val mailRegex =
            "^[0-9A-Za-z_-]([\\.0-9A-Za-z_-]*)@[0-9a-zA-Z_-]+(\\.[0-9a-zA-Z_-]+)".toRegex()
        return str.matches(mailRegex)
    }

    /*获取当前版本号*/
    @JvmStatic
    fun getVersionCode(): Int {
        var versionCode = 0
        try {
            val packageInfo = APP.get().packageManager.getPackageInfo(APP.get().packageName, 0)
            versionCode = packageInfo.versionCode
        } catch (e: PackageManager.NameNotFoundException) {
            SDKSingleton.loggerBl.handleThrowable(0, e, getFunctionInfo())
            e.printStackTrace()
        }
        return versionCode
    }

    /*获取当前版本名*/
    @JvmStatic
    fun getVersionName(): String {
        val packageManager = APP.get().packageManager
        val packageInfo: PackageInfo
        var versionName = ""
        try {
            packageInfo = packageManager.getPackageInfo(APP.get().packageName, 0)
            versionName = packageInfo.versionName ?: ""
        } catch (e: PackageManager.NameNotFoundException) {
            SDKSingleton.loggerBl.handleThrowable(0, e, getFunctionInfo())
            e.printStackTrace()
        }
        return versionName
    }

    var currentLocale: Locale = Locale.SIMPLIFIED_CHINESE

    fun setLanguage(context: Context) {
        val languageMode = SPSingleton.get().getInt(SPKeyDefine.SP_CurrentLanguage, -1)
        when (languageMode) {
            -1 -> {
                val language = Locale.getDefault().language
                val country = Locale.getDefault().country
                when (language.uppercase()) {
                    "ZH" -> {
                        currentLocale = if (country.uppercase().isEqualOne("TW", "HK")) {
                            Locale.TRADITIONAL_CHINESE
                        } else {
                            Locale.SIMPLIFIED_CHINESE
                        }
                    }

                    else -> {
                        currentLocale = if (country.uppercase().isEqualOne("SG", "MY", "CN")) {
                            Locale.SIMPLIFIED_CHINESE
                        } else {
                            Locale.TRADITIONAL_CHINESE
                        }
                    }

                }
            }

            0 -> {
                currentLocale = Locale.SIMPLIFIED_CHINESE
            }

            1 -> {
                currentLocale = Locale.TRADITIONAL_CHINESE
            }
        }
        updateCurrentLocale(context, currentLocale)
    }

    fun getLanguage(): String {
        val languageMode = SPSingleton.get().getInt(SPKeyDefine.SP_CurrentLanguage, -1)
        when (languageMode) {
            -1 -> {
                val language = Locale.getDefault().language
                val country = Locale.getDefault().country
                when (language.uppercase()) {
                    "ZH" -> {
                        return if (country.uppercase().isEqualOne("TW", "HK")) {
                            LanguageMode.TraditionalChinese.value
                        } else {
                            LanguageMode.SimplifiedChinese.value
                        }
                    }

                    else -> {
                        return if (country.uppercase().isEqualOne("SG", "MY", "CN")) {
                            LanguageMode.SimplifiedChinese.value
                        } else {
                            LanguageMode.TraditionalChinese.value
                        }
                    }

                }
            }

            0 -> {
                return LanguageMode.SimplifiedChinese.value
            }

            1 -> {
                return LanguageMode.TraditionalChinese.value
            }
        }
        return LanguageMode.SimplifiedChinese.value
    }

    fun updateCurrentLocale(context: Context, local: Locale) {
        val resources: Resources = context.resources
        val configuration = resources.configuration
        configuration.setLocale(local)
        resources.updateConfiguration(configuration, resources.displayMetrics)
    }

    /**
     * 保存当前的屏幕亮度值，并使之生效
     */
    fun setScreenBrightness(activity: Activity, paramIntValue: Float) {
        var paramInt = paramIntValue
        val localWindow = activity.window
        val localLayoutParams = localWindow.attributes
        if (paramInt == 0f) {
            paramInt = 1f
        }
        localLayoutParams.screenBrightness = paramInt / 255.0f
        localWindow.attributes = localLayoutParams
    }

    fun setOriginalPrice(currency: String?, originalPrice: Float): Spanned? {
        return Html.fromHtml(
            findString(R.string.original_price) + "&#8194<s>${
                UnitFormatUtil.formatPrice(
                    currency,
                    originalPrice
                )
            }</s>"
        )
    }

    fun formatPrice(currency: String?, price: Float, priceCNY: Float): String {
        return "&#8194<font color='#e54643'>  ${UnitFormatUtil.formatPrice(currency, price)}" +
                "&#8194(" + findString(R.string.about) + "¥ ${
            String.format(
                "%.2f",
                priceCNY
            )
        })&#8194</font>"
    }

    fun setCurrentPrice(
        currency: String?,
        price: Float,
        priceCNY: Float,
        discount: Float,
        isActivity: Boolean = false
    ): Spanned {
        var formatText =
            (if (isActivity) findString(R.string.title_activity_price) else findString(R.string.current_price)) + "&#8194<font color='#e54643'>  ${
                UnitFormatUtil.formatPrice(
                    currency,
                    price
                )
            }" +
                    "&#8194(" + findString(R.string.about) + "¥ ${
                String.format(
                    "%.2f",
                    priceCNY
                )
            })&#8194</font>"
        val discountText = UnitFormatUtil.formatDiscountText(discount)
        if (discountText.isNotEmpty()) {
            formatText = "$formatText($discountText)"
        }
        return Html.fromHtml(formatText)
    }

    fun formatUrl(url: String, map: HashMap<String, String>): URL {
        val builtUri: Uri.Builder = Uri.parse(url).buildUpon()!!
        for (entry in map.entries) {
            if (entry.value.isNotEmpty()) {
                builtUri.appendQueryParameter(entry.key, entry.value)
            }
        }
        builtUri.build()
        return URL(builtUri.toString())
    }

    fun removeUrlParam(urlString: String, vararg name: String): String {
        var url = urlString
        for (s in name) {
            // 使用replaceAll正则替换,replace不支持正则
            url = url.replace("&?$s=[^&]*".toRegex(), "")
        }
        return url
    }

    @JvmStatic
    fun isAppOnForeground(): Boolean {
        val am = APP.get().getSystemService(Context.ACTIVITY_SERVICE) as ActivityManager
        val packageName: String = APP.get().packageName
        val appProcesses = am.runningAppProcesses ?: return false
        for (appProcess in appProcesses) {
            // The name of the process that this object is associated with.
            if (appProcess.processName == packageName && appProcess.importance == ActivityManager.RunningAppProcessInfo.IMPORTANCE_FOREGROUND) {
                return true
            }
        }
        return false
    }

    suspend fun syncNotification(): SyncResult {
        // 关闭优惠推荐功能取消 11.25日
//        var typeList: ArrayList<Int>? = null
//        //这个是关闭了获取优惠推荐，没有关闭的话就传null,表示同步所有
//        if (!SPSingleton.get().getBoolean(SPKeyDefine.SP_ReceiveCouponNotification, true)) {
//            typeList = ArrayList()
//            typeList.add(1)
//        }
        return SDKSingleton.syncBl.syncAllNotificationData(null)
    }

    @JvmStatic
    val isApkInDebug: Boolean
        get() {
            try {
                val info = APP.get().applicationInfo
                return info.flags and ApplicationInfo.FLAG_DEBUGGABLE != 0
            } catch (e: Exception) {
                return false
            }
        }

    /**
     * 禁止EditText输入空格和换行符
     *
     * @param editText EditText输入框
     */
    fun setEditTextInputSpace(editText: EditText?) {
        val filter = InputFilter { source, _, _, _, _, _ ->
            if (source == " " || source.toString().contentEquals("\n")) {
                ""
            } else {
                null
            }
        }
        editText!!.filters = arrayOf(filter)
    }

    /**
     * 判断是否是 Android 15 及以上版本
     */
    fun isAboveAndroid15(): Boolean {
        return Build.VERSION.SDK_INT > Build.VERSION_CODES.UPSIDE_DOWN_CAKE
    }
}
