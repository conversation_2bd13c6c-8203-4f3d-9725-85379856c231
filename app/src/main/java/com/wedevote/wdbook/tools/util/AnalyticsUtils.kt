package com.wedevote.wdbook.tools.util

import android.app.Application
import android.content.Context
import com.amazonaws.auth.CognitoCredentialsProvider
import com.amazonaws.mobile.config.AWSConfiguration
import com.amazonaws.mobileconnectors.pinpoint.PinpointConfiguration
import com.amazonaws.mobileconnectors.pinpoint.PinpointManager
import com.amazonaws.mobileconnectors.pinpoint.analytics.AnalyticsClient
import com.amazonaws.regions.Regions
import com.aquila.lib.log.KLog
import com.microsoft.clarity.Clarity
import com.microsoft.clarity.ClarityConfig
import com.microsoft.clarity.models.LogLevel
import com.wdbible.app.wedevotebible.tools.security.EncodingUtils.md5
import com.wedevote.wdbook.analytics.EventLogger
import com.wedevote.wdbook.analytics.PlatformInformation
import com.wedevote.wdbook.base.APP
import com.wedevote.wdbook.base.APPConfig
import com.wedevote.wdbook.base.SDKSingleton
import com.wedevote.wdbook.constants.AnalyticsConstants
import com.wedevote.wdbook.constants.AppEnvironment
import com.wedevote.wdbook.constants.Constants
import com.wedevote.wdbook.ui.read.ReadingSessionTracker
import org.json.JSONObject
import java.util.concurrent.Executors
import java.util.concurrent.locks.ReentrantLock

/***
 * @date 创建时间 12/8/20 10:09 AM
 * <AUTHOR> Qian kai
 * @description
 */
object AnalyticsUtils {

    private var pinpointAnalyticsClient: AnalyticsClient? = null

    private val executor = Executors.newCachedThreadPool()

    private val lock = ReentrantLock()

    private val submitRateLimiter = RateLimiter(300000) // 5 分钟内至多 submitEvents 一次

    fun init(thisApp: Application) {
        initPinpoint(thisApp)
        initClarity(thisApp)
        initEventLogger()

        updateAnalyticsUserID()
    }

    private fun initClarity(thisApp: Application) {
        // Clarity初始化
        val config =
            if (APPConfig.env == AppEnvironment.TEST) {
                ClarityConfig(Constants.CLARITY_PROJECT_ID_TEST, null, LogLevel.Verbose)
            } else {
                ClarityConfig(Constants.CLARITY_PROJECT_ID)
            }
        Clarity.initialize(thisApp, config)
    }

    private fun initPinpoint(thisApp: Context) {
        val appId = AnalyticsConstants.PINPOINT_APP_ID
        val configMap = mapOf(
            "PinpointAnalytics" to mapOf(
                "AppId" to appId,
                "Region" to AnalyticsConstants.PINPOINT_REGION
            )
        )
        try {
            val pinpointConfig = PinpointConfiguration(
                thisApp,
                CognitoCredentialsProvider(AnalyticsConstants.IDENTITY_POOL_ID, Regions.US_WEST_2),
                AWSConfiguration(JSONObject(configMap))
            )
            pinpointAnalyticsClient = PinpointManager(pinpointConfig).analyticsClient
            KLog.i("Success initializing Pinpoint")
        } catch (e: Exception) {
            KLog.e("Exception when initializing Pinpoint : $e")
            e.printStackTrace()
        }
    }

    private fun initEventLogger() {
        EventLogger.initializeLogger(
            PlatformInformation(APP.get()),
            APPConfig.env.analyticsEndPoint,
            sdk = SDKSingleton.sdk
        )
        ReadingSessionTracker.handleAbnormalTermination(APP.get())
    }

    fun logEvent(eventString: String, vararg params: String): Boolean {
        // 如果 Event Name 为空，则废弃本事件上报
        if (eventString.isEmpty()) {
            KLog.e("Event name is empty, skip submitting")
            return false
        }

        val map = HashMap<String, String>()
        if (params.isNotEmpty()) {
            if (params.size % 2 == 0) {
                for (i in params.indices step 2) {
                    map[params[i]] = params[i + 1]
                }
            } else {
                KLog.e("注意，传递的参数不符合要求，必须是偶数个")
                return false
            }
        }

        EventLogger.logEvent(eventString, map)

        /**
         * AWS Pinpoint
         */
        if (pinpointAnalyticsClient != null) {
            executor.execute {
                try {
                    lock.lock()
                    // Log Event of AWS Pinpoint
                    val event = pinpointAnalyticsClient?.createEvent(eventString)
                    map.forEach { (key, value) ->
                        event?.addAttribute(key, value)
                    }
                    pinpointAnalyticsClient?.recordEvent(event)
                    KLog.i("Pinpoint record event success:", "$eventString:${map.toJsonStr()}")
                } catch (e: Exception) {
                    KLog.e("Pinpoint record event failed : $e")
                    e.printStackTrace()
                } finally {
                    lock.unlock()
                }
            }
        } else {
            KLog.e("Pinpoint - pinpointAnalyticsClient is null, cannot record event $eventString.")
            return false
        }

        return true
    }

    /**
     * Pinpoint 的 submitEvents（上报所有存储在本地数据库的事件）的时机，改成
     * 切换 Tab 页的时候，并且 5 分钟之内再次切换不会重复调用
     */
    fun pinpointSubmitEvents() {
        if (pinpointAnalyticsClient != null) {
            if (submitRateLimiter.shouldAllowAction()) {
                executor.execute {
                    lock.lock()
                    pinpointAnalyticsClient?.submitEvents({
                        KLog.i("AWS Pinpoint - submitted events successfully.")
                        lock.unlock()
                    }
                    ) { t ->
                        KLog.e("AWS Pinpoint - Error submitting events : $t")
                        t.printStackTrace()
                        lock.unlock()
                    }
                }
            }
        } else {
            KLog.e("AWS Pinpoint - pinpointAnalyticsClient is null, cannot submit events.")
        }
    }

    @JvmStatic
    fun updateAnalyticsUserID() {
        val deviceId = SDKSingleton.sessionBl.deviceId
        val userId = SDKSingleton.sessionBl.userId
        val userIdSend = if (userId.isNotEmpty() && Constants.ANONYMOUS_USER_ID != userId) {
            md5(userId) + "|" + md5(deviceId)
        } else {
            "|" + md5(deviceId)
        }

        EventLogger.setUserID(userId)

        /**
         * AWS Pinpoint
         */
        if (pinpointAnalyticsClient != null) {
            try {
                pinpointAnalyticsClient?.addGlobalAttribute("userId", userIdSend)
            } catch (e: Exception) {
                KLog.e("Exception when adding Pinpoint global attribute userId : $e")
                e.printStackTrace()
            }
        }
    }
}
